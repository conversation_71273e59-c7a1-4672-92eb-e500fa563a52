<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
    </domain-config>
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user added CAs while debuggable only -->
            <certificates src="user" />
            
        </trust-anchors>
    </debug-overrides>
            <base-config cleartextTrafficPermitted="true"></base-config>
</network-security-config>