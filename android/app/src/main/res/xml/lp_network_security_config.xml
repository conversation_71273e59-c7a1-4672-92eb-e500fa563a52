<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!--Set application-wide security config using base-config tag.-->
    <base-config cleartextTrafficPermitted="true" />
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user added CAs while debuggable only -->
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>

    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">lz.changiairport.com</domain>
        <pin-set>
            <pin digest="SHA-256">++MBgDH5WGvL9Bcn5Be30cRcL0f5O+NyoXuWtQdX1aI=</pin>
            <pin digest="SHA-256">f0KW/FtqTjs108NpYj42SrGvOB2PpxIVM8nWxjPqJGE=</pin>
            <pin digest="SHA-256">NqvDJlas/GRcYbcWE8S/IceH9cq77kg0jVhZeAPXq8k=</pin>
            <pin digest="SHA-256">9+ze1cZgR9KO1kZrVDxA4HQ6voHRCSVNz4RdTCx4U8U=</pin>
            <pin digest="SHA-256">KwccWaCgrnaw6tsrrSO61FgLacNgG2MMLq8GE6+oP5I=</pin>
            <pin digest="SHA-256">4a6cPehI7OG6cuDZka5NDZ7FR8a60d3auda+sKfg4Ng=</pin>
        </pin-set>
    </domain-config>

    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">sandbox.api.liquidpay.com</domain>
        <pin-set>
            <pin digest="SHA-256">QLjfWUXQbLQABe4qTwuIz+b5bekZxSfSc3+0rDfGYEM=</pin>
            <pin digest="SHA-256">8Rw90Ej3Ttt8RRkrg+WYDS9n7IS03bk5bjP/UXPtaY8=</pin>
        </pin-set>
        <trustkit-config enforcePinning="true"/>
    </domain-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">liquidpay-dev.apigee.net</domain>
        <pin-set>
            <pin digest="SHA-256">RgXzjcSR2v7o3RXD81TEzPPZ5SFOJFpeZe6O+gEH8VQ=</pin>
            <pin digest="SHA-256">YZPgTZ+woNCCCIW3LH2CxQeLzB/1m42QcCTBSdgayjs=</pin>
        </pin-set>
        <trustkit-config enforcePinning="true"/>
    </domain-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">changiappsit-aem.lz.changiairport.com</domain>
    </domain-config>
</network-security-config>