package com.changiairport.cagapp3

// Unimodules https://docs.expo.io/bare/installing-unimodules/#configuration-for-android
import com.adobe.marketing.mobile.AdobeCallback
import com.adobe.marketing.mobile.Identity
import com.adobe.marketing.mobile.InvalidInitException
import com.adobe.marketing.mobile.Lifecycle
import com.adobe.marketing.mobile.LoggingMode
import com.adobe.marketing.mobile.MobileCore
import com.adobe.marketing.mobile.MobileServices
import com.adobe.marketing.mobile.Signal
import com.adobe.marketing.mobile.UserProfile
import com.adobe.marketing.mobile.WrapperType
import com.adobe.marketing.mobile.Analytics
import com.adobe.marketing.mobile.Campaign
import com.adobe.marketing.mobile.Target

import android.app.Application
import android.content.Context
import android.util.Log

// import com.changiairport.cagapp3.generated.BasePackageList;
import com.braze.support.BrazeLogger
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
// import com.rnfingerprint.FingerprintAuthPackage;
import com.facebook.react.ReactInstanceManager
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.soloader.SoLoader
//import java.util.List
//import java.util.Arrays
//import java.util.logging.Level
//import java.util.logging.Logger
 
import com.braze.BrazeActivityLifecycleCallbackListener

class MainApplication : Application(), ReactApplication {

  override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): List<ReactPackage> =
            PackageList(this).packages.apply {
              // Packages that cannot be autolinked yet can be added manually here, for example:
              add(MyAppPackage())
              // add(MyReactNativePackage())
            }
 
        override fun getJSMainModuleName(): String = "index"
 
        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG
 
        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
      }

  override val reactHost: ReactHost
      get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate();
    registerActivityLifecycleCallbacks(BrazeActivityLifecycleCallbackListener());
    // BrazeLogger.setLogLevel(Log.DEBUG);
    SoLoader.init(this, /* native exopackage */ false);
     if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load();
    }

    MobileCore.setApplication(this);
    MobileCore.setLogLevel(LoggingMode.DEBUG);
    MobileCore.setWrapperType(WrapperType.REACT_NATIVE);
//    logger.log(Level.INFO, "adobe analytics enabled");
    try {
      Analytics.registerExtension();
      MobileServices.registerExtension();
      Identity.registerExtension();
      Lifecycle.registerExtension();
      Signal.registerExtension();
      UserProfile.registerExtension();
      Campaign.registerExtension();
      Target.registerExtension();
      MobileCore.start(object : AdobeCallback<Any?> {
        override fun call(o: Any?) {
          MobileCore.configureWithAppID("669a23884c44/a4d46dc0e7a2/launch-7be9e717566b-development");
        }
      })
    } catch (e: InvalidInitException) {
        //Log the exception
    }
  }
}
