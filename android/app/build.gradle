apply plugin: "com.android.application"
apply plugin: 'kotlin-android'
apply plugin: "com.facebook.react"

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.appdistribution'

import com.android.build.OutputFile
import org.apache.tools.ant.taskdefs.condition.Os

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")
 
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
 
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
 
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to create two separate APKs instead of one:
 *   - An APK that only works on ARM devices
 *   - An APK that only works on x86 devices
 * The advantage is the size of the APK is reduced by about 4MB.
 * Upload all the APKs to the Play Store and people will download
 * the correct one based on the CPU architecture of their device.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Architectures to build native code for.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
    }
    namespace "com.changiairport.cagapp3"
    defaultConfig {
        applicationId "com.changiairport.cagapp3"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0.0"
        testBuildType System.getProperty('testBuildType', 'debug')
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        vectorDrawables.useSupportLibrary = true
    }

    // cpay sdk v1.7.0 related config..
    buildFeatures {
        viewBinding = true 
    } 

    repositories {
        flatDir { dirs 'libs' }
    }

    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include (*reactNativeArchitectures())
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('RELEASE_STORE_FILE')) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PD
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            shrinkResources true
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }
        }
    }
    packagingOptions {
        exclude 'META-INF/com.android.tools/proguard/coroutines.pro'
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    flavorDimensions "ichangi3"
    //Flavours of the app
    productFlavors {
        sit {
            applicationId = "com.changiairport.cagapp3.sit"
        }
        uat {
            applicationId = "com.changiairport.cagapp3.uat"
        }
        preprod {
            applicationId = "com.changiairport.cagapp3.preprod"
        }
        prod {
            applicationId = "com.changiairport.cagapp"
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // https://developer.android.com/studio/build/configure-apk-splits.html
            // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        defaultConfig.versionCode * 1000 + versionCodes.get(abi)
            }

        }
    }
}

def supportLibraryVersion = '28.0.0'
def contraintLayoutVersion = '1.1.3'
def playServiceVersion = '21.0.1'
def retrofitVersion = '2.9.0'
def okhttpLoggerVersion = '4.7.2'
def zxingVersion = '3.3.0'
def picassoVersion = '2.8'
def cardioVersion = '5.5.1'
def trustkitVersion = '1.1.3'

dependencies {
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    annotationProcessor 'androidx.lifecycle:lifecycle-compiler:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.2.0'
    implementation "io.github.singpass:singpass-webview-client:1.2.0"

    implementation 'com.facebook.fresco:fresco:3.2.0'
    implementation 'com.facebook.fresco:animated-gif:3.2.0'
    implementation 'com.facebook.fresco:webpsupport:3.2.0'
    // Optionally, to display animated WebP images, you have to add:
    implementation 'com.facebook.fresco:animated-webp:3.2.0'
    // implementation fileTree(dir: "libs", include: ["*.jar"])

    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation 'io.branch.sdk.android:adobebranchextension:1.+'
    implementation 'com.adobe.marketing.mobile:audience:1.+'
    implementation 'com.adobe.marketing.mobile:target:1.+'
    implementation 'com.adobe.marketing.mobile:analytics:1.+'
    implementation 'com.adobe.marketing.mobile:mobileservices:1.+'
    implementation 'com.adobe.marketing.mobile:userprofile:1.+'
    implementation 'com.adobe.marketing.mobile:sdk-core:1.+'
    implementation 'com.google.code.gson:gson:2.8.8'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.12.5'

    androidTestImplementation('com.wix:detox:+') { transitive = true }
    androidTestImplementation 'junit:junit:4.12'
    implementation "androidx.annotation:annotation:1.1.0"

    implementation "androidx.core:core-splashscreen:1.0.0"
    implementation 'androidx.biometric:biometric:1.1.0'

    // implementation "androidx.constraintlayout:constraintlayout:$contraintLayoutVersion"
    //Android support library
    implementation ("com.google.android.gms:play-services-location:$playServiceVersion") {
        exclude group: 'com.android.support', module: 'support-v4'
    }
    // implementation ("com.google.android.gms:play-services-safetynet:18.0.1") {
    //     exclude group: 'com.android.support', module: 'support-v4'
    // }
    // implementation "com.android.support:appcompat-v7:$supportLibraryVersion"
    // implementation "com.android.support:design:$supportLibraryVersion"
    // implementation "com.android.support:cardview-v7:$supportLibraryVersion"
    // implementation "com.android.support.constraint:constraint-layout:$contraintLayoutVersion"
    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    //Retrofit
    implementation "com.squareup.retrofit2:converter-scalars:$retrofitVersion"
    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-jackson:$retrofitVersion"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttpLoggerVersion"
    implementation("com.squareup.okhttp3:okhttp-urlconnection:4.12.0")
    //Card io
    implementation "io.card:android-sdk:$cardioVersion"
    //Zxing QR scanner
    implementation "com.google.zxing:core:$zxingVersion"
    implementation "com.google.zxing:javase:$zxingVersion"
    //Picasso
    implementation ("com.squareup.picasso:picasso:$picassoVersion") {
        exclude group: 'com.android.support'
    }
    //Trustkit
    implementation "com.datatheorem.android.trustkit:trustkit:$trustkitVersion"

    implementation('io.jsonwebtoken:jjwt-api:0.10.5')
    runtimeOnly('io.jsonwebtoken:jjwt-jackson:0.10.5')

    implementation project(':react-native-qualtrics')

    // Cpay sdk 1.8.1 dep..
    implementation ('androidx.browser:browser:1.5.0') 
    // CPay sdk 1.12.0 dep..
    implementation "com.liquid.widget:sdk:$cpaySdkVersion"

    debugImplementation 'com.facebook.soloader:soloader:0.10.5'

     if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    // Google Maps dependency
    implementation 'com.google.android.gms:play-services-maps:18.1.0'
}

task printAppId {
    doLast {
        def variantName = project.hasProperty("variantName") ? project.property("variantName") : "debug"
        android.applicationVariants.all { variant ->
            if (variant.name == variantName) {
                println "Final applicationId: ${variant.applicationId}"
            }
        }
    }
}
task listVariants {
    doLast {
        println "Available variants:"
        android.applicationVariants.all { variant ->
            println variant.name
        }
    }
}
