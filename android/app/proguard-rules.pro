# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

## from react-native-svg
-keep public class com.horcrux.svg.** {*;}

# Hermes options
-keep class com.facebook.hermes.unicode.** { *; }
-keep class com.facebook.jni.** { *; }

# Shortcut Badge
-keep class me.leolin.shortcutbadger.impl.** { <init>(...); }

# Reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }

# ChangiPay
-keep class widget.liquidpay.com.widget.model.** { *; }
-keep class widget.liquidpay.com.widget.network.LPService { *; }
-keep class widget.liquidpay.com.widget.handler.LPPaymentInterface { public <methods>; }
-keep class widget.liquidpay.com.widget.handler.LPWidgetBuilder { public <methods>; }
-keep class widget.liquidpay.com.widget.handler.LPWidgetInterface { public <methods>; }
-keep class widget.liquidpay.com.widget.handler.LPException { public <methods>; }
-keep class widget.liquidpay.com.widget.utility.LPUtils { public <methods>; }
-keep class widget.liquidpay.com.widget.handler.LPWidgetStatus {
    <fields>;
    public <methods>;
}
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# maplibre
-dontnote com.mapbox.mapboxsdk.**
-keep class com.mapbox.mapboxsdk.** { *; }

# qualtrics
-keep class com.qualtrics.** {*;}

# Retrofit and ts.b related rules
-keep interface ts.b { *; }
-keep class ts.y { *; }
-keep class ts.k { *; }
-keep class ts.v { *; }
-keep class ts.u { *; }

# Annotations and other Retrofit related
-keepattributes Signature
-keepattributes RuntimeVisibleAnnotations
-keepattributes AnnotationDefault
-keepattributes Exceptions

# Retrofit
-dontwarn okhttp3.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# 0.75
-dontwarn com.adobe.mobile.Visitor
-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerClient$Builder
-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerClient
-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerStateListener
-dontwarn java.awt.Point
-dontwarn javax.imageio.spi.ImageInputStreamSpi
-dontwarn javax.imageio.spi.ImageOutputStreamSpi
-dontwarn javax.imageio.spi.ImageReaderSpi
-dontwarn javax.imageio.spi.ImageWriterSpi
-dontwarn com.iap.android.container.resource.ResourceManager$Companion
-dontwarn com.iap.android.container.resource.ResourceManager
-dontwarn com.iap.android.container.resource.manifest.IManifestResourceObserver
-dontwarn com.iap.android.container.resource.manifest.impl.ManifestResourceHandle
-dontwarn com.iap.android.container.resource.protocol.IResourceCache
-dontwarn com.iap.android.container.resource.protocol.IResourceHandle
-dontwarn com.facebook.imagepipeline.nativecode.WebpTranscoder