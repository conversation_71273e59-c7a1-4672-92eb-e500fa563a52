// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* ichangiFeTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* ichangiFeTests.m */; };
		079159BC5CCA4880A7DC6351 /* Lato-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FF55EC73BC9E47DBB19B90E3 /* Lato-Italic.ttf */; };
		082CAB609FE9431F9A6B82F2 /* Lato-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F400D6C93DD45B280AD5D8F /* Lato-BlackItalic.ttf */; };
		0884F34B2CA2C4000067B86E /* Teko-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0884F3462CA2C3FF0067B86E /* Teko-SemiBold.ttf */; };
		0884F34C2CA2C4000067B86E /* Teko-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0884F3472CA2C3FF0067B86E /* Teko-Regular.ttf */; };
		0884F34D2CA2C4000067B86E /* Teko-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0884F3482CA2C4000067B86E /* Teko-Medium.ttf */; };
		0884F34E2CA2C4000067B86E /* Teko-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0884F3492CA2C4000067B86E /* Teko-Bold.ttf */; };
		0884F34F2CA2C4000067B86E /* Teko-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0884F34A2CA2C4000067B86E /* Teko-Light.ttf */; };
		0C00EDC82C748A73004F8F00 /* WidgetKitHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C00EDC72C748A73004F8F00 /* WidgetKitHelper.swift */; };
		0C00EDC92C748A73004F8F00 /* WidgetKitHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C00EDC72C748A73004F8F00 /* WidgetKitHelper.swift */; };
		0CE3FC0E2C72FE24009793C4 /* UpdateWidgetModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 0CE3FC0D2C72FE24009793C4 /* UpdateWidgetModule.m */; };
		0CE3FC0F2C72FE24009793C4 /* UpdateWidgetModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 0CE3FC0D2C72FE24009793C4 /* UpdateWidgetModule.m */; };
		12DACBA5E4A6DCA908BADB98 /* libPods-ichangiFe-ichangiFeTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B05C7E5ADE61A81AAA3EC899 /* libPods-ichangiFe-ichangiFeTests.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		17840EB828DF1FFC00DC0757 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 17840EB728DF1FFC00DC0757 /* GoogleService-Info.plist */; };
		17840EB928DF1FFC00DC0757 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 17840EB728DF1FFC00DC0757 /* GoogleService-Info.plist */; };
		17B7C15929ADB0460071CCCE /* LiquidPayWidget.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 17B7C15729ADB0460071CCCE /* LiquidPayWidget.xcframework */; };
		17B7C15A29ADB0610071CCCE /* LiquidPayWidget.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 17B7C15729ADB0460071CCCE /* LiquidPayWidget.xcframework */; };
		17B7C15B29ADB0610071CCCE /* LiquidPayWidget.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 17B7C15729ADB0460071CCCE /* LiquidPayWidget.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		25A4F24F23D14F138A4835C7 /* Lato-HairlineItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEADFC39CB6245E9BADAE10A /* Lato-HairlineItalic.ttf */; };
		2B39A55A5E50443E81C88C44 /* Lato-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE8ADC4817154770AD7ADD86 /* Lato-Regular.ttf */; };
		2C048B0C2965548F000E783E /* RNEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C048B0B2965548F000E783E /* RNEventEmitter.m */; };
		2C048B0D2965548F000E783E /* RNEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C048B0B2965548F000E783E /* RNEventEmitter.m */; };
		2C048B0F296554B2000E783E /* RNEventEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C048B0E296554B2000E783E /* RNEventEmitter.swift */; };
		2C048B10296554B2000E783E /* RNEventEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C048B0E296554B2000E783E /* RNEventEmitter.swift */; };
		342D249ED0038397424EB565 /* libPods-ichangiFe.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A3751D3A3AB4BEF5D45827CE /* libPods-ichangiFe.a */; };
		43228EFB4F2442BB8F7608CB /* Lato-Hairline.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C74D97872B9F49EA84DB95B9 /* Lato-Hairline.ttf */; };
		52666EB88372473A9AF52CD5 /* Lato-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 44941A6063E94C1FB8E0A163 /* Lato-BoldItalic.ttf */; };
		61F66FFDE1034041BBC50A33 /* Lato-Semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 834ECC9EB65C4A26809F5BFE /* Lato-Semibold.ttf */; };
		67DCC39368B1423CB4AA48DD /* Lato-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 966464DD054749319AE7B35E /* Lato-Heavy.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		90F5E9D651B14973A42C6464 /* Lato-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 267310D9936743DFB1B9FFB2 /* Lato-Thin.ttf */; };
		9DCCC320B33F4E5A82C96ADE /* Lato-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5DD225BF772642A2A3786A28 /* Lato-ThinItalic.ttf */; };
		A18D30CF81A94E39A88A5154 /* Lato-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D6F52D3521B2462AB313A4D2 /* Lato-Light.ttf */; };
		AAACCF9CB08F4E1A96824ED5 /* Lato-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0DFCE198DAC74D8CB84C75F4 /* Lato-MediumItalic.ttf */; };
		BB9B746AE4F1428399E4BE98 /* Lato-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 582DFB731A554358B010E446 /* Lato-Medium.ttf */; };
		CB94FC00D06A495486F819FA /* Lato-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F7F64B231FCE4DB99352E041 /* Lato-Bold.ttf */; };
		DAB423EFE45E45CD89D0BD19 /* Lato-HeavyItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A70F36C2485F43909FFF875A /* Lato-HeavyItalic.ttf */; };
		E11878912C6370C300EF469F /* Lato-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BE8ADC4817154770AD7ADD86 /* Lato-Regular.ttf */; };
		E11878952C6370C300EF469F /* Lato-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FF55EC73BC9E47DBB19B90E3 /* Lato-Italic.ttf */; };
		E118789B2C6370C300EF469F /* Lato-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 44941A6063E94C1FB8E0A163 /* Lato-BoldItalic.ttf */; };
		E118789F2C6370C300EF469F /* Lato-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F7F64B231FCE4DB99352E041 /* Lato-Bold.ttf */; };
		E1286FDE292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E1286FDD292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard */; };
		E1286FDF292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E1286FDD292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard */; };
		E1286FE4292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1286FE3292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift */; };
		E1286FE5292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1286FE3292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift */; };
		E12F74702B732E5B005885DC /* DataModule.m in Sources */ = {isa = PBXBuildFile; fileRef = E12F746F2B732E5B005885DC /* DataModule.m */; };
		E143636628CAA0FE00E4F6C8 /* main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E143636528CAA0FE00E4F6C8 /* main.storyboard */; };
		E143636728CAA0FE00E4F6C8 /* main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E143636528CAA0FE00E4F6C8 /* main.storyboard */; };
		E143636928CAA82700E4F6C8 /* MyViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E143636828CAA82700E4F6C8 /* MyViewController.swift */; };
		E143636A28CAA82700E4F6C8 /* MyViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E143636828CAA82700E4F6C8 /* MyViewController.swift */; };
		E14E4A662AA9B8B000ADCCF1 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = EF486179A1309C75691A46D3 /* Mapbox */; };
		E14E4A672AA9B8B000ADCCF1 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = 8B97F171CB9C24406F5382CC /* Mapbox */; };
		E16A52D02B4EBF2A000DD5B7 /* BrazeReactDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = E16A52CF2B4EBF2A000DD5B7 /* BrazeReactDelegate.m */; };
		E1A0AD722C61BF8D005B1F21 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E1A0AD712C61BF8D005B1F21 /* WidgetKit.framework */; };
		E1A0AD742C61BF8D005B1F21 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E1A0AD732C61BF8D005B1F21 /* SwiftUI.framework */; };
		E1A0AD772C61BF8D005B1F21 /* changi_rewardBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1A0AD762C61BF8D005B1F21 /* changi_rewardBundle.swift */; };
		E1A0AD7B2C61BF8D005B1F21 /* changi_reward.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1A0AD7A2C61BF8D005B1F21 /* changi_reward.swift */; };
		E1A0AD7F2C61BF8F005B1F21 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E1A0AD7E2C61BF8F005B1F21 /* Assets.xcassets */; };
		E1A0AD832C61BF8F005B1F21 /* changi-rewardExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = E1A0AD702C61BF8D005B1F21 /* changi-rewardExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E1AB31982BB590DF00E25234 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = E1AB31972BB590DF00E25234 /* PrivacyInfo.xcprivacy */; };
		E1B1A61128BBF4F00056AEDE /* ChangiPayModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1B1A61028BBF4F00056AEDE /* ChangiPayModule.swift */; };
		E1B1A61228BBF4F00056AEDE /* ChangiPayModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1B1A61028BBF4F00056AEDE /* ChangiPayModule.swift */; };
		E1B1A61528BBF55A0056AEDE /* RCTBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = E1B1A61428BBF55A0056AEDE /* RCTBridge.m */; };
		E1B1A61628BBF55A0056AEDE /* RCTBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = E1B1A61428BBF55A0056AEDE /* RCTBridge.m */; };
		E1E02A5C2B61175300BFC352 /* BrazeEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = E1E02A5B2B61175300BFC352 /* BrazeEventEmitter.m */; };
		E1E02A5D2B61175300BFC352 /* BrazeEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = E1E02A5B2B61175300BFC352 /* BrazeEventEmitter.m */; };
		E1E02A632B6267AF00BFC352 /* InAppBrowserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E1E02A622B6267AF00BFC352 /* InAppBrowserManager.m */; };
		E1E02A642B6267AF00BFC352 /* InAppBrowserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E1E02A622B6267AF00BFC352 /* InAppBrowserManager.m */; };
		E20512B8912C459D8A09EB78 /* Lato-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8451590EE3C1408D8FDC0639 /* Lato-LightItalic.ttf */; };
		E6B480C41F824457AC81884D /* Lato-SemiboldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9DDBBE66A883425D862577F0 /* Lato-SemiboldItalic.ttf */; };
		F2864D6924F44A5DA5D1BF5D /* Lato-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AB9809B31C0645E5A06C5478 /* Lato-Black.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = ichangiFe;
		};
		E1A0AD812C61BF8F005B1F21 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E1A0AD6F2C61BF8D005B1F21;
			remoteInfo = "changi-rewardExtension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		179C695F298CAC1A005E30C2 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				17B7C15B29ADB0610071CCCE /* LiquidPayWidget.xcframework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		E1A0AD842C61BF90005B1F21 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				E1A0AD832C61BF8F005B1F21 /* changi-rewardExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		00E356EE1AD99517003FC87E /* ichangiFeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ichangiFeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* ichangiFeTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ichangiFeTests.m; sourceTree = "<group>"; };
		0884F3462CA2C3FF0067B86E /* Teko-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Teko-SemiBold.ttf"; path = "../assets/fonts/Teko-SemiBold.ttf"; sourceTree = "<group>"; };
		0884F3472CA2C3FF0067B86E /* Teko-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Teko-Regular.ttf"; path = "../assets/fonts/Teko-Regular.ttf"; sourceTree = "<group>"; };
		0884F3482CA2C4000067B86E /* Teko-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Teko-Medium.ttf"; path = "../assets/fonts/Teko-Medium.ttf"; sourceTree = "<group>"; };
		0884F3492CA2C4000067B86E /* Teko-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Teko-Bold.ttf"; path = "../assets/fonts/Teko-Bold.ttf"; sourceTree = "<group>"; };
		0884F34A2CA2C4000067B86E /* Teko-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Teko-Light.ttf"; path = "../assets/fonts/Teko-Light.ttf"; sourceTree = "<group>"; };
		0C00EDC72C748A73004F8F00 /* WidgetKitHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetKitHelper.swift; sourceTree = "<group>"; };
		0C00EDCB2C75E06E004F8F00 /* changi-rewardExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "changi-rewardExtension.entitlements"; sourceTree = "<group>"; };
		0CE3FC0D2C72FE24009793C4 /* UpdateWidgetModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpdateWidgetModule.m; sourceTree = "<group>"; };
		0CE3FC102C72FE59009793C4 /* UpdateWidgetModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpdateWidgetModule.h; sourceTree = "<group>"; };
		0DFCE198DAC74D8CB84C75F4 /* Lato-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-MediumItalic.ttf"; path = "../assets/fonts/Lato-MediumItalic.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* ichangiFe.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ichangiFe.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = ichangiFe/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = ichangiFe/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ichangiFe/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ichangiFe/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = ichangiFe/main.m; sourceTree = "<group>"; };
		17840EAE28D8728A00DC0757 /* ichangiFe.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = ichangiFe.entitlements; path = ichangiFe/ichangiFe.entitlements; sourceTree = "<group>"; };
		17840EB728DF1FFC00DC0757 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		17B7C15729ADB0460071CCCE /* LiquidPayWidget.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = LiquidPayWidget.xcframework; sourceTree = "<group>"; };
		267310D9936743DFB1B9FFB2 /* Lato-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Thin.ttf"; path = "../assets/fonts/Lato-Thin.ttf"; sourceTree = "<group>"; };
		2853C5EA8F9CD31D567557AE /* Pods-ichangiFe.sit.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.sit.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.sit.xcconfig"; sourceTree = "<group>"; };
		2C048B0B2965548F000E783E /* RNEventEmitter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNEventEmitter.m; sourceTree = "<group>"; };
		2C048B0E296554B2000E783E /* RNEventEmitter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNEventEmitter.swift; sourceTree = "<group>"; };
		2E541935CE549B82463E1222 /* Pods-ichangiFe-ichangiFeTests.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.preprod.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.preprod.xcconfig"; sourceTree = "<group>"; };
		44941A6063E94C1FB8E0A163 /* Lato-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-BoldItalic.ttf"; path = "../assets/fonts/Lato-BoldItalic.ttf"; sourceTree = "<group>"; };
		487FAE155F0A96FBA3D0EDE6 /* Pods-ichangiFe.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.preprod.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.preprod.xcconfig"; sourceTree = "<group>"; };
		4B5E2EFA5EE4F4305A7327FD /* Pods-ichangiFe-ichangiFeTests.sit.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.sit.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.sit.xcconfig"; sourceTree = "<group>"; };
		582DFB731A554358B010E446 /* Lato-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Medium.ttf"; path = "../assets/fonts/Lato-Medium.ttf"; sourceTree = "<group>"; };
		5DD225BF772642A2A3786A28 /* Lato-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-ThinItalic.ttf"; path = "../assets/fonts/Lato-ThinItalic.ttf"; sourceTree = "<group>"; };
		6700E751E4DDF7EC5468D0BE /* Pods-ichangiFe-ichangiFeTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.release.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.release.xcconfig"; sourceTree = "<group>"; };
		77AD3F7EC0E712F82DB19ABF /* Pods-ichangiFe-ichangiFeTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.debug.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.debug.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = ichangiFe/LaunchScreen.storyboard; sourceTree = "<group>"; };
		834ECC9EB65C4A26809F5BFE /* Lato-Semibold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Semibold.ttf"; path = "../assets/fonts/Lato-Semibold.ttf"; sourceTree = "<group>"; };
		8451590EE3C1408D8FDC0639 /* Lato-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-LightItalic.ttf"; path = "../assets/fonts/Lato-LightItalic.ttf"; sourceTree = "<group>"; };
		966464DD054749319AE7B35E /* Lato-Heavy.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Heavy.ttf"; path = "../assets/fonts/Lato-Heavy.ttf"; sourceTree = "<group>"; };
		9DDBBE66A883425D862577F0 /* Lato-SemiboldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-SemiboldItalic.ttf"; path = "../assets/fonts/Lato-SemiboldItalic.ttf"; sourceTree = "<group>"; };
		9F400D6C93DD45B280AD5D8F /* Lato-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-BlackItalic.ttf"; path = "../assets/fonts/Lato-BlackItalic.ttf"; sourceTree = "<group>"; };
		A3751D3A3AB4BEF5D45827CE /* libPods-ichangiFe.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ichangiFe.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A3A82A91C7707C6088C606F3 /* Pods-ichangiFe.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.release.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.release.xcconfig"; sourceTree = "<group>"; };
		A70F36C2485F43909FFF875A /* Lato-HeavyItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-HeavyItalic.ttf"; path = "../assets/fonts/Lato-HeavyItalic.ttf"; sourceTree = "<group>"; };
		AB9809B31C0645E5A06C5478 /* Lato-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Black.ttf"; path = "../assets/fonts/Lato-Black.ttf"; sourceTree = "<group>"; };
		ADB10BBEF2881D9DC65AB5BD /* Pods-ichangiFe.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.debug.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.debug.xcconfig"; sourceTree = "<group>"; };
		B056E6608A6BEF24AD9EE90C /* Pods-ichangiFe.uat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.uat.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.uat.xcconfig"; sourceTree = "<group>"; };
		B05C7E5ADE61A81AAA3EC899 /* libPods-ichangiFe-ichangiFeTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ichangiFe-ichangiFeTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		BE8ADC4817154770AD7ADD86 /* Lato-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Regular.ttf"; path = "../assets/fonts/Lato-Regular.ttf"; sourceTree = "<group>"; };
		BEADFC39CB6245E9BADAE10A /* Lato-HairlineItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-HairlineItalic.ttf"; path = "../assets/fonts/Lato-HairlineItalic.ttf"; sourceTree = "<group>"; };
		C74D97872B9F49EA84DB95B9 /* Lato-Hairline.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Hairline.ttf"; path = "../assets/fonts/Lato-Hairline.ttf"; sourceTree = "<group>"; };
		CA576A738194737890256CB8 /* Pods-ichangiFe-ichangiFeTests.uat.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.uat.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.uat.xcconfig"; sourceTree = "<group>"; };
		D6F52D3521B2462AB313A4D2 /* Lato-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Light.ttf"; path = "../assets/fonts/Lato-Light.ttf"; sourceTree = "<group>"; };
		DC199562DD1A21E1A2A502B9 /* Pods-ichangiFe.production.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe.production.xcconfig"; path = "Target Support Files/Pods-ichangiFe/Pods-ichangiFe.production.xcconfig"; sourceTree = "<group>"; };
		E1286FDD292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = ChangiLaunchScreen.storyboard; path = ichangiFe/ChangiLaunchScreen.storyboard; sourceTree = "<group>"; };
		E1286FE3292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangiLaunchScreenUIView.swift; sourceTree = "<group>"; };
		E12F746F2B732E5B005885DC /* DataModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DataModule.m; sourceTree = "<group>"; };
		E12F74712B732E8C005885DC /* DataModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DataModule.h; sourceTree = "<group>"; };
		E143636528CAA0FE00E4F6C8 /* main.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = main.storyboard; sourceTree = "<group>"; };
		E143636828CAA82700E4F6C8 /* MyViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyViewController.swift; sourceTree = "<group>"; };
		E16A52CF2B4EBF2A000DD5B7 /* BrazeReactDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrazeReactDelegate.m; sourceTree = "<group>"; };
		E16A52D12B4EBFCE000DD5B7 /* BrazeReactDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrazeReactDelegate.h; sourceTree = "<group>"; };
		E1A0AD702C61BF8D005B1F21 /* changi-rewardExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "changi-rewardExtension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		E1A0AD712C61BF8D005B1F21 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		E1A0AD732C61BF8D005B1F21 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		E1A0AD762C61BF8D005B1F21 /* changi_rewardBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = changi_rewardBundle.swift; sourceTree = "<group>"; };
		E1A0AD7A2C61BF8D005B1F21 /* changi_reward.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = changi_reward.swift; sourceTree = "<group>"; };
		E1A0AD7E2C61BF8F005B1F21 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		E1A0AD802C61BF8F005B1F21 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E1AB31972BB590DF00E25234 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = ichangiFe/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		E1B1A61028BBF4F00056AEDE /* ChangiPayModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangiPayModule.swift; sourceTree = "<group>"; };
		E1B1A61428BBF55A0056AEDE /* RCTBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTBridge.m; sourceTree = "<group>"; };
		E1B1A61828BBF5C70056AEDE /* ichangiFe-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ichangiFe-Bridging-Header.h"; sourceTree = "<group>"; };
		E1E02A5A2B61173600BFC352 /* BrazeEventEmitter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrazeEventEmitter.h; sourceTree = "<group>"; };
		E1E02A5B2B61175300BFC352 /* BrazeEventEmitter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrazeEventEmitter.m; sourceTree = "<group>"; };
		E1E02A612B62678A00BFC352 /* InAppBrowserManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InAppBrowserManager.h; sourceTree = "<group>"; };
		E1E02A622B6267AF00BFC352 /* InAppBrowserManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InAppBrowserManager.m; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		F61C6E292E8B27B35B921084 /* Pods-ichangiFe-ichangiFeTests.production.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ichangiFe-ichangiFeTests.production.xcconfig"; path = "Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests.production.xcconfig"; sourceTree = "<group>"; };
		F7F64B231FCE4DB99352E041 /* Lato-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Bold.ttf"; path = "../assets/fonts/Lato-Bold.ttf"; sourceTree = "<group>"; };
		FF55EC73BC9E47DBB19B90E3 /* Lato-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Lato-Italic.ttf"; path = "../assets/fonts/Lato-Italic.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E14E4A672AA9B8B000ADCCF1 /* Mapbox in Frameworks */,
				17B7C15929ADB0460071CCCE /* LiquidPayWidget.xcframework in Frameworks */,
				12DACBA5E4A6DCA908BADB98 /* libPods-ichangiFe-ichangiFeTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E14E4A662AA9B8B000ADCCF1 /* Mapbox in Frameworks */,
				17B7C15A29ADB0610071CCCE /* LiquidPayWidget.xcframework in Frameworks */,
				342D249ED0038397424EB565 /* libPods-ichangiFe.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E1A0AD6D2C61BF8D005B1F21 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E1A0AD742C61BF8D005B1F21 /* SwiftUI.framework in Frameworks */,
				E1A0AD722C61BF8D005B1F21 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* ichangiFeTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* ichangiFeTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = ichangiFeTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* ichangiFe */ = {
			isa = PBXGroup;
			children = (
				17840EAE28D8728A00DC0757 /* ichangiFe.entitlements */,
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				17840EB728DF1FFC00DC0757 /* GoogleService-Info.plist */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				E143636528CAA0FE00E4F6C8 /* main.storyboard */,
				E143636828CAA82700E4F6C8 /* MyViewController.swift */,
				E1286FDD292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard */,
				E1286FE3292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift */,
				E1AB31972BB590DF00E25234 /* PrivacyInfo.xcprivacy */,
			);
			name = ichangiFe;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				17B7C15729ADB0460071CCCE /* LiquidPayWidget.xcframework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
				A3751D3A3AB4BEF5D45827CE /* libPods-ichangiFe.a */,
				B05C7E5ADE61A81AAA3EC899 /* libPods-ichangiFe-ichangiFeTests.a */,
				E1A0AD712C61BF8D005B1F21 /* WidgetKit.framework */,
				E1A0AD732C61BF8D005B1F21 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				0C00EDCB2C75E06E004F8F00 /* changi-rewardExtension.entitlements */,
				0C00EDC72C748A73004F8F00 /* WidgetKitHelper.swift */,
				E12F746E2B732E21005885DC /* groups */,
				E1E02A622B6267AF00BFC352 /* InAppBrowserManager.m */,
				E1E02A612B62678A00BFC352 /* InAppBrowserManager.h */,
				E1E02A5B2B61175300BFC352 /* BrazeEventEmitter.m */,
				E1E02A5A2B61173600BFC352 /* BrazeEventEmitter.h */,
				E16A52D12B4EBFCE000DD5B7 /* BrazeReactDelegate.h */,
				E16A52CF2B4EBF2A000DD5B7 /* BrazeReactDelegate.m */,
				E1B1A61828BBF5C70056AEDE /* ichangiFe-Bridging-Header.h */,
				E1B1A61428BBF55A0056AEDE /* RCTBridge.m */,
				2C048B0B2965548F000E783E /* RNEventEmitter.m */,
				2C048B0E296554B2000E783E /* RNEventEmitter.swift */,
				0CE3FC102C72FE59009793C4 /* UpdateWidgetModule.h */,
				0CE3FC0D2C72FE24009793C4 /* UpdateWidgetModule.m */,
				E1B1A61028BBF4F00056AEDE /* ChangiPayModule.swift */,
				13B07FAE1A68108700A75B9A /* ichangiFe */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* ichangiFeTests */,
				E1A0AD752C61BF8D005B1F21 /* changi-reward */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				A90D23087009C6CD05A00CA1 /* Pods */,
				DBD48DABC9194B7FB23CE728 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* ichangiFe.app */,
				00E356EE1AD99517003FC87E /* ichangiFeTests.xctest */,
				E1A0AD702C61BF8D005B1F21 /* changi-rewardExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A90D23087009C6CD05A00CA1 /* Pods */ = {
			isa = PBXGroup;
			children = (
				ADB10BBEF2881D9DC65AB5BD /* Pods-ichangiFe.debug.xcconfig */,
				A3A82A91C7707C6088C606F3 /* Pods-ichangiFe.release.xcconfig */,
				487FAE155F0A96FBA3D0EDE6 /* Pods-ichangiFe.preprod.xcconfig */,
				DC199562DD1A21E1A2A502B9 /* Pods-ichangiFe.production.xcconfig */,
				B056E6608A6BEF24AD9EE90C /* Pods-ichangiFe.uat.xcconfig */,
				2853C5EA8F9CD31D567557AE /* Pods-ichangiFe.sit.xcconfig */,
				77AD3F7EC0E712F82DB19ABF /* Pods-ichangiFe-ichangiFeTests.debug.xcconfig */,
				6700E751E4DDF7EC5468D0BE /* Pods-ichangiFe-ichangiFeTests.release.xcconfig */,
				2E541935CE549B82463E1222 /* Pods-ichangiFe-ichangiFeTests.preprod.xcconfig */,
				F61C6E292E8B27B35B921084 /* Pods-ichangiFe-ichangiFeTests.production.xcconfig */,
				CA576A738194737890256CB8 /* Pods-ichangiFe-ichangiFeTests.uat.xcconfig */,
				4B5E2EFA5EE4F4305A7327FD /* Pods-ichangiFe-ichangiFeTests.sit.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		DBD48DABC9194B7FB23CE728 /* Resources */ = {
			isa = PBXGroup;
			children = (
				0884F3492CA2C4000067B86E /* Teko-Bold.ttf */,
				0884F34A2CA2C4000067B86E /* Teko-Light.ttf */,
				0884F3482CA2C4000067B86E /* Teko-Medium.ttf */,
				0884F3472CA2C3FF0067B86E /* Teko-Regular.ttf */,
				0884F3462CA2C3FF0067B86E /* Teko-SemiBold.ttf */,
				AB9809B31C0645E5A06C5478 /* Lato-Black.ttf */,
				9F400D6C93DD45B280AD5D8F /* Lato-BlackItalic.ttf */,
				F7F64B231FCE4DB99352E041 /* Lato-Bold.ttf */,
				44941A6063E94C1FB8E0A163 /* Lato-BoldItalic.ttf */,
				C74D97872B9F49EA84DB95B9 /* Lato-Hairline.ttf */,
				BEADFC39CB6245E9BADAE10A /* Lato-HairlineItalic.ttf */,
				966464DD054749319AE7B35E /* Lato-Heavy.ttf */,
				A70F36C2485F43909FFF875A /* Lato-HeavyItalic.ttf */,
				FF55EC73BC9E47DBB19B90E3 /* Lato-Italic.ttf */,
				D6F52D3521B2462AB313A4D2 /* Lato-Light.ttf */,
				8451590EE3C1408D8FDC0639 /* Lato-LightItalic.ttf */,
				582DFB731A554358B010E446 /* Lato-Medium.ttf */,
				0DFCE198DAC74D8CB84C75F4 /* Lato-MediumItalic.ttf */,
				BE8ADC4817154770AD7ADD86 /* Lato-Regular.ttf */,
				834ECC9EB65C4A26809F5BFE /* Lato-Semibold.ttf */,
				9DDBBE66A883425D862577F0 /* Lato-SemiboldItalic.ttf */,
				267310D9936743DFB1B9FFB2 /* Lato-Thin.ttf */,
				5DD225BF772642A2A3786A28 /* Lato-ThinItalic.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		E12F746E2B732E21005885DC /* groups */ = {
			isa = PBXGroup;
			children = (
				E12F746F2B732E5B005885DC /* DataModule.m */,
				E12F74712B732E8C005885DC /* DataModule.h */,
			);
			path = groups;
			sourceTree = "<group>";
		};
		E1A0AD752C61BF8D005B1F21 /* changi-reward */ = {
			isa = PBXGroup;
			children = (
				E1A0AD762C61BF8D005B1F21 /* changi_rewardBundle.swift */,
				E1A0AD7A2C61BF8D005B1F21 /* changi_reward.swift */,
				E1A0AD7E2C61BF8F005B1F21 /* Assets.xcassets */,
				E1A0AD802C61BF8F005B1F21 /* Info.plist */,
			);
			path = "changi-reward";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* ichangiFeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "ichangiFeTests" */;
			buildPhases = (
				FD303031DA1672BEEE9261E7 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				309D25149B134C46E34E82EF /* [CP] Embed Pods Frameworks */,
				05CDF15FBDAD775DC36566AE /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = ichangiFeTests;
			packageProductDependencies = (
				8B97F171CB9C24406F5382CC /* Mapbox */,
			);
			productName = ichangiFeTests;
			productReference = 00E356EE1AD99517003FC87E /* ichangiFeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* ichangiFe */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ichangiFe" */;
			buildPhases = (
				D295D19AFB466741A9D68897 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				179C695F298CAC1A005E30C2 /* Embed Frameworks */,
				E1A0AD842C61BF90005B1F21 /* Embed Foundation Extensions */,
				4B0486F82B67F05F004C2689 /* Run Script */,
				89833D578C6B80B702797568 /* [CP] Embed Pods Frameworks */,
				5A4278F677A72CEABE7C4AB2 /* [CP] Copy Pods Resources */,
				64320F369E264F0103DC44FC /* [CP-User] [RNFB] Core Configuration */,
				FA7C6466CB95C5E628149FE2 /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				E1A0AD822C61BF8F005B1F21 /* PBXTargetDependency */,
			);
			name = ichangiFe;
			packageProductDependencies = (
				EF486179A1309C75691A46D3 /* Mapbox */,
			);
			productName = ichangiFe;
			productReference = 13B07F961A680F5B00A75B9A /* ichangiFe.app */;
			productType = "com.apple.product-type.application";
		};
		E1A0AD6F2C61BF8D005B1F21 /* changi-rewardExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E1A0AD8B2C61BF90005B1F21 /* Build configuration list for PBXNativeTarget "changi-rewardExtension" */;
			buildPhases = (
				E1A0AD6C2C61BF8D005B1F21 /* Sources */,
				E1A0AD6D2C61BF8D005B1F21 /* Frameworks */,
				E1A0AD6E2C61BF8D005B1F21 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "changi-rewardExtension";
			productName = "changi-rewardExtension";
			productReference = E1A0AD702C61BF8D005B1F21 /* changi-rewardExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						LastSwiftMigration = 1340;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = Q73U6GYWJC;
						LastSwiftMigration = 1340;
					};
					E1A0AD6F2C61BF8D005B1F21 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ichangiFe" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			packageReferences = (
				2D481A3B270C97E7C6945CA2 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */,
			);
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* ichangiFe */,
				00E356ED1AD99517003FC87E /* ichangiFeTests */,
				E1A0AD6F2C61BF8D005B1F21 /* changi-rewardExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E143636728CAA0FE00E4F6C8 /* main.storyboard in Resources */,
				17840EB928DF1FFC00DC0757 /* GoogleService-Info.plist in Resources */,
				E1286FDF292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0884F34B2CA2C4000067B86E /* Teko-SemiBold.ttf in Resources */,
				0884F34C2CA2C4000067B86E /* Teko-Regular.ttf in Resources */,
				0884F34D2CA2C4000067B86E /* Teko-Medium.ttf in Resources */,
				0884F34E2CA2C4000067B86E /* Teko-Bold.ttf in Resources */,
				0884F34F2CA2C4000067B86E /* Teko-Light.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				E143636628CAA0FE00E4F6C8 /* main.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				F2864D6924F44A5DA5D1BF5D /* Lato-Black.ttf in Resources */,
				082CAB609FE9431F9A6B82F2 /* Lato-BlackItalic.ttf in Resources */,
				CB94FC00D06A495486F819FA /* Lato-Bold.ttf in Resources */,
				E1AB31982BB590DF00E25234 /* PrivacyInfo.xcprivacy in Resources */,
				52666EB88372473A9AF52CD5 /* Lato-BoldItalic.ttf in Resources */,
				43228EFB4F2442BB8F7608CB /* Lato-Hairline.ttf in Resources */,
				25A4F24F23D14F138A4835C7 /* Lato-HairlineItalic.ttf in Resources */,
				67DCC39368B1423CB4AA48DD /* Lato-Heavy.ttf in Resources */,
				E1286FDE292BE6BF00B3C23B /* ChangiLaunchScreen.storyboard in Resources */,
				DAB423EFE45E45CD89D0BD19 /* Lato-HeavyItalic.ttf in Resources */,
				079159BC5CCA4880A7DC6351 /* Lato-Italic.ttf in Resources */,
				A18D30CF81A94E39A88A5154 /* Lato-Light.ttf in Resources */,
				E20512B8912C459D8A09EB78 /* Lato-LightItalic.ttf in Resources */,
				BB9B746AE4F1428399E4BE98 /* Lato-Medium.ttf in Resources */,
				AAACCF9CB08F4E1A96824ED5 /* Lato-MediumItalic.ttf in Resources */,
				2B39A55A5E50443E81C88C44 /* Lato-Regular.ttf in Resources */,
				61F66FFDE1034041BBC50A33 /* Lato-Semibold.ttf in Resources */,
				E6B480C41F824457AC81884D /* Lato-SemiboldItalic.ttf in Resources */,
				17840EB828DF1FFC00DC0757 /* GoogleService-Info.plist in Resources */,
				90F5E9D651B14973A42C6464 /* Lato-Thin.ttf in Resources */,
				9DCCC320B33F4E5A82C96ADE /* Lato-ThinItalic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E1A0AD6E2C61BF8D005B1F21 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E1A0AD7F2C61BF8F005B1F21 /* Assets.xcassets in Resources */,
				E11878912C6370C300EF469F /* Lato-Regular.ttf in Resources */,
				E11878952C6370C300EF469F /* Lato-Italic.ttf in Resources */,
				E118789B2C6370C300EF469F /* Lato-BoldItalic.ttf in Resources */,
				E118789F2C6370C300EF469F /* Lato-Bold.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\nBUNDLE_REACT_NATIVE=\"/bin/sh $REACT_NATIVE_XCODE\"\n\n# RN 0.69+\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"$BUNDLE_REACT_NATIVE\\\"\"\n";
		};
		05CDF15FBDAD775DC36566AE /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/AppsFlyerFramework/AppsFlyerLib_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeKit/BrazeKit.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeLocation/BrazeLocation.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeUI/BrazeUI.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting/FirebaseABTesting_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDynamicLinks/FirebaseDynamicLinks_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps/GoogleMapsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/RCT-Folly_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo/RNDeviceInfoPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVGFilters.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React-Core_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/React-cxxreact_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/boost/boost_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/LottiePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/Lottie_React_Native_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-maps/GoogleMapsPrivacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/RNImagePickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-maps/ReactNativeMapsPrivacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AppsFlyerLib_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeKit.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeLocation.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeUI.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseABTesting_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCore_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreExtension_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCrashlytics_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseDynamicLinks_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseMessaging_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseRemoteConfig_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMapsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Promises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCT-Folly_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNDeviceInfoPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImageCropPickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNSVGFilters.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-Core_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-cxxreact_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SDWebImage.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/boost_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/glog_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LottiePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Lottie_React_Native_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/nanopb_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMapsPrivacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImagePickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReactNativeMapsPrivacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		309D25149B134C46E34E82EF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Dynatrace/xcframework/Dynatrace.framework/Dynatrace",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKGamingServicesKit/FBSDKGamingServicesKit.framework/FBSDKGamingServicesKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Qualtrics/Qualtrics.framework/Qualtrics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Dynatrace.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKGamingServicesKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKShareKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Qualtrics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ichangiFe-ichangiFeTests/Pods-ichangiFe-ichangiFeTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4B0486F82B67F05F004C2689 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"$XCODE_VERSION_MAJOR\" = \"1600\" ]; then\n  echo \"Remove signature files (Xcode 16 workaround)\"\n  if [ \"${CONFIGURATION}\" = \"Sit\" ]; then\n    rm -rf \"$BUILD_DIR/Sit-iphoneos/Mapbox.xcframework-ios.signature\"\n  fi\n  if [ \"${CONFIGURATION}\" = \"Uat\" ]; then\n    rm -rf \"$BUILD_DIR/Uat-iphoneos/Mapbox.xcframework-ios.signature\"\n  fi\n  if [ \"${CONFIGURATION}\" = \"PreProd\" ]; then\n    rm -rf \"$BUILD_DIR/PreProd-iphoneos/Mapbox.xcframework-ios.signature\"\n  fi\n  if [ \"${CONFIGURATION}\" = \"Release\" ]; then\n    rm -rf \"$BUILD_DIR/Release-iphoneos/Mapbox.xcframework-ios.signature\"\n  fi\nfi\n";
		};
		5A4278F677A72CEABE7C4AB2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ichangiFe/Pods-ichangiFe-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/AppsFlyerFramework/AppsFlyerLib_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeKit/BrazeKit.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeLocation/BrazeLocation.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/BrazeUI/BrazeUI.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting/FirebaseABTesting_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDynamicLinks/FirebaseDynamicLinks_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps/GoogleMapsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/RCT-Folly_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo/RNDeviceInfoPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVGFilters.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React-Core_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/React-cxxreact_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/boost/boost_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/LottiePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/Lottie_React_Native_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-maps/GoogleMapsPrivacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/RNImagePickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/react-native-maps/ReactNativeMapsPrivacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AppsFlyerLib_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeKit.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeLocation.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/BrazeUI.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseABTesting_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCore_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreExtension_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCrashlytics_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseDynamicLinks_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseMessaging_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseRemoteConfig_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMapsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Promises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCT-Folly_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNDeviceInfoPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImageCropPickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNSVGFilters.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-Core_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-cxxreact_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SDWebImage.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/boost_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/glog_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LottiePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Lottie_React_Native_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/nanopb_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMapsPrivacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImagePickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReactNativeMapsPrivacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ichangiFe/Pods-ichangiFe-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		64320F369E264F0103DC44FC /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		89833D578C6B80B702797568 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ichangiFe/Pods-ichangiFe-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Dynatrace/xcframework/Dynatrace.framework/Dynatrace",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKGamingServicesKit/FBSDKGamingServicesKit.framework/FBSDKGamingServicesKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Qualtrics/Qualtrics.framework/Qualtrics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Dynatrace.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKGamingServicesKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKShareKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Qualtrics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ichangiFe/Pods-ichangiFe-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D295D19AFB466741A9D68897 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ichangiFe-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FA7C6466CB95C5E628149FE2 /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FD303031DA1672BEEE9261E7 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ichangiFe-ichangiFeTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E1B1A61628BBF55A0056AEDE /* RCTBridge.m in Sources */,
				2C048B10296554B2000E783E /* RNEventEmitter.swift in Sources */,
				2C048B0D2965548F000E783E /* RNEventEmitter.m in Sources */,
				0C00EDC92C748A73004F8F00 /* WidgetKitHelper.swift in Sources */,
				E1286FE5292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift in Sources */,
				E1B1A61228BBF4F00056AEDE /* ChangiPayModule.swift in Sources */,
				E1E02A642B6267AF00BFC352 /* InAppBrowserManager.m in Sources */,
				0CE3FC0F2C72FE24009793C4 /* UpdateWidgetModule.m in Sources */,
				E143636A28CAA82700E4F6C8 /* MyViewController.swift in Sources */,
				E1E02A5D2B61175300BFC352 /* BrazeEventEmitter.m in Sources */,
				00E356F31AD99517003FC87E /* ichangiFeTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E1B1A61528BBF55A0056AEDE /* RCTBridge.m in Sources */,
				E1E02A632B6267AF00BFC352 /* InAppBrowserManager.m in Sources */,
				0C00EDC82C748A73004F8F00 /* WidgetKitHelper.swift in Sources */,
				0CE3FC0E2C72FE24009793C4 /* UpdateWidgetModule.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				2C048B0C2965548F000E783E /* RNEventEmitter.m in Sources */,
				2C048B0F296554B2000E783E /* RNEventEmitter.swift in Sources */,
				E1E02A5C2B61175300BFC352 /* BrazeEventEmitter.m in Sources */,
				E16A52D02B4EBF2A000DD5B7 /* BrazeReactDelegate.m in Sources */,
				E12F74702B732E5B005885DC /* DataModule.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				E143636928CAA82700E4F6C8 /* MyViewController.swift in Sources */,
				E1B1A61128BBF4F00056AEDE /* ChangiPayModule.swift in Sources */,
				E1286FE4292BEF8600B3C23B /* ChangiLaunchScreenUIView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E1A0AD6C2C61BF8D005B1F21 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E1A0AD772C61BF8D005B1F21 /* changi_rewardBundle.swift in Sources */,
				E1A0AD7B2C61BF8D005B1F21 /* changi_reward.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* ichangiFe */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
		E1A0AD822C61BF8F005B1F21 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E1A0AD6F2C61BF8D005B1F21 /* changi-rewardExtension */;
			targetProxy = E1A0AD812C61BF8F005B1F21 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 77AD3F7EC0E712F82DB19ABF /* Pods-ichangiFe-ichangiFeTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6700E751E4DDF7EC5468D0BE /* Pods-ichangiFe-ichangiFeTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ADB10BBEF2881D9DC65AB5BD /* Pods-ichangiFe.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3.sit;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "Fsoft Provisioning SIT";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A3A82A91C7707C6088C606F3 /* Pods-ichangiFe.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3.sit;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "Fsoft Provisioning SIT";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		1758D98229EFDD7D00CB1AA9 /* PreProd */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = PreProd;
		};
		1758D98329EFDD7D00CB1AA9 /* PreProd */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 487FAE155F0A96FBA3D0EDE6 /* Pods-ichangiFe.preprod.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3.preprod;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "Fsoft Provisioning SIT";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning PreProd";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = PreProd;
		};
		1758D98429EFDD7D00CB1AA9 /* PreProd */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2E541935CE549B82463E1222 /* Pods-ichangiFe-ichangiFeTests.preprod.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = PreProd;
		};
		4C7099FF261CD11100879F8E /* Sit */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Sit;
		};
		4C709A00261CD11100879F8E /* Sit */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2853C5EA8F9CD31D567557AE /* Pods-ichangiFe.sit.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3.sit;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "Fsoft Provisioning SIT";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Sit;
		};
		4C709A01261CD11100879F8E /* Sit */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B5E2EFA5EE4F4305A7327FD /* Pods-ichangiFe-ichangiFeTests.sit.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = Sit;
		};
		4C709A04261CD11900879F8E /* Uat */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Uat;
		};
		4C709A05261CD11900879F8E /* Uat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B056E6608A6BEF24AD9EE90C /* Pods-ichangiFe.uat.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				ENABLE_BITCODE = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3.uat;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "CNX Tigerspike UAT";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Uat;
		};
		4C709A06261CD11900879F8E /* Uat */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CA576A738194737890256CB8 /* Pods-ichangiFe-ichangiFeTests.uat.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = Uat;
		};
		4C709A09261CD11F00879F8E /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Production;
		};
		4C709A0A261CD11F00879F8E /* Production */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DC199562DD1A21E1A2A502B9 /* Pods-ichangiFe.production.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				BUILD_LIBRARY_FOR_DISTRIBUTION = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ichangiFe/ichangiFe.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFe/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocalization\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNKeychain\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMAppLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMPermissionsInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/UMReactNativeAdapter\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cookies\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"$(SDKROOT)/usr/lib/swift\"",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.changiairport.cagapp3;
				PRODUCT_NAME = ichangiFe;
				PROVISIONING_PROFILE_SPECIFIER = "Fsoft Provisioning SIT";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFe-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Production;
		};
		4C709A0B261CD11F00879F8E /* Production */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F61C6E292E8B27B35B921084 /* Pods-ichangiFe-ichangiFeTests.production.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ENABLE_MODULES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = ichangiFeTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "ichangiFeTests-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ichangiFe.app/ichangiFe";
			};
			name = Production;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E1A0AD852C61BF90005B1F21 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning SIT - Changi Reward Widget";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E1A0AD862C61BF90005B1F21 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning SIT - Changi Reward Widget";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E1A0AD872C61BF90005B1F21 /* PreProd */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = PreProd;
		};
		E1A0AD882C61BF90005B1F21 /* Production */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning SIT - Changi Reward Widget";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Production;
		};
		E1A0AD892C61BF90005B1F21 /* Uat */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning SIT - Changi Reward Widget";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Uat;
		};
		E1A0AD8A2C61BF90005B1F21 /* Sit */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "changi-rewardExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = Q73U6GYWJC;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "changi-reward/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "changi-reward";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.changiairport.cagapp3.sit.changi-reward";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Fsoft Provisioning SIT - Changi Reward Widget";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Sit;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "ichangiFeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
				1758D98429EFDD7D00CB1AA9 /* PreProd */,
				4C709A0B261CD11F00879F8E /* Production */,
				4C709A06261CD11900879F8E /* Uat */,
				4C709A01261CD11100879F8E /* Sit */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Sit;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ichangiFe" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
				1758D98329EFDD7D00CB1AA9 /* PreProd */,
				4C709A0A261CD11F00879F8E /* Production */,
				4C709A05261CD11900879F8E /* Uat */,
				4C709A00261CD11100879F8E /* Sit */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Sit;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ichangiFe" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
				1758D98229EFDD7D00CB1AA9 /* PreProd */,
				4C709A09261CD11F00879F8E /* Production */,
				4C709A04261CD11900879F8E /* Uat */,
				4C7099FF261CD11100879F8E /* Sit */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Sit;
		};
		E1A0AD8B2C61BF90005B1F21 /* Build configuration list for PBXNativeTarget "changi-rewardExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E1A0AD852C61BF90005B1F21 /* Debug */,
				E1A0AD862C61BF90005B1F21 /* Release */,
				E1A0AD872C61BF90005B1F21 /* PreProd */,
				E1A0AD882C61BF90005B1F21 /* Production */,
				E1A0AD892C61BF90005B1F21 /* Uat */,
				E1A0AD8A2C61BF90005B1F21 /* Sit */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Sit;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2D481A3B270C97E7C6945CA2 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/maplibre/maplibre-gl-native-distribution";
			requirement = {
				kind = exactVersion;
				version = 5.13.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		8B97F171CB9C24406F5382CC /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2D481A3B270C97E7C6945CA2 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
		EF486179A1309C75691A46D3 /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2D481A3B270C97E7C6945CA2 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
