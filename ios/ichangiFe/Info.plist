<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Changi</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.changiairport.cagapp3.sit</string>
				<string>com.changiairport.cagapp3.uat</string>
				<string>com.changiairport.cagapp3.preprod</string>
				<string>com.changiairport.cagapp3</string>
				<string>com.changiairport.cagapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>cagichangi</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>cagichangi</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb570050831983217</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.changiairport.cagapp.liquidwidget</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>DTXApplicationID</key>
	<string>53fd0ed1-dd88-4900-9234-353e03eff99e</string>
	<key>DTXBeaconURL</key>
	<string>https://bf79709fuy.bf.dynatrace.com/mbeacon</string>
	<key>DTXDisableWebRequestsInstrumentationV2</key>
	<true/>
	<key>DTXExcludedControls</key>
	<array>
		<string>PickerView</string>
		<string>Switch</string>
	</array>
	<key>DTXFlavor</key>
	<string>react_native</string>
	<key>DTXLogLevel</key>
	<string>ALL</string>
	<key>DTXStartupLoadBalancing</key>
	<true/>
	<key>DTXUserOptIn</key>
	<false/>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<true/>
	<key>FacebookAppID</key>
	<string>570050831983217</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<true/>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Changi</string>
	<key>GoogleMapsAPIKey</key>
	<string>AIzaSyAefBE4TZIvWgr_0dHNbKnoWVBGZ8lsJsU</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>cagichangi</string>
		<string>itms-apps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<false/>
		<key>NSPinnedDomains</key>
		<dict>
			<key>lz.changiairport.com</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSPinnedCAIdentities</key>
				<array>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>++MBgDH5WGvL9Bcn5Be30cRcL0f5O+NyoXuWtQdX1aI=</string>
					</dict>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>f0KW/FtqTjs108NpYj42SrGvOB2PpxIVM8nWxjPqJGE=</string>
					</dict>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>NqvDJlas/GRcYbcWE8S/IceH9cq77kg0jVhZeAPXq8k=</string>
					</dict>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>9+ze1cZgR9KO1kZrVDxA4HQ6voHRCSVNz4RdTCx4U8U=</string>
					</dict>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>KwccWaCgrnaw6tsrrSO61FgLacNgG2MMLq8GE6+oP5I=</string>
					</dict>
					<dict>
						<key>SPKI-SHA256-BASE64</key>
						<string>4a6cPehI7OG6cuDZka5NDZ7FR8a60d3auda+sKfg4Ng=</string>
					</dict>
				</array>
			</dict>
		</dict>
	</dict>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>This lets you save events to your Calendar.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This lets you save events to your Calendar.</string>
	<key>NSCameraUsageDescription</key>
	<string>This lets you scan QR codes and boarding passes, take photos for feedback, and enable video-calling function to connect with our Virtual Changi Experience Ambassadors.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>You will be able to log in to your account more quickly and securely.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>LocationAllway</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Location access for searching the publication</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Location access for searching the publication</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This enables the video-calling function for you to connect with our Virtual Changi Experience Ambassadors.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Your message to user when the photo library is accessed for the first time</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This lets you share photos from your library, or save photos to your Camera Roll.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Lato-Black.ttf</string>
		<string>Lato-BlackItalic.ttf</string>
		<string>Lato-Bold.ttf</string>
		<string>Lato-BoldItalic.ttf</string>
		<string>Lato-Hairline.ttf</string>
		<string>Lato-HairlineItalic.ttf</string>
		<string>Lato-Heavy.ttf</string>
		<string>Lato-HeavyItalic.ttf</string>
		<string>Lato-Italic.ttf</string>
		<string>Lato-Light.ttf</string>
		<string>Lato-LightItalic.ttf</string>
		<string>Lato-Medium.ttf</string>
		<string>Lato-MediumItalic.ttf</string>
		<string>Lato-Regular.ttf</string>
		<string>Lato-Semibold.ttf</string>
		<string>Lato-SemiboldItalic.ttf</string>
		<string>Lato-Thin.ttf</string>
		<string>Lato-ThinItalic.ttf</string>
		<string>Teko-SemiBold.ttf</string>
		<string>Teko-Regular.ttf</string>
		<string>Teko-Medium.ttf</string>
		<string>Teko-Bold.ttf</string>
		<string>Teko-Light.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
