# iChangi 3.0

## The official codebase for iChangi 3.0 development

This is the official repo for development of iChangi 3.0 frontend, that is based on [Infinite Red](https://infinite.red) boilerplate, which consists of bleeding-edge changes to our React Native stack.

**Currently includes:**

- React Native
- React Navigation
- Redux
- TypeScript
- And more!

## Quick Start

**Step 1**:- Clone the repo & install dependencies through setup script

```
git clone URL
```

```
$ ./bin/setup
```

> Special for Mac M1 machine
>
> ```
> $ ./bin/setup_m1
> ```

**Step 2**:- Once the installation is done, you can run the below commands

for iOS,

```
npx react-native run-ios
```

or

```
npm run ios
```

> Special for Mac M1 machine
>
> ```
> arch -x86_64 react-native run-ios
> ```

for Android,

```
npx react-native run-android --variant sitDebug --appIdSuffix=sit
```

or

```
npm run android
```

> Special for Mac M1 machine
>
> ```
> arch -x86_64 react-native run-android
> ```

The iChangi project's structure will look similar to the below:

```
iChangi-project
├── app
│   ├── components
│   ├── i18n
│   ├── utils
│   ├── models
│   ├── navigators
│   ├── screens
│   ├── services
│   ├── theme
│   ├── app.tsx
├── test
│   ├── __snapshots__
│   ├── mock-i18n.ts
│   ├── mock-reactotron.ts
│   ├── setup.ts
├── README.md
├── android
│   ├── app
│   ├── build.gradle
│   ├── gradle
│   ├── gradle.properties
│   ├── gradlew
│   ├── gradlew.bat
│   ├── keystores
│   └── settings.gradle
├── ignite
│   ├── ignite.json
│   └── plugins
├── index.js
├── ios
│   ├── iChangi
│   ├── iChangi-tvOS
│   ├── iChangi-tvOSTests
│   ├── iChangi.xcodeproj
│   └── iChangiTests
├── .env
└── package.json

```

## Build Variants

The different build variants available are `sit`, `uat`, & `prod` variants.

### `iOS`

```
npm run build:ios
```

### `Android`

The generated build variant will be available in `android/app/build/outputs/apk`
**Sit Variant**

```
npm run build:android-bundle-sit
npm run build:android-apk-sit
```

**Uat Variant**

```
npm run build:android-bundle-uat
npm run build:android-apk-uat
```

**Production Variant**

```
npm run build:android-bundle-prod
npm run build:android-apk-prod
```

### How to add & activate custom fonts?

Read [custom font setup](./assets/fonts/custom-fonts.md)

### ./app directory

Included in the codebase is the `app` directory. This is a directory you would normally have to create when using vanilla React Native.

The inside of the src directory looks similar to the following:

```
app
│── components
│── i18n
├── models
├── navigators
├── screens
├── services
├── theme
├── utils
└── app.tsx
```

**components**
This is where your React components will live. Each component will have a directory containing the `.tsx` file, along with a story file, and optionally `.presets`, and `.props` files for larger components. The app will come with some commonly used components like Button.

**i18n**
This is where your translations will live if you are using `react-native-i18n`.

**models**
This is where your app's models will live. Each model has a directory which will contain the `mobx-state-tree` model file, test file, and any other supporting files like actions, types, etc.

**navigators**
This is where your `react-navigation` navigators will live.

**screens**
This is where your screen components will live. A screen is a React component which will take up the entire screen and be part of the navigation hierarchy. Each screen will have a directory containing the `.tsx` file, along with any assets or other helper files.

**services**
Any services that interface with the outside world will live here (think REST APIs, Push Notifications, etc.).

**theme**
Here lives the theme for your application, including spacing, colors, and typography.

**utils**
This is a great place to put miscellaneous helpers and utilities. Things like date helpers, formatters, etc. are often found here. However, it should only be used for things that are truely shared across your application. If a helper or utility is only used by a specific component or model, consider co-locating your helper with that component or model.

**app.tsx** This is the entry point to your app. This is where you will find the main App component which renders the rest of the application.

### ./ignite directory

The `ignite` directory stores all things related to Ignite(boilerplate), including CLI and boilerplate items. Here you will find generators, plugins and examples to help you get started with React Native.

## Running e2e tests

Read [e2e setup instructions](./e2e/README.md).

## References

- [2018 aka Bowser](https://github.com/infinitered/ignite-bowser)
- [2017 aka Andross](https://github.com/infinitered/ignite-andross)
- [2016 aka Ignite 1.0](https://github.com/infinitered/ignite-ir-boilerplate-2016)

## Checkout branch strategy
- Feature branch is checkedout from latest develop: git checkout {branchType}/{developmentStory}
  where:  branchType: feature -> new feature
                      bugfix  -> fix jira bug or internal bug
          developmentStory: is development story on jira instead of Epic id, eg: CAPP-7500
  Example:  git checkout feature/CAPP-7500
            git checkout bugfix/CAPP-7500

## Commit convention
- development format: CAPP-{ticket-id} feat:|fix:|docs:|style:|refactor:|test:|chore: {description}
  eg: CAPP-6488 feat: feature flight details
- technical issue / REG issue format: CA30-269 feat:|fix:|docs:|style:|refactor:|test:|chore: {description}
  eg: CA30-269 fix: regression CAG2023-155
- Notes:
  feat: (new feature for the user, not a new feature for build script)
  fix: (bug fix for the user, not a fix to a build script)
  docs: (changes to the documentation)
  style: (formatting, missing semi colons, etc; no production code change)
  refactor: (refactoring production code, eg. renaming a variable)
  test: (adding missing tests, refactoring tests; no production code change)
  chore: (updating grunt tasks etc; no production code change)
- References: https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716

## Automation testing compatibility
- Add testID/accessibilityLabel to Components that have user interaction
- Components: Sorts of touchable, Pressable, Text, and maybe other components if automation team request
- Eg: `<Text testID={"welcome-text"} accessibilityLabel={"welcome-text"}>`
- testID/accessibilityLabel should be unique, especially in a List
- Notes: This rule will be enforced by sonar scan later, if this fails, PR will not be merged