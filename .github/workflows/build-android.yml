
name: Build Android APK

on:
  workflow_call:
    inputs:
      DEPLOY_ENV:
        required: false
        type: string
      PROJID:
        required: false
        type: string


jobs:
  build-android-apk:
    runs-on: [self-hosted,macos-runner]

    env:
      LANG: en_US.UTF-8
      ANDROID_SDK_ROOT: /Users/<USER>/Library/Android/sdk
      JAVA_HOME: /Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      # with:
      #   token: ${{ secrets.GH_PAT }}

    - name: Verify environment
      run: |
        export PATH=":/usr/local/bin:$PATH"
        export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
        export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"
        echo "JAVA_HOME: $JAVA_HOME"
        echo "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"
        echo "PATH: $PATH"
        java -version
        xcodebuild -version
        node -v
        npm -v
        watchman --version || echo "Watchman not installed"

    - name: Clean watchman and npm cache
      run: |
        export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
        export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"
        watchman watch-del-all || echo "Watchman clean skipped"
        sudo chown -R $(id -u):$(id -g) "$HOME/.npm"
        npm cache clean --force

    - name: Build Android Bundle for DEV environment
      if: ${{ github.ref_name == 'develop' && inputs.DEPLOY_ENV == 'dev' }}
      env:
        CI: false
      run: |
        export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
        export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"

        #rm -rf android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf ${GITHUB_WORKSPACE}/android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf node_modules ios/Pods ios/Podfile.lock

        # cp /Users/<USER>/jenkins-agent/workspace/DIVA/CHANGI-APP/package.json .

        npm install --legacy-peer-deps --verbose
        ###react-native-gesture-handler@2.21.1
        cd ios && pod install && pod update SDWebImageWebPCoder && cd ..
        npm run postinstall
        cd android
        ./gradlew clean
        cd ..
        watchman watch-del-all || echo "Watchman clean skipped"
        npm run build:android-bundle-${{ inputs.DEPLOY_ENV }} --verbose
        npm run build:android-apk-${{ inputs.DEPLOY_ENV }} --verbose
        echo "Build Android complete!"
        pwd
        ls -alh
        ls -alht android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release



    - name: Build Android Bundle for UAT environment
      if: ${{ github.ref_name == 'uat' && inputs.DEPLOY_ENV == 'uat' }}
      run: |
        export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
        export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"

        #rm -rf android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf ${GITHUB_WORKSPACE}/android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf node_modules ios/Pods ios/Podfile.lock

        # cp /Users/<USER>/jenkins-agent/workspace/DIVA/CHANGI-APP/package.json .

        npm install --legacy-peer-deps --verbose
        ###react-native-gesture-handler@2.21.1
        cd ios && pod install && pod update SDWebImageWebPCoder && cd ..
        npm run postinstall
        cd android
        ./gradlew clean
        cd ..
        watchman watch-del-all || echo "Watchman clean skipped"
        npm run build:android-bundle-${{ inputs.DEPLOY_ENV }} --verbose
        npm run build:android-apk-${{ inputs.DEPLOY_ENV }} --verbose
        echo "Build Android complete!"
        pwd
        ls -alh
        ls -alht android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release


    - name: Build Android Bundle for SIT environment
      if: ${{ github.ref_name == 'sit' && inputs.DEPLOY_ENV == 'sit' }}
      run: |
        export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
        export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"

        #rm -rf android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf ${GITHUB_WORKSPACE}/android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk
        rm -rf node_modules ios/Pods ios/Podfile.lock

        # cp /Users/<USER>/jenkins-agent/workspace/DIVA/CHANGI-APP/package.json .

        npm install --legacy-peer-deps --verbose
        ###react-native-gesture-handler@2.21.1
        rm -f ~/.cocoapods/repos/cocoapods/.git/index.lock || true
        cd ios && pod install && pod update SDWebImageWebPCoder
        cd ..
        pwd && ls -alh
        npm run postinstall
        cd android
        ./gradlew clean
        cd ..
        watchman watch-del-all || echo "Watchman clean skipped"
        npm run build:android-bundle-${{ inputs.DEPLOY_ENV }} --verbose
        npm run build:android-apk-${{ inputs.DEPLOY_ENV }} --verbose
        echo "Build Android complete!"
        pwd
        ls -alh
        ls -alht android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release


    - name: Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-apk-${{ inputs.DEPLOY_ENV }}
        path: android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk

    - name: Install Firebase CLI
      run: |
        firebase --version

    - name: Set up Firebase service account
      run: |
        echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}' > ${{ github.workspace }}/firebase.json
        ls -lah ${{ github.workspace }}
      env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json

    - name: Upload Android APK to Firebase
      if: ${{ github.ref_name == 'sit' || inputs.DEPLOY_ENV == 'sit' }}
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json
      run: |
        APK_PATH=$(ls android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk | head -n 1)
        if [ -z "$APK_PATH" ]; then
          echo "APK not found!"
          exit 1
        fi
        BUILD_NUMBER=${{ github.run_number }}
        firebase projects:list --token ${{ secrets.FIREBASE_TOKENS }}
        # firebase appdistribution:distribute "$APK_PATH" \
        #   --app "${{ secrets.FIREBASE_APP_ID_ANDROID_SIT }}" \
        #   --token "${{ secrets.FIREBASE_TOKENS }}" \
        #   --groups "jenkins_pipeline_builds,Fsoft" \
        #   --release-notes "Firebase App distribution - iChangi-3.0 Android-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --debug

        firebase appdistribution:distribute "$APK_PATH" \
          --app "${{ secrets.FIREBASE_APP_ID_ANDROID_SIT }}" \
          --groups "Fsoft" \
          --release-notes "Firebase App distribution - iChangi-3.0 Android-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --debug

    - name: Upload Android APK to Firebase Uat/Preprod/Prod
      if: ${{ github.ref_name != 'sit' || inputs.DEPLOY_ENV != 'sit' }}
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json
      run: |
        APK_PATH=$(ls android/app/build/outputs/apk/${{ inputs.DEPLOY_ENV }}/release/*.apk | head -n 1)
        if [ -z "$APK_PATH" ]; then
          echo "APK not found!"
          exit 1
        fi
        BUILD_NUMBER=${{ github.run_number }}
        firebase projects:list --token ${{ secrets.FIREBASE_TOKENS }}
        # firebase appdistribution:distribute "$APK_PATH" \
        #   --app "${{ secrets.FIREBASE_APP_ID_ANDROID_SIT }}" \
        #   --token "${{ secrets.FIREBASE_TOKENS }}" \
        #   --groups "jenkins_pipeline_builds,Fsoft" \
        #   --release-notes "Firebase App distribution - iChangi-3.0 Android-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --debug

        firebase appdistribution:distribute "$APK_PATH" \
          --app "${{ secrets.FIREBASE_APP_ID_ANDROID_SIT }}" \
          --groups "Fsoft" \
          --release-notes "Firebase App distribution - iChangi-3.0 Android-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --
            
  # Cleanup-Workspace:
  #   runs-on: [self-hosted, macos-runner]
  #   needs: build-android-apk
  #   if: success()
  #   steps:
  #     - name: Cleanup workspace folder
  #       run: |
  #         echo "Cleaning up workspace..."
  #         sudo rm -rf "${{ github.workspace }}"
  #         echo "Workspace cleaned."