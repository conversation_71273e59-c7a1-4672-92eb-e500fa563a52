name:  CHANGIAPP-FE Release iOS SIT

on:
  push:
    branches: [ "sit"]

run-name: CHANGIAPP-FE Release iOS SIT

jobs:
#   Read-Version:
#     runs-on: ubuntu-latest
#     outputs:
#       release_version: ${{ steps.set-version.outputs.version }}
#     steps:
#       - uses: actions/checkout@v4
#       - id: set-version
#         run: |
#           ENV=${{ inputs.DEPLOY_ENV || 'dev' }}
#           VERSION=$(grep "^${ENV}=" .github/workflows/build-config.env | cut -d'=' -f2)
#           echo "version=${VERSION}" >> $GITHUB_OUTPUT
#           echo "Retrieved version: ${VERSION} for environment: ${ENV}"

  Fortify_Stage:
    if: ${{ github.ref_name != 'sit' }}
    permissions:
      id-token: write
      contents: read
    uses: cag-dso/pipelines/.github/workflows/fortifyscan.yml@DSO-FORTIFY
    with:
      PROJECTID: "changiapp-fe"
    secrets: inherit

  SonarQube_Stage:
    if: ${{ github.ref_name != 'sit' }}
    permissions:
      id-token: write
      contents: read
    uses: cag-dso/pipelines/.github/workflows/new-sonarscan.yml@DSO-SONARSCAN
    with:
      PROJECTID: "changiapp-fe-SIT"
      cache-folder: "changiapp-fe"
      environment: "sit"
      Node_Version: "22"
    secrets: inherit

  Nexus_Stage:
    if: ${{ github.ref_name != 'sit' }}
    permissions:
      id-token: write
      contents: read
    uses: cag-dso/pipelines/.github/workflows/nexusiq-be.yml@DSO-NEXUSIQ
    with:
      NIQ_APP_ID: "changiapp-fe"
      scan-targets: "."
      environment: "sit"
    secrets: inherit

  Build-iOS-Stage:
    # needs: [Nexus_Stage]
    permissions:
      id-token: write
      contents: read
    uses: cag-diva/changiapp-fe/.github/workflows/build-ios.yml@develop
    with:
      DEPLOY_ENV: "sit"
      PROJID: "changiapp-fe"
      SCHEME: "Sit"
    secrets: inherit
