
name: Build iOS App

on:
  workflow_call:
    inputs:
      DEPLOY_ENV:
        required: false
        type: string
      PROJID:
        required: false
        type: string
      Node_Version:
        required: false
        type: string
      SCHEME:
        required: true
        type: string

jobs:
  build-ios:
    runs-on: [self-hosted,macos-runner]

    env:
      SCHEME: ${{ inputs.SCHEME || 'sit'}}
      CONFIGURATION: Release
      ARCHIVE_PATH: ${{ github.workspace }}/ios/build/Release.xcarchive
      EXPORT_PATH: ${{ github.workspace }}/ios/build/Products/IPA/
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Verify environment
        run: |
          export PATH=":/usr/local/bin:$PATH"
          export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
          export PATH="/usr/local/n/versions/node/22.17.0/bin:$PATH"
          export LANG=en_US.UTF-8
          echo "JAVA_HOME: $JAVA_HOME"
          echo "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"
          echo "PATH: $PATH"
          java -version
          xcodebuild -version
          node -v
          npm -v
          watchman --version || echo "Watchman not installed"

      - name: Import signing certificate
        run: |
          security list-keychains
          security unlock-keychain -p ${{ secrets.KEY_CHAIN_PASS }} /Users/<USER>/Library/Keychains/login.keychain-db
          security unlock-keychain -p ${{ secrets.KEY_CHAIN_PASS }} /Users/<USER>/Library/Keychains/ios-app.keychain-db
          security find-identity -p codesigning -v

      - name: Clean previous builds
        continue-on-error: ${{inputs.DEPLOY_ENV == 'sit' || inputs.SCHEME == 'Sit'}}
        env:
          CI: false
        run: |
          export LANG=en_US.UTF-8
          rm -rf ios/build/
          sudo rm -rf ~/Library/Developer/Xcode/DerivedData
          sudo rm -rf ~/Library/Developer/Xcode/DerivedData/*
          cd ios
          rm -rf Pods Podfile.lock
          pod cache clean --all
          pod deintegrate
          cd ..
          watchman watch-del-all || true
          npm cache clean --force
          ./bin/setup

      - name: Install CocoaPods
        run: |
          export LANG=en_US.UTF-8
          cd ios
          pod install

      # - name: Patch Xcode Run Script Phase
      #   continue-on-error: true
      #   run: |
      #       export LANG=en_US.UTF-8
      #       echo "Patching Xcode Run Script Phase"
      #       # sed -i '' 's/\[ "\$XCODE_VERSION_MAJOR" = "1600" \]/[ "$XCODE_VERSION_MAJOR" = "1500" ]/g' ios/ichangiFE.xcodeproj/project.pbxproj
      #       sed -i '' "s/\[ \"\\\$XCODE_VERSION_MAJOR\" = \"1600\" \]/[ \"\$XCODE_VERSION_MAJOR\" = \"1500\" ]/g" ios/ichangiFE.xcodeproj/project.pbxproj
      #       #cp /Users/<USER>/actions-runner-diva/_work/changiapp-fe/project.pbxproj ${{ github.workspace }}/ios/ichangiFE.xcodeproj/project.pbxproj

      - name: Patch Xcode Run Script Phase
        continue-on-error: true
        run: |
          export LANG=en_US.UTF-8
          export LC_ALL=en_US.UTF-8
          echo "Patching Xcode Run Script Phase"

          grep 'XCODE_VERSION_MAJOR' ios/ichangiFE.xcodeproj/project.pbxproj || echo "Nothing matched"
          sed -i '' 's/\\\"\$XCODE_VERSION_MAJOR\\\" = \\\"1600\\\"/\\\"$XCODE_VERSION_MAJOR\\\" = \\\"1500\\\"/g' ios/ichangiFE.xcodeproj/project.pbxproj
          echo "Done patching"

      - name: Run iOS Build (Archive)
        run: |
          export LANG=en_US.UTF-8
          cd ios
          xcodebuild -list -project ichangiFe.xcodeproj
          xcodebuild -resolvePackageDependencies -project ichangiFe.xcodeproj

          ##xcodebuild: error: “Mapbox.xcframework-ios.signature”
          find . -type d -name "Signatures" -exec rm -rf {} +

          xcodebuild clean archive \
            -workspace ichangiFe.xcworkspace \
            -scheme "$SCHEME" \
            -configuration "$CONFIGURATION" \
            -destination 'generic/platform=iOS' \
            -allowProvisioningUpdates \
            -archivePath "$ARCHIVE_PATH"

      - name: Release iOS IPA (SIT)
        if: inputs.DEPLOY_ENV == 'sit' || inputs.SCHEME == 'Sit'
        env:
          EXPORT_OPTIONS_PLIST_SIT: ${{ secrets.EXPORT_OPTIONS_PLIST_SIT }}
        run: |
          cd ios
          xcodebuild -exportArchive \
            -archivePath "$ARCHIVE_PATH" \
            -exportOptionsPlist "$EXPORT_OPTIONS_PLIST_SIT" \
            -exportPath "$EXPORT_PATH" \
            -allowProvisioningUpdates

      - name: Upload IPA artifact
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: iOS-IPA-${{ inputs.SCHEME }}
          path: ${{ github.workspace }}/ios/build/Products/IPA/*.ipa

      - name: Set up Firebase service account
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}' > ${{ github.workspace }}/firebase.json
          ls -alh ${{ github.workspace }}
        env:
            GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json

      - name: Upload iOS IPA to Firebase
        if: ${{ github.ref_name == 'sit' || inputs.DEPLOY_ENV == 'sit' }}
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
          GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json
        run: |
          IOS_PATH=$(ls $EXPORT_PATH/*.ipa| head -n 1)
          if [ -z "$IOS_PATH" ]; then
            echo "IPA not found!"
            exit 1
          fi
          BUILD_NUMBER=${{ github.run_number }}
          firebase projects:list --token ${{ secrets.FIREBASE_TOKENS }}
          # firebase appdistribution:distribute "$IOS_PATH" \
          #   --app "${{ secrets.FIREBASE_APP_ID_IOS_SIT }}" \
          #   --token "${{ secrets.FIREBASE_TOKENS }}" \
          #   --groups "jenkins_pipeline_builds,Fsoft" \

          firebase appdistribution:distribute "$IOS_PATH" \
            --app "${{ secrets.FIREBASE_APP_ID_IOS_SIT }}" \
            --groups "Fsoft" \
            --release-notes "Firebase App distribution - iChangi-3.0 iOS-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --debug

      - name: Upload iOS IPA to Firebase Uat/Preprod/Prod
        if: ${{ github.ref_name != 'sit' || inputs.DEPLOY_ENV != 'sit' }}
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
          GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/firebase.json
        run: |
          IOS_PATH=$(ls $EXPORT_PATH/*.ipa| head -n 1)
          if [ -z "$IOS_PATH" ]; then
            echo "IPA not found!"
            exit 1
          fi
          BUILD_NUMBER=${{ github.run_number }}
          firebase projects:list --token ${{ secrets.FIREBASE_TOKENS }}
          # firebase appdistribution:distribute "$IOS_PATH" \
          #   --app "${{ secrets.FIREBASE_APP_ID_IOS_SIT }}" \
          #   --token "${{ secrets.FIREBASE_TOKENS }}" \
          #   --groups "jenkins_pipeline_builds,Fsoft" \

          firebase appdistribution:distribute "$IOS_PATH" \
            --app "${{ secrets.FIREBASE_APP_ID_IOS_SIT }}" \
            --groups "Fsoft,changi-app-core-team" \
            --release-notes "Firebase App distribution - iChangi-3.0 iOS-${{ inputs.DEPLOY_ENV }}-Build $BUILD_NUMBER build by CAG Jenkins CI/CD\n\n--iChangi" --debug

  # Cleanup-Workspace:
  #   runs-on: [self-hosted, macos-runner]
  #   needs: build-ios
  #   if: success()
  #   steps:
  #     - name: Cleanup workspace folder
  #       run: |
  #         echo "Cleaning up workspace..."
  #         sudo rm -rf "${{ github.workspace }}"
  #         echo "Workspace cleaned."
