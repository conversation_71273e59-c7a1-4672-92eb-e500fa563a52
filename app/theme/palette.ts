export const palette = {
  // Purple (Primary colours)
  basePurple: "#6C217F",
  lightPurple: "#7A35B0",
  lightestPurple: "#ECE0F5",
  darkPurple: "#4C1759",
  lighterPurple: "#AB76D5",
  purple8F58BE: "#8F58BE",
  purpleD5BBEA: "#D5BBEA",
  purple671A9D: "#671A9D",
  // Blue
  baseBlue: "#0057B8",
  blueDarker: "#001D3D",
  blueDark: "#003F85",
  lightestBlue: "#CCEDFF",
  lightBlue: "#1F89FF",
  blueLightest: "#018FBB",
  // Iris Blue
  darkIrisBlue: "#006070",
  lightestIrisBlue: "#EBFBFE",
  // Grey (UI colours)
  almostBlackGrey: "#121212",
  darkestGrey: "#454545",
  darkGrey: "#787878",
  midGrey: "#ABABAB",
  lightGrey: "#D1D1D1",
  lighterGrey: "#E5E5E5",
  halfLighterGrey: "rgba(229, 229, 229, 0.5)",
  lightestGrey: "#F7F7F7",
  lightShadeOfGrey: "#eee",
  almostWhiteGrey: "#FCFCFC",
  almostWhiteGrey80: "#FCFCFCCC",
  whiteGrey: "#FFFFFF",
  greyf8f8f8: "#f8f8f8",
  greydedede: "#dedede",
  greyCCCCCC: "#cccccc",
  greyD9D9D9: "#D9D9D9",
  darkGrey999: "#999999",
  buntingGrey: "#2D3142",
  shadowColor: "rgba(18, 18, 18, 0.2)",
  lightBeige: "#FCE1B6",
  // Red
  red: "#EE3424",
  baseRed: "#CC1000",
  lightestRed: "#FFE3E0",
  lightRed: "#FF4333",
  // Pink
  pink700: "#D90C7B",
  // Green
  green: "#008272",
  basegreen: "#008545",
  darkGreen: "#005C30",
  lightestGreen: "#E2EFDA",
  // Gradient colours
  gradientColor1Start: "#7A35B0",
  gradientColor1End: "#8A2AA2",
  gradientColor2Start: "#231852",
  gradientColor2Mid: "#53277E",
  gradientColor2End: "#920DC0",
  gradientColor3Start: "#342A69",
  gradientColor3Mid: "#5C3985",
  gradientColor3End: "#920DC0",
  // Other
  silver: "#C4C4C4",
  carbon: "#333333",
  shadow: "#0D000000",
  black: "#000000",
  transparent: "transparent",
  overlayColor: "#121212CC",
  lineColorCommonPopup: "rgba(60, 60, 67, 0.36)",
  backgroundCommonPopup: "rgba(242, 242, 242, 0.9)",
  backgroundFlightTimeline: "rgba(18, 18, 18, 0.6)",
  backgroundFlightCheckin: "rgba(69, 69, 69, 0.8)",
  backgroundCommonPopupNoOpacity: "rgba(242, 242, 242, 1)",
  backgroundStartFlightInfoV2: "rgba(153, 153, 153, 0.80)",
  backgroundEndFlightInfoV2: "rgba(69, 69, 69, 0.80)",
  whiteColorOpacity: "rgba(252, 252, 252, 0.8)",
  shortNameColor: "rgba(60, 60, 67, 0.3)",
  baseOrange: "#F5A01A",
  orangeDark: "#AB6C07",
  lightOrange: "#E06A27",
  lighterOrange: "#FEF5E7",
  orangeWarning300: "#F9C26C",
  orangeWarning700: "#E06A27",
  orangeWarning600: "#F2721B",
  sunsetOrange: "#FF5444",
  fuchsia7009: "#9C1D3C",
  // Yellow
  baseYellow: "#FFD200",
  yellowLight: "#C6D12A",
  yellowLightest: "#CDD52D",
  // Color of tier
  memberTier: "#7A35B0",
  memberTierBackground: "#ECE0F5",
  memberTierUnitLabelColor: "#7A35B0",
  memberTierCardGradient: ["#6950A1", "#B28DC1"],
  memberTierIcon: "#8F58BE",
  memberTierIconBackground: "#F9F5FC",
  memberTierRedeemBtnGradient: ["#8A2AA2", "#7A35B0"],
  memberTierDashedLine: "#ECE0F5",
  memberTierBenefitsButtonGradient: ["#842FB6", "#961FA8"],
  goldTier: "#BE975E",
  goldTierBackground: "#E5D5BF",
  goldTierUnitLabelColor: "#A28050",
  goldTierCardGradient: ["#7D5723", "#E2C17F"],
  goldTierIcon: "#BE975E",
  goldTierIconBackground: "#F2EADF",
  goldTierRedeemBtnGradient: ["#947C4E", "#B29E78"],
  goldTierDashedLine: "#F2EADF",
  goldTierBenefitsButtonGradient: ["#997B46", "#B69D73"],
  platinumTier: "#4A69A6",
  platinumTierBackground: "#DEE6F6",
  platinumTierUnitLabelColor: "#254687",
  platinumTierCardGradient: ["#29343C", "#456374"],
  platinumTierIcon: "#4A69A6",
  platinumTierIconBackground: "#F7F7F7",
  platinumTierRedeemBtnGradient: ["#29343C", "#456374"],
  platinumTierDashedLine: "#DEE6F6",
  platinumTierBenefitsButtonGradient: ["#26343D", "#3C6476"],
  monarchTier: "#454545",
  monarchTierBackground: "#cccccc",
  monarchTierUnitLabelColor: "#454545",
  monarchTierCardGradient: ["#121212", "#121212"],
  monarchTierIcon: "#454545",
  monarchTierIconBackground: "#F7F7F7",
  monarchTierRedeemBtnGradient: ["#121212", "#121212"],
  monarchTierDashedLine: "#E5E5E5",
  monarchTierBenefitsButtonGradient: ["#121212", "#121212"],
  iShopChangiGradient: ["#B83351", "#6C2963"],
  bookViaApp: ["#7E1BCC", "#BA32AA"],
  // Order status colors v2
  os2Danger: "#F5831E",
  os2Success: "#121212",
  os2Secondary: "#F5831E",
  overlayOnboarding: ["#121212", "#12121200"],
  overlayFlightDetailV2: ["rgba(18, 18, 18, 0.8)", "rgba(18, 18, 18, 0.4)"],
  overlayModalSaveFlightV2: ["rgba(18, 18, 18, 0.9)", "rgba(18, 18, 18, 0.5)"],
  overlayLinearT1Jewel: ["#1999AF", "#BFCF31"],
  overlayLinearT1T4: ["#DDB2FF", "#9838F8"],
  platinumLinearGradient: ["rgba(229, 229, 229, 0.80)", "rgba(153, 153, 153, 0.50)"],
  flightJourneyLinearGradient: ["#999999", "#6D6D6D"],
  almostWhite30: "#FCFCFC4D",
  //Explore V2
  backgroundMemberInfo: [
    "rgba(70, 38, 140, 0.96)",
    "rgba(105, 80, 161, 0.96)",
    "rgba(252, 252, 252, 0.96)",
  ],
  backgroundGoldInfo: [
    "rgba(144, 109, 40, 0.96)",
    "rgba(174, 152, 110, 0.96)",
    "rgba(252, 252, 252, 0.96)",
  ],
  backgroundPlantiumInfo: [
    "rgba(41, 52, 60, 0.96)",
    "rgba(43, 78, 99, 0.96)",
    "rgba(252, 252, 252, 0.96)",
  ],
  backgroundMonarchInfo: [
    "rgba(41, 52, 60, 0.96)",
    "rgba(43, 78, 99, 0.96)",
    "rgba(252, 252, 252, 0.96)",
  ],
}
