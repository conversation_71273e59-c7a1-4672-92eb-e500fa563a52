import { Pressable, StyleSheet, View } from "react-native"
import { COMPONENT_NAME, PromoCodeItemType, PromoCodeTypeEnum } from "../promo-codes.constants"
import {
  ChangiRewardsPrivilegesGoldIcon,
  ChangiRewardsPrivilegesMemberIcon,
  ChangiRewardsPrivilegesMonarchIcon,
  ChangiRewardsPrivilegesPlatinumIcon,
  Copy,
  ISCLogo,
} from "assets/icons"
import { newPresets, Text } from "app/elements/text"
import DashedLine from "react-native-dashed-line"
import { color } from "app/theme"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import { MutableRefObject, useMemo } from "react"
import { useRewardTier } from "app/hooks/useRewardTier"
import { Tier } from "app/models/enum"
import { NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import Clipboard from "@react-native-clipboard/clipboard"
import { palette } from "app/theme/palette"
import { trackAction, AdobeTagName } from "app/services/adobe"

interface PropsType {
  copiedToastRef?: MutableRefObject<any>
  dataLength?: number
  fetchPromoCodeDetails?: Function
  index?: number
  item?: PromoCodeItemType
}

const PromoCodeItem = (props: PropsType) => {
  const { copiedToastRef, dataLength, fetchPromoCodeDetails, index, item } = props
  const { handleNavigation } = useHandleNavigation("PROMO_CODE_ITEM")
  const { currentTier } = useRewardTier()
  const isFirstItem = index === 0
  const isLastItem = index === dataLength - 1

  const PromoIcon = useMemo(() => {
    switch (item?.promoType) {
      case PromoCodeTypeEnum.ISC:
        return ISCLogo
      case PromoCodeTypeEnum.CR:
        switch (currentTier) {
          case Tier.Member:
          case Tier.StaffMember:
            return ChangiRewardsPrivilegesMemberIcon
          case Tier.Gold:
          case Tier.StaffGold:
            return ChangiRewardsPrivilegesGoldIcon
          case Tier.Platinum:
          case Tier.StaffPlatinum:
            return ChangiRewardsPrivilegesPlatinumIcon
          case Tier.Monarch:
          case Tier.StaffMonarch:
            return ChangiRewardsPrivilegesMonarchIcon
          default:
            return ChangiRewardsPrivilegesMemberIcon // Default to Member icon if tier is not recognized
        }
      default:
        return ISCLogo // Default to ISCLogo if type is not recognized
    }
  }, [currentTier, JSON.stringify(item)])

  const onPressPromoCodeItem = () => {
    const campaignNameValue = item?.campaignName || "null"
    trackAction(AdobeTagName.CAppPromoCodes, {
      [AdobeTagName.CAppPromoCodes]: `${item?.title} | ${campaignNameValue}`,
    })

    if (item?.promoType === PromoCodeTypeEnum.ISC) {
      handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.ishopchangiPromoCode, {
        redirect: item?.cta,
      })
    } else if (item?.promoType === PromoCodeTypeEnum.CR) {
      fetchPromoCodeDetails?.(item)
    }
  }

  const onPressCopyPromoCode = () => {
    Clipboard.setString(item?.promoCode)
    copiedToastRef?.current?.show?.()
  }

  return (
    <MultimediaTouchableOpacity
      accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}`}
      onPress={onPressPromoCodeItem}
      style={[
        styles.containerStyle,
        isFirstItem && styles.firstItemContainerStyle,
        isLastItem && styles.lastItemContainerStyle,
      ]}
      testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}`}
    >
      <View
        accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Icon`}
        style={styles.iconContainerStyle}
        testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Icon`}
      >
        <PromoIcon height={32} width={32} />
      </View>
      <View style={styles.contentContainerStyle}>
        <View style={styles.infoSectionContainerStyle}>
          {item?.campaignName && (
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__CampaignName`}
              style={styles.campaignNameTextStyle}
              testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__CampaignName`}
              text={item?.campaignName}
            />
          )}
          {item?.title && (
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Title`}
              style={styles.titleTextStyle}
              testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Title`}
              text={item?.title}
            />
          )}
          {item?.validityPeriod && (
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__ValidityPeriod`}
              style={styles.validityPeriodTextStyle}
              testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__ValidityPeriod`}
              text={item?.validityPeriod}
            />
          )}
          {item?.description && (
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Description`}
              style={styles.descriptionTextStyle}
              testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__Description`}
              text={item?.description}
            />
          )}
        </View>
        {item?.promoCode && (
          <>
            <DashedLine
              dashGap={4}
              dashThickness={1}
              dashLength={4}
              dashColor={color.palette.lighterGrey}
              style={{ marginVertical: 12 }}
            />
            <View style={styles.promoCodeContainerStyle}>
              <Pressable style={{ flex: 1 }} onPress={() => {}}>
                <Text style={styles.promoCodeLabelTextStyle} tx="promoCodes.promoCodeLabel" />
                <Text
                  accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCode`}
                  numberOfLines={1}
                  style={styles.promoCodeValueTextStyle}
                  testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCode`}
                  text={item?.promoCode}
                />
              </Pressable>
              <MultimediaTouchableOpacity
                accessibilityLabel={`${COMPONENT_NAME}__PromoCodeItem__${
                  index + 1
                }__PromoCodeCopyBtn`}
                onPress={onPressCopyPromoCode}
                style={styles.promoCodeCopyBtnStyle}
                testID={`${COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCodeCopyBtn`}
              >
                <Copy height={16} width={16} />
              </MultimediaTouchableOpacity>
            </View>
          </>
        )}
      </View>
    </MultimediaTouchableOpacity>
  )
}

const styles = StyleSheet.create({
  containerStyle: {
    alignItems: "stretch",
    flexDirection: "row",
    gap: 1,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  firstItemContainerStyle: {
    marginTop: 24,
  },
  lastItemContainerStyle: {
    marginBottom: 200,
  },
  iconContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 8,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 8,
    justifyContent: "center",
    paddingHorizontal: 12,
    elevation: 2,
    shadowColor: palette.almostBlackGrey,
    shadowOpacity: 0.15,
    shadowRadius: 3,
    shadowOffset: { width: -1.5, height: 1 },
    marginRight: 0.5
  },
  contentContainerStyle: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 12,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 12,
    padding: 12,
    elevation: 2,
    shadowColor: palette.almostBlackGrey,
    shadowOpacity: 0.15,
    shadowRadius: 3,
    shadowOffset: { width: 1.5, height: 1 },
  },
  infoSectionContainerStyle: {
    flex: 1,
    gap: 4,
  },
  campaignNameTextStyle: {
    ...newPresets.XSmallBold,
    color: color.palette.lightPurple,
    textTransform: "none",
  },
  titleTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  validityPeriodTextStyle: {
    ...newPresets.caption2Bold,
    color: color.palette.darkestGrey,
  },
  descriptionTextStyle: {
    ...newPresets.caption2Regular,
  },
  promoCodeContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    gap: 4,
    justifyContent: "space-between",
  },
  promoCodeLabelTextStyle: {
    ...newPresets.XSmallRegular,
    color: color.palette.darkestGrey,
  },
  promoCodeValueTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.almostBlackGrey,
    letterSpacing: 0.6,
  },
  promoCodeCopyBtnStyle: {
    alignItems: "center",
    borderColor: color.palette.purpleD5BBEA,
    borderRadius: 60,
    borderWidth: 1,
    height: 28,
    justifyContent: "center",
    paddingHorizontal: 10,
  },
})

export default PromoCodeItem
