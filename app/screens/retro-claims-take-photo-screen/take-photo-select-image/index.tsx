import React, { useContext, useEffect, useRef, useState } from "react"
import { View, Alert, Image, StyleSheet, Platform, TouchableOpacity, Dimensions } from "react-native"
import { useSelector } from "react-redux"
import ImageCropPicker from "react-native-image-crop-picker"
import ImageEditor from "@react-native-community/image-editor"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import {
  RESULTS,
  PERMISSIONS,
  openSettings,
  request as requestPermission,
} from "react-native-permissions"

import { translate } from "app/i18n"
import { Text } from "app/elements/text"
import { AemSelectors } from "app/redux/aemRedux"
import { createResizedImage } from "app/utils/media-helper"
import { DT_ANALYTICS_LOG_EVENT_NAME, dtManualActionEvent } from "app/services/firebase"
import { RetroClaimsTakePhotoContext } from "../retro-claims-take-photo-screen"

import { CROPPER_OVERLAY_STYLES } from "../constants"
import { MODAL_SCREENS, TAKE_PHOTO_STEP } from "../types"
import {
  QuestionMark,
  FlashOnOnly,
  FlashOffOnly,
  ImageGallery,
  BackArrowCircle,
  Camera as CameraIcon,
} from "ichangi-fe/assets/icons"
import MyCamera, { MyCameraRef } from "app/components/camera"

import { styles } from "./styles"

const windowWidth = Dimensions.get("window").width
const windowHeight = Dimensions.get("window").height
const screenHeight = Dimensions.get("screen").height

const TakePhotoSelectImage = ({ navigation }) => {
  const {
    isCameraGranted,
    retroClaimConfigs,
    setIsCameraGranted,
    setReceiptImage,
    setCurrentScreen,
    setModalScreen,
    setIsFromGallery,
    setShowOnboarding,
    onOpenGalleryToSelect,
  } = useContext(RetroClaimsTakePhotoContext)
  const insets = useSafeAreaInsets()

  const [isFlashModeOn, setIsFlashModeOn] = useState(false)
  const [layoutData, setLayoutData] = useState({ headerHeight: 0, bottomHeight: 0 })

  const cameraRef = useRef<MyCameraRef>(null)

  const messageCommon = useSelector(AemSelectors.getMessagesCommon)
  const msg61 = messageCommon?.find((e) => e?.code === "MSG61")
  const msg62 = messageCommon?.find((e) => e?.code === "MSG62")

  const isIOS = Platform.OS === "ios"
  const insetsTop = isIOS ? 0 : insets.top || 0

  const rationale = {
    title: msg61?.title || translate("requestPermission.camera.title"),
    message: msg61?.message || translate("requestPermission.camera.message"),
    buttonPositive: msg61?.secondButton || translate("requestPermission.camera.buttonPositive"),
    buttonNegative: msg61?.firstButton || translate("requestPermission.camera.buttonNegative"),
  }

  const flashIcon = isFlashModeOn ? (
    <FlashOnOnly style={styles.bottomButtonIcon as any} />
  ) : (
    <FlashOffOnly style={styles.bottomButtonIcon as any} />
  )

  const cropperOverlayStyle = {
    height:
      windowHeight +
      insetsTop -
      ((layoutData?.headerHeight || 0) + (layoutData?.bottomHeight || 0)),
  }

  const onHeaderLayout = (e) => {
    const headerHeight = e?.nativeEvent?.layout?.height
    setLayoutData({ ...layoutData, headerHeight })
  }

  const onBottomLayout = (e) => {
    const bottomHeight = e?.nativeEvent?.layout?.height
    setLayoutData({ ...layoutData, bottomHeight })
  }

  const onPressHelpButton = () => {
    setModalScreen(MODAL_SCREENS.ONBOARDING_OVERLAY)
    setShowOnboarding(true)
  }

  const onChangeFlashMode = () => {
    setIsFlashModeOn(!isFlashModeOn)
  }

  const onPressTakePhotoButton = async () => {
    const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_RETRO_CLAIM_TAKE_PHOTO)
    const imageData = await cameraRef?.current?.takePicture?.()
    if (imageData?.path) {
      dtAction.reportStringValue('take-photo', 'success')
      const imagePath = isIOS ? imageData?.path : `file://${imageData?.path}`
      Image.getSize(imagePath, (width, height) => {
        if (width && height) {
          ;(async () => {
            const yAxisRatioHeight = isIOS ? windowHeight : screenHeight
            const xAxisRatio = width / windowWidth
            const yAxisRatio = height / yAxisRatioHeight

            dtAction.reportStringValue("image-width", `${width}`)
            dtAction.reportStringValue("image-height", `${height}`)

            const offsetX = CROPPER_OVERLAY_STYLES.LEFT_RIGHT_WIDTH * xAxisRatio
            const offsetY =
              (layoutData.headerHeight + CROPPER_OVERLAY_STYLES.TOP_BOTTOM_WIDTH) * yAxisRatio
            const newWidth = width - 2 * offsetX
            const newHeight =
              height -
              (layoutData.headerHeight +
                layoutData.bottomHeight +
                2 * CROPPER_OVERLAY_STYLES.TOP_BOTTOM_WIDTH) *
                yAxisRatio
            const cropTopData = {
              offset: { x: offsetX, y: offsetY },
              size: { width: newWidth, height: newHeight },
            }
            const croppedTopImage = await ImageEditor.cropImage(imagePath, cropTopData)

            ImageCropPicker.openCropper({
              mediaType: "photo",
              path: croppedTopImage?.uri,
              freeStyleCropEnabled: true,
              ...(isIOS && { width: newWidth, height: newHeight }),
              // For Android
              cropperStatusBarColor: "#000000",
              cropperActiveWidgetColor: "#ab76d5",
              cropperToolbarColor: "black",
              cropperToolbarWidgetColor: "#ffffff",
              // For iOS
              cropperCancelColor: "#ab76d5",
              cropperChooseText: "Next",
              cropperChooseColor: "#ab76d5",
            })
              .then(async (croppingResult) => {
                dtAction.reportStringValue("cropping-result", "success")
                const { retro_claim_quality, retro_claim_max_height, retro_claim_max_width } =
                  retroClaimConfigs || {}
                const resizeImageConfigs = {
                  ...(retro_claim_max_width && { maxWidth: Number(retro_claim_max_width) }),
                  ...(retro_claim_max_height && { maxHeight: Number(retro_claim_max_height) }),
                  ...(retro_claim_quality && { quality: Number(retro_claim_quality) }),
                }

                dtAction.reportStringValue("configs-quality", `${retro_claim_quality}`)
                dtAction.reportStringValue("configs-max-width", `${retro_claim_max_width}`)
                dtAction.reportStringValue("configs-max-height", `${retro_claim_max_height}`)
                const base64Image = await createResizedImage({
                  imageData: croppingResult,
                  ...resizeImageConfigs,
                })

                setReceiptImage(base64Image)
                setCurrentScreen(TAKE_PHOTO_STEP.CONFIRM_PAGE)
              })
              .catch((error) => {
                dtAction.reportStringValue(
                  "cropping-result",
                  `Error: ${error?.message || "unknown"}`,
                )
                console.log("error", error)
              })
              .finally(() => {
                dtAction.leaveAction()
              })
          })()
        } else {
          dtAction.reportStringValue('get-size', 'failed')
          dtAction.leaveAction()
        }
      })
      setIsFromGallery(false)
    } else {
      dtAction.reportStringValue('take-photo', 'failed')
      dtAction.leaveAction()
    }
    setIsFlashModeOn(false)
  }

  const handlePressBackButton = async () => {
    navigation?.goBack?.()
  }

  useEffect(() => {
    setTimeout(() => {
      requestPermission(
        isIOS ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA,
        rationale,
      ).then(async (result) => {
        if ([RESULTS.BLOCKED, RESULTS.DENIED].some((val) => val === result)) {
          setIsCameraGranted(false)
          Alert.alert(
            msg62?.title || translate("retroClaims.needAccessPermission.title"),
            msg62?.message || translate("retroClaims.needAccessPermission.description"),
            [
              {
                text:
                  msg62?.firstButton || translate("retroClaims.needAccessPermission.firstButton"),
                isPreferred: true,
                onPress: openSettings,
              },
              {
                text:
                  msg62?.secondButton || translate("retroClaims.needAccessPermission.secondButton"),
                onPress: () => null,
              },
            ],
          )
        } else if (result === RESULTS.GRANTED) {
          setIsCameraGranted(true)
        }
      })
    }, 1000)
  }, [])

  return (
    <View style={styles.wrapper}>
      {isCameraGranted && (
        <MyCamera
          ref={cameraRef}
          flashMode={isFlashModeOn}
          photoRatio={windowHeight / windowWidth}
        />
      )}

      {!cameraRef?.current?.isCameraReady && <View style={styles.deniedStyle} />}

      <View style={styles.headerWrapper} onLayout={onHeaderLayout}>
        <TouchableOpacity onPress={handlePressBackButton}>
          <BackArrowCircle width={28} height={28} style={styles.backArrowIcon as any} />
        </TouchableOpacity>

        <TouchableOpacity onPress={onPressHelpButton}>
          <QuestionMark width={24} height={24} style={styles.questionMarkIcon as any} />
        </TouchableOpacity>
      </View>

      {!!cameraRef?.current?.isCameraReady && (
        <View style={[styles.cropperOverlay, cropperOverlayStyle]}>
          <View style={styles.cropperOverlayTop} />
          <View style={styles.cropperOverlayLeft} />
          <View style={styles.cropperOverlayRight} />
          <View style={styles.cropperOverlayBottom} />
        </View>
      )}

      <View style={styles.bottomWrapper} onLayout={onBottomLayout}>
        <Text style={styles.guideMessage} tx="retroClaims.takePhotoGuideMessage" />

        <View style={styles.bottomButtons}>
          <TouchableOpacity onPress={onChangeFlashMode} style={styles.bottomButtonItem}>
            {flashIcon}
          </TouchableOpacity>

          <TouchableOpacity
            disabled={!isCameraGranted}
            style={styles.takePhotoButton}
            onPress={onPressTakePhotoButton}
          >
            <CameraIcon width={32} height={32} style={styles.takePhotoIcon} />
          </TouchableOpacity>

          <TouchableOpacity onPress={onOpenGalleryToSelect} style={styles.bottomButtonItem}>
            <ImageGallery style={styles.bottomButtonIcon as any} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

export default TakePhotoSelectImage
