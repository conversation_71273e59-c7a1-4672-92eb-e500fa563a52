import React from 'react';
import { Dimensions, Platform, StyleSheet, TouchableOpacity, View, Image } from 'react-native';
import { Text } from 'app/elements/text';
import { typography } from 'app/theme/typography';
import { SearchBox, RewardIcon } from 'assets/icons';
import { useNavigation } from '@react-navigation/native'
import { NavigationConstants } from 'app/utils/constants';

const widthScreen = Dimensions.get('window').width;
const widthViewSearchDefault = widthScreen - 32 - 36 - 12

const ViewHeader = React.memo(() => {
  const navigation = useNavigation<any>();

  const navigateToSearch = () => {
    navigation.navigate("search")
  }

  const navigateToRedeem = () => {
    navigation.navigate(NavigationConstants.redemptionCatalogueScreen)
  }

  return (
    <View style={styles.container}>
      <View style={styles.viewRowContent}>
        <TouchableOpacity style={styles.viewSearch} onPress={navigateToSearch} activeOpacity={0.7}>
          <SearchBox width={16} height={16} color={"#FCFCFC99"} />
          <Text tx="exploreScreenV2.titleSearch" style={styles.txtTitle} numberOfLines={1} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.marginIcon} onPress={navigateToRedeem} activeOpacity={0.7}>
          <Image source={RewardIcon} />
        </TouchableOpacity>
      </View>
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 100,
    justifyContent: 'flex-end',
    paddingBottom: 8,
    paddingHorizontal: 16,
  },
  viewRowContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  marginIcon: {
    marginLeft: 12,
  },
  viewSearch: {
    height: 36,
    width: widthViewSearchDefault,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    backgroundColor: '#FFFFFF1A'
  },
  backdropContainerStyle: {
    flex: 1,
    borderRadius: 8,
    backgroundColor: "#FFFFFF1A",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
  },
  txtTitle: {
    fontFamily: typography.regular,
    color: "#FCFCFC99",
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    marginLeft: 6
  },
})

export { ViewHeader }
