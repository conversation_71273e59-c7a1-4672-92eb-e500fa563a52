import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { ExploreSelectors } from 'app/redux/exploreRedux';
import { MytravelCreators, MytravelSelectors } from 'app/redux/mytravelRedux';
import { handleCondition, ifAllTrue } from 'app/utils';
import React, { useMemo } from 'react';
import { StyleSheet, View, TouchableOpacity, Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ProfileSelectors } from "app/redux/profileRedux";
import { isEmpty } from "lodash"
import { UpComingEventComponent } from "app/components/homepage-masthead/upcoming-event";
import { UpComingEventState } from "app/components/homepage-masthead/upcoming-event/upcoming-event-props";
import { color, typography } from "app/theme";
import { ReloadIcon } from "assets/icons";
import { Text } from "app/elements/text";

const ViewFlightUpcoming = () => {
  const dispatch = useDispatch();
  const userProfile = useSelector(ProfileSelectors.profilePayload)
  const getUpComingEventError = useSelector(ExploreSelectors.getUpComingEventError)
  const getUpComingLoading = useSelector(ExploreSelectors.getUpComingLoading)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const isLoggedIn: boolean = useSelector(AuthSelectors.isLoggedIn)
  const upComingEventData = useSelector(ExploreSelectors.upComingEventsData)
  const getUpcomingEventsSuccess = useSelector(ExploreSelectors.getUpcomingEventsSuccess)
  const profileError = useSelector(ProfileSelectors.profileError)

  const loadingType = useMemo(() => {
    return ifAllTrue([!getUpComingLoading, !myTravelFlightsPayload?.loading])
  }, [getUpComingLoading, myTravelFlightsPayload])

  const isShowUpcomingEventView = useMemo(() => {
    if (ifAllTrue([isLoggedIn, !isEmpty(upComingEventData) || getUpComingEventError])) return true
    return false
  }, [isLoggedIn, upComingEventData, getUpComingEventError, getUpcomingEventsSuccess, profileError])

  const isError = getUpComingEventError !== null && loadingType && isLoggedIn

  const onReLoadUpComingEvent = () => {
    dispatch(MytravelCreators.flyMyTravelFlightsRequest(userProfile?.email))
  }

  return (
    <>
      <View style={[styles.container, { marginLeft: isError ? 0 : -12 }]}>
        {handleCondition(
          isError,
          <View style={styles.upcomingEventErrContainer}>
            {[1, 2].map((index) => {
              return (
                <View key={index} style={styles.viewError}>
                  <TouchableOpacity style={styles.buttonReload}
                    onPress={onReLoadUpComingEvent}
                  >
                    <ReloadIcon color={color.palette.darkGrey} width={40} height={40} />
                    <Text style={styles.txtButton} tx="common.reload" />
                  </TouchableOpacity>
                </View>
              )
            })}
          </View>,
          isShowUpcomingEventView && (
            <UpComingEventComponent
              data={upComingEventData}
              type={UpComingEventState.default}
              styleContainer={styles.viewList}
            />
          ),
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  viewList: {
    position: 'relative',
    bottom: 0,
    marginTop: -8
  },
  upcomingEventErrContainer: {
    width: '100%',
    flexDirection: 'row',
    paddingLeft: 16,
    gap: 12
  },
  viewError: {
    width: 280,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 16,

    shadowColor: color.palette.almostBlackGrey,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.08,
    shadowRadius: 20,
    elevation: 4,
  },
  buttonReload: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtButton: {
    fontFamily: typography.bold,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    color: color.palette.lightPurple,
  }
});

export { ViewFlightUpcoming }
