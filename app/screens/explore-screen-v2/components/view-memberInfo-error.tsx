import { color, typography } from "app/theme"
import { ReloadIcon } from "assets/icons"
import React from "react"
import { Platform, StyleSheet, TouchableOpacity, View } from "react-native"
import { Text } from "app/elements/text"

const ViewMemberInfoError = React.memo((props: any) => {
  const { onReload } = props
  return (
    <View style={styles.container}>
      <View style={styles.viewTxt}>
        <Text style={styles.title}>Hi</Text>
        <View style={[styles.viewRow, { gap: 12 }]}>
          <TouchableOpacity style={styles.viewRow} activeOpacity={0.7}>
            <ReloadIcon color={color.palette.midGrey} />
            <Text style={styles.txtPoint} tx="common.reload" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.viewRow} activeOpacity={0.7}>
            <ReloadIcon color={color.palette.midGrey} />
            <Text style={styles.txtPoint} tx="common.reload" />
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity style={styles.button} onPress={onReload} activeOpacity={0.7}>
        <ReloadIcon color={color.palette.midGrey} />
        <Text style={styles.txtButton} tx="common.reload" />
      </TouchableOpacity>
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 18,
    paddingVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  viewTxt: {
    flex: 1,
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.darkGrey999,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    marginBottom: 8,
  },
  viewRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  txtPoint: {
    fontFamily: typography.regular,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
    color: color.palette.almostWhiteGrey80,
    marginLeft: 4,
  },
  button: {
    width: 120,
    height: 32,
    borderRadius: 99,
    backgroundColor: color.palette.lighterGrey,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
  txtButton: {
    fontFamily: typography.bold,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
    color: color.palette.darkGrey999,
    marginLeft: 6,
  },
})

export { ViewMemberInfoError }
