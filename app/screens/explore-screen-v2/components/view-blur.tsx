import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import { sectionsPayloadForExplore } from "ichangi-fe/app/screens/explore-screen/json/sectionsData"
import BaseImage from 'app/elements/base-image/base-image';
import { BlurView } from "@react-native-community/blur"

const widthScreen = Dimensions.get('window').width;

const ViewBlurBackground = React.memo((props: any) => {
  const { url } = props;
  return (
    <View style={styles.container}>
      <BaseImage
        source={{ uri: url ? url : sectionsPayloadForExplore.homePageMastHead.default.loggedIn.imageUrl }}
        style={styles.image}
        resizeMode='cover'
      />
      <BlurView
        style={styles.backdropContainerStyle}
        blurType="dark"
        blurAmount={3}
        reducedTransparencyFallbackColor={"#12121299"}
        overlayColor={"#12121299"}
      />
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: widthScreen,
    position: 'absolute',
    top: 0
  },
  image: {
    flex: 1
  },
  backdropContainerStyle: {
    flex: 1,
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0
  }
})

export { ViewBlurBackground }