import { Text } from "app/elements/text"
import { color, typography } from "app/theme"
import React, { useMemo } from "react"
import { Platform, StyleSheet, TouchableOpacity, View } from "react-native"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { useDispatch, useSelector } from "react-redux"
import { useRewardTier } from "app/hooks/useRewardTier"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { get } from "lodash"
import { Wallet } from "assets/icons"
import { ViewMemberInfoError } from "./view-memberInfo-error"
import {
  NavigationConstants,
  PLACEHOLDER_ANIMATION_SPEED_IN_MS,
  SOURCE_SYSTEM,
} from "app/utils/constants"
import { useNavigation } from "@react-navigation/native"
import ChangiEcardController from "app/components/changi-ecard/changi-ecard-controler"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import ProfileActions from "app/redux/profileRedux"

const LOADING_COLORS = [color.palette.lighterGrey, color.background, color.palette.lighterGrey]

const transformRewardPoint = (rewardsData) => {
  const points = get(rewardsData, "reward.point")
  if (points) {
    const pointSubstr = points.substring(0, 8)
    if (points.length > 10) {
      return `${pointSubstr}...`
    }
    return points
  }
  return ""
}

const ViewMemberInfoLoading = () => {
  return (
    <View style={styles.container}>
      <View style={styles.viewTxt}>
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingTitle}
        />
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingSubTitle}
        />
      </View>
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={LOADING_COLORS}
        shimmerStyle={styles.loadingButton}
      />
    </View>
  )
}

const ViewMemberInfo = React.memo((props: any) => {
  const { profileFetching, profileError, userProfile, rewardsFetching, rewardsError } = props
  const dispatch = useDispatch()
  const navigation = useNavigation<any>()
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const { memberIconInfo } = useRewardTier()
  const BFIcon = memberIconInfo?.bfIcon
  const colorTextButton = memberIconInfo?.colorTextRewardCard
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const rewardPoints = useMemo(() => transformRewardPoint(rewardsData), [rewardsData])
  const firstName = get(userProfile, "firstName")

  const renderTitle = () => {
    if (isLoggedIn) {
      return `Hi ${firstName}`
    } else {
      return "Hi!"
    }
  }

  const onLogin = () => {
    navigation.navigate(NavigationConstants.authScreen, {
      sourceSystem: SOURCE_SYSTEM.OTHERS,
      callBackAfterLoginSuccess: undefined,
      callBackAfterLoginCancel: undefined,
    })
  }

  const onShowRewardCard = () => {
    if (isLoggedIn) {
      ChangiEcardController.showModal(navigation)
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.OTHERS,
        callBackAfterLoginSuccess: () => {
          setTimeout(() => {
            ChangiEcardController.showModal(navigation)
          }, 500)
        },
        callBackAfterLoginCancel: undefined,
      })
    }
  }

  const onReload = () => {
    dispatch(ProfileActions.profileReset())
    dispatch(ProfileActions.profileRequest())
  }

  const renderContentSubTitle = () => {
    if (isLoggedIn) {
      return (
        <View style={[styles.viewRow, { gap: 12 }]}>
          <Text style={styles.txtPoint}>{rewardPoints} pts</Text>
          <View style={styles.viewRow}>
            <Wallet color={color.palette.whiteGrey} />
            <Text style={styles.txtPoint} tx="homepageMasthead.wallet" />
          </View>
        </View>
      )
    } else {
      return (
        <TouchableOpacity onPress={onLogin}>
          <Text tx="homepageMasthead.logInText" style={styles.txtPoint} />
        </TouchableOpacity>
      )
    }
  }

  if (profileFetching || rewardsFetching) {
    return <ViewMemberInfoLoading />
  }

  if (profileError || rewardsError) {
    return <ViewMemberInfoError onReload={onReload} />
  }

  return (
    <View style={styles.container}>
      <View style={styles.viewTxt}>
        <Text style={styles.title}>{renderTitle()}</Text>
        {renderContentSubTitle()}
      </View>
      <TouchableOpacity style={styles.button} onPress={onShowRewardCard} activeOpacity={0.7}>
        <BFIcon />
        <Text
          tx="homepageMasthead.rewardsCard"
          style={[styles.txtButton, { color: colorTextButton }]}
        />
      </TouchableOpacity>
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 18,
    paddingVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.whiteGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    marginBottom: 8,
  },
  button: {
    width: 120,
    height: 32,
    borderRadius: 99,
    backgroundColor: color.palette.whiteGrey,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
  viewTxt: {
    flex: 1,
  },
  viewRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  txtButton: {
    fontFamily: typography.bold,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  },
  txtPoint: {
    fontFamily: typography.regular,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
    color: color.palette.almostWhiteGrey80,
  },
  loadingTitle: {
    width: 88,
    height: 16,
    borderRadius: 4,
    marginBottom: 8,
  },
  loadingSubTitle: {
    width: 88,
    height: 16,
    borderRadius: 4,
  },
  loadingButton: {
    width: 120,
    height: 32,
    borderRadius: 99,
  },
})

export { ViewMemberInfo }
