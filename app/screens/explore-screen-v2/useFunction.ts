import { ForYouSelectors } from "app/redux/forYouRedux"
import { ProfileSelectors } from "app/redux/profileRedux"
import { useState } from "react"
import { useSelector } from "react-redux"

const useFunction = () => {
  const rewardsFetching = useSelector(ForYouSelectors.rewardsFetching)
  const profileFetching = useSelector(ProfileSelectors.profileFetching)
  const userProfile = useSelector(ProfileSelectors.profilePayload)
  const profileError = useSelector(ProfileSelectors.profileError)
  const rewardsError = useSelector(ForYouSelectors.rewardsError)

  const [urlImageBlur, setUrlImageBlur] = useState(null)

  return {
    urlImageBlur,
    setUrlImageBlur,
    profileFetching,
    profileError,
    userProfile,
    rewardsFetching,
    rewardsError
  }
}

export { useFunction }
