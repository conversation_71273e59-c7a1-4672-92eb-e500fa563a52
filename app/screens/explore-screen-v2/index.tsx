import React from "react"
import { StyleSheet, View, ScrollView } from "react-native"
import { useFunction } from "./useFunction"
import { ViewBlurBackground, ViewHeader, ViewMemberInfo, ViewFlightUpcoming } from "./components"
import { color } from "app/theme"
import { FocusAwareStatusBar } from "app/components/focus-status-bar"
import LinearGradient from "react-native-linear-gradient"
import { useRewardTier } from "app/hooks/useRewardTier"

const ExploreScreenV2 = () => {
  const { memberIconInfo } = useRewardTier()
  const {
    urlImageBlur,
    setUrlImageBlur,
    profileFetching,
    profileError,
    userProfile,
    rewardsFetching,
    rewardsError,
  } = useFunction()

  return (
    <>
      <FocusAwareStatusBar translucent backgroundColor="transparent" barStyle={"light-content"} />
      <ViewBlurBackground url={urlImageBlur} />
      <ScrollView contentContainerStyle={styles.container}>
        <ViewHeader />
        <View style={styles.viewContent}>
          <LinearGradient
            colors={
              profileFetching || profileError || rewardsFetching
                ? [color.palette.almostWhiteGrey, color.palette.almostWhiteGrey]
                : memberIconInfo?.backgroundMemberInfoExploreV2
            }
            locations={[0, 0.6173, 0.9497]}
            style={styles.viewMemberInfo}
          >
            <ViewMemberInfo
              rewardsFetching={rewardsFetching}
              profileFetching={profileFetching}
              profileError={profileError}
              userProfile={userProfile}
              rewardsError={rewardsError}
            />
          </LinearGradient>
          <View style={styles.viewMargin}>
            <ViewFlightUpcoming />
          </View>
        </View>
      </ScrollView>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  viewContent: {
    flex: 1,
    marginTop: 8,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  viewMemberInfo: {
    width: "100%",
    height: 172,
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
  },
  viewMargin: {
    flex: 1,
    marginTop: -90
  }
})

export default ExploreScreenV2
