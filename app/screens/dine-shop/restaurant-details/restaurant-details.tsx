import React, { useEffect, useRef, useState } from "react"
import {
  LayoutAnimation,
  Linking,
  Platform,
  SafeAreaView,
  ScrollView,
  TextStyle,
  TouchableOpacity,
  UIManager,
  View,
  ViewStyle,
} from "react-native"
import { get, isEmpty } from "lodash"
import * as styles from "./restaurant-details-styles"
import { RestaurantLoadingDetailsScreen } from "app/screens/dine-shop/restaurant-details/restuarant-details-loading-view"
import { Text } from "app/elements/text/text"
import NetInfo from "@react-native-community/netinfo"
import { getDotUnicode, NavigationConstants, StateCode } from "app/utils/constants"
import { useDispatch, useSelector } from "react-redux"
import { RootState } from "app/redux/store"
import DineCreators, { DineSelectors } from "../../../redux/dineRedux"
import { translate } from "app/i18n"
import {
  BackButton,
  DownArrow,
  TopArrow,
  DirectionsOutline,
  ClockOutline,
  PhoneOutline,
  CheckDate,
  FoodMenuIcon,
  ShopBagIcon,
  InfoRed,
} from "ichangi-fe/assets/icons"
import { InfoBanner } from "app/components/info-banner/info-banner"
import { Hero } from "app/components/hero/hero"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { ErrorScreen } from "app/screens/dine-shop/dine-shop-offer-details/error"
import { AdobeTagName, AdobeValueByTagName, commonTrackingScreen, getExperienceCloudId, trackAction } from "app/services/adobe"
import { LoadingOverlay } from "app/components/loading-modal"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { BottomSheetUnableLoadLocation } from "app/components/bottom-sheet-unable-load-location/bottom-sheet-unable-load-location"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { CuisineInDetail } from "app/sections/cusine-in-detail/cusine-in-detail"
import { color } from "app/theme"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import ReadMore from "app/components/read-more/read-more"
import AvailableStaffDiscounts from "../available-staff-discounts"
import AvailableDealsPromos from "../available-deals-promos"
import { getStaffPerkAvailableDiscount, getDealsPromosAvailableDiscount } from "app/sagas/staffPerkSaga"
import { isFlagON, REMOTE_CONFIG_FLAGS, getFeatureFlagInit } from "app/services/firebase/remote-config"
import { BottomSheetMapUnavailable } from "app/components/bottom-sheet-map-unavailable/bottom-sheet-map-unavailable"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { WebViewHeaderTypes } from "app/models/enum"
import { getISCLinkRedirectTarget } from "app/helpers/deeplink/deeplink-parameter"
import { useModal } from "app/hooks/useModal"
import { BottomSheetError } from "app/components/bottom-sheet-error"

const paddingContentScrollView: ViewStyle = { paddingBottom: 50 }

const loadingType = "loading"
const SCREEN_NAME = "RestaurantDetailsScreen"

const safeViewHeaderForAndroid = (inset) => {
  const _safeViewHeaderForAndroid: ViewStyle = {
    marginTop: Platform.OS === "android" && inset?.top ? inset?.top : 0,
    marginStart: 14,
  }
  return _safeViewHeaderForAndroid
}

const getDay = (day) => {
  return day || translate("restaurantDetails.daily")
}

const touchableReserve: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  borderWidth: 2,
  borderColor: color.palette.lightPurple,
  paddingVertical: 10,
  borderRadius: 60,
}

const wrapChope: ViewStyle = {
  marginHorizontal: 24,
  paddingTop: 16,
}

const touchableTextChope: TextStyle = {
  fontSize: 16,
  lineHeight: 24,
  marginLeft: 14,
  color: color.palette.lightPurple,
}

const getMapName = (item, dotUnicode) => {
  if (item?.mapName && item.openCloseStatus?.status && !item?.hourComment) {
    return item.mapName + `${"  "}` + dotUnicode + `${"  "}`
  }

  return item?.mapName
}

const renderCloseStatus = (item) => {
  if (item.openCloseStatus) {
    return (
      <Text
        preset="bodyTextBold"
        style={[{ color: item.openCloseStatus.colorCode }, styles.statusTextStyle]}
      >
        {item.openCloseStatus.status}
      </Text>
    )
  }

  return null
}

const getTextNumber = (item, number) => {
  return (item ? ", " : "") + number
}

const ExpandableComponent = ({
  item,
  onClickFunction,
  key,
  tenantName,
  _setLoading,
  dialCall,
  dotUnicode,
  setConnection,
  refRetryAction,
  index,
}) => {
  const [layoutHeight, setLayoutHeight] = useState(0)
  const navigation: any = useNavigation()
  const unableToLoadLocationRef = useRef(null)
  const mapUnavailable = useRef(null)
  const [mapRMFlag, setMapRMFlag] = useState(false)
  const hourComment = item?.hourComment

  useEffect(() => {
    if (item.isExpanded) {
      setLayoutHeight(null)
      return
    }
    setLayoutHeight(0)
  }, [item.isExpanded])

  const handleSpace = (indx, size) => {
    if (indx < size) return { marginBottom: 8 }
    return {}
  }

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const handleFindDirection = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      refRetryAction.current = null
      setConnection(true)
      if (!mapRMFlag) {
        return mapUnavailable?.current.show()
      }
      trackAction(AdobeTagName.CAppATOMSEntryClick, {
        [AdobeTagName.CAppATOMSEntryClick]: `Dine Details|Get Directions|${item?.locationDetails?.[index].localRef}`,
      })
      trackAction(AdobeTagName.CAppDineDetail, {
        [AdobeTagName.CAppDineDetail]: `Dine Information | Get Direction`,
      })

      navigation.push(NavigationConstants.changiMap, {
        localRef: item?.locationDetails?.[index].localRef,
      })
      return
    }
    refRetryAction.current = handleFindDirection
    setConnection(false)
  }

  const viewMenuOnPress = (menuLink: string) => {
    trackAction(AdobeTagName.CAppDineDetailDineInformationViewMenu, {
      [AdobeTagName.CAppDineDetailDineInformationViewMenu]: tenantName,
    })
    trackAction(AdobeTagName.CAppDineDetail, {
      [AdobeTagName.CAppDineDetail]: `Dine Information | ${translate("restaurantDetails.viewMenu")}`,
    })

    navigation.navigate(NavigationConstants.webview, { uri: menuLink })
  }
  return (
    <View style={styles.wrapInformationDetail}>
      <TouchableOpacity
        style={styles.header}
        onPress={onClickFunction}
        testID={`${SCREEN_NAME}__TouchableExpand__${key}`}
        accessibilityLabel={`${SCREEN_NAME}__TouchableExpand__${key}`}
      >
        <View style={styles.flexStyleHeader}>
          <View style={styles.headerStyle}>
            <Text preset="h4" style={styles.txtColor} text={getMapName(item, dotUnicode)} />
            {!hourComment && renderCloseStatus(item)}
          </View>
          <View style={styles.arrowIconStyle}>
            {item.isExpanded ? <TopArrow /> : <DownArrow />}
          </View>
        </View>
      </TouchableOpacity>
      <View style={[{ height: layoutHeight }, styles.visibileStyle]}>
        <View key={"content"} style={styles.content}>
          {hourComment && (
            <Text numberOfLines={3} style={styles.temporaryClosedStyle}>
              {hourComment}
            </Text>
          )}
          <View style={styles.flexStyle}>
            <DirectionsOutline style={styles.directionIconstyle} />
            <View style={styles.areaInfoViewStyle}>
              <Text numberOfLines={2} preset="bodyTextRegular" style={styles.text}>
                {item.terminal}
                {" " + item.description ? " " + item.description + ", " : " "}
                {item.unitNo ? "#" + item.unitNo : " "},{" "}
                {item.area + translate("restaurantDetails.area")}
              </Text>
              <TouchableOpacity
                onPress={handleFindDirection}
                disabled={isEmpty(item?.locationDetails?.[index]?.localRef)}
              >
                <Text
                  preset="bodyTextBold"
                  style={
                    isEmpty(item?.locationDetails?.[index]?.localRef)
                      ? styles.getDirectionStyleDisabled
                      : styles.getDirectionStyle
                  }
                >
                  Get directions
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {item.timingsInfo?.length > 0 ? (
            <View style={styles.flexStyle}>
              <ClockOutline style={styles.clockIconStyle} />
              <View style={styles.timingsInfoViewStyle}>
                {item.timingsInfo?.map((res, keyVal) => {
                  return (
                    <View
                      key={keyVal}
                      style={[styles.timingStyle, handleSpace(keyVal, item?.timingsInfo?.length)]}
                    >
                      <Text preset="bodyTextRegular" style={styles.timingsInfoDayText}>
                        {getDay(res.day)}
                      </Text>
                      <Text preset="bodyTextRegular" style={styles.timingsText}>
                        {res.timings}
                      </Text>
                    </View>
                  )
                })}
              </View>
            </View>
          ) : null}
          {item.phoneDetails?.length > 0 ? (
            <View style={styles.flexStyle}>
              <PhoneOutline style={styles.phoneIconStyle} />
              {item.phoneDetails.map((number, i) => {
                return (
                  <TouchableOpacity
                    key={i}
                    onPress={() => dialCall(number)}
                    testID={`${SCREEN_NAME}__TouchableDialCall__${key}`}
                    accessibilityLabel={`${SCREEN_NAME}__TouchableDialCall__${key}`}
                  >
                    <Text preset={"textLink"} style={styles.phoneNumberStyle} key={i}>
                      {getTextNumber(i, number)}
                    </Text>
                  </TouchableOpacity>
                )
              })}
            </View>
          ) : null}
          {!isEmpty(item?.locationDetails?.[index]?.menuLink) && (
            <TouchableOpacity
              onPress={() => viewMenuOnPress(item?.locationDetails?.[index]?.menuLink)}
            >
              <View style={styles.menuLinkContainer}>
                <FoodMenuIcon />
                <Text
                  tx={"restaurantDetails.viewMenu"}
                  preset="textLink"
                  style={styles.menuLinkStyles}
                  numberOfLines={1}
                />
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
      <View style={styles.separator} />
      <BottomSheetUnableLoadLocation ref={unableToLoadLocationRef} />
      <BottomSheetMapUnavailable ref={mapUnavailable} />
    </View>
  )
}

export const RestaurantDetailsScreen = ({ navigation, route }) => {
  const tenantId = useRef<string>("")
  tenantId.current = route.params.tenantId
  const name = route.params.name || ""
  const dotUnicode = getDotUnicode()
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()
  const inset = useSafeAreaInsets()
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const restaurantData: any = useSelector<RootState, any>((data) =>
    DineSelectors.restaurantDetailsData(data),
  )
  const changiEatsPayload = get(restaurantData, "changiEatsPayload")
  const changiRewardsPayload = get(restaurantData, "changiRewardsPayload")
  const nameTagLocationPayload = get(restaurantData, "nameTagLocationPayload")
  const blogsAndReviewsPayload = get(restaurantData, "blogsAndReviewsPayload")
  const [isConnection, setConnection] = useState(true)

  const restaurantDetailsData = nameTagLocationPayload?.data
  const rewardInfoData = changiRewardsPayload?.data
  const changiEatsData = changiEatsPayload?.data
  const blogsAndReviewsData = blogsAndReviewsPayload?.data
  const locationDetails = restaurantDetailsData?.locationDetails
  const [listDataSource, setListDataSource] = useState([])

  const profile = useSelector(ProfileSelectors.profilePayload)
  const isStaff = profile?.airportStaff
  const [availableDiscounts, setAvailableDiscounts] = useState([])
  const [availableDealsPromos, setAvailableDealsPromos] = useState([])
  const refRetryAction = useRef(null)
  const {isModalVisible, openModal, closeModal} = useModal("dineDetailError")

  const onPressBackButton = () => {
    navigation.goBack()
    trackAction(AdobeTagName.CAppDineDetail, {
      [AdobeTagName.CAppDineDetail]: "Back to Previous Page",
    })
  }

  const getStaffAvailableDiscount = async () => {
    const featureFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.TENANTDETAILS_STAFFPERKS)
    if (!featureFlagEnable || !isLoggedIn || !isStaff) {
      return
    }
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      const staffDiscount = await getStaffPerkAvailableDiscount({ tenantId: tenantId.current })
      if (!isEmpty(staffDiscount)) {
        setAvailableDiscounts(staffDiscount)
      }
    }
  }

  const getRestaurantDetails = React.useCallback(async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setConnection(true)
      dispatch(DineCreators.restaurantDetailsRequest(tenantId.current))
      dispatch(DineCreators.restaurantBlogsAndReviewsRequest(tenantId.current))
      dispatch(DineCreators.restaurantChangiEatsRequest(tenantId.current))
      dispatch(DineCreators.restaurantRewardInfoRequest(tenantId.current))
      if (isStaff && !isEmpty(tenantId.current)) {
        getStaffAvailableDiscount()
      }
      // Fetch Deals & Promos tenant details
      const isShopDineV2Enabled = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)
      if (isShopDineV2Enabled) {
        const promos = await getDealsPromosAvailableDiscount({ tenantId: tenantId.current })
        if (!isEmpty(promos)) {
          setAvailableDealsPromos(promos)
        }
      }
      return
    }
    setConnection(false)
  }, [])

  useCurrentScreenActiveAndPreviousScreenHook(`Dine_Detail_${name}`)
  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      commonTrackingScreen(
        `Dine_Detail_${name}`,
        getPreviousScreen(),
        isLoggedIn,
      )
    })
    return unsubscribeFocus
  }, [navigation])

  useFocusEffect(
    React.useCallback(() => {
      getRestaurantDetails()
    }, []),
  )

  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true)
  }
  React.useEffect(() => {
    setListDataSource(locationDetails)
  })
  const updateLayout = (index) => {
    trackAction(AdobeTagName.CAppDineDetail, {
      [AdobeTagName.CAppDineDetail]: `${AdobeValueByTagName.CAppDineDetailDineInformation}View Information`,
    })

    LayoutAnimation.configureNext({
      ...LayoutAnimation.Presets.easeInEaseOut,
      duration: 100,
    })
    const array = [...listDataSource]
    array.forEach((_value, placeindex) => {
      if (placeindex === index) {
        array[placeindex].isExpanded = !array[placeindex].isExpanded
      } else {
        array[placeindex].isExpanded = false
      }
    })
    setListDataSource(array)
  }

  const transformPhoneNumber = (phone) => {
    if (Platform.OS === "android") {
      return `tel:${phone}`
    }
    return `telprompt:${phone}`
  }

  const dialCall = (num: string) => {
    const phoneNumber = transformPhoneNumber(num)
    trackAction(AdobeTagName.CAppDineDetail, {
      [AdobeTagName.CAppDineDetail]: `${AdobeValueByTagName.CAppDineDetailDineInformation}Phone Number`,
    })

    Linking.openURL(phoneNumber)
  }

  const handleReadMorePress = (handlePress) => {
    trackAction(AdobeTagName.CAppDineDetail, {
      [AdobeTagName.CAppDineDetail]: `${AdobeValueByTagName.CAppDineDetailAbout}Read more`,
    })
    handlePress?.()
  }

  const handleReserveChope = () => {
    trackAction(AdobeTagName.CAppDineDetailDineInformationReserve, {
      [AdobeTagName.CAppDineDetailDineInformationReserve]: `${translate(
        "restaurantDetails.reserve",
      )}`,
    })
    navigation.navigate(NavigationConstants.playpassWebview, {
      uri: restaurantDetailsData?.chopeUrl,
      needBackButton: true,
      needCloseButton: true,
    })
  }

  const readMore = (handlePress) => {
    return (
      <View
        style={
          changiEatsData && blogsAndReviewsData?.length > 0
            ? styles.loadMoreShowLessView
            : styles.loadMoreShowLessPaddingView
        }
      >
        <Text
          preset="bodyTextBold"
          style={styles.loadMoreShowLessTextStyle}
          onPress={() => handleReadMorePress(handlePress)}
        >
          Read more
        </Text>
        <TouchableOpacity
          onPress={() => handleReadMorePress(handlePress)}
          testID={`${SCREEN_NAME}__TouchableReadMore`}
          accessibilityLabel={`${SCREEN_NAME}__TouchableReadMore`}
        >
          <DownArrow style={styles.loadMoreShowLessIconStyle} />
        </TouchableOpacity>
      </View>
    )
  }

  const showLess = (handlePress) => {
    return (
      <View
        style={
          changiEatsData && blogsAndReviewsData?.length > 0
            ? styles.loadMoreShowLessView
            : styles.loadMoreShowLessPaddingView
        }
      >
        <Text preset="bodyTextBold" style={styles.loadMoreShowLessTextStyle} onPress={handlePress}>
          Show less
        </Text>
        <TouchableOpacity
          onPress={handlePress}
          testID={`${SCREEN_NAME}__TouchableShowLess`}
          accessibilityLabel={`${SCREEN_NAME}__TouchableShowLess`}
        >
          <TopArrow style={styles.loadMoreShowLessIconStyle} />
        </TouchableOpacity>
      </View>
    )
  }

  const renderTagLocation = () => {
    if (nameTagLocationPayload?.type === loadingType) {
      return <RestaurantLoadingDetailsScreen />
    }
    if (nameTagLocationPayload?.hasError) {
      return (
        <ErrorComponent
          style={styles.topErrorComponentStyle}
          type={ErrorComponentType.standard}
          onPressed={() => {
            dispatch(DineCreators.restaurantDetailsRequest(tenantId.current))
          }}
          testID={`${SCREEN_NAME}__ErrorComponentRestaurantDetailsRequest`}
          accessibilityLabel={`${SCREEN_NAME}__ErrorComponentRestaurantDetailsRequest`}
        />
      )
    }
    if (restaurantDetailsData) {
      return (
        <>
          <Hero
            heroImagesUrl={restaurantDetailsData.heroCarouselImage}
            logoImageUrl={restaurantDetailsData.heroCarouselLogo}
            type={nameTagLocationPayload.type}
            testID={`${SCREEN_NAME}__HeroCarouselImage`}
            accessibilityLabel={`${SCREEN_NAME}__HeroCarouselImage`}
            showWave={false}
            onSwiperChange={() => {
              trackAction(AdobeTagName.CAppDineDetail, {
                [AdobeTagName.CAppDineDetail]: "Carousel",
              })
            }}
          />
          <View>
            <Text preset="h1" style={styles.titleText}>
              {restaurantDetailsData.title}
            </Text>
            {!isEmpty(availableDiscounts) && (
              <AvailableStaffDiscounts
                availableDiscounts={availableDiscounts}
                navigation={navigation}
                storeName={restaurantDetailsData.title}
              />
            )}
            {!isEmpty(availableDealsPromos) && (
              <AvailableDealsPromos
                dealsPromos={availableDealsPromos}
                navigation={navigation}
                storeName={restaurantDetailsData.title}
              />
            )}
            <ScrollView
              testID={`${SCREEN_NAME}__ScrollViewListDataSource`}
              accessibilityLabel={`${SCREEN_NAME}__ScrollViewListDataSource`}
            >
              {listDataSource?.map((item, key) => (
                <ExpandableComponent
                  onClickFunction={() => {
                    updateLayout(key)
                  }}
                  item={item}
                  key={key}
                  index={key}
                  tenantName={name}
                  dialCall={dialCall}
                  dotUnicode={dotUnicode}
                  _setLoading={setLoading}
                  setConnection={setConnection}
                  refRetryAction={refRetryAction}
                />
              ))}
            </ScrollView>
          </View>
        </>
      )
    }

    return null
  }

  const renderInfoBanner = () => {
    if (changiRewardsPayload?.hasError) {
      return (
        <ErrorComponent
          type={ErrorComponentType.standard}
          onPressed={() => {
            dispatch(DineCreators.restaurantRewardInfoRequest(tenantId.current))
          }}
        />
      )
    }

    if (rewardInfoData) {
      return (
        <View style={styles.infoBannerView}>
          <InfoBanner
            title={rewardInfoData.rewardTitle}
            state={rewardInfoData.rewardState}
            type={rewardInfoData.type}
            text={rewardInfoData.rewardText}
            linkText={rewardInfoData.rewardLinkText}
            link={rewardInfoData.rewardLink}
            onPressed={() => {
              trackAction(AdobeTagName.CAppDineDetailRewardsTab, {
                [AdobeTagName.CAppDineDetailRewardsTab]: rewardInfoData?.rewardTitle,
              })
              navigation.navigate(NavigationConstants.webview, {
                uri: rewardInfoData.rewardLink,
              })
            }}
          />
        </View>
      )
    }

    return null
  }

  const renderAboutRestaurant = () => {
    if (restaurantDetailsData?.description) {
      return (
        <View style={styles.aboutViewStyle}>
          <Text preset="h4" style={styles.aboutViewHeaderText}>
            {translate("restaurantDetails.about")}
          </Text>
          <ReadMore
            numberOfLines={5}
            renderTruncatedFooter={readMore}
            renderRevealedFooter={showLess}
          >
            <Text
              preset="bodyTextRegular"
              style={styles.aboutViewText}
              text={restaurantDetailsData.description}
            />
          </ReadMore>
        </View>
      )
    }

    return null
  }

  const renderChope = () => (
    <>
      <View style={wrapChope}>
        <TouchableOpacity style={touchableReserve} onPress={handleReserveChope}>
          <CheckDate />
          <Text tx="restaurantDetails.reserve" style={touchableTextChope} preset={"caption1Bold"} />
        </TouchableOpacity>
      </View>
    </>
  )
  const renderContent = () => {
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        testID={`${SCREEN_NAME}__ScrollViewTagLocation`}
        accessibilityLabel={`${SCREEN_NAME}__ScrollViewTagLocation`}
        contentContainerStyle={paddingContentScrollView}
      >
        {renderTagLocation()}
        {!isEmpty(restaurantDetailsData?.chopeUrl) && renderChope()}
        {renderInfoBanner()}
        {renderIscCtaButton()}
        {renderAboutRestaurant()}
        <CuisineInDetail data={restaurantDetailsData?.exploreCategories} screen="DINE" />
      </ScrollView>
    )
  }

  const hasPageError =
    nameTagLocationPayload?.hasError &&
    changiRewardsPayload?.hasError &&
    changiEatsPayload?.hasError &&
    blogsAndReviewsPayload?.hasError

  const onRetryError = () => {
    if (refRetryAction.current) {
      refRetryAction.current?.()
      return
    }
    getRestaurantDetails()
  }

  const handleNavigateCSMIShopchangi = async(url: string) => {
    const ecid = await getExperienceCloudId()
    const target = getISCLinkRedirectTarget(url)

    const payload = {
      stateCode: StateCode.ISHOPCHANGI,
      input: {
        ...target,
        ecid,
      },
    }
    try {
      const response = await getDeepLinkV2(payload, true)
      if (response?.redirectUri) {
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        throw response
      }
    } catch (error) {
      openModal()
    }
  }

  const renderIscCtaButton = () => {
    if(isEmpty(nameTagLocationPayload?.data?.aemTenantDetails?.iscurl)){
      return null
    }
    return(
      <TouchableOpacity style={styles.iscCTAButton} onPress={() => handleNavigateCSMIShopchangi(nameTagLocationPayload?.data?.aemTenantDetails?.iscurl)}>
        <ShopBagIcon />
        <Text style={styles.iscCTAText} tx="shopDetailScreen.shopOnIshopChangi"/>
      </TouchableOpacity>
    )
  }

  return (
    <View style={styles.container} testID="ShopDetailsScreen">
      <>
        <SafeAreaView style={styles.headerViewStyle}>
          <TouchableOpacity
            onPress={onPressBackButton}
            style={safeViewHeaderForAndroid(inset)}
            testID={`${SCREEN_NAME}__TouchableBackButton`}
            accessibilityLabel={`${SCREEN_NAME}__TouchableBackButton`}
          >
            <BackButton />
          </TouchableOpacity>
        </SafeAreaView>
        {hasPageError ? (
          <ErrorScreen
            onReload={() => getRestaurantDetails()}
            testID={`${SCREEN_NAME}__ErrorScreenGetRestaurantDetails`}
            accessibilityLabel={`${SCREEN_NAME}__ErrorScreenGetRestaurantDetails`}
          />
        ) : (
          renderContent()
        )}
      </>
      <LoadingOverlay visible={loading} />
      <ErrorOverlayNoConnection
        reload={true}
        header
        hideScreenHeader={false}
        headerBackgroundColor="transparent"
        visible={!isConnection}
        onBack={() => {
          navigation.goBack()
        }}
        onReload={onRetryError}
      />
      <BottomSheetError
        openPendingModal
        icon={<InfoRed />}
        visible={isModalVisible}
        shouldFitContentHeight
        animationInTiming={500}
        animationOutTiming={500}
        title={translate("popupError.somethingWrongOneline")}
        colorMsg={color.palette.almostBlackGrey}
        errorMessage={translate("popupError.networkErrorMessage")}
        onClose={closeModal}
        buttonText={translate("common.close")}
        testID={`${SCREEN_NAME}__BottomSheetErrorISC`}
        accessibilityLabel={`${SCREEN_NAME}__BottomSheetErrorISC`}
      />
    </View>
  )
}
