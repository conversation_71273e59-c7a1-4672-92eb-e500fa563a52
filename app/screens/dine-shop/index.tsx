import { DineShopScreen } from "../dine-shop/dine-shop"
import { DineScreenV2 } from "../dine-screen-v2"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"

const DineShopScreenWrapper = (props) => {
  const isShopDineV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)
  if (isShopDineV2)
    return (
      <DineScreenV2 {...props} />
    )

  return <DineShopScreen {...props} />
}

export { DineShopScreenWrapper }
