import { presets } from "app/elements/text"
import { color } from "app/theme"
import { StyleSheet } from "react-native"

export const styles = StyleSheet.create({
  contentItemStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingBottom: 16,
    paddingTop: 12,
  },
  leftContentItemStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 8,
  },
  midContentItemStyle: {
    flex: 1,
  },
  rightContentItemStyle: {
    marginLeft: 16,
    marginRight: 12,
    marginTop: 16,
  },
  separatorItemStyle: {
    height: 1,
    backgroundColor: color.palette.lighterGrey
  },
  container: {
    borderColor: color.palette.lighterGrey,
    borderRadius: 12,
    borderWidth: 1,
    marginHorizontal: 24,
    marginTop: 16,
  },
  dateStyle: {
    ...presets.caption2Regular,
    color: color.palette.almostBlackGrey,
  },
  titleStyle: {
    ...presets.caption1Bold,
    color: color.palette.almostBlackGrey,
    marginBottom: 4,
    textAlign: "left",
  },
  wrapLabel: {
    marginLeft: -1,
    marginTop: -1,
  },
})


