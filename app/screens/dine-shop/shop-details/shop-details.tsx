import React, { useEffect, useRef, useState } from "react"
import {
  ScrollView,
  TouchableOpacity,
  View,
  SafeAreaView,
  LayoutAnimation,
  Linking,
  Platform,
  UIManager,
  ViewStyle,
} from "react-native"
import { ShopDetailsLoadingScreen } from "./shop-details-loading-view"
import { <PERSON> } from "app/components/hero/hero"
import {
  BackButton,
  DownArrow,
  TopArrow,
  DirectionsOutline,
  ClockOutline,
  PhoneOutline,
  ShopBagIcon,
  InfoRed,
} from "ichangi-fe/assets/icons"
import * as styles from "./shop-details-styles"
import { Text } from "app/elements/text/text"
import { getDotUnicode, NavigationConstants, StateCode } from "app/utils/constants"
import { useDispatch, useSelector } from "react-redux"
import NetInfo from "@react-native-community/netinfo"
import { isEmpty } from "lodash"
import { RootState } from "app/redux/store"
import { AdobeTagName, commonTrackingScreen, getExperienceCloudId, trackAction } from "app/services/adobe"
import ShopCreators, { Shop, ShopSelectors } from "../../../redux/shopRedux"
import ReadMore from "app/components/read-more/read-more"
import { translate } from "../../../i18n"
import { ErrorScreen } from "../dine-shop-offer-details/error"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { InfoBanner } from "app/components/info-banner/info-banner"
import { handleCondition } from "app/utils"
import { BottomSheetUnableLoadLocation } from "app/components/bottom-sheet-unable-load-location/bottom-sheet-unable-load-location"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { CuisineInDetail } from "app/sections/cusine-in-detail/cusine-in-detail"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import AvailableStaffDiscounts from "../available-staff-discounts"
import AvailableDealsPromos from "../available-deals-promos"
import { isFlagON, REMOTE_CONFIG_FLAGS, getFeatureFlagInit } from "app/services/firebase/remote-config"
import { getStaffPerkAvailableDiscount, getDealsPromosAvailableDiscount } from "app/sagas/staffPerkSaga"
import { BottomSheetMapUnavailable } from "app/components/bottom-sheet-map-unavailable/bottom-sheet-map-unavailable"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { getISCLinkRedirectTarget } from "app/helpers/deeplink/deeplink-parameter"
import { WebViewHeaderTypes } from "app/models/enum"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import { useModal } from "app/hooks/useModal"
import { color } from "app/theme"

const SCREEN_NAME = "ShopDetailsScreen"

const getDay = (day) => {
  return day || translate("shopDetailScreen.daily")
}

const getMapName = (item, dotUnicode) => {
  if (item?.mapName && item.openCloseStatus?.status && !item?.hourComment) {
    return item.mapName + `${"  "}` + dotUnicode + `${"  "}`
  }

  return item?.mapName
}

const handleSpace = (indx, size) => {
  if (indx < size) return { marginBottom: 8 }
  return {}
}

const renderCloseStatus = (item) => {
  if (item.openCloseStatus) {
    return (
      <Text
        preset="bodyTextBold"
        style={[{ color: item.openCloseStatus.colorCode }, styles.statusTextStyle]}
      >
        {item.openCloseStatus.status}
      </Text>
    )
  }

  return null
}

const getTextNumber = (item, number) => {
  return (item ? ", " : "") + number
}

const ExpandableComponent = ({
  item,
  onClickFunction,
  key,
  dotUnicode,
  dialCall,
  refRetryAction,
  setConnection,
  index,
}) => {
  const [layoutHeight, setLayoutHeight] = useState(0)
  const navigation = useNavigation()
  const unableToLoadLocationRef = useRef(null)
  const mapUnavailable = useRef(null)
  const [mapRMFlag, setMapRMFlag] = useState(false)
  const hourComment = item?.hourComment

  useEffect(() => {
    if (item.isExpanded) {
      setLayoutHeight(null)
      return
    }
    setLayoutHeight(0)
  }, [item.isExpanded])

  const phoneNumberStyle =
    Platform.OS === "ios" ? styles.phoneNumberTextStyle : styles.phoneNumberTextStyleAndroid

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const handleFindDirection = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      refRetryAction.current = null
      setConnection(true)
      if (!mapRMFlag) {
        return mapUnavailable?.current.show()
      }
      trackAction(AdobeTagName.CAppATOMSEntryClick, {
        [AdobeTagName.CAppATOMSEntryClick]: `Shop Details|Get Directions|${item?.locationDetails?.[index].localRef}`,
      })
      //@ts-ignore
      navigation.push(NavigationConstants.changiMap, {
        localRef: item.locationDetails?.[index].localRef,
      })
      return
    }
    refRetryAction.current = handleFindDirection
    setConnection(false)
  }
  return (
    <View style={styles.wrapInformationDetail}>
      <TouchableOpacity
        style={styles.header}
        onPress={onClickFunction}
        testID={`${SCREEN_NAME}__TouchableExpand__${key}`}
        accessibilityLabel={`${SCREEN_NAME}__TouchableExpand__${key}`}
      >
        <View style={styles.flexStyle}>
          <View style={styles.headerStyle}>
            <Text preset="h4" style={styles.txtColor} text={getMapName(item, dotUnicode)} />
            {!hourComment && renderCloseStatus(item)}
          </View>
          <View style={styles.arrowIconStyle}>
            {handleCondition(item.isExpanded, <TopArrow />, <DownArrow />)}
          </View>
        </View>
      </TouchableOpacity>
      <View style={[{ height: layoutHeight }, styles.visibilityStyle]}>
        <View key={"content"} style={styles.content}>
          {hourComment && (
            <Text numberOfLines={3} style={styles.temporaryClosedStyle}>
              {hourComment}
            </Text>
          )}
          <View style={styles.flexStyle}>
            <DirectionsOutline style={styles.directionIconStyle} />
            <View style={styles.areaInfoViewStyle}>
              <Text numberOfLines={2} preset="bodyTextRegular" style={styles.text}>
                {handleCondition(item.terminal, item.terminal + " ", "")}
                {handleCondition(item.description, item.description + ", ", "")}
                {handleCondition(item.unitNo, "#" + item.unitNo + ", ", "")}
                {handleCondition(item.area, item.area + translate("shopDetailScreen.area"), "")}
              </Text>
              <TouchableOpacity
                onPress={handleFindDirection}
                disabled={isEmpty(item?.locationDetails?.[index]?.localRef)}
              >
                <Text
                  preset="bodyTextBold"
                  style={
                    isEmpty(item?.locationDetails?.[index]?.localRef)
                      ? styles.getDirectionStyleDisabled
                      : styles.getDirectionStyle
                  }
                >
                  Get directions
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.directionContainerView}>
            <TouchableOpacity
              style={styles.directionTouchableOpacityStyle}
              testID={`${SCREEN_NAME}__TouchableGetDirections__${key}`}
              accessibilityLabel={`${SCREEN_NAME}__TouchableGetDirections__${key}`}
              onPress={handleFindDirection}
              disabled={!item.localRef}
            >
              <Text
                preset={"textLink"}
                text={translate("shopDetailScreen.getDirections")}
                style={
                  item.localRef
                    ? handleCondition(
                      Platform.OS === "android",
                      styles.directionTextStyleAndroid,
                      null,
                    )
                    : handleCondition(
                      Platform.OS === "android",
                      styles.directionTextStyleAndroidDisabled,
                      null,
                    )
                }
              />
            </TouchableOpacity>
          </View>
          {handleCondition(
            item.timingsInfo?.length > 0,
            <View style={styles.flexStyle}>
              <View style={styles.timingContainerStyle}>
                <ClockOutline style={styles.clockIconStyle} />
                <View style={styles.timingsInfoViewStyle}>
                  {item.timingsInfo?.map((res, keyVal) => {
                    return (
                      <View
                        key={keyVal}
                        style={[styles.timingStyle, handleSpace(keyVal, item?.timingsInfo?.length)]}
                      >
                        <Text preset="bodyTextRegular" style={styles.timingsInfoDayText}>
                          {getDay(res.day)}
                        </Text>
                        <Text preset="bodyTextRegular" style={styles.timingsText}>
                          {res.timings}
                        </Text>
                      </View>
                    )
                  })}
                </View>
              </View>
            </View>,
            null,
          )}
          {handleCondition(
            item.phoneDetails.length > 0,
            <View style={styles.flexStyle}>
              <View style={styles.phoneContainerStyle}>
                <PhoneOutline style={styles.phoneIconStyle} />
                {item.phoneDetails.map((number, i) => {
                  return (
                    <TouchableOpacity
                      key={i}
                      onPress={() => dialCall(number)}
                      testID={`${SCREEN_NAME}__TouchableDialCall__${key}`}
                      accessibilityLabel={`${SCREEN_NAME}__TouchableDialCall__${key}`}
                    >
                      <Text preset={"textLink"} numberOfLines={2} style={phoneNumberStyle}>
                        {getTextNumber(i, number)}
                      </Text>
                    </TouchableOpacity>
                  )
                })}
              </View>
            </View>,
            null,
          )}
        </View>
      </View>
      <View
        style={handleCondition(
          item.isExpanded,
          styles.separatorExpanded,
          styles.separatorNotExpanded,
        )}
      />
      <BottomSheetUnableLoadLocation ref={unableToLoadLocationRef} />
      <BottomSheetMapUnavailable ref={mapUnavailable} />
    </View>
  )
}

const safeViewHeaderForAndroid = (inset) => {
  const _safeViewHeaderForAndroid: ViewStyle = {
    marginTop: Platform.OS === "android" && inset?.top ? inset?.top : 0,
    marginStart: 14,
  }
  return _safeViewHeaderForAndroid
}
const ShopDetailsScreen = (props: any) => {
  const loading = "loading"
  const navigation = useNavigation<any>()
  const {isModalVisible, openModal, closeModal} = useModal("shopDetailError")

  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const tenantId = props.route.params?.tenantId
  const name = props.route.params?.name || ""

  const dotUnicode = getDotUnicode()
  const dispatch = useDispatch()

  const { shopDetailScreenPayload, shopRewardInfoPayloadData }: any =
    useSelector<RootState, Shop>((data) => ShopSelectors.shopDetailScreenPayloadData(data)) || {}
  const shopDetails = shopDetailScreenPayload?.data
  const rewardInfoData = shopRewardInfoPayloadData?.data
  const locationDetails = shopDetails?.locationDetails
  const [listDataSource, setListDataSource] = useState([])
  const inset = useSafeAreaInsets()

  const profile = useSelector(ProfileSelectors.profilePayload)
  const isStaff = profile?.airportStaff
  const [availableDiscounts, setAvailableDiscounts] = useState([])
  const [availableDealsPromos, setAvailableDealsPromos] = useState([])
  const [isConnection, setConnection] = useState(true)
  const refRetryAction = useRef(null)

  const getStaffAvailableDiscount = async () => {
    const featureFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.TENANTDETAILS_STAFFPERKS)
    if (!featureFlagEnable || !isLoggedIn || !isStaff) {
      return
    }
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      const staffDiscount = await getStaffPerkAvailableDiscount({ tenantId })
      if (!isEmpty(staffDiscount)) {
        setAvailableDiscounts(staffDiscount)
      }
    }
  }
  
  useCurrentScreenActiveAndPreviousScreenHook(`Shop_Detail_${name}`)
  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      commonTrackingScreen(
        `Shop_Detail_${name}`,
        getPreviousScreen(),
        isLoggedIn,
      )
    })
    // getShopNameTagLocation()
    return unsubscribeFocus
  }, [navigation])

  useFocusEffect(
    React.useCallback(() => {
      getShopNameTagLocation()
    }, []),
  )

  const getShopNameTagLocation = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setConnection(true)
      dispatch(ShopCreators.shopDetailScreenRequest(tenantId))
      dispatch(ShopCreators.shopRewardInfoRequest(tenantId))
      if (isStaff && !isEmpty(tenantId)) {
        getStaffAvailableDiscount()
      }
      const isShopDineV2Enabled = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)
      if (isShopDineV2Enabled) {
        const promos = await getDealsPromosAvailableDiscount({ tenantId })
        if (!isEmpty(promos)) {
          setAvailableDealsPromos(promos)
        }
      }
      return
    }
    setConnection(false)
  }

  React.useEffect(() => {
    setListDataSource(locationDetails)
  })

  if (Platform.OS === "android") {
    UIManager.setLayoutAnimationEnabledExperimental(true)
  }

  const updateLayout = (index) => {
    LayoutAnimation.configureNext({
      ...LayoutAnimation.Presets.easeInEaseOut,
      duration: 100,
    })
    const array = [...listDataSource]
    array.forEach((_value, placeindex) => {
      if (placeindex === index) {
        array[placeindex].isExpanded = !array[placeindex].isExpanded
      } else {
        array[placeindex].isExpanded = false
      }
    })
    setListDataSource(array)
  }

  const transformPhoneNumber = (phone) => {
    if (Platform.OS === "android") {
      return `tel:${phone}`
    }
    return `telprompt:${phone}`
  }

  const dialCall = (num: string) => {
    const phoneNumber = transformPhoneNumber(num)

    trackAction(AdobeTagName.CAppShopDetailShopInformationPhoneNumber, {
      [AdobeTagName.CAppShopDetailShopInformationPhoneNumber]: num,
    })

    Linking.openURL(phoneNumber)
  }

  const handleReadMorePress = (handlePress) => {
    trackAction(AdobeTagName.CAppShopDetailAbout, {
      [AdobeTagName.CAppShopDetailAbout]: "1",
    })
    handlePress?.()
  }
  const readMore = (handlePress) => {
    return (
      <View style={styles.loadMoreShowLessView}>
        <TouchableOpacity onPress={() => handleReadMorePress(handlePress)}>
          <Text preset="bodyTextBold" style={styles.loadMoreShowLessTextStyle}>
            {translate("shopDetailScreen.readMore")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleReadMorePress(handlePress)}
          testID={`${SCREEN_NAME}__TouchableReadMore`}
          accessibilityLabel={`${SCREEN_NAME}__TouchableReadMore`}
        >
          <DownArrow style={styles.loadMoreShowLessIconStyle} />
        </TouchableOpacity>
      </View>
    )
  }

  const showLess = (handlePress) => {
    return (
      <View style={styles.loadMoreShowLessView}>
        <Text preset="bodyTextBold" style={styles.loadMoreShowLessTextStyle} onPress={handlePress}>
          {translate("shopDetailScreen.showLess")}
        </Text>
        <TouchableOpacity
          onPress={handlePress}
          testID={`${SCREEN_NAME}__TouchableShowLess`}
          accessibilityLabel={`${SCREEN_NAME}__TouchableShowLess`}
        >
          <TopArrow style={styles.loadMoreShowLessIconStyle} />
        </TouchableOpacity>
      </View>
    )
  }

  const onRetryError = () => {
    if (refRetryAction.current) {
      refRetryAction.current?.()
      return
    }
    getShopNameTagLocation()
  }

  const handleNavigateCSMIShopchangi = async(url: string) => {
    const ecid = await getExperienceCloudId()
    const target = getISCLinkRedirectTarget(url)

    const payload = {
      stateCode: StateCode.ISHOPCHANGI,
      input: {
        ...target,
        ecid,
      },
    }
    try {
      const response = await getDeepLinkV2(payload, true)
      if (response?.redirectUri) {
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        throw response
      }
    } catch (error) {
      openModal()
    }
  }

  const renderIscCtaButton = () => {
    if(isEmpty(shopDetails?.aemTenantDetails?.iscurl)){
      return null
    }
    return(
      <TouchableOpacity style={styles.iscCTAButton} onPress={() => handleNavigateCSMIShopchangi(shopDetails?.aemTenantDetails?.iscurl)}>
        <ShopBagIcon />
        <Text style={styles.iscCTAText} tx="shopDetailScreen.shopOnIshopChangi"/>
      </TouchableOpacity>
    )
  }
  

  const customStaffAvailableDiscountStyle: ViewStyle = { marginTop: 24 }
  return (
    <View style={styles.container} testID="ShopDetailsScreen">
      {handleCondition(
        shopDetailScreenPayload?.type === loading,
        <ShopDetailsLoadingScreen />,
        <>
          <SafeAreaView style={styles.headerViewStyle}>
            <TouchableOpacity
              onPress={() => {
                props.navigation.goBack()
              }}
              style={safeViewHeaderForAndroid(inset)}
              testID={`${SCREEN_NAME}__TouchableBackButton`}
              accessibilityLabel={`${SCREEN_NAME}__TouchableBackButton`}
            >
              <BackButton />
            </TouchableOpacity>
          </SafeAreaView>
          <ScrollView
            showsVerticalScrollIndicator={false}
            testID={`${SCREEN_NAME}__ScrollView`}
            accessibilityLabel={`${SCREEN_NAME}__ScrollView`}
          >
            {handleCondition(
              shopDetails,
              <>
                <Hero
                  heroImagesUrl={shopDetails?.heroCarouselImage}
                  logoImageUrl={shopDetails?.heroCarouselLogo}
                  type={shopDetailScreenPayload?.type}
                  testID={`${SCREEN_NAME}__Hero`}
                  accessibilityLabel={`${SCREEN_NAME}__Hero`}
                  showWave={false}
                  onSwiperChange={() => {
                    trackAction(AdobeTagName.CAppShopDetailCarouselImage, {
                      [AdobeTagName.CAppShopDetailCarouselImage]: "1",
                    })
                  }}
                />

                <>
                  <View>
                    <Text preset="h1" style={styles.titleText}>
                      {shopDetails?.title}
                    </Text>
                  </View>
                  {!isEmpty(availableDiscounts) && (
                    <AvailableStaffDiscounts
                      availableDiscounts={availableDiscounts}
                      navigation={navigation}
                      customContainerStyle={customStaffAvailableDiscountStyle}
                      storeName={shopDetails?.title}
                    />
                  )}
                  {!isEmpty(availableDealsPromos) && (
                    <AvailableDealsPromos
                      dealsPromos={availableDealsPromos}
                      navigation={navigation}
                      storeName={shopDetails?.title}
                    />
                  )}
                  {listDataSource?.map((item, key) => (
                    <ExpandableComponent
                      onClickFunction={() => {
                        updateLayout(key)
                      }}
                      item={item}
                      key={key}
                      index={key}
                      dialCall={dialCall}
                      dotUnicode={dotUnicode}
                      setConnection={setConnection}
                      refRetryAction={refRetryAction}
                    />
                  ))}
                  {renderIscCtaButton()}
                  {handleCondition(
                    shopRewardInfoPayloadData?.hasError,
                    <ErrorComponent
                      type={ErrorComponentType.standard}
                      onPressed={() => {
                        dispatch(ShopCreators.shopRewardInfoRequest(tenantId))
                      }}
                    />,
                    handleCondition(
                      rewardInfoData,
                      <View style={styles.infoBannerView}>
                        <InfoBanner
                          title={rewardInfoData?.rewardTitle}
                          state={rewardInfoData?.rewardState}
                          type={rewardInfoData?.type}
                          text={rewardInfoData?.rewardText}
                          linkText={rewardInfoData?.rewardLinkText}
                          link={rewardInfoData?.rewardLink}
                          onPressed={() =>
                            //@ts-ignore
                            navigation.navigate(NavigationConstants.webview, {
                              uri: rewardInfoData?.rewardLink,
                            })
                          }
                        />
                      </View>,
                      null,
                    ),
                  )}
                  <View style={styles.aboutViewStyle}>
                    {handleCondition(
                      shopDetails?.description,
                      <>
                        <Text preset="h4" style={styles.aboutViewHeaderText}>
                          {translate("shopDetailScreen.about")}
                        </Text>
                        <ReadMore
                          numberOfLines={5}
                          renderTruncatedFooter={readMore}
                          renderRevealedFooter={showLess}
                        >
                          <Text
                            preset="bodyTextRegular"
                            style={handleCondition(
                              Platform.OS === "ios",
                              styles.aboutViewText,
                              styles.aboutViewTextAndroid,
                            )}
                          >
                            {shopDetails?.description}
                          </Text>
                        </ReadMore>
                      </>,
                      null,
                    )}
                  </View>
                </>
              </>,
              null,
            )}
            <CuisineInDetail data={shopDetails?.exploreCategories} screen="SHOP" />
          </ScrollView>
        </>,
      )}
      {handleCondition(
        shopDetailScreenPayload?.hasError && shopDetailScreenPayload?.type !== loading,
        <ErrorScreen
          onReload={() => getShopNameTagLocation()}
          testID={`${SCREEN_NAME}__ErrorScreenGetShopNameTagLocation`}
          accessibilityLabel={`${SCREEN_NAME}__ErrorScreenGetShopNameTagLocation`}
        />,
        null,
      )}
      <ErrorOverlayNoConnection
        reload={true}
        header
        hideScreenHeader={false}
        headerBackgroundColor="transparent"
        visible={!isConnection}
        onBack={() => {
          navigation.goBack()
        }}
        onReload={onRetryError}
      />
      <BottomSheetError
        openPendingModal
        icon={<InfoRed />}
        visible={isModalVisible}
        shouldFitContentHeight
        animationInTiming={500}
        animationOutTiming={500}
        colorMsg={color.palette.almostBlackGrey}
        title={translate("popupError.somethingWrongOneline")}
        errorMessage={translate("popupError.networkErrorMessage")}
        onClose={closeModal}
        buttonText={translate("common.close")}
        testID={`${SCREEN_NAME}__BottomSheetErrorISC`}
        accessibilityLabel={`${SCREEN_NAME}__BottomSheetErrorISC`}
      />
    </View>
  )
}

export { ShopDetailsScreen }
