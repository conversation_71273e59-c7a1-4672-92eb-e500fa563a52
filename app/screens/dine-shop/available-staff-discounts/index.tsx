import React from "react"
import { FlatList, TouchableOpacity, View } from "react-native"
import StaffPerkLabel from "./staff-perk-label"
import { Text } from "app/elements/text"
import { CaretRight, StaffDiscountTag } from "ichangi-fe/assets/icons"
import { styles } from "./styles"
import StaffPerkPromotionDetailController from "app/components/staff-perk-promotion-detail/staff-perk-promotion-detail-controller"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { formatCampaignDateRange } from "app/utils/date-time/date-time"

const AvailableStaffDiscounts = ({ availableDiscounts, navigation, customContainerStyle, storeName = '' }) => {
  const separatorItem = () => <View style={styles.separatorItemStyle} />
  return (
    <View style={{ ...styles.staffPerkAvailableDiscounts, ...customContainerStyle }}>
      <View style={styles.wrapPerkLabel}>
        <StaffPerkLabel />
      </View>
      <FlatList
        data={availableDiscounts}
        renderItem={({ item }) => <AvailableDiscountItem item={item} navigation={navigation} storeName={storeName}/>}
        keyExtractor={(_, index) => index.toString()}
        ItemSeparatorComponent={separatorItem}
      />
    </View>
  )
}

const AvailableDiscountItem = ({ item, navigation, storeName }) => {
  const { title, campaignStartDate, campaignEndDate } = item || ""
  const handlePress = () => {
    trackAction(AdobeTagName.CAppShopDetailStaffPerks, {
      [AdobeTagName.CAppShopDetailStaffPerks]: `${storeName} | ${title}`,
    })
    StaffPerkPromotionDetailController.showModal(navigation, { item }, true)
  }
  return (
    <TouchableOpacity activeOpacity={0.5} onPress={handlePress}>
      <View style={styles.contentAvailableDiscountItemStyle}>
        <View style={styles.leftContentAvailableDiscountItemStyle}>
          <StaffDiscountTag />
        </View>
        <View style={styles.midContentAvailableDiscountItemStyle}>
          <Text text={title} numberOfLines={2} style={styles.titleDiscountStyle} />
          <Text
            text={formatCampaignDateRange(campaignStartDate, campaignEndDate)}
            style={styles.textDateStyle}
          />
        </View>
        <View style={styles.rightContentAvailableDiscountItemStyle}>
          <CaretRight />
        </View>
      </View>
    </TouchableOpacity>
  )
}

export default AvailableStaffDiscounts
