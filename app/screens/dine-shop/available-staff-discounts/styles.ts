import { presets } from "app/elements/text"
import { color } from "app/theme"
import { StyleSheet } from "react-native"

export const styles = StyleSheet.create({
  contentAvailableDiscountItemStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingBottom: 16,
    paddingTop: 12,
  },
  leftContentAvailableDiscountItemStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 8,
  },
  midContentAvailableDiscountItemStyle: {
    flex: 1,
  },
  rightContentAvailableDiscountItemStyle: {
    marginLeft: 16,
    marginRight: 12,
    marginTop: 16
  },
  separatorItemStyle: {
    height: 1,
    backgroundColor: color.palette.lighterGrey
  },
  staffPerkAvailableDiscounts: {
    borderColor: color.palette.lighterGrey,
    borderRadius: 12,
    borderWidth: 1,
    marginHorizontal: 24,
    marginTop: 16,
  },
  textDateStyle: {
    ...presets.caption2Regular,
    color: color.palette.almostBlack<PERSON>rey,
  },
  titleDiscountStyle: {
    ...presets.caption1Bold,
    color: color.palette.almostBlackGrey,
    marginBottom: 4,
    textAlign: "left"
  },
  wrapItem: {
    alignItems: "center",
    flexDirection: "row"
  },
  wrapPerkLabel: {
    marginLeft: -1,
    marginTop: -1
  }

})
