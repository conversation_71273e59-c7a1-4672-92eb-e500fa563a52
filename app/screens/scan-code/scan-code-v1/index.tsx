import React, { useState, useEffect, useRef } from "react"
import {
  View,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  ViewStyle,
  Platform,
} from "react-native"
import { presets, Text } from "app/elements/text"
import { color } from "app/theme"
import { useIsFocused, useNavigation, useRoute } from "@react-navigation/native"
import moment from "moment"
import { DateFormats } from "app/utils/date-time/date-time"
import { translate } from "app/i18n"
import { useDispatch, useSelector } from "react-redux"
import { store } from "app/redux/store"
import { LoadingOverlay } from "app/components/loading-modal"
import { AemSelectors } from "app/redux/aemRedux"
import { AdobeTagName, AdobeValueByTagName, commonTrackingScreen, trackAction } from "app/services/adobe"
import ProfileActions from "app/redux/profileRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import LinearGradient from "react-native-linear-gradient"
import { choosePictureFromGallery } from "app/utils/media-helper"
import { get, isEmpty } from "lodash"
import RNQRGenerator from "rn-qr-generator"
import BackButtonSvg from "../backbutton"
import { RNHoleView } from "react-native-hole-view"
import { FlashModeOff, FlashModeOn } from "ichangi-fe/assets/icons"
import { checkFlightAvailable } from "app/sagas/flySaga"
import { handleCondition } from "app/utils"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import MyCamera, { BOARDING_PASS_ACCEPTED_SCAN_TYPE, MyCameraRef } from "app/components/camera"

const TIMEOUT_OFFSET = 30000
const { width, height } = Dimensions.get("window")

const MASK_WIDTH = 327
const MASK_HEIGHT = height >= 850 ? 450 : height - 400
const MASK_POSITION_Y = 150
const HOLE_VIEW_BACKGROUND = "rgba(0, 0, 0, 0.4)"
const MASK_PADDING_LEFT = width / 2 - (MASK_WIDTH / 2)
const DURATION = 500
let timer: any

const ScanCodeScreenV1 = () => {
  const navigation = useNavigation()
  const dispatch = useDispatch()
  const cameraRef = useRef<MyCameraRef>(null)
  const [needScan, setNeedScan] = useState(true)
  const readyScan = !!cameraRef?.current?.isCameraReady
  const [onFlash, setOnFlash] = useState(false)
  const [time, setTime] = useState(0)
  const [loadingDectectBoardingPass, setLoadingDectectBoardingPass] = useState(false)
  const isFocused = useIsFocused()
  const route: any = useRoute()
  const flightNumberRef = useRef("")
  const shouldTrackDetectedFlightNumber = route?.params?.shouldTrackDetectedFlightNumber

  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const [isCheckingFlightAvailable, setIsCheckingFlightAvailable] = useState(false)

  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const msg52 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG52")
  const msg53 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG53")
  const msg54 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG54")

  const positionAnimatedLine = useRef(new Animated.Value(0)).current
  const [cameraStarted, setCameraStarted] = useState(false)

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(positionAnimatedLine, {
          toValue: MASK_HEIGHT - 60,
          duration: DURATION,
          useNativeDriver: true,
        }),
        Animated.timing(positionAnimatedLine, {
          toValue: 0,
          duration: DURATION,
          useNativeDriver: true,
        }),
      ]),
    ).start()
  }, [])

  const animatedLineStyle: ViewStyle = {
    transform: [{ translateY: positionAnimatedLine }],
  }
  const resetState = () => {
    setNeedScan(true)
    setOnFlash(false)
    setTime(0)
  }

  useCurrentScreenActiveAndPreviousScreenHook("Explore_ScanCode")
  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      commonTrackingScreen(
        "Explore_ScanCode",
        getPreviousScreen(),
        isLoggedIn,
      )
    })
    return unsubscribeFocus
  }, [navigation])

  useEffect(() => {
    if (isFocused) {
      timer = setInterval(() => {
        setTime((prevTime) => prevTime + 1000)
      }, 1000)
    }
    return () => {
      clearInterval(timer)
    }
  }, [isFocused])

  const alertCanNotDetect = () => {
    Alert.alert(
      handleCondition(
        msg54?.title,
        msg54?.title,
        translate("scanCode.noBoardingPassDetected.title"),
      ),
      handleCondition(
        msg54?.message,
        msg54?.message,
        translate("scanCode.noBoardingPassDetected.description"),
      ),
      [
        {
          text: handleCondition(
            msg54?.firstButton,
            msg54?.firstButton,
            translate("scanCode.noBoardingPassDetected.firstButton"),
          ),
          style: "cancel",
          onPress: () => {
            setNeedScan(true)
            handleGoBack()
          },
        },
        {
          text: handleCondition(
            msg54?.secondButton,
            msg54?.secondButton,
            translate("scanCode.noBoardingPassDetected.secondButton"),
          ),
          onPress: () => {
            setNeedScan(true)
          },
        },
      ],
    )
  }

  useEffect(() => {
    if (
      handleCondition(
        readyScan && needScan && time && time % TIMEOUT_OFFSET === 0 && isFocused,
        true,
        false,
      )
    ) {
      setNeedScan(false)
      alertCanNotDetect()
    }
  }, [time])

  useEffect(() => {
    if (!isFocused) {
      clearInterval(timer)
    }
  }, [isFocused])

  // Handle after get full permission and start scan
  const dayToDate = (day) => {
    const year = new Date().getFullYear()
    const date = new Date(year, 0, day)
    return moment(new Date(date)).format(DateFormats.YearMonthDay)
  }

  const mappingFlightData = (checkFlight) => {
    const newScheduledDate =
      checkFlight?.direction === "DEP" ? checkFlight?.scheduled_date : checkFlight?.origin_dep_date
    return {
      logo: checkFlight?.airline_details?.logo_url,
      flightNumber: checkFlight?.flight_number,
      departingCode: "SIN",
      destinationCode: "PER",
      flightDate: newScheduledDate,
      scheduledDate: newScheduledDate,
      state: "default",
      codeShare: checkFlight?.slave_flights,
      destinationPlace: "Perth",
      departingPlace: "Singapore",
      timeOfFlight: checkFlight?.scheduled_time,
      flightStatus: checkFlight?.flight_status,
      flightStatusMapping: checkFlight?.status_mapping?.listing_status_en,
      beltStatusMapping: null,
      statusColor: "Red",
      showGate: !!checkFlight?.status_mapping?.show_gate,
      isSaved: false,
      flightUniqueId: `${checkFlight?.flight_number}_${checkFlight?.newScheduledDate}`,
      estimatedTimestamp: null,
      actualTimestamp: null,
      direction: checkFlight?.direction,
      terminal: checkFlight?.terminal,
      checkInRow: checkFlight?.check_in_row,
      displayBelt: null,
      displayTimestamp: "2023-09-22 00:05",
      viaAirportDetails: null,
    }
  }

  const readDataFromBoardingPass = async (data) => {
    const dataSplit = data.replace(/\s+/g, " ").trim()?.split(" ")
    const indexOfFlightNumber = dataSplit.findIndex(
      (newData) => newData.length === 4 && !isNaN(newData),
    )
    let flyItem: any
    if (indexOfFlightNumber > 0) {
      const placeInformation = dataSplit[indexOfFlightNumber - 1]
      const timeInformation = dataSplit[indexOfFlightNumber + 1]
      if (placeInformation && placeInformation.length === 8) {
        const departingCode = placeInformation.substring(0, 3)
        const destinationCode = placeInformation.substring(3, 6)
        const arlineCode = placeInformation.substring(6, 8)
        const flightNumber = `${arlineCode}${Number(dataSplit[indexOfFlightNumber])}`
        flyItem = {
          departingCode,
          destinationCode,
          arlineCode,
          direction: handleCondition(departingCode === "SIN", "DEP", "ARR"),
          flightNumber: flightNumber,
        }

        if (shouldTrackDetectedFlightNumber) {
          flightNumberRef.current = flightNumber
          trackAction(AdobeTagName.CAppFlyFlightSearchScanBoardingPass, {
            [AdobeTagName.CAppFlyFlightSearchScanBoardingPass]: flightNumber,
          })
        }
      } else {
        flyItem = false
      }
      if (timeInformation && !isNaN(timeInformation?.substring(0, 3))) {
        const scheduleDateTime = dayToDate(timeInformation?.substring(0, 3))
        flyItem = {
          ...flyItem,
          flightDate: scheduleDateTime,
          flightUniqueId: `${flyItem?.flightNumber}_${scheduleDateTime}`,
        }
      } else {
        flyItem = false
      }
    }
    if (!flyItem && isFocused) {
      Alert.alert(
        handleCondition(
          msg53?.title,
          msg53?.title,
          translate("scanCode.invalidBoardingPass.title"),
        ),
        handleCondition(
          msg53?.message,
          msg53?.message,
          translate("scanCode.invalidBoardingPass.description"),
        ),
        [
          {
            text: handleCondition(
              msg53?.firstButton,
              msg53?.firstButton,
              translate("scanCode.invalidBoardingPass.firstButton"),
            ),
            style: "cancel",
            onPress: () => {
              setNeedScan(true)
              handleGoBack()
            },
          },
          {
            text: handleCondition(
              msg53?.secondButton,
              msg53?.secondButton,
              translate("scanCode.invalidBoardingPass.secondButton"),
            ),
            onPress: () => {
              setNeedScan(true)
            },
          },
        ],
      )
    } else {
      setIsCheckingFlightAvailable(true)
      const checkFlight = await checkFlightAvailable({
        direction: flyItem?.direction,
        flightNumber: flyItem?.flightNumber,
        scheduledDate: flyItem?.flightDate,
        airlineCode: flyItem?.flightNumber?.substring(0, 2),
        flightStatus: null,
      })
      setIsCheckingFlightAvailable(false)
      const invalidStatus = ["departed", "landed"]
      if (
        (!checkFlight?.flight_number ||
          invalidStatus.includes(checkFlight.flight_status?.toLowerCase())) &&
        isFocused
      ) {
        Alert.alert(
          handleCondition(msg52?.title, msg52?.title, translate("scanCode.flightNotFound.title")),
          handleCondition(
            msg52?.message,
            msg52?.message,
            translate("scanCode.flightNotFound.description"),
          ),
          [
            {
              text: handleCondition(
                msg52?.firstButton,
                msg52?.firstButton,
                translate("scanCode.flightNotFound.firstButton"),
              ),
              style: "cancel",
              onPress: () => {
                setNeedScan(true)
                handleGoBack()
              },
            },
            {
              text: handleCondition(
                msg52?.secondButton,
                msg52?.secondButton,
                translate("scanCode.flightNotFound.secondButton"),
              ),
              onPress: () => {
                setNeedScan(true)
              },
            },
          ],
        )
      } else if (checkFlight?.flight_number) {
        const flightDataMapped = mappingFlightData(checkFlight)
        resetState()
        clearInterval(timer)
        navigation.navigate("flightDetails", {
          payload: {
            item: {
              ...flightDataMapped,
              departingCode: flyItem.departingCode,
              destinationCode: flyItem.destinationCode,
            },
            itemIndex: 0,
            sectionIndex: 1,
            flightNavigationType: "departureLanding",
          },
          isFromScanBoardingPass: true,
          direction: checkFlight?.direction,
        })
      }
    }
  }

  const handleGoBack = () => {
    resetState()
    clearInterval(timer)
    navigation.goBack()
    if (shouldTrackDetectedFlightNumber && !flightNumberRef.current) {
      trackAction(AdobeTagName.CAppFlyFlightSearchScanBoardingPass, {
        [AdobeTagName.CAppFlyFlightSearchScanBoardingPass]: AdobeValueByTagName.CAppSearchResultIncompleteScan,
      })
    }
  }
  const readFromCamera = (codes) => {
    if (codes?.length > 0 && BOARDING_PASS_ACCEPTED_SCAN_TYPE.includes(codes?.[0]?.type) && codes?.[0]?.value) {
      setNeedScan(false)
      readDataFromBoardingPass(codes?.[0].value)
    }
  }

  const toggleFlashMode = () => {
    setOnFlash(!onFlash)
  }

  const uploadBoardingPassFromGallery = () => {
    setNeedScan(false)
    setTimeout(async () => {
      try {
        const imageData: any = await choosePictureFromGallery({ multiple: false })
        const uriImage = get(imageData, "uri")
        if (uriImage) {
          setLoadingDectectBoardingPass(true)
          RNQRGenerator.detect({
            uri: uriImage,
          })
            .then((response) => {
              const { values } = response
              if (!isEmpty(values)) {
                setLoadingDectectBoardingPass(false)
                readDataFromBoardingPass(values?.[0])
              } else {
                setLoadingDectectBoardingPass(false)
                alertCanNotDetect()
              }
            })
            .catch((error) => {
              setNeedScan(true)
              console.log("Cannot detect QR code in image", error)
            })
        }
      } catch (error) {
        setNeedScan(true)
        console.log("Error while selecting the picture from gallery", error)
      }
    }, 200)
  }

  const headerScan = () => {
    return (
      <View style={styles.wrapHeaderScan}>
        <TouchableOpacity onPress={() => handleGoBack()}>
          <BackButtonSvg />
        </TouchableOpacity>
        <View style={styles.wrapRightAction}></View>
      </View>
    )
  }

  const uploadBoardingPassBtn = () => {
    return (
      <View style={styles.wrapUploadBoardingPassBtn}>
        <TouchableOpacity onPress={uploadBoardingPassFromGallery}>
          <LinearGradient
            style={styles.touchableUploadBoardingPassStyle}
            start={{ x: 0, y: 1 }}
            end={{ x: 1, y: 0 }}
            colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
          >
            <Text text="Upload E-Boarding Pass" style={styles.textBtnUploadBoardingPassStyle} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    )
  }
  const footerScan = () => {
    return (
      <View style={styles.wrapFooterScan}>
        <TouchableOpacity onPress={() => toggleFlashMode()}>
          {handleCondition(onFlash, <FlashModeOn />, <FlashModeOff />)}
        </TouchableOpacity>
      </View>
    )
  }

  const onCameraStarted = () => {
    setTimeout(() => {
      setCameraStarted(true)
    }, 200)
  }

  return (
    <View style={{ flex: 1 }}>
      <MyCamera
        ref={cameraRef}
        flashMode={onFlash}
        isCodeScanned={needScan}
        onCodeScanned={readFromCamera}
        onCodeScannedTypes={BOARDING_PASS_ACCEPTED_SCAN_TYPE}
        isActive={isFocused}
        onStarted={onCameraStarted}
      />
      {cameraStarted && <RNHoleView
        style={styles.holeViewStyle}
        holes={[
          {
            x: MASK_PADDING_LEFT,
            y: MASK_POSITION_Y,
            width: MASK_WIDTH,
            height: MASK_HEIGHT,
            borderRadius: 12,
          },
        ]}
      >
        {headerScan()}
        <View style={styles.bottomStyle}>
          <Text tx={"scanCode.description"} style={styles.description} preset={"caption1Bold"} />
          {uploadBoardingPassBtn()}
          {footerScan()}
        </View>
      </RNHoleView>}
      {cameraStarted && <Animated.View style={[styles.animatedLine, animatedLineStyle]} />}
      <LoadingOverlay
        visible={handleCondition(
          isCheckingFlightAvailable || loadingDectectBoardingPass,
          true,
          false,
        )}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  animatedLine: {
    backgroundColor: color.palette.lightPurple,
    height: 4,
    left: MASK_PADDING_LEFT - 1,
    position: "absolute",
    top: MASK_POSITION_Y + 20,
    width: MASK_WIDTH,
  },
  bottomStyle: {
    alignItems: "center",
    paddingHorizontal: 52,
    position: "absolute",
    top: MASK_POSITION_Y + MASK_HEIGHT + 16,
    width: "100%",
  },
  description: {
    ...presets.caption1Bold,
    color: color.palette.whiteGrey,
    letterSpacing: 0.06,
    lineHeight: 18,
    marginBottom: 16,
    textAlign: "center",
    textAlignVertical: "center",
  },
  holeViewStyle: {
    backgroundColor: Platform.OS === 'android' ? 'clear' :  HOLE_VIEW_BACKGROUND,
    height: "100%",
    position: "absolute",
    width: "100%",
  },
  preview: {
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end",
  },
  textBtnUploadBoardingPassStyle: {
    ...presets.caption1Bold,
    color: color.palette.whiteGrey,
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 2,
    textAlignVertical: "center",
  },
  touchableUploadBoardingPassStyle: {
    borderRadius: 60,
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  wrapFooterScan: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 24,
    paddingHorizontal: 20,
  },
  wrapHeaderScan: {
    alignItems: "center",
    backgroundColor: color.transparent,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    position: "absolute",
    top: 60,
    width: "100%",
  },
  wrapRightAction: {
    flexDirection: "row",
  },
  wrapUploadBoardingPassBtn: {
    alignItems: "center",
  },
})
export default ScanCodeScreenV1
