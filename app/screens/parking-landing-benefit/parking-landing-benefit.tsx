import React, { useRef } from 'react';
import { StyleSheet, View, StatusBar } from 'react-native';
import { TabActive, TabInActive, TabExpire, TabFilter } from './component';
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs"
import { NavigationConstants } from 'app/utils/constants';
import { translate } from "app/i18n/translate";

const Tab = createMaterialTopTabNavigator()

const ParkingLandingBenefitScreen = () => {
  const pressedTabLabel = useRef("")

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <Tab.Navigator
        initialRouteName={NavigationConstants.ParkingLandingBenefitActive}
        backBehavior="none"
        tabBar={(props) => {
          return (
            <TabFilter
              props={{ ...props }}
            />
          )
        }}
      >
        <Tab.Screen
          name={NavigationConstants.ParkingLandingBenefitActive}
          options={{
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: translate("parkingLandingBenefit.tabFilter.active")
          }}
          listeners={{
            tabPress: (e) => {
              pressedTabLabel.current = translate("parkingLandingBenefit.tabFilter.active")
            },
          }}
        >
          {(props) => (
            <TabActive
              {...props}
              pressedTabLabel={pressedTabLabel}
            />
          )}
        </Tab.Screen>
        <Tab.Screen
          name={NavigationConstants.ParkingLandingBenefitInActive}
          options={{
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: translate("parkingLandingBenefit.tabFilter.in-active"),
          }}
          listeners={{
            tabPress: (e) => {
              pressedTabLabel.current = translate("parkingLandingBenefit.tabFilter.in-active")
            },
          }}
        >
          {(props) => (
            <TabInActive
              {...props}
              pressedTabLabel={pressedTabLabel}
            />
          )}
        </Tab.Screen>
        <Tab.Screen
          name={NavigationConstants.ParkingLandingBenefitExpire}
          options={{
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: translate("parkingLandingBenefit.tabFilter.expired"),
          }}
          listeners={{
            tabPress: (e) => {
              pressedTabLabel.current = translate("parkingLandingBenefit.tabFilter.expired")
            },
          }}
        >
          {(props) => (
            <TabExpire
              {...props}
              pressedTabLabel={pressedTabLabel}
            />
          )}
        </Tab.Screen>
      </Tab.Navigator>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  }
})

export default ParkingLandingBenefitScreen;