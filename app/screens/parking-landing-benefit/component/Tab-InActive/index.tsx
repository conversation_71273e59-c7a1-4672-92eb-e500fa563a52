import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, ScrollView } from 'react-native';
import { color } from 'app/theme';
import { TitleDescription } from './components/title-description';
import { ViewItem } from './components/view-item';
import { LoadingInActive } from './components/view-loading';
import { EmptyInActive } from './components/view-empty';
import { LoadingModal } from "app/components/loading-modal/loading-modal";
import { useDispatch } from 'react-redux';
import SystemActions from "app/redux/systemRedux"
import { isFlagON, REMOTE_CONFIG_FLAGS } from 'app/services/firebase/remote-config';
import { NavigationConstants, StateCode } from 'app/utils/constants';
import { getDeepLinkV2 } from 'app/sagas/pageConfigSaga';
import { useNavigation } from '@react-navigation/native';
import ParkingBenefitErrorOverlay from '../error-overlay';
import NetInfo from "@react-native-community/netinfo";
import { useParkingBenefit } from '../../parking-landing-benefit.hooks';
import { AdobeTagName, trackAction } from 'app/services/adobe';
import { translate } from 'app/i18n/translate';
import { useFocusEffect } from '@react-navigation/native';

const TabInActive = React.memo((props: any) => {
  const { pressedTabLabel } = props;
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const [loadingModal, setLoadingModal] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const scrollRef = useRef(null)
  const { data, isError, isFetching, isRefresh, dataParked, setIsRefresh } = useParkingBenefit({
    payloadKey: "inactiveCoupons",
    scrollRef,
  })

  const isParked = dataParked?.status === "PARKED"
  const parkingStatus = isParked ? "Parked" : "Not Parked"
  const trackingValue = `${parkingStatus} | Tile | ${translate("parkingLandingBenefit.tabFilter.in-active")}`

  const handleItem = async (item) => {
    const titleTrackingValue = item?.title ?? "null"
    trackAction(AdobeTagName.CAppBenefitsSummary, {
      [AdobeTagName.CAppBenefitsSummary]: `${trackingValue} | ${titleTrackingValue} | ${translate("parkingLandingBenefit.inactive.button")}`,
    })
    setLoadingModal(true)
    try {
      const enableFlightCSM_CarParss = isFlagON(REMOTE_CONFIG_FLAGS.CSM_CARPARSS)
      const payload = {
        stateCode: enableFlightCSM_CarParss ? StateCode?.CARPASS_DRIVE : StateCode?.CARPASS,
        input: {
          "stateCode": "coupon",
          "bookingId": item?.booking_id
        },
      }
      const getLink = await getDeepLinkV2(payload, true)
      navigation.navigate(NavigationConstants.webview, {
        uri: getLink?.redirectUri,
        basicAuthCredential: getLink?.basicAuth,
      })
      setLoadingModal(false)
    } catch (error) {
      setLoadingModal(false)
      setTimeout(() => {
        dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
      }, 600)
    }
  }

  const checkConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    setIsConnected(isConnected)
  }

  useEffect(() => {
    checkConnection()
  }, [navigation])

  const renderItem = ({ item, index }) => {
    return <ViewItem item={item} index={index} onActive={() => handleItem(item)} />
  }

  const renderTabContent = useMemo(() => {
    // API error / no internet case
    if (!isFetching && (isError || !isConnected)) {
      return <ParkingBenefitErrorOverlay />
    }
    // Empty case
    if (data?.length === 0 && !isFetching) {
      return <EmptyInActive trackingValue={trackingValue} />
    }
    return (
      <>
        <TitleDescription />
        {isFetching ? (
          <LoadingInActive />
        ) : (
          <View style={styles.viewContent}>
            <FlatList
              data={data}
              renderItem={renderItem}
              keyExtractor={(index) => index?.toString()}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={<View style={styles.viewBottom} />}
              ref={scrollRef}
              nestedScrollEnabled
            />
          </View>
        )}
        <LoadingModal visible={loadingModal} />
      </>
    )
  }, [JSON.stringify(data), isConnected, isError, isFetching, loadingModal, renderItem])

  useFocusEffect(
    useCallback(() => {
      if (pressedTabLabel?.current === translate("parkingLandingBenefit.tabFilter.in-active") && !!dataParked?.status) {
        trackAction(AdobeTagName.CAppBenefitsSummary, {
          [AdobeTagName.CAppBenefitsSummary]: `${parkingStatus} | Tab | ${translate(
            "parkingLandingBenefit.tabFilter.in-active",
          )}`,
        })
      }
    }, [dataParked?.status, parkingStatus, pressedTabLabel]),
  )

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
        colors={[color.palette.lightGrey]}
        onRefresh={() => setIsRefresh?.(true)}
        progressBackgroundColor="transparent"
        progressViewOffset={16}
        refreshing={isRefresh}
        tintColor={color.palette.lightGrey}
        />
      }
      showsVerticalScrollIndicator={false}
      style={styles.container}
    >
      {renderTabContent}
    </ScrollView>
  )
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey
  },
  viewContent: {
    flex: 1,
    paddingHorizontal: 16,
    gap: 12
  },
  viewBottom:{
    height: 40
  }
})

export { TabInActive }
