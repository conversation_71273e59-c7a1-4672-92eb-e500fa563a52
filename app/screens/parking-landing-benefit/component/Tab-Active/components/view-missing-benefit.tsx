import { Text } from 'app/elements/text';
import { AemSelectors } from 'app/redux/aemRedux';
import { color } from 'app/theme';
import { typography } from 'app/theme/typography';
import { useHandleNavigation } from 'app/utils/navigation-helper';
import React from 'react';
import { Platform, StyleSheet, View, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { translate } from "app/i18n/translate";
import { COMPONENT_NAME } from '../constants';
import { trackAction, AdobeTagName } from 'app/services/adobe';

const ViewMissingBenefit = React.memo(({ trackingValue }: {trackingValue: string}) => {
  const { handleNavigation } = useHandleNavigation("PARKING_LANDING_BENEFIT_ACTIVE")
  const informativeCommon = useSelector(AemSelectors.getMessagesCommon)
  const MSG99 = informativeCommon?.find((e) => e?.code === "MSG99")
  const buttonText = MSG99?.firstButton ? MSG99?.firstButton : translate("parkingLandingBenefit.missingBenefit.button")

  const onPressFindOutMore = () => {
    const { type, value } = MSG99?.firstNavigation || {}
    const { redirectFirst } = MSG99 || {}
    if (!type) return
    trackAction(AdobeTagName.CAppBenefitsSummary, {
      [AdobeTagName.CAppBenefitsSummary]: `${trackingValue} | ${buttonText}`,
    })
    handleNavigation(type, value, redirectFirst)
  }

  return (
    <View style={styles.container}>
      <Text style={styles.txtTitle} testID={`${COMPONENT_NAME}___MISSING_BENEFIT__Title`}>
        {MSG99?.title ? MSG99?.title : translate("parkingLandingBenefit.missingBenefit.title")}
      </Text>
      <Text>
        <TouchableOpacity onPress={onPressFindOutMore} activeOpacity={0.9}>
          <Text
            style={styles.txtButton}
            testID={`${COMPONENT_NAME}___MISSING_BENEFIT__TitleButton`}
          >{buttonText} </Text>
        </TouchableOpacity>
        <Text style={styles.txtContent}
          testID={`${COMPONENT_NAME}___MISSING_BENEFIT__Message`}
        >{MSG99?.message ? MSG99?.message : translate("parkingLandingBenefit.missingBenefit.message")}</Text>
      </Text>
    </View>
  );
});;

const styles = StyleSheet.create({
  container: {
    marginTop: 50,
    marginHorizontal: 16,
    marginBottom: 30
  },
  txtTitle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    marginBottom: 4
  },
  txtButton: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
    marginBottom: -3
  },
  txtContent: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  }
})

export { ViewMissingBenefit }