import { NoParkingBenefitInActive } from 'assets/icons';
import React from 'react';
import { View, StyleSheet, Platform, TouchableOpacity } from 'react-native';
import { translate } from "app/i18n/translate";
import { Text } from 'app/elements/text';
import { color, typography } from 'app/theme';
import { LinearGradient } from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import { AemSelectors } from "app/redux/aemRedux";
import { useHandleNavigation } from 'app/utils/navigation-helper';
import { trackAction, AdobeTagName } from 'app/services/adobe';

const EmptyActive = React.memo(({ trackingValue }: { trackingValue: string }) => {
  const { handleNavigation } = useHandleNavigation("PARKING_LANDING_BENEFIT_INACTIVE")
  const informativeCommon = useSelector(AemSelectors.getErrorsCommon)
  const EHR31 = informativeCommon?.find((e) => e?.code === "EHR31")
  const buttonText = EHR31?.buttonLabel ? EHR31?.buttonLabel : translate("parkingLandingBenefit.inactive.findOutMore")

  const onPressFindOutMore = () => {
    const { type, value } = EHR31?.navigationFirst || {}
    const { redirectFirst } = EHR31 || {}
    if (!type) return
    trackAction(AdobeTagName.CAppBenefitsSummary, {
      [AdobeTagName.CAppBenefitsSummary]: `${trackingValue} | ${buttonText}`,
    })
    handleNavigation(type, value, redirectFirst)
  }

  const convertString = () => {
    const str = EHR31?.subHeader
    if (str.includes("<enter>")) {
      return str.replace("<enter>", "\n \n")
    } else {
      return str
    }
  }

  return (
    <View style={styles.container}>
      <NoParkingBenefitInActive />
      <Text style={styles.txtTitle}>{EHR31?.header ? EHR31?.header : translate("parkingLandingBenefit.active.title_empty")}</Text>
      <Text style={styles.txtContent}>{EHR31?.subHeader ? convertString() : translate("parkingLandingBenefit.active.content_empty")}</Text>
      <TouchableOpacity onPress={onPressFindOutMore}>
        <LinearGradient
          colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
          end={{ x: 0, y: 1 }}
          start={{ x: 0, y: 0 }}
          style={styles.button}
        >
          <Text style={styles.txtButton}>
            {buttonText}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
      <View style={styles.viewRow} />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: 24,
    paddingHorizontal: 16
  },
  txtTitle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    marginTop: 16
  },
  txtContent: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    marginTop: 8,
    textAlign: 'center',
    paddingHorizontal: 8
  },
  button: {
    width: 124,
    height: 28,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 16
  },
  txtButton: {
    fontFamily: typography.bold,
    color: color.palette.whiteGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 20,
  },
  viewRow: {
    width: '100%',
    height: 1,
    backgroundColor: color.palette.lighterGrey,
    marginTop: 50
  }
})

export { EmptyActive }