import { Text } from 'app/elements/text';
import { color, typography } from 'app/theme';
import React from 'react';
import { Platform, StyleSheet, View, TouchableOpacity } from 'react-native';
import { translate } from "app/i18n/translate";
import { COMPONENT_NAME } from '../constants';
import { useSelector } from 'react-redux';
import { AemSelectors } from "app/redux/aemRedux";
import { HtmlRichtext } from "app/elements/html-richtext"
import { useNavigation } from '@react-navigation/native';
import { NavigationConstants } from 'app/utils/constants';
import { trackAction, AdobeTagName } from 'app/services/adobe';

const TitleDescription = React.memo(({ trackingValue }: { trackingValue: string }) => {
  const navigation = useNavigation<any>()
  const informativeCommon = useSelector(AemSelectors.getMessagesCommon)
  const MSG98 = informativeCommon?.find((e) => e?.code === "MSG98")
  const descriptionDefault = "<p>There will be no pro-ration for <b>2H Free Parking</b> and <b>CR Daily Complimentary Parking</b>. The respective benefits will be fully utilised upon exit from car park.</p>\n"

  const onPress = () => {
    trackAction(AdobeTagName.CAppBenefitsSummary, {
      [AdobeTagName.CAppBenefitsSummary]: `${trackingValue} | ${translate("parkingLandingBenefit.active.FAQ")}`,
    })
    const { value } = MSG98?.firstNavigation || {}
    navigation.navigate(NavigationConstants.FAQLanding, { url: value })
  }

  return (
    <View style={styles.container}>
      <Text style={styles.txtTitle} testID={`${COMPONENT_NAME}__title`}>
        {MSG98?.title ? MSG98?.title : translate("parkingLandingBenefit.active.title")}
      </Text>
      <HtmlRichtext style={styles.txtDescription} value={MSG98?.description ? MSG98?.description : descriptionDefault} />
      <TouchableOpacity onPress={onPress}
        accessibilityLabel={`${COMPONENT_NAME}__ButtonFAQ`}
        testID={`${COMPONENT_NAME}__ButtonFAQ`}
      >
        <Text style={styles.txtButton}
          testID={`${COMPONENT_NAME}__TitleButtonFAQ`}
        >{MSG98?.firstButton ? MSG98?.firstButton : translate("parkingLandingBenefit.active.FAQ")}</Text>
      </TouchableOpacity>
    </View>
  );
})

const styles = StyleSheet.create({
  container: {
    padding: 24,
    gap: 4,
    paddingBottom: 16
  },
  txtTitle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
  },
  txtDescription: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  },
  txtButton: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  }
})

export { TitleDescription }