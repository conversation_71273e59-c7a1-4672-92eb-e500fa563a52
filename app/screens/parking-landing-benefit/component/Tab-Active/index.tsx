import { color, typography } from 'app/theme';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, View, ScrollView, Platform, RefreshControl } from 'react-native';
import { TitleDescription } from './components/title-description';
import { ViewParked } from './components/view-parked';
import { ViewItem } from './components/view-item';
import { Text } from 'app/elements/text';
import { LoadingActive } from './components/view-loading';
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { LOADING_COLORS } from "app/screens/for-you/components/miffy-gamification-banner/miffy-gamification-banner.styles"
import { NavigationConstants, PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { ViewMissingBenefit } from './components/view-missing-benefit';
import { ViewNotApplicable } from './components/view-notApplicable';
import { COMPONENT_NAME } from './constants';
import { EmptyActive } from './components/view-empty';
import { MultimediaTouchableOpacity } from 'app/components/multimedia-touchable-opacity/multimedia-touchable-opacity';
import { useParkingBenefit } from '../../parking-landing-benefit.hooks';
import ParkingBenefitErrorOverlay from '../error-overlay';
import NetInfo from "@react-native-community/netinfo";
import { trackAction, AdobeTagName } from 'app/services/adobe';
import { translate } from 'app/i18n';
import { useFocusEffect } from '@react-navigation/native';

interface PropsType {
  navigation?: any
  pressedTabLabel?: React.MutableRefObject<string | undefined>;
}

const TabActive = React.memo((props: PropsType) => {
  const { navigation, pressedTabLabel } = props;
  const scrollRef = useRef(null)
  const [isConnected, setIsConnected] = useState(true)
  const { data, isError, isFetching, dataParked, isRefresh, setIsRefresh } = useParkingBenefit({
    payloadKey: "activeBenefits",
    scrollRef,
  })
  const isParked = dataParked?.status === "PARKED"
  const parkingStatus = isParked ? "Parked" : "Not Parked"
  const trackingValue = `${parkingStatus} | Tile | ${translate("parkingLandingBenefit.tabFilter.active")} | null`

  const handlePressTotalSection = () => {
    const dataToBeSent = `${trackingValue} | ${translate("parkingLandingBenefit.active.totalSection.title")}`
    trackAction(AdobeTagName.CAppBenefitsSummary, {
      [AdobeTagName.CAppBenefitsSummary]: dataToBeSent,
    })
    navigation?.navigate?.(NavigationConstants.carPark, { activeTab: 1 })
  }

  const checkConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    setIsConnected(isConnected)
  }

  useEffect(() => {
    checkConnection()
  }, [navigation])

  const renderTabContent = useMemo(() => {
    if (!isFetching && (isError || !isConnected)) {
      return <ParkingBenefitErrorOverlay />
    }
    return (
      <>
        {data?.applicable?.length === 0 ? (
          <EmptyActive trackingValue={trackingValue} />
        ) : (
          <>
            <TitleDescription trackingValue={trackingValue} />
            {isParked && <ViewParked value={dataParked?.carparkLocation} />}
            <View style={styles.viewContent}>
              <View style={styles.viewList}>
                {isFetching ? (
                  <LoadingActive />
                ) : (
                  <>
                    {data?.applicable?.map((item, index) => {
                      return <ViewItem item={item} index={index} key={index} />
                    })}
                  </>
                )}
              </View>
              <View style={styles.viewBottomContent}>
                <MultimediaTouchableOpacity
                  accessibilityLabel={`${COMPONENT_NAME}__TotalSection__DescriptionSide`}
                  onPress={handlePressTotalSection}
                  style={styles.viewLeft}
                  testID={`${COMPONENT_NAME}__TotalSection__DescriptionSide`}
                >
                  <Text
                    accessibilityLabel={`${COMPONENT_NAME}__viewBottomContent__title`}
                    style={styles.title}
                    testID={`${COMPONENT_NAME}__viewBottomContent__title`}
                    tx="parkingLandingBenefit.active.totalSection.title"
                  />
                  <Text
                    accessibilityLabel={`${COMPONENT_NAME}__viewBottomContent__subTitle`}
                    style={styles.subTitle}
                    testID={`${COMPONENT_NAME}__viewBottomContent__subTitle`}
                    tx={
                      isParked || isFetching
                        ? "parkingLandingBenefit.active.totalSection.parked.title"
                        : "parkingLandingBenefit.active.totalSection.notParked.title"
                    }
                  />
                  <Text
                    accessibilityLabel={`${COMPONENT_NAME}__viewBottomContent__content`}
                    style={styles.content}
                    testID={`${COMPONENT_NAME}__viewBottomContent__content`}
                    tx={
                      isParked || isFetching
                        ? "parkingLandingBenefit.active.totalSection.parked.description"
                        : "parkingLandingBenefit.active.totalSection.notParked.description"
                    }
                  />
                </MultimediaTouchableOpacity>
                <View style={styles.viewRight}>
                  {isFetching ? (
                    <ShimmerPlaceholder
                      duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                      shimmerColors={LOADING_COLORS}
                      shimmerStyle={styles.loadingText}
                    />
                  ) : (
                    <>
                      <Text
                        style={styles.time}
                        testID={`${COMPONENT_NAME}__viewBottomContentRight__time`}
                      >
                        {data?.totalDuration}
                      </Text>
                      <Text
                        style={styles.price}
                        testID={`${COMPONENT_NAME}__viewBottomContentRight__price`}
                      >
                        {`$${data?.totalBalance}`}
                      </Text>
                    </>
                  )}
                </View>
              </View>
            </View>
          </>
        )}
        {data?.notApplicable?.length > 0 && <ViewNotApplicable data={data} />}
        <ViewMissingBenefit trackingValue={trackingValue} />
      </>
    )
  }, [JSON.stringify(data), JSON.stringify(dataParked), isConnected, isError, isFetching, isParked])

  useFocusEffect(
    useCallback(() => {
      if (pressedTabLabel?.current === translate("parkingLandingBenefit.tabFilter.active") && !!dataParked?.status) {
        trackAction(AdobeTagName.CAppBenefitsSummary, {
          [AdobeTagName.CAppBenefitsSummary]: `${parkingStatus} | Tab | ${translate(
            "parkingLandingBenefit.tabFilter.active",
          )}`,
        })
      }
    }, [dataParked?.status, parkingStatus, pressedTabLabel]),
  )

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        ref={scrollRef}
        refreshControl={
          <RefreshControl
            colors={[color.palette.lightGrey]}
            onRefresh={() => setIsRefresh?.(true)}
            progressBackgroundColor="transparent"
            progressViewOffset={16}
            refreshing={isRefresh}
            tintColor={color.palette.lightGrey}
          />
        }
      >
        {renderTabContent}
      </ScrollView>
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey
  },
  viewContent: {
    paddingTop: 16,
    paddingHorizontal: 16
  },
  viewList: {
    paddingTop: 16,
    paddingBottom: 24,
    paddingHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
    gap: 24
  },
  viewBottomContent: {
    padding: 16,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderColor: color.palette.lighterGrey,
    flexDirection: 'row'
  },
  viewLeft: {
    width: '60%'
  },
  viewRight: {
    width: '40%',
    alignItems: 'flex-end'
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
  },
  subTitle: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
    marginVertical: 4
  },
  content: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  },
  price: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  },
  time: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  loadingText: {
    width: 64, height: 12, borderRadius: 12
  }
})

export { TabActive } 