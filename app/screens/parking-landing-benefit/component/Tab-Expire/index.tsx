import React, { useEffect, useMemo, useState, useCallback } from "react"
import { View, StyleSheet, ScrollView, RefreshControl } from "react-native"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"
import { useParkingBenefit } from "../../parking-landing-benefit.hooks"
import { useSelector } from "react-redux"
import { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { translate } from "app/i18n"
import ExpiredUsedLoadingSkeleton from "./components/loading-skeleton"
import NetInfo from "@react-native-community/netinfo"
import ParkingBenefitErrorOverlay from "../error-overlay"
import ExpiredCoupons from "./components/expired-coupons"
import { useFocusEffect } from '@react-navigation/native';
import { trackAction, AdobeTagName } from 'app/services/adobe';

interface PropsType {
  navigation?: any
  pressedTabLabel?: React.MutableRefObject<string | undefined>
}

const TabExpire = React.memo((props: PropsType) => {
  const { navigation, pressedTabLabel } = props
  const scrollRef = React.useRef(null)
  const { data, isError, isFetching, dataParked, isRefresh, setIsRefresh } = useParkingBenefit({
    payloadKey: "expiredCoupons",
    scrollRef,
  })
  const [isConnected, setIsConnected] = useState(true)
  const aemCommonData = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.AEM_COMMON_DATA))
  const inf28 = aemCommonData?.data?.informatives?.find((e) => e?.code === "INF28")
  const inf29 = aemCommonData?.data?.informatives?.find((e) => e?.code === "INF29")
  const hasCoupon = data?.length > 0

  const isParked = dataParked?.status === "PARKED"
  const parkingStatus = isParked ? "Parked" : "Not Parked"

  const message = useMemo(() => {
    if (hasCoupon || isFetching) {
      return inf28?.informativeText || translate("parkingLandingBenefit.expiredUsed.hasCouponMsg")
    }
    return inf29?.informativeText || translate("parkingLandingBenefit.expiredUsed.noCouponMsg")
  }, [hasCoupon, inf28?.informativeText, inf29?.informativeText, isFetching])

  const checkConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    setIsConnected(isConnected)
  }

  useEffect(() => {
    checkConnection()
  }, [navigation])

  const renderTabContent = useMemo(() => {
    if (!isFetching && (isError || !isConnected)) {
      return <ParkingBenefitErrorOverlay />
    }
    return (
      <>
        <Text style={styles.messageTextStyle} text={message} />
        {isFetching && <ExpiredUsedLoadingSkeleton />}
        {data?.map?.((item, index, list) => (
          <ExpiredCoupons
            isLastItem={index === list?.length - 1}
            item={item}
            key={`${index}_${item?.title}`}
          />
        ))}
      </>
    )
  }, [JSON.stringify(data), isConnected, isError, isFetching, message])

  useFocusEffect(
    useCallback(() => {
      if (pressedTabLabel?.current === translate("parkingLandingBenefit.tabFilter.expired") && !!dataParked?.status) {
        trackAction(AdobeTagName.CAppBenefitsSummary, {
          [AdobeTagName.CAppBenefitsSummary]: `${parkingStatus} | Tab | ${translate(
            "parkingLandingBenefit.tabFilter.expired",
          )}`,
        })
      }
    }, [dataParked?.status, parkingStatus, pressedTabLabel]),
  )

  return (
    <View style={styles.wrapperStyle}>
      <ScrollView
        ref={scrollRef}
        style={styles.containerStyle}
        refreshControl={
          <RefreshControl
            colors={[color.palette.lightGrey]}
            onRefresh={() => setIsRefresh?.(true)}
            progressBackgroundColor="transparent"
            progressViewOffset={16}
            refreshing={isRefresh}
            tintColor={color.palette.lightGrey}
          />
        }
      >
        {renderTabContent}
      </ScrollView>
    </View>
  )
})

const styles = StyleSheet.create({
  wrapperStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
  },
  containerStyle: {
    paddingHorizontal: 16,
  },
  messageTextStyle: {
    ...newPresets.caption2Regular,
    marginVertical: 24,
    textAlign: "center",
  },
})

export { TabExpire }
