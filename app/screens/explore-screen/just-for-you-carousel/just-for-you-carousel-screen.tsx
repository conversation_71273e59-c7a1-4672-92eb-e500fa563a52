import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { presets, Text } from "app/elements/text"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { translate } from "app/i18n"
import { NavigationConstants, PLACEHOLDER_ANIMATION_SPEED_IN_MS, StateCode } from "app/utils/constants"
import React, { useContext, useState } from "react"
import {
  ScrollView,
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  ImageBackground,
  ImageStyle,
  InteractionManager,
} from "react-native"
import LinearGradient from "react-native-linear-gradient"
import { useSelector } from "react-redux"
import Card from "./carousel-card"
import LoadingView from "./loading-screen"
import { isEmpty } from "lodash"
import {
  lightGreyLoadingColors,
  loadingScreen,
  mainScreenStyles,
} from "./styles.styles"
import { ShopSelectors } from "app/redux/shopRedux"
import {
  adobeRetrieveLocationContent,
  AdobeTagName,
  DEFAULT_LOCATION_CONTENT,
  getExperienceCloudId,
  trackAction,
} from "app/services/adobe"
import sha256 from "crypto-js/sha256"
import { ProfileSelectors } from "app/redux/profileRedux"
import { ExploreContext } from "app/services/context/explore"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { color } from "app/theme"
import { CaretWhiteRight } from "ichangi-fe/assets/icons"
import { NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { ISCBanner } from "ichangi-fe/assets/backgrounds"
import { ErrorCloudComponent } from "app/components/error-cloud/error-cloud"
import { AemSelectors } from "app/redux/aemRedux"
import { simpleCondition } from "app/utils"
import restApi from "app/services/api/request"
import { getJustForYouCarousel } from "app/models/queries"
import { graphqlOperation } from "aws-amplify"
import { env } from "app/config/env-params"
import { getISCInputParamsDeepLink } from "app/helpers/deeplink/deeplink-parameter"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { WebViewHeaderTypes } from "app/models/enum"
import { DT_ANALYTICS_LOG_EVENT_NAME, dtManualActionEvent } from "app/services/firebase"

const LINEAR_GRADIENT = {
  "#8A2AA2": "#8A2AA2",
  "#F7F7F7": "#F7F7F7",
  "#7A35B0": "#7A35B0",
}

const PositionShopAll = {
  top: "Top",
  last: "Last",
}
const lastItemEmptyStyle: ViewStyle = {
  marginBottom: -50,
}

const wrapHeaderJustForYou: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 24,
  paddingBottom: 16,
}

const wrapShopAllTextStyle: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-end",
}

const mainTitleStyle: TextStyle = {
  ...presets.h4,
  color: color.palette.whiteGrey,
}

const shopAllTextStyle: TextStyle = {
  ...presets.caption1Bold,
  color: color.palette.whiteGrey,
  marginRight: 4,
}

const ISCBannerStyle: ImageStyle = {
  width: 164,
  borderRadius: 16,
  marginRight: 16,
  alignItems: "center",
  justifyContent: "flex-end",
  paddingBottom: 28,
}
const shopAllEndButtonStyle: ViewStyle = {
  paddingVertical: 4,
  paddingHorizontal: 29,
  borderRadius: 60,
  height: 28,
}

const textShopAllEndButtonStyle: TextStyle = {
  ...presets.caption1Bold,
  color: color.palette.whiteGrey,
}

const ReturnView = ({
  justForYouCarouselData,
  justForYouCarouselFetching,
  useSeparator,
  navigation,
  testID,
  accessibilityLabel,
  isFlagOn,
  handleNavigation,
  userId,
}) => {

  const handleNavigateCSMIShopchangi = async(url: string) => {
    const ecid = await getExperienceCloudId()
    const target = getISCInputParamsDeepLink(url)
    const payload = {
      stateCode: StateCode.ISHOPCHANGI,
      input: {
        ...target,
        ecid,
      },
    }
    try {
      const response = await getDeepLinkV2(payload, true)
      if (response?.redirectUri) {
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        navigation.navigate(NavigationConstants.webview, {
          uri: url,
        })
      }
    } catch (error) {
      navigation.navigate(NavigationConstants.webview, {
        uri: url,
      })
    }
  }

  const handleOpenISC = (type) => {
    trackAction(AdobeTagName.CAppShopTenantPromoSwimlane, {
      [AdobeTagName.CAppShopTenantPromoSwimlane]: `Explore | ${type} | ${translate(
        "justForYouCarouselScreen.shopAll",
      )}`,
    })
    if(isFlagOn){
      const allHomePage = "https://www.ishopchangi.com/en/home?utm_source=ichangi_app&utm_medium=at&utm_campaign=rec"
      handleNavigateCSMIShopchangi(allHomePage)
    } else {
      handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.ishopchangi)
    }
  }

  const backgroundLinearGradientParams = () => {
    return {
      colors: [LINEAR_GRADIENT["#7A35B0"], LINEAR_GRADIENT["#F7F7F7"]],
    }
  }

  const linearGradientStyle = () => {
    if (useSeparator) {
      return {
        ...mainScreenStyles.containerWithSeparator,
      }
    } else {
      return {
        ...mainScreenStyles.container,
      }
    }
  }

  return (
    <LinearGradient
      style={linearGradientStyle()}
      {...backgroundLinearGradientParams()}
    >
      {justForYouCarouselFetching ? (
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lightGreyLoadingColors}
          shimmerStyle={loadingScreen.titleLoading}
        />
      ) : (
        <View style={wrapHeaderJustForYou}>
          <Text style={mainTitleStyle}>
            {justForYouCarouselData?.sectionTitle || translate("justForYouCarouselScreen.title")}
          </Text>
          <TouchableOpacity
            style={wrapShopAllTextStyle}
            onPress={() => handleOpenISC(PositionShopAll.top)}
          >
            <Text tx="justForYouCarouselScreen.shopAll" style={shopAllTextStyle} />
            <CaretWhiteRight width={20} height={20} />
          </TouchableOpacity>
        </View>
      )}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={mainScreenStyles.contentContainer}>
          {justForYouCarouselFetching ? (
            <LoadingView />
          ) : (
            <>
              {justForYouCarouselData?.data?.map((element, index) => (
                <Card
                  item={element}
                  key={index}
                  navigation={navigation}
                  testID={`${testID}__Card`}
                  accessibilityLabel={`${accessibilityLabel}__Card__${index}`}
                  userId={userId}
                  index={index}
                  isNewStyle={isFlagOn}
                />
              ))}
              <ImageBackground
                source={ISCBanner}
                imageStyle={ISCBannerStyle}
                style={ISCBannerStyle}
              >
                <TouchableOpacity onPress={() => handleOpenISC(PositionShopAll.last)}>
                  <LinearGradient
                    style={shopAllEndButtonStyle}
                    colors={[LINEAR_GRADIENT["#8A2AA2"], LINEAR_GRADIENT["#7A35B0"]]}
                    locations={[0.172, 0.8513]}
                    useAngle={true}
                    angle={-281.74}
                    angleCenter={{ x: 0.5, y: 0.5 }}
                  >
                    <Text
                      tx="justForYouCarouselScreen.shopAll"
                      style={textShopAllEndButtonStyle}
                    />
                  </LinearGradient>
                </TouchableOpacity>
              </ImageBackground>
            </>
          )}
        </View>
      </ScrollView>
    </LinearGradient>
  )
}

const OldCarousel = ({
  testID = "JustForYouCarousel",
  accessibilityLabel = "JustForYouCarousel",
  useSeparator = false,
  isLastItem,
  isFlagOn,
  setJustForYouDataFetching,
}) => {
  const [justForYouData, setJustForYouData] = useState(null)
  const [justForYouFetching, setJustForYouFetching] = useState(null)
  const navigation = useNavigation()
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const { handleNavigation } = useHandleNavigation("JUST_FOR_YOU_EXPLORE_SCREEN")

  const requestApi = React.useCallback(async () => {
    const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_JUST_FOR_YOU)
    dtAction.reportStringValue("feature_flag", "OFF")
    try {
      const response = await restApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getJustForYouCarousel),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const data = response?.data?.data?.getExploreJustForYou || null
      if (data) {
        dtAction.reportStringValue('status', 'success')
        setJustForYouData(data)
        setJustForYouFetching(false)
        setJustForYouDataFetching(false)
      } else throw new Error("No Data")
    } catch (error) {
      dtAction.reportStringValue('status', 'failed')
      setJustForYouData({ error: { message: error.message } })
      setJustForYouFetching(false)
      setJustForYouDataFetching(false)
    } finally {
      dtAction.leaveAction()
    }
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        setJustForYouDataFetching(true)
        setJustForYouFetching(true)
        requestApi()
      })
    }, []),
  )

  if (
    (!justForYouFetching && !justForYouData?.data) ||
    justForYouData?.error ||
    (!justForYouFetching && !justForYouData?.data?.length)
  ) {
    return <View />
  }

  if (isEmpty(justForYouData?.data)) {
    if (isLastItem) return <View style={lastItemEmptyStyle} />
    return <View />
  }

  return (
    <ReturnView
      justForYouCarouselData={justForYouData}
      justForYouCarouselFetching={justForYouFetching}
      useSeparator={useSeparator}
      navigation={navigation}
      testID={testID}
      accessibilityLabel={accessibilityLabel}
      isFlagOn={isFlagOn}
      handleNavigation={handleNavigation}
      userId={profilePayload?.id}
    />
  )
}

export const NewCarousel = ({
  testID = "JustForYouCarousel",
  accessibilityLabel = "JustForYouCarousel",
  useSeparator = false,
  isLastItem,
  isFlagOn,
  setJustForYouDataFetching,
}) => {
  const navigation = useNavigation()
  const [justForYouCarouselData, setJustForYou] = useState(null)
  const [justForYouCarouselFetching, setJustForYouFetching] = useState(null)
  const [justForYouCarouselError, setJustForYouError] = useState(null)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const recommendedProductsPayload = useSelector(ShopSelectors.recommendedProductsPayload)
  const { handleNavigation } = useHandleNavigation("JUST_FOR_YOU_EXPLORE_SCREEN")
  const errorData = useSelector(AemSelectors.getErrorsCommon)

  const getNewJustForYouLanding = async (recommendedProducts, profile, timeout = 0) => {
    const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_JUST_FOR_YOU)
    dtAction.reportStringValue("feature_flag", "ON")
    setJustForYouError(false)
    setJustForYouFetching(true)
    setJustForYouDataFetching(true)
    if (timeout) {
      await new Promise((resolve) => setTimeout(resolve, timeout))
    }
    const response = await adobeRetrieveLocationContent(
      {
        login_status: profile ? 0 : 1,
        uid: profile?.id,
        hashed_email: profile?.email
          ? sha256(profile?.email?.toLowerCase())?.toString()
          : undefined,
        ...simpleCondition({
          condition: recommendedProducts?.profile_parameters,
          ifValue: recommendedProducts?.profile_parameters,
          elseValue: {},
        }),
      },
      simpleCondition({
        condition: recommendedProducts?.parameters,
        ifValue: recommendedProducts?.parameters,
        elseValue: {},
      }),
      recommendedProducts?.m_box_name,
    )
    // console.log("adobe__getJustForYouLanding__adobeRetrieveLocationContent__response", response)
    if (response && response !== DEFAULT_LOCATION_CONTENT) {
      try {
        const jsonObject = JSON.parse(response)
        if (jsonObject?.code) {
          setJustForYouError(true)
          dtAction.reportStringValue('status', 'failed')
        } else {
          setJustForYou(jsonObject)
          dtAction.reportStringValue('status', 'success')
        }
      } catch (error) {
        setJustForYouError(true)
        dtAction.reportStringValue('status', 'failed')
        console.log("adobe__JUST_FOR_YOU_JSON_PARSE_ERROR", error)
      }
    } else {
      setJustForYou(null)
      dtAction.reportStringValue('data', 'success_without_data')
    }
    dtAction.leaveAction()
    setJustForYouFetching(false)
    setJustForYouDataFetching(false)
  }

  const onReload = () => {
    getNewJustForYouLanding(recommendedProductsPayload, profilePayload, 1000)
  }

  useFocusEffect(
    React.useCallback(() => {
      if (recommendedProductsPayload) {
        getNewJustForYouLanding(recommendedProductsPayload, profilePayload)
      }
    }, [recommendedProductsPayload, profilePayload?.id]),
  )

  if (!justForYouCarouselFetching && !justForYouCarouselData?.data?.length) {
    if (justForYouCarouselError) {
      return (
        <ErrorCloudComponent
          errorData={errorData}
          onPress={onReload}
          testID={`EXPLORE_JUST_FOR_YOU__ErrorCloudComponent`}
          accessibilityLabel={`EXPLORE_JUST_FOR_YOU__ErrorCloudComponent`}
        />
      )
    } else {
      if (isLastItem) return <View style={lastItemEmptyStyle} />
      return <View />
    }
  }

  return (
    <ReturnView
      justForYouCarouselData={justForYouCarouselData}
      justForYouCarouselFetching={justForYouCarouselFetching}
      useSeparator={useSeparator}
      navigation={navigation}
      testID={testID}
      accessibilityLabel={accessibilityLabel}
      isFlagOn={isFlagOn}
      handleNavigation={handleNavigation}
      userId={profilePayload?.id}
    />
  )
}

const JustForYouCarousel = (args) => {
  const ExploreContextHandler = useContext(ExploreContext)
  const isFlagOn = isFlagOnCondition(ExploreContextHandler?.exploreJustForYouFlag)

  if (!ExploreContextHandler?.exploreJustForYouFlag) {
    return <View />
  }

  if (isFlagOn) {
    return <NewCarousel {...args} isFlagOn={isFlagOn} />
  }

  return <OldCarousel {...args} isFlagOn={isFlagOn} />
}

export default React.memo(JustForYouCarousel)
