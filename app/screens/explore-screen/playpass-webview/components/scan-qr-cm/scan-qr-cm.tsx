import React, { useState, useRef } from "react"
import { View, TouchableOpacity, Dimensions, ViewStyle, Platform } from "react-native"
import { get } from "lodash"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Text } from "app/elements/text"
import { GalleryWithoutBorder, FlashOn, <PERSON>Off, CrossWhite } from "ichangi-fe/assets/icons"
import { choosePictureFromGallery } from "app/utils/media-helper"
import styles from "./index.styles"
import { RNHoleView } from "react-native-hole-view"
import { useIsFocused } from "@react-navigation/native"
import MyCamera, { MyCameraRef, CodeType } from "app/components/camera"

const { width, height } = Dimensions.get("window")
const WIDTH_SCAN_VIEW = width - 80
const HEIGHT_SCAN_VIEW = width - 80
const POSITION_X = 40
const POSITION_Y = height - (height / 2 + WIDTH_SCAN_VIEW / 2)

const holeViewStyle: ViewStyle = {
  position: "absolute",
  width: "100%",
  height: "100%",
  backgroundColor: Platform.OS === 'android' ? 'clear' : "rgba(0,0,0,0.8)",
}

const uploadQRButtonStyle: ViewStyle = {
  borderColor: "rgba(252,252,252, 0.3)",
  borderWidth: 2,
  borderRadius: 60,
  paddingHorizontal: 24,
  paddingVertical: 10,
  flexDirection: "row",
  alignItems: "center",
}

const ScanQRCMScreen = (props) => {
  const { onClosedSheet, handleUploadCM, handleQRScan } = props
  const cameraRef = useRef<MyCameraRef>(null)
  const [onFlash, setOnFlash] = useState(false)
  const inset = useSafeAreaInsets()

  const [needScan, setNeedScan] = useState(true)
  const codeTypeForQR: CodeType[] = ['qr']
  const codeTypeForBarCode: CodeType[] = ['code-128', 'itf', 'code-39', 'code-93', 'pdf-417']
  const onCodeScannedTypes = props?.isBottomSheetBarCodeScanVisible ? codeTypeForBarCode : codeTypeForQR
  const [cameraStarted, setCameraStarted] = useState(false)
  const isFocused = useIsFocused()

  const toggleFlashMode = () => {
    setOnFlash(!onFlash)
  }

  const handleGallerySelected = async () => {
    setTimeout(async () => {
      try {
        const imageData: any = await choosePictureFromGallery({ multiple: false })
        const base64Img = get(imageData, "base64")
        if (base64Img) {
          handleUploadCM(base64Img)
        }
      } catch (error) {
        console.log("Error while selecting the picture from gallery", error)
      }
    }, 200)
  }

  const renderHeader = () => {
    return (
      <View style={[styles.containerHeader, { height: inset.top + 60 }]}>
        <View style={styles.wrapHeader}>
          <View style={styles.leftHeader} />
          <TouchableOpacity onPress={toggleFlashMode}>
            {onFlash ? <FlashOn /> : <FlashOff />}
          </TouchableOpacity>
          <TouchableOpacity onPress={onClosedSheet} style={styles.rightHeader}>
            <CrossWhite width={13} height={13} />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderFooter = () => {
    return (
      <View style={styles.wrapFooter}>
        <TouchableOpacity style={uploadQRButtonStyle} disabled>
          <GalleryWithoutBorder />
          <Text text="Upload QR" style={styles.textUpload} onPress={handleGallerySelected} />
        </TouchableOpacity>
      </View>
    )
  }

  const readFromCamera = (codes, frame) => {
    if (codes?.length > 0 && codes?.[0]?.type && codes?.[0]?.value) {
      if (
        codeTypeForBarCode.includes(codes?.[0]?.type) ||
        codeTypeForQR.includes(codes?.[0]?.type)
      ) {
        setNeedScan(false)
        handleQRScan(codes?.[0]?.value)
      }
    }
  }

  const onCameraStarted = () => {
    setTimeout(() => {
      setCameraStarted(true)
    }, 200)
  }

  return (
    <>
      <View style={styles.containerUploadReceipt}>
        <MyCamera
          ref={cameraRef}
          flashMode={onFlash}
          isCodeScanned={needScan}
          onCodeScanned={readFromCamera}
          onCodeScannedTypes={onCodeScannedTypes}
          isActive={isFocused}
          onStarted={onCameraStarted}
        />
        {cameraStarted && <RNHoleView
          style={holeViewStyle}
          holes={[
            {
              x: POSITION_X,
              y: POSITION_Y,
              width: WIDTH_SCAN_VIEW,
              height: HEIGHT_SCAN_VIEW,
              borderRadius: 25,
            },
          ]}
        >
          {renderHeader()}
          <View style={styles.wrapBellowContent}>{renderFooter()}</View>
        </RNHoleView>}
      </View>
    </>
  )
}

export default ScanQRCMScreen
