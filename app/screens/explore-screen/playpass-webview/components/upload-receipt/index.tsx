import React, { useState, useRef } from "react"
import { View, TouchableOpacity, Platform, Image } from "react-native"
import { get } from "lodash"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Text } from "app/elements/text"
import { FlashOutLine, CrossBlue, CloseCross } from "ichangi-fe/assets/icons"
import { choosePictureFromGallery } from "app/utils/media-helper"
import { color } from "app/theme"
import RNFS from "react-native-fs"
import styles from "./index.styles"
import ImageResizer from "react-native-image-resizer"
import MyCamera, { MyCameraRef } from "app/components/camera"

const resizeImage = async ({ path, maxWidth, maxHeight, compressFormat, quality }) => {
  const response = await ImageResizer.createResizedImage(
    path,
    maxWidth,
    maxHeight,
    compressFormat,
    quality,
  )
  return response
}

const UploadReceiptScreen = (props) => {
  const { onClosedSheet, handleTakePicture } = props
  const cameraRef = useRef<MyCameraRef>(null)
  const [onFlash, setOnFlash] = useState(false)
  const inset = useSafeAreaInsets()

  const takePicture = async function () {
    const imageData = await cameraRef?.current?.takePicture?.()

    if (imageData?.path) {
      const imagePath = Platform.OS === "ios" ? imageData?.path : `file://${imageData?.path}`
      Image.getSize(imagePath, async (width, height) => {
        if (width && height) {
          const resizedImage: any = await resizeImage({
              path: imageData?.path,
              maxWidth: width < 1024 ? width : 1024,
              maxHeight: height < 1024 ? height : 1024,
              compressFormat: "JPEG",
              quality: 100,
          })
          const base64 = await RNFS.readFile(resizedImage.uri, "base64")
          if (base64) {
            handleTakePicture(base64)
          }
          setOnFlash(false)
        }
      })
    }
  }

  const toggleFlashMode = () => {
    setOnFlash(!onFlash)
  }

  const handleGallerySelected = async () => {
    setTimeout(async () => {
      try {
        const imageData: any = await choosePictureFromGallery({ multiple: false })
        const base64Img = get(imageData, "base64")
        if (base64Img) {
          handleTakePicture(base64Img)
        }
      } catch (error) {
        console.log("Error while selecting the picture from gallery", error)
      }
    }, 200)
  }
  
  const onPressClose = () => {
    onClosedSheet?.()
  }

  const renderHeader = () => {
    return (
      <View style={[styles.containerHeader, { height: inset.top + 44 }]}>
        <View style={styles.wrapHeader}>
          <View style={styles.leftHeader} />
          <TouchableOpacity onPress={toggleFlashMode}>
            {onFlash ? (
              <FlashOutLine style={{ color: color.palette.lighterGrey }} />
            ) : (
              <View>
                <FlashOutLine style={{ color: color.palette.lighterGrey }} />
                <CloseCross style={styles.closeCrossStyles} width={15} height={15} />
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity onPress={onPressClose}>
            <CrossBlue width={24} height={24} />
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderFooter = () => {
    return (
      <View style={styles.wrapFooter}>
        <View style={styles.wrapTakePictureButton}>
          <TouchableOpacity onPress={takePicture} style={styles.takePictureButton} />
        </View>
        <View>
          <Text
            text="Upload from Photos"
            style={styles.textUpload}
            onPress={handleGallerySelected}
          />
        </View>
      </View>
    )
  }

  return (
    <>
      <View style={styles.containerUploadReceipt}>
        <MyCamera
          ref={cameraRef}
          flashMode={onFlash}
        />
        {!!cameraRef?.current?.isCameraReady ? (
          <View style={styles.layerAction}>
            {renderHeader()}
            <View style={styles.wrapBellowContent}>{renderFooter()}</View>
          </View>
        ) : null}
      </View>
    </>
  )
}

export default UploadReceiptScreen
