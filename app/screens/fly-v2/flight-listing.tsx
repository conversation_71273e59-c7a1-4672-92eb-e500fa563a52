import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs"
import { RouteProp, useFocusEffect, useRoute } from "@react-navigation/native"
import { Text } from "app/elements/text/text"
import { translate } from "app/i18n"
import { FlightListingCreators } from "app/redux/flightListingRedux"
import SearchActions from "app/redux/searchRedux"
import { color } from "app/theme"
import { ArrowLeftV2 } from "assets/icons"
import { isEmpty } from "lodash"
import React, { useEffect, useMemo, useRef, useState } from "react"
import {
  ActivityIndicator,
  Image,
  InteractionManager,
  LayoutChangeEvent,
  Platform,
  StatusBar,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native"
import { GestureHandlerRootView } from "react-native-gesture-handler"
import Animated, {
  interpolate,
  useAnimatedStyle,
  withSpring,
  withTiming
} from "react-native-reanimated"
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context"
import { useDispatch } from "react-redux"
import ArrivalScreen from "./arrival"
import FlyListingTab from "./components/fly-listing-tab"
import SaveFlight from "./components/save-flight"
import { SearchBottomSheet } from "./components/search-bottom-sheet"
import {
  FlightListingContextProvider,
  useFlightListingContext,
} from "./contexts/flight-listing-context"
import DepartureScreen from "./departure"
import styles from "./flight-listing.styles"

export const GESTURE_THRESHOLD = 90

type FlightListingRouteParams = {
  screen?: string
}

const MARGIN_TOP = Platform.select({ ios: 0, android: 20 })

const Tab = createMaterialTopTabNavigator()

const SCREEN_NAME = "FlightListing"

const FlightListingContent = ({ navigation }) => {
  const route = useRoute<RouteProp<{ params: FlightListingRouteParams }, "params">>()
  const initialRouteName = route.params?.screen
  const dispatch = useDispatch()
  const { top } = useSafeAreaInsets()

  const {
    searchBottomSheetRef,
    shouldShowSearchBottomSheetOnFocus,
    isRefreshing,
    lastUpdated,
    scrollY,
    setSelectedTab,
    contentPosition,
  } = useFlightListingContext()

  const [titleHeight, setTitleHeight] = useState(0)

  const arrivalListRef = useRef(null)
  const departureListRef = useRef(null)

  const containerAnimatedStyle = useAnimatedStyle(() => {
    const clampMarginTop = Math.min(Math.max(titleHeight, 60), 120)

    const marginTop = interpolate(
      scrollY.value,
      [0, clampMarginTop],
      [clampMarginTop + MARGIN_TOP, MARGIN_TOP],
      "clamp",
    )

    const translateY = interpolate(contentPosition.value, [0, GESTURE_THRESHOLD], [0, 60])

    const springMarginTop = withSpring(marginTop, {
      damping: 15,
      stiffness: 50,
    })

    return {
      marginTop: springMarginTop,
      transform: [{ translateY }],
    }
  })

  const headerAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, titleHeight], [1, 0], "clamp")

    return {
      opacity,
    }
  })

  const backButtonStyle = useMemo<ViewStyle>(() => {
    return {
      flexDirection: "row",
      alignItems: "center",
      position: "absolute",
      zIndex: 999,
      top: top + MARGIN_TOP,
      height: 32,
      left: 16,
    }
  }, [top])

  const headerStyle = useMemo<ViewStyle>(() => {
    return {
      position: "absolute",
      alignSelf: "center",
      top: top + MARGIN_TOP,
      width: "100%",
    }
  }, [top, headerAnimatedStyle])

  const refreshIndicatorAnimatedStyle = useAnimatedStyle(() => {
    const polateOpacity = interpolate(contentPosition.value, [0, GESTURE_THRESHOLD], [0, 1])
    return {
      opacity: polateOpacity,
    }
  })

  const onLayoutHeader = (e: LayoutChangeEvent) => {
    setTitleHeight(e.nativeEvent.layout.height)
  }

  useFocusEffect(() => {
    // We hide the search bottom sheet when the user navigates away from the flight listing screen.
    // When the user returns to the flight listing screen, we show the search bottom sheet if the user had previously opened it.
    if (shouldShowSearchBottomSheetOnFocus.current) {
      searchBottomSheetRef.current?.open()
      shouldShowSearchBottomSheetOnFocus.current = false
    }
  })

  useEffect(() => {
    dispatch(SearchActions.resetSearchKeywordCollection())
    dispatch(SearchActions.popularSearchKeywordRequest())
    setSelectedTab(initialRouteName)
    return () => {
      dispatch(SearchActions.setSearchKeyword(""))
      dispatch(
        FlightListingCreators.setFlightListingFilter({
          direction: initialRouteName,
        }),
      )
    }
  }, [])

  useEffect(() => {
    if (contentPosition.value && !isRefreshing) {
      InteractionManager.runAfterInteractions(() => {
        contentPosition.value = withTiming(0)
      })
    }
  }, [isRefreshing])

  return (
    <GestureHandlerRootView>
      <View
        style={styles.container}
        testID={`${SCREEN_NAME}__FlightListingScreen`}
        accessibilityLabel={`${SCREEN_NAME}__FlightListingScreen`}
      >
        <StatusBar translucent backgroundColor="transparent" />

        <Image
          source={require("./flight-listing-bg.jpg")}
          style={styles.fixedBackground}
          resizeMode="cover"
        />

        {/* Back button & Title */}
        <SafeAreaView style={styles.headerSafeArea} edges={["top", "left", "right"]}>
          <TouchableOpacity style={backButtonStyle} onPress={() => navigation.goBack()}>
            <ArrowLeftV2 color={color.palette.whiteGrey} />
          </TouchableOpacity>
          <Animated.View style={[headerStyle, headerAnimatedStyle]} onLayout={onLayoutHeader}>
            <View style={styles.headerRow}>
              <Text style={styles.headerTitle} tx="flightLanding.flightInformation" />
            </View>
            <View style={styles.realtimeCard}>
              <Text
                style={[styles.liveText, { opacity: !isRefreshing ? 1 : 0 }]}
                text={
                  isEmpty(lastUpdated)
                    ? '-'
                    : translate("flightListingV2.lastUpdated") + " " + lastUpdated
                }
              ></Text>
              <Animated.View
                style={[styles.loadingIndicatorContainer, refreshIndicatorAnimatedStyle]}
              >
                <ActivityIndicator
                  style={styles.loadingIndicator}
                  size="small"
                  color={color.palette.whiteGrey}
                />
              </Animated.View>
            </View>
          </Animated.View>
          {/* Tab Navigator */}
          <Animated.View style={[styles.flightContainer, containerAnimatedStyle]}>
            <Tab.Navigator
              initialRouteName={initialRouteName}
              tabBar={(props) => <FlyListingTab {...props} />}
              screenOptions={{
                swipeEnabled: false,
                sceneStyle: {
                  backgroundColor: "transparent",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                },
              }}
            >
              <Tab.Screen
                name="ARR"
                options={{
                  tabBarLabel: translate("flightLanding.arrivalTabTitle"),
                  tabBarTestID: `${SCREEN_NAME}_arrivalTab`,
                  tabBarAccessibilityLabel: `${SCREEN_NAME}_arrivalTab`,
                  lazy: true,
                }}
              >
                {(props) => <ArrivalScreen {...props} ref={arrivalListRef} />}
              </Tab.Screen>
              <Tab.Screen
                name="DEP"
                options={{
                  tabBarLabel: translate("flightLanding.departureTabTitle"),
                  tabBarTestID: `${SCREEN_NAME}_departureTab`,
                  tabBarAccessibilityLabel: `${SCREEN_NAME}_departureTab`,
                  lazy: true,
                }}
              >
                {(props) => <DepartureScreen {...props} ref={departureListRef} />}
              </Tab.Screen>
            </Tab.Navigator>
            <SaveFlight />
          </Animated.View>
        </SafeAreaView>
        <SearchBottomSheet ref={searchBottomSheetRef} />
      </View>
    </GestureHandlerRootView>
  )
}

export const FlightListing = ({ navigation }) => {
  return (
    <FlightListingContextProvider>
      <FlightListingContent navigation={navigation} />
    </FlightListingContextProvider>
  )
}
