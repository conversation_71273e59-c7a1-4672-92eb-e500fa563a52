import { useEffect, useState, useMemo } from "react"
import path from "app/services/api/apis.json"
import restApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { FacilityServiceApiItem } from "./types"
import { mapApiItemToFacilityServiceItem } from "./data-mapping"
import type { IFacilityServiceItem } from "./components/facility-service-item"
import { Location, useFacilitiesServicesContext } from "./context/facilities-services-context"
import { InteractionManager } from "react-native"

export function useFacilitiesServices() {
  const [allFacilities, setAllFacilities] = useState<IFacilityServiceItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [hasApiError, setHasApiError] = useState(false)
  const { locations, publicArea, transitArea } = useFacilitiesServicesContext()

  const fetchFacilities = async () => {
    setHasApiError(false)
    try {
      const paramsArray = path.getFacilitiesServices.split(" ")
      const method = paramsArray[0] || "GET"
      const url = env()?.AEM_URL + paramsArray[1]
      const response = await restApi({ url, method })
      const items: FacilityServiceApiItem[] = Array.isArray(response?.data?.list)
        ? response?.data?.list
        : []
      setAllFacilities(items.map(mapApiItemToFacilityServiceItem))
    } catch (err) {
      setHasApiError(true)
    }
  }

  useEffect(() => {
    const initialFetch = async () => {
      setLoading(true)
      await fetchFacilities()
      setLoading(false)
    }
    
    InteractionManager.runAfterInteractions(() => {
      initialFetch()
    })
  }, [])

  // Filter facilities based on selected filters
  const facilities = useMemo(() => {
    return allFacilities.filter(facility => {
      // Filter by location
      if (locations.length > 0 && locations.length !== 5) {
        const hasSelectedLocation = facility.locations.some(loc => locations.includes(loc as Location))
        if (!hasSelectedLocation) return false
      }
      // Filter by area type
      if (publicArea && !facility.isPublicArea) return false
      if (transitArea && !facility.isTransitArea) return false

      return true
    })
  }, [allFacilities, locations, publicArea, transitArea])

  const refresh = async () => {
    setRefreshing(true)
    await Promise.all([
      fetchFacilities(),
      new Promise(resolve => setTimeout(resolve, 1000)),
    ])
    setRefreshing(false)
  }

  return { facilities, loading, hasApiError, refresh, refreshing }
}
