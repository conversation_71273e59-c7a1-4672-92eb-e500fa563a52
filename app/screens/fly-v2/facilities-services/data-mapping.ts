import { FacilityServiceApiItem } from "./types"
import { IFacilityServiceItem } from "./components/facility-service-item"
import { env } from "app/config/env-params"

const mapLocationTag = (tagTitle: string | undefined): string | null => {
  if (!tagTitle) return null;
  const lower = tagTitle.trim().toLowerCase();
  if (lower === "all") return null;
  if (lower === "terminal 1") return "T1";
  if (lower === "terminal 2") return "T2";
  if (lower === "terminal 3") return "T3";
  if (lower === "terminal 4") return "T4";
  if (lower === "jewel") return "Jewel";
  return null;
};

export const mapApiItemToFacilityServiceItem = (
  item: FacilityServiceApiItem,
): IFacilityServiceItem => {
  // Extract and normalize locations
  const locations = item.location
    ?.map(loc => mapLocationTag(loc.tagTitle))
    .filter((loc): loc is string => loc !== null);

  // Extract and normalize areas
  const areas = item.area?.map(a => a.tagTitle.toLowerCase()) || [];
  const isPublicArea = areas.some(area => area.includes("public"));
  const isTransitArea = areas.some(area => area.includes("transit"));

  return {
    id: item.contentId,
    title: item.title,
    locations: locations || [],
    locationDisplayText: locations?.join(", "),
    isPublicArea,
    isTransitArea,
    image: item.image ? env()?.AEM_URL + item.image : undefined,
    navigation: item.navigation
      ? { type: item.navigation.type, value: item.navigation.value }
      : undefined,
  }
}
