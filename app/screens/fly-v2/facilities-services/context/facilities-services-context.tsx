import React, { createContext, useContext, useMemo, useState } from 'react'

export const FacilitiesServicesContext = createContext<FilterContextValue | null>(null)

interface Props {
  children: React.ReactNode
}

export const AVAILABLE_LOCATIONS = ['Jewel', 'T1', 'T2', 'T3', 'T4'] as const
export type Location = typeof AVAILABLE_LOCATIONS[number]

export interface FilterState {
  locations: Location[]
  publicArea: boolean
  transitArea: boolean
}

export interface FilterContextValue extends FilterState {
  // Location actions
  setLocations: (locations: Location[]) => void
  toggleLocation: (location: Location) => void
  
  // Area type actions
  togglePublicArea: () => void
  toggleTransitArea: () => void
  
  // Reset
  resetFilters: () => void
}

export const initialFilterState: FilterState = {
  locations: [],
  publicArea: false,
  transitArea: false
}


export const FacilitiesServicesProvider: React.FC<Props> = ({ children }) => {
  const [state, setState] = useState<FilterState>(initialFilterState)

  const toggleLocation = (location: Location) => {
    setState(prev => ({
      ...prev,
      locations: prev.locations.includes(location)
        ? prev.locations.filter(loc => loc !== location)
        : [...prev.locations, location]
    }))
  }

  const setLocations = (locations: Location[]) => {
    setState(prev => ({
      ...prev,
      locations
    }))
  }

  const togglePublicArea = () => {
    setState(prev => ({
      ...prev,
      publicArea: !prev.publicArea
    }))
  }

  const toggleTransitArea = () => {
    setState(prev => ({
      ...prev,
      transitArea: !prev.transitArea
    }))
  }

  const resetFilters = () => {
    setState(initialFilterState)
  }

  const contextValue = useMemo<FilterContextValue>(() => ({
    ...state,
    setLocations,
    toggleLocation,
    togglePublicArea,
    toggleTransitArea,
    resetFilters
  }), [state, setLocations, toggleLocation, togglePublicArea, toggleTransitArea, resetFilters])

  return (
    <FacilitiesServicesContext.Provider value={contextValue}>
      {children}
    </FacilitiesServicesContext.Provider>
  )
}

export const useFacilitiesServicesContext = (): FilterContextValue => {
  const context = useContext(FacilitiesServicesContext)
  if (!context) {
    throw new Error('useFacilitiesServicesContext must be used within a FacilitiesServicesProvider')
  }
  return context
}
