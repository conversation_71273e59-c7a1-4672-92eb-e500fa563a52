import React from "react"
import { View, StyleSheet, Image, RefreshControl } from "react-native"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import Animated, {
  useSharedValue,
  useAnimatedScroll<PERSON>andler,
  interpolate,
  useAnimatedStyle,
  Extrapolation,
} from "react-native-reanimated"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import type { PrimaryParamList } from "app/navigators/main-navigator"
import { color } from "app/theme/color"
import { NavigationConstants } from "app/utils/constants"
import { useFacilitiesServices } from "./useFacilitiesServices"
import { FacilitiesServicesProvider } from "./context/facilities-services-context"
import { FacilityServiceItem, FacilityServiceItemSkeleton } from "./components/facility-service-item"
import { AnimatedHeader } from "./components/animated-header"
import {
  HEADER_HEIGHT,
  FILTER_BAR_HEIGHT,
  SCREEN_WIDTH,
  HEADER_BACKGROUND_HEIGHT_SCALED,
  HEADER_BOTTOM,
} from "./constants"
import { FilterBar } from "./components/filter-bar"
import { useInternetConnection } from "../hooks/use-internet-connection"
import { EmptyState } from "../components/empty-state"

const FacilitiesServicesContent = () => {
  const navigation = useNavigation<StackNavigationProp<PrimaryParamList, "facilitiesServices">>()
  const { facilities, loading, hasApiError, refreshing, refresh } = useFacilitiesServices()
  const { isInternetConnected, checkInternetConnection } = useInternetConnection()
  const insets = useSafeAreaInsets()

  const scrollY = useSharedValue(0)
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y
    },
  })
  const borderRadiusStyle = useAnimatedStyle(() => ({
    borderTopLeftRadius: interpolate(scrollY.value, [0, 15], [20, 0], Extrapolation.CLAMP),
    borderTopRightRadius: interpolate(scrollY.value, [0, 15], [20, 0], Extrapolation.CLAMP),
  }))
  const stickyFilterBarAnimatedStyle = useAnimatedStyle(() => ({
    opacity: scrollY.value > 15 ? 1 : 0,
    pointerEvents: scrollY.value > 15 ? "auto" : "none",
  }))

  const shouldShowFilterBar = !loading && !refreshing && !hasApiError

  const handleGoBack = () => {
    navigation?.goBack()
  }

  const handleSearch = () => {
    // @ts-ignore
    navigation.navigate(NavigationConstants.search, {
      focusTextInput: true,
    })
  }

  function renderContent() {
    if (!isInternetConnected) {
      return (
        <View style={styles.noInternetContainer}>
          <EmptyState.NoInternet onPressReload={checkInternetConnection} />
        </View>
      )
    }
    if (loading || refreshing) {
      return (
        <View style={styles.listSkeletonsContainer}>
          {Array.from({ length: 5 }).map((_, index) => (
            <FacilityServiceItemSkeleton key={index} />
          ))}
        </View>
      )
    }
    if (hasApiError) {
      return (
        <View style={styles.apiErrorContainer}>
          <EmptyState.ApiError onPressReload={refresh} />
        </View>
      )
    }
    if (facilities.length === 0) {
      return (
        <View style={styles.noResultsContainer}>
          <EmptyState.NoResults />
        </View>
      )
    }
    return (
      <View style={styles.listItemsContainer}>
        {facilities.map((item) => (
          <FacilityServiceItem key={item.id} item={item} />
        ))}
      </View>
    )
  }

  return (
    <View style={styles.screenContainer}>
      <Image
        source={require("./facilities-services-bg.jpg")}
        style={styles.fixedBackground}
        resizeMode="contain"
      />
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: insets.bottom }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={refresh} progressViewOffset={100} tintColor={color.palette.whiteGrey} />}
      >
        <View style={styles.pseduoHeader} />
        <Animated.View style={[styles.contentContainer, borderRadiusStyle]}>
          {shouldShowFilterBar ? <FilterBar onSearchPress={handleSearch} /> : <View style={styles.pseudoFilterBar} />}
          {renderContent()}
        </Animated.View>
      </Animated.ScrollView>

      {shouldShowFilterBar && (
        <Animated.View style={[styles.stickyFilterBar, stickyFilterBarAnimatedStyle]}>
          <FilterBar onSearchPress={handleSearch} />
        </Animated.View>
      )}
      {/* Header need to be render after filter bar so its shadow can overlay filter bar */}
      <AnimatedHeader scrollY={scrollY} onBackPress={handleGoBack} />
    </View>
  )
}

export const FacilitiesServices = () => {
  return (
    <FacilitiesServicesProvider>
      <FacilitiesServicesContent />
    </FacilitiesServicesProvider>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
  },
  fixedBackground: {
    width: SCREEN_WIDTH,
    height: HEADER_BACKGROUND_HEIGHT_SCALED,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pseduoHeader: {
    height: HEADER_HEIGHT,
    marginBottom: 15,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
  },
  listSkeletonsContainer: {
    flex: 1,
    gap: 24,
    marginTop: 16,
  },
  listItemsContainer: {
    flex: 1,
    gap: 20,
    marginTop: 16,
  },
  stickyFilterBar: {
    position: "absolute",
    top: HEADER_BOTTOM,
    left: 0,
    right: 0,
    height: FILTER_BAR_HEIGHT,
    zIndex: 1000,
  },
  noInternetContainer: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 24,
    marginTop: 36,
  },
  apiErrorContainer: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 24,
    marginTop: 40,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 24,
    marginTop: 40,
  },
  pseudoFilterBar: {
    height: FILTER_BAR_HEIGHT,
  },
})