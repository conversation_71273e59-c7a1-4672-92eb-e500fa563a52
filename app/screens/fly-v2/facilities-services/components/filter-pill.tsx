import React from "react"
import { StyleSheet, TouchableOpacity, View } from "react-native"
import { color } from "app/theme"
import { newPresets, Text } from "app/elements/text"
import { ArrowDown, CloseCross } from "assets/icons"

interface SelectProps {
  icon?: React.ReactNode
  label: string
  active?: boolean
  onPress?: () => void
  testID?: string
}

const Select: React.FC<SelectProps> = ({ label, active = false, onPress, icon, testID }) => {
  return (
    <TouchableOpacity
      style={[styles.selectContainer, active && styles.selectContainerActive]}
      onPress={onPress}
      testID={testID}
      accessibilityLabel={testID}
    >
      {icon &&
        React.cloneElement(icon as React.ReactElement, {
          width: 16,
          height: 16,
          color: active ? color.palette.lightPurple : color.palette.darkestGrey,
        })}

      <Text style={[styles.text, active && styles.textActive]}>{label}</Text>

      <ArrowDown
        width={16}
        height={16}
        color={active ? color.palette.lightPurple : color.palette.darkestGrey}
      />
    </TouchableOpacity>
  )
}

interface ToggleProps {
  icon?: React.ReactNode
  label: string
  active?: boolean
  onPress?: () => void
  testID?: string
}

const Toggle: React.FC<ToggleProps> = ({ label, active = false, onPress, icon, testID }) => {
  return (
    <TouchableOpacity
      style={[styles.toggleContainer, active && styles.toggleContainerActive]}
      onPress={onPress}
      testID={testID}
      accessibilityLabel={testID}
    >
      {icon && (
        <View style={{ marginRight: 3 }}>
          {React.cloneElement(icon as React.ReactElement, {
            width: 16,
            height: 16,
            color: active ? color.palette.lightPurple : color.palette.darkestGrey,
          })}
        </View>
      )}

      <Text style={[styles.text, active && styles.textActive]}>{label}</Text>

      {active && (
        <View style={{ marginLeft: 2 }}>
          <CloseCross width={16} height={16} color={color.palette.lightPurple} opacity={0.5} />
        </View>
      )}
    </TouchableOpacity>
  )
}

export const FilterPill = {
  Select,
  Toggle,
}

const styles = StyleSheet.create({
  selectContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 30,
    paddingHorizontal: 10,
    borderRadius: 99,
    borderWidth: 1,
    gap: 2,
    borderColor: color.palette.lighterGrey,
    backgroundColor: color.palette.whiteGrey,
  },
  toggleContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 30,
    paddingHorizontal: 9,
    borderRadius: 99,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
    backgroundColor: color.palette.whiteGrey,
  },
  selectContainerActive: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
  },
  toggleContainerActive: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
    paddingRight: 5,
  },
  text: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
    fontSize: 14,
  },
  textActive: {
    color: color.palette.lightPurple,
  },
})
