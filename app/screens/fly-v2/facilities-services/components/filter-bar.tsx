import React, { FC } from "react"
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native"
import { color } from "app/theme/color"
import { SearchIconV2, LocationOutline, FilterV2 } from "assets/icons"
import { FilterPill } from "./filter-pill"
import { FilterSheet } from "./filter-sheet"
import { useFacilitiesServicesContext } from "../context/facilities-services-context"
import { translate } from "app/i18n"
import { useModal } from "app/hooks/useModal"
import { FILTER_BAR_HEIGHT } from "../constants"

interface FilterBar {
  onSearchPress: () => void
}

export const FilterBar: FC<FilterBar> = (props) => {
  const { onSearchPress } = props
  const { openModal: openFilterSheet } = useModal("facilitiesServicesFilter")
  const { 
    locations,
    publicArea,
    transitArea,
    togglePublicArea,
    toggleTransitArea
  } = useFacilitiesServicesContext()

  const hasLocationFilter = locations.length > 0
  const hasFilter = hasLocationFilter || publicArea || transitArea

  return (
    <View style={styles.filtersContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}
      >
        <View style={styles.filtersRow}>
          <TouchableOpacity style={styles.filterIconButton} onPress={onSearchPress}>
            <SearchIconV2 width={16} height={16} color={color.palette.darkestGrey} />
          </TouchableOpacity>

          <TouchableOpacity style={[styles.filterIconButton, hasFilter && styles.activeFilterIconButton]} onPress={openFilterSheet}>
            <FilterV2 width={16} height={16} color={hasFilter ? color.palette.lightPurple : color.palette.darkestGrey} />
          {hasFilter && <View style={styles.activeDot} />}

          </TouchableOpacity>

          <FilterPill.Select
            label={hasLocationFilter ? locations.sort().join(", ") : translate("facilitiesServices.location")}
            icon={<LocationOutline />}
            active={hasLocationFilter}
            testID="facilities-services-location-filter"
            onPress={openFilterSheet}
          />

          <FilterPill.Toggle
            label={translate("facilitiesServices.public")}
            active={publicArea}
            onPress={() => togglePublicArea()}
            testID="facilities-services-public-filter"
          />

          <FilterPill.Toggle
            label={translate("facilitiesServices.transit")}
            active={transitArea}
            onPress={() => toggleTransitArea()}
            testID="facilities-services-transit-filter"
          />

          <FilterSheet />
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  filtersContainer: {
    height: FILTER_BAR_HEIGHT,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  filtersRow: {
    flexDirection: "row",
    gap: 4,
    alignItems: "center",
  },
  filterIconButton: {
    height: 30,
    width: 36,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 99,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
  },
  activeFilterIconButton: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
  },
  activeDot: {
    width: 7,
    height: 7,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    backgroundColor: color.palette.lightPurple,
    borderRadius: 10,
    position: "absolute",
    top: 0,
    right: 0,
  },
})
