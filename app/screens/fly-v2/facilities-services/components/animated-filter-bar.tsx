import React, { <PERSON> } from "react"
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native"
import { color } from "app/theme/color"
import { SearchIconV2, LocationOutline, FilterV2 } from "assets/icons"
import { FilterPill } from "./filter-pill"
import { FilterSheet } from "./filter-sheet"
import { useFacilitiesServicesContext } from "../context/facilities-services-context"
import Animated, { useAnimatedStyle, SharedValue } from "react-native-reanimated"
import { FILTER_BAR_TOP, HEADER_BOTTOM } from "../constants"
import { translate } from "app/i18n"
import { useModal } from "app/hooks/useModal"
interface AnimatedFilterBarProps {
  scrollY: SharedValue<number>
  onSearchPress: () => void
}

export const AnimatedFilterBar: FC<AnimatedFilterBarProps> = (props) => {
  const { scrollY, onSearchPress } = props
  const { openModal: openFilterSheet } = useModal("facilitiesServicesFilter")
  const { 
    locations,
    publicArea,
    transitArea,
    togglePublicArea,
    toggleTransitArea
  } = useFacilitiesServicesContext()

  const hasLocationFilter = locations.length > 0
  const hasFilter = hasLocationFilter || publicArea || transitArea

  const filtersAnimatedStyle = useAnimatedStyle(() => {
    // If user scroll down, move the filter bar together with the content
    if (scrollY.value <= 0) {
      return { top: FILTER_BAR_TOP + Math.abs(scrollY.value) }
    }
    // If user scroll up, move the filter bar together with the content
    // and stick if filter bar reach the bottom of header
    return { top: Math.max(HEADER_BOTTOM, FILTER_BAR_TOP - scrollY.value) }
  })

  return (
    <Animated.View style={[styles.filtersContainer, filtersAnimatedStyle]}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}
      >
        <View style={styles.filtersRow}>
          <TouchableOpacity style={styles.filterIconButton} onPress={onSearchPress}>
            <SearchIconV2 width={16} height={16} color={color.palette.darkestGrey} />
          </TouchableOpacity>

          <TouchableOpacity style={[styles.filterIconButton, hasFilter && styles.activeFilterIconButton]} onPress={openFilterSheet}>
            <FilterV2 width={16} height={16} color={hasFilter ? color.palette.lightPurple : color.palette.darkestGrey} />
          {hasFilter && <View style={styles.activeDot} />}

          </TouchableOpacity>

          <FilterPill.Select
            label={hasLocationFilter ? locations.sort().join(", ") : translate("facilitiesServices.location")}
            icon={<LocationOutline />}
            active={hasLocationFilter}
            testID="facilities-services-location-filter"
            onPress={openFilterSheet}
          />

          <FilterPill.Toggle
            label={translate("facilitiesServices.public")}
            active={publicArea}
            onPress={() => togglePublicArea()}
            testID="facilities-services-public-filter"
          />

          <FilterPill.Toggle
            label={translate("facilitiesServices.transit")}
            active={transitArea}
            onPress={() => toggleTransitArea()}
            testID="facilities-services-transit-filter"
          />

          <FilterSheet />
        </View>
      </ScrollView>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  filtersContainer: {
    position: "absolute",
    top: FILTER_BAR_TOP,
    left: 0,
    right: 0,
    height: 54,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  filtersRow: {
    flexDirection: "row",
    gap: 4,
    alignItems: "center",
  },
  filterIconButton: {
    height: 30,
    width: 36,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 99,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
  },
  activeFilterIconButton: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
  },
  activeDot: {
    width: 7,
    height: 7,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    backgroundColor: color.palette.lightPurple,
    borderRadius: 10,
    position: "absolute",
    top: 0,
    right: 0,
  },
})
