import React from "react"
import { TouchableOpacity, View, StyleSheet } from "react-native"
import { Text, newPresets } from "app/elements/text"
import { color } from "app/theme"
import { getDotUnicode } from "app/utils/constants"
import { useHandleNavigation } from "app/utils/navigation-helper"

// TODO: Consider moving this to a shared component
import ItemImage from "app/screens/search-v2/components/item-image"

export interface IFacilityServiceItem {
  id?: string
  title?: string
  locations: string[]
  locationDisplayText?: string
  isPublicArea: boolean
  isTransitArea: boolean
  image?: string
  navigation?: { type?: string; value?: any }
}

interface FacilityServiceItemProps {
  item: IFacilityServiceItem
}

const getAreaString = (isPublicArea: boolean, isTransitArea: boolean): string => {
  const areas = []
  if (isPublicArea) areas.push("Public")
  if (isTransitArea) areas.push("Transit")
  return areas.length ? areas.join(", ") : "-"
}

export const FacilityServiceItem: React.FC<FacilityServiceItemProps> = ({ item }) => {
  const { handleNavigation } = useHandleNavigation("FLIGHT_LANDING_V2_FACILITIES_SERVICES")
  const hasLocation = !!item?.locationDisplayText
  const hasArea = item.isPublicArea || item.isTransitArea
  const showDot = hasLocation && hasArea

  const handlePress = () => {
    const type = item?.navigation?.type
    const value = item?.navigation?.value
    if (type && value) {
      handleNavigation(type, value)
    }
  }

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <ItemImage
        defaultSource={require("../../default-no-image.png")}
        style={styles.image}
        source={{ uri: item?.image }}
      />
      <View style={styles.content}>
        <View style={styles.textColumn}>
          <Text style={styles.title} numberOfLines={2} textBreakStrategy="highQuality">
            {item?.title ?? "-"}
          </Text>
          <View style={styles.locationAreaRow}>
            {hasLocation && (
              <Text preset="caption1Bold" style={styles.location}>
                {item.locationDisplayText}
              </Text>
            )}
            {showDot && <Text style={styles.dot}>{getDotUnicode()}</Text>}
            {hasArea && (
              <Text style={styles.area} preset="caption1Regular">
                {getAreaString(item.isPublicArea, item.isTransitArea)}
              </Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}

export { FacilityServiceItemSkeleton } from "./skeleton"

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingLeft: 20,
    paddingRight: 40,
    gap: 16,
  },
  image: {
    width: 48,
    height: 48,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    alignItems: "flex-start",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
    minHeight: 66,
    paddingBottom: 24,
  },
  textColumn: {
    flex: 1,
  },
  title: {
    ...newPresets.bodyTextBlackBold,
    color: color.palette.almostBlackGrey,
    marginBottom: 4,
    fontWeight: "900",
  },
  locationAreaRow: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  location: {
    color: color.palette.almostBlackGrey,
    textAlign: "left",
  },
  area: {
    color: color.palette.darkestGrey,
    textAlign: "left",
  },
  dot: {
    ...newPresets.caption1Regular,
    color: color.palette.darkestGrey,
    marginHorizontal: 4,
  },
})
