import { color } from "app/theme"
import React from "react"
import { View } from "react-native"
import { StyleSheet } from "react-native"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"

export const FacilityServiceItemSkeleton = () => {
  const shimmerColors = [color.palette.lighterGrey, color.background, color.palette.lighterGrey]

  return (
    <View style={styles.container}>
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={shimmerColors}
        shimmerStyle={styles.imagePlaceholder}
      />
      <View style={styles.content}>
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={shimmerColors}
          shimmerStyle={styles.placeholder1}
        />
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={shimmerColors}
          shimmerStyle={styles.placeholder2}
        />
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={shimmerColors}
          shimmerStyle={styles.placeholder3}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingLeft: 20,
    paddingRight: 40,
    height: 84,
    gap: 16,
  },
  imagePlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: color.palette.lighterGrey,
  },
  content: {
    flex: 1,
    alignSelf: "stretch",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  placeholder1: {
    width: "100%",
    height: 12,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
    marginBottom: 12,
  },
  placeholder2: {
    width: "50%",
    height: 12,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
    marginBottom: 12,
  },
  placeholder3: {
    width: "30%",
    height: 12,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
  },
})
