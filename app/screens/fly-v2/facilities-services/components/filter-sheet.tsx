import React, { useState, useEffect } from "react"
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native"
import { Check, CloseCross, PublicAreaIconV2, TransitAreaIcon } from "assets/icons"
import { color } from "app/theme"
import { newPresets, Text } from "app/elements/text"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { FilterPill } from "./filter-pill"
import {
  AVAILABLE_LOCATIONS,
  Location,
  useFacilitiesServicesContext,
} from "../context/facilities-services-context"
import { Checkbox } from "app/elements/checkbox/checkbox"
import { LinearGradient } from "react-native-linear-gradient"
import { translate } from "app/i18n"
import { useModal } from "app/hooks/useModal"

const SCREEN_HEIGHT = Dimensions.get("window").height
const SHEET_HEIGHT = SCREEN_HEIGHT - 64

export const FilterSheet = () => {
  const { isModalVisible, closeModal } = useModal("facilitiesServicesFilter")
  const {
    locations: contextLocations,
    publicArea: contextPublicArea,
    transitArea: contextTransitArea,
    setLocations: setContextLocations,
    togglePublicArea,
    toggleTransitArea,
  } = useFacilitiesServicesContext()
  // Local state for the sheet
  const [locations, setLocations] = useState(contextLocations)
  const [publicArea, setPublicArea] = useState(contextPublicArea)
  const [transitArea, setTransitArea] = useState(contextTransitArea)

  // Sync local state with context when context changes
  useEffect(() => {
    if (!isModalVisible) {
      setLocations(contextLocations)
      setPublicArea(contextPublicArea)
      setTransitArea(contextTransitArea)
    }
  }, [isModalVisible, contextLocations, contextPublicArea, contextTransitArea])

  const handleClearAll = () => {
    setLocations([])
    setPublicArea(false)
    setTransitArea(false)
  }

  const handleApply = () => {
    setContextLocations(locations)
    if (publicArea !== contextPublicArea) togglePublicArea()
    if (transitArea !== contextTransitArea) toggleTransitArea()
    closeModal()
  }

  const toggleLocation = (location: Location) => {
    setLocations((prev) =>
      prev.includes(location) ? prev.filter((loc) => loc !== location) : [...prev, location],
    )
  }

  const toggleAllLocations = () => {
    setLocations(locations.length === AVAILABLE_LOCATIONS.length ? [] : [...AVAILABLE_LOCATIONS])
  }

  return (
    <BottomSheet
      isModalVisible={Boolean(isModalVisible)}
      onClosedSheet={closeModal}
      stopDragCollapse
      onBackPressHandle={closeModal}
      containerStyle={styles.container}
      animationInTiming={300}
      animationOutTiming={300}
      openPendingModal
    >
      {/* Header */}
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle} tx="facilitiesServices.filters" />
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={closeModal}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          <CloseCross width={24} height={24} color={color.palette.darkestGrey} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Location Title */}
        <Text tx="facilitiesServices.location" style={styles.sectionTitle} />

        {/* Area Type Pills */}
        <View style={styles.pillsRow}>
          <FilterPill.Toggle
            icon={<PublicAreaIconV2 />}
            label={translate("facilitiesServices.publicArea")}
            active={publicArea}
            onPress={() => setPublicArea(!publicArea)}
            testID="filter-sheet-public-area"
          />
          <FilterPill.Toggle
            icon={<TransitAreaIcon />}
            label={translate("facilitiesServices.transitArea")}
            active={transitArea}
            onPress={() => setTransitArea(!transitArea)}
            testID="filter-sheet-transit-area"
          />
        </View>

        {/* Location Checkboxes */}
        <View style={styles.checkboxList}>
          <TouchableOpacity style={styles.checkboxRow} onPress={toggleAllLocations}>
            <Checkbox
              value={locations.length === AVAILABLE_LOCATIONS.length}
              onToggle={toggleAllLocations}
              outlineStyle={styles.checkboxOutline}
              fillStyle={styles.checkboxFill}
              icon={<Check fill={color.palette.whiteGrey} width={21} height={21} />}
            />
            <Text tx="facilitiesServices.all" style={styles.checkboxLabel} />
          </TouchableOpacity>
          {AVAILABLE_LOCATIONS.map((location) => (
            <TouchableOpacity
              key={location}
              style={styles.checkboxRow}
              onPress={() => toggleLocation(location)}
            >
              <Checkbox
                value={locations.includes(location)}
                onToggle={() => toggleLocation(location)}
                outlineStyle={styles.checkboxOutline}
                fillStyle={styles.checkboxFill}
                icon={<Check fill={color.palette.whiteGrey} width={21} height={21} />}
              />
              <Text text={location} style={styles.checkboxLabel} />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Bottom Buttons */}
      <View style={styles.footer}>
        <View style={styles.footerRow}>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearAll}
            testID="filter-sheet-clear-button"
          >
            <Text tx="facilitiesServices.clearAll" style={styles.clearButtonText} />
          </TouchableOpacity>

          <TouchableOpacity onPress={handleApply} testID="filter-sheet-apply-button">
            <LinearGradient
              style={styles.linearGradient}
              start={{ x: 0, y: 1 }}
              end={{ x: 1, y: 0 }}
              colors={[color.palette.gradientColor1End, color.palette.gradientColor1Start]}
            >
              <Text tx="facilitiesServices.applyFilters" style={styles.applyButtonText} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </BottomSheet>
  )
}

const styles = StyleSheet.create({
  container: {
    height: SHEET_HEIGHT,
    backgroundColor: color.palette.whiteGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  headerContainer: {
    height: 64,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    width: "100%",
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  headerIcon: {
    position: "absolute",
    right: 16,
    top: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  sectionTitle: {
    ...newPresets.bodyTextBlackBold,
    marginBottom: 12,
    color: color.palette.almostBlackGrey,
  },
  pillsRow: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 16,
  },
  checkboxList: {
    gap: 12,
    marginTop: 12,
  },
  checkboxRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    height: 30,
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  checkboxOutline: {
    borderColor: color.palette.midGrey,
    width: 16,
    height: 16,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
  },
  checkboxFill: {
    width: 16,
    height: 16,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: color.palette.lightPurple,
  },
  checkboxLabel: {
    ...newPresets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
  },
  footer: {
    height: 96,
    justifyContent: "flex-start",
    borderTopWidth: 1,
    borderTopColor: color.palette.lighterGrey,
  },
  footerRow: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginTop: 11,
  },
  clearButton: {
    flex: 1,
    height: 44,
    justifyContent: "center",
    alignItems: "flex-start",
  },
  clearButtonText: {
    ...newPresets.bodyTextBold,
    fontSize: 14,
    lineHeight: 18,
    color: color.palette.lightPurple,
  },
  applyButton: {
    flex: 1,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    backgroundColor: color.palette.basePurple,
  },
  applyButtonText: {
    ...newPresets.bodyTextBold,
    color: color.palette.whiteGrey,
  },
  linearGradient: {
    height: 44,
    width: 176,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 999,
  },
})
