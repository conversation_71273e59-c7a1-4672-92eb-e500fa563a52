import { useEffect, useState } from "react"
import NetInfo from "@react-native-community/netinfo"

export function useInternetConnection() {
  const [isInternetConnected, setIsInternetConnected] = useState(true)

  useEffect(() => {
    checkInternetConnection()
  }, [])

  const checkInternetConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    setIsInternetConnected(isConnected)
  }

  return { isInternetConnected, checkInternetConnection }
}
