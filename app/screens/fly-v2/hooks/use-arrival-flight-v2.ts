import { env } from "app/config/env-params"
import { useFlightGamification } from "app/hooks/useFlightGamification"
import { useFlightSaveErrorHandling } from "app/hooks/useFlightSaveErrorHandling"
import {
  deleteMyTravelFlightDetail,
  getFlightsV2,
  getFlyEarlierListQuery,
  myTravelInsertFlight,
  searchFlights,
} from "app/models/queries"
import { FlyCreators, FlySelectors, mergeToDateGroup } from "app/redux/flyRedux"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { store } from "app/redux/store"
import { FlyList } from "app/redux/types/fly/fly"
import {
  formatResponseMyTravelInsertFlight,
  formatResponseRemoveMyTravelFlight,
  getQueryFilter,
  handleScheduledDateForEarlierFly,
  handleScheduledDateForFly,
  handleScheduledTimeForEarlierFly,
  handleScheduledTimeForFly,
} from "app/sagas/flySaga"
import { useFlightListingContext } from "app/screens/fly-v2/contexts/flight-listing-context"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { FB_EVENT_NAME } from "app/services/facebook/event-name"
import {
  ANALYTICS_LOG_EVENT_NAME,
  FE_LOG_PREFIX,
  analyticsLogEvent,
  convertStringValue,
  dtACtionLogEvent,
  dtBizEvent,
  dtManualActionEvent,
} from "app/services/firebase/analytics"
import {
  DateFormats,
  flyModuleDateFormatting,
  flyModuleUpdatedTime,
  getCurrentDateSingapore,
  getDateSingapore,
} from "app/utils/date-time/date-time"
import { graphqlOperation } from "aws-amplify"
import { isEmpty } from "lodash"
import moment, { Moment } from "moment"
import momentTz from "moment-timezone"
import { useEffect, useMemo, useRef, useState } from "react"
import DeviceInfo from "react-native-device-info"
import { AppEventsLogger } from "react-native-fbsdk-next"
import { useDispatch, useSelector } from "react-redux"
import requestApi from "../../../services/api/request"
import NetInfo from "@react-native-community/netinfo"

const SCREEN_NAME = "ArrivalResultScreenV2__"

interface IGetFlightListRequest {
  direction: string
  filterDate: Moment
  filters: string[]
  isFilter: boolean
  isLoadFlightAfter24h: boolean
  isLoadMore?: boolean
  filterAirline?: string
  filterCityAirport?: string
}

interface ISearchFlightListRequest {
  direction: string
  filterDate: Moment
  filterTerminal: string[]
  keyword: string
  pageSize?: string
  pageNumber?: string
  isLoadMore?: boolean
  filterAirline?: string
  filterCityAirport?: string
}

const PAGE_SIZE = 30
export const useArrivalFlightV2 = () => {
  const [sectionList, setSectionList] = useState([])
  const [searchPageNumber, setSearchPageNumber] = useState(1)
  const [searchPageTotal, setSearchPageTotal] = useState(0)
  const searchMoreEnabled = useMemo(() => {
    return searchPageNumber * PAGE_SIZE < searchPageTotal
  }, [searchPageNumber, searchPageTotal])

  const [lastUpdatedTime, setLastUpdatedTime] = useState(flyModuleUpdatedTime())
  const [nextToken, setNextToken] = useState("")
  const [isEndLoadMore, setEndLoadMore] = useState(false)
  const [isNetworkError, setNetworkError] = useState(false)
  const [networkFailedAttempt, setNetworkFailedAttempt] = useState(0)
  const [previousToken, setPreviousToken] = useState("")
  const [isEndEarlierFlights, setIsEndEarlierFlights] = useState(false)
  const [isLoadingEarlierFlights, setIsLoadingEarlierFlights] = useState(false)
  const [canLoadMore, setCanLoadMore] = useState(true)

  const loadingRef = useRef(false)
  const dispatch = useDispatch()

  const { flightArrivalResultPayload: flyData } = useSelector(
    FlySelectors.flightArrivalResultPayload,
  )
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  useFlightSaveErrorHandling()
  const { rewardGamificationSavedFlights } = useFlightGamification()
  const { setLastUpdated, isLoading, setIsLoading, setIsRefreshing } = useFlightListingContext()

  const sectionListRef = useRef([])
  const isFirstLoadRef = useRef(true)

  const setDataSectionList = async (flyData) => {
    const { isConnected } = await NetInfo.fetch()

    if (isConnected) {
      if (flyData?.payload) {
        const temp = flyData?.payload?.map((item) => {
          return {
            title: item.date,
            data: item.flightListingData,
          }
        })
        setSectionList(temp || [])
        sectionListRef.current = temp
      } else {
        setSectionList([])
        sectionListRef.current = []
      }
    }
  }

  useEffect(() => {
    setDataSectionList(flyData)
  }, [flyData])

  const resetSearchData = () => {
    setSearchPageNumber(1)
    setSearchPageTotal(0)
  }

  const resetLoadListData = () => {
    setNextToken("")
  }

  const getFlyArrivalList = async (request: IGetFlightListRequest) => {
    const {
      direction,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
      isLoadMore,
      filterAirline,
      filterCityAirport,
    } = request
    try {
      if (loadingRef.current || isLoading) {
        return
      }
      loadingRef.current = true
      setIsLoading(true)
      if (!isLoadMore) {
        setIsRefreshing(true)
      }
      const scheduledDate = getDateSingapore(filterDate)
      const body = {
        direction,
        page_size: 30,
        ...(isLoadMore ? { next_token: nextToken } : {}),
        scheduled_date: handleScheduledDateForFly(isFilter, isLoadFlightAfter24h, scheduledDate),
        scheduled_time: handleScheduledTimeForFly(isLoadFlightAfter24h),
        ...getQueryFilter(filters),
        airline: filterAirline,
        airport: filterCityAirport,
      }

      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getFlightsV2, { ...body }),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })

      if (response?.data?.data?.getFlights) {
        const action = {
          payload: response.data,
        }

        const convertedData: any = new FlyList().success(
          action,
          store.getState().flyReducer?.flyCodesPayload,
          store.getState().mytravelReducer?.myTravelFlightsPayload,
        )

        let tempFlightList = []

        if (isLoadMore) {
          const mergeList = {
            data: [...flyData?.payload, ...convertedData.data],
          }
          tempFlightList = mergeToDateGroup(mergeList)
        } else {
          if (response?.data?.data?.getFlights?.flights?.length === 0) {
            const today = new Date(getCurrentDateSingapore())

            tempFlightList = [
              {
                date: flyModuleDateFormatting(moment(today), DateFormats.DateWithDayMonthYear),
                flightListingData: [],
                sortedDate: moment(today).format(DateFormats.YearMonthDay),
                currentDate: moment(today).format(DateFormats.YearMonthDay),
              },
            ]
          } else {
            tempFlightList = convertedData?.data
          }
        }
        if (tempFlightList?.[0]?.currentDate > getCurrentDateSingapore()) {
          const startDate = new Date(getCurrentDateSingapore())
          const endDate = new Date(tempFlightList?.[0]?.currentDate)

          let dateList = []

          for (let date = startDate; date < endDate; date.setDate(date.getDate() + 1)) {
            dateList.push({
              date: flyModuleDateFormatting(moment(date), DateFormats.DateWithDayMonthYear),
              flightListingData: [],
              sortedDate: moment(date).format(DateFormats.YearMonthDay),
              currentDate: moment(date).format(DateFormats.YearMonthDay),
            })
          }

          tempFlightList = [...dateList, ...tempFlightList]
        }
        setNetworkError(false)
        setNetworkFailedAttempt(0)
        dispatch(FlyCreators.flyArrivalListSuccessV2(tempFlightList))
        setIsLoading(false)
        setNextToken(action.payload.data?.getFlights?.next_token)
        setEndLoadMore((_state) => {
          if (action.payload.data?.getFlights?.next_token) {
            return false
          } else {
            return true
          }
        })
        if (!isLoadMore) {
          setIsEndEarlierFlights(false)
        }
        setLastUpdated(flyModuleUpdatedTime())
        isFirstLoadRef.current = false
      } else {
        setCanLoadMore(false)
        setLastUpdated("")
        if (!isFirstLoadRef.current) {
          setNetworkFailedAttempt((s) => s + 1)
        } else {
          setNetworkError(true)
        }
      }
    } catch (err) {
      setCanLoadMore(false)
      setLastUpdated("")
      if (!isFirstLoadRef.current) {
        setNetworkFailedAttempt((s) => s + 1)
      } else {
        setNetworkError(true)
      }
    } finally {
      setIsLoading(false)
      loadingRef.current = false
      setLastUpdatedTime(flyModuleUpdatedTime())
      if (!isLoadMore) {
        setIsRefreshing(false)
      }
      resetSearchData()
    }
  }

  const searchArrivalFlight = async (request: ISearchFlightListRequest) => {
    const {
      direction,
      filterDate,
      filterTerminal,
      keyword,
      pageSize = 30,
      isLoadMore = false,
      filterAirline,
      filterCityAirport,
    } = request
    try {
      if (loadingRef.current || isLoading) {
        return
      }
      loadingRef.current = true
      setIsLoading(true)
      const scheduledDate = moment(filterDate).format(DateFormats.YearMonthDay)

      const bodyRequest = {
        text: keyword,
        filter: {
          direction,
          scheduled_date: scheduledDate,
          ...getQueryFilter(filterTerminal),
          airline: filterAirline,
          airport: filterCityAirport,
        },
        page_number: isLoadMore ? searchPageNumber + 1 : 1,
        page_size: pageSize,
      }
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(searchFlights, bodyRequest),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const mappedResponse = {
        data: {
          getFlights: {
            flights: response.data.data.flights.items,
          },
        },
      }
      const action = {
        payload: mappedResponse,
      }

      const convertedData: any = new FlyList().success(
        action,
        store.getState().flyReducer?.flyCodesPayload,
        store.getState().mytravelReducer?.myTravelFlightsPayload,
      )

      let tempFlightList = []
      if (isLoadMore) {
        const mergeList = {
          data: [...flyData?.payload, ...convertedData.data],
        }
        tempFlightList = mergeToDateGroup(mergeList)
      } else {
        tempFlightList = convertedData?.data
      }
      setNetworkError(false)
      dispatch(FlyCreators.flyArrivalListSuccessV2(tempFlightList))
      setIsLoading(false)
      setSearchPageNumber(response.data.data.flights.page_number)
      setSearchPageTotal(response.data.data.flights.total)
    } catch (err) {
      setNetworkError(true)
    } finally {
      loadingRef.current = false
      setIsLoading(false)
      setLastUpdatedTime(flyModuleUpdatedTime())
      resetLoadListData()
    }
  }

  const myTravelInsertFlightQuery = async (
    input,
    payload,
    successCallback: (isFirstSaveFlight: boolean) => void,
    failureCallback: () => void,
  ) => {
    const query = {
      input: {
        deviceId: DeviceInfo.getUniqueIdSync(),
        flightDirection: input?.flightDirection,
        ocidEmail: input?.enterpriseUserId,
        flightNo: input?.flightNumber,
        iataAirportCode:
          payload?.item?.direction === FlightDirection.arrival
            ? payload?.item?.destinationCode
            : payload?.item?.departingCode,
        isPassenger: input?.flightPax,
        scheduledDate: payload?.item?.flightDate,
        scheduledTime: payload?.item?.timeOfFlight || payload?.item?.scheduledTime,
        odtt: "OD",
        flightStatus: payload?.item?.flightStatus,
      },
    }
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-arrival-listing-save`)
    try {
      dtAction.reportStringValue(
        "flight-arrival-listing-save-press-flightNumber",
        `${query.input?.flightNo}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-listing-save-press-scheduledDate",
        `${query.input.scheduledDate}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-listing-save-press-flightDirection",
        `${query.input.flightDirection}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-listing-save-press-isPassenger",
        `${query.input.isPassenger}`,
      )
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(myTravelInsertFlight, query),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      if (response.data.errors) {
        dtAction.reportStringValue(
          "flight-arrival-listing-save-error-response",
          convertStringValue(JSON.stringify(response.data.errors)),
        )
        dispatch(MytravelCreators.flyMyTravelInsertFlightFailure(payload))
        failureCallback()
        return
      }
      dispatch(
        MytravelCreators.flyMyTravelInsertFlightSuccess(
          formatResponseMyTravelInsertFlight(response),
          {
            ...payload,
            isPassenger: input?.flightPax,
          },
        ),
      )
      dtAction.reportStringValue("flight-arrival-listing-save-success", "success")
      // get gamification reward game chance
      if (response?.data?.data?.saveFlight?.eligibleForGameChance) {
        const {
          flightNumber,
          actualTimestamp,
          displayTimestamp,
          scheduledDate,
          scheduledTime,
          timeOfFlight,
        } = payload?.item
        const priorityTime =
          actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime || timeOfFlight}`
        const formatedScheduledTime = momentTz(priorityTime).format("YYYY-MM-DD HH:mm").toString()
        const currentTimeToUTC = momentTz()
          .tz("Asia/Singapore")
          .format("YYYY-MM-DD HH:mm")
          .toString()
        const gameChanceInput = {
          flightNumber: flightNumber,
          flightDatetime: formatedScheduledTime,
          saveTimestamp: currentTimeToUTC,
        }
        rewardGamificationSavedFlights(gameChanceInput)
      }

      const isFirstSaveFlight = myTravelFlightsPayload?.getMyTravelFlightDetails?.length == 0
      successCallback(isFirstSaveFlight)
      analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL)
      dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL)
      dtBizEvent(
        SCREEN_NAME,
        ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL,
        "TAP-ON-SAVE-ARR-FLIGHT",
        {},
      )
      AppEventsLogger.logEvent(FB_EVENT_NAME.SAVE_FLIGHT_ARRIVAL, null)
    } catch (err) {
      dtAction.reportStringValue(
        "flight-arrival-listing-save-error",
        convertStringValue(err?.message),
      )
      dispatch(MytravelCreators.flyMyTravelInsertFlightFailure(payload))
      failureCallback()
    } finally {
      dtAction.leaveAction()
    }
  }

  const removeSavedFlight = async (
    payload,
    successCallback: () => void,
    failureCallback: () => void,
  ) => {
    const query = {
      input: {
        flightDirection: payload?.item?.flightDirection || payload?.item?.direction,
        flightNo: payload?.item?.flightNumber,
        scheduledDate: payload?.item?.scheduledDate || payload?.item?.flightDate,
      },
    }
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-arrival-listing-unsave`)
    try {
      dtAction.reportStringValue(
        "flight-arrival-listing-unsave-query-flightNo",
        `${query.input.flightNo}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-listing-unsave-query-scheduledDate",
        `${query.input.scheduledDate}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-listing-unsave-query-flightDirection",
        `${query.input.flightDirection}`,
      )
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(deleteMyTravelFlightDetail, query),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      if (response.data.errors) {
        dtAction.reportStringValue(
          "flight-arrival-listing-unsave-error-response",
          convertStringValue(JSON.stringify(response.data.errors)),
        )
        dispatch(MytravelCreators.flyMyTravelRemoveFlightFailure(payload))
        failureCallback()
        return
      }
      dispatch(
        MytravelCreators.flyMyTravelRemoveFlightSuccess(
          formatResponseRemoveMyTravelFlight(response?.data),
          payload,
        ),
      )
      dtAction.reportStringValue("flight-arrival-listing-unsave-success", "success")
      successCallback()
    } catch (err) {
      dtAction.reportStringValue(
        "flight-arrival-listing-unsave-error",
        convertStringValue(err?.message),
      )
      dispatch(MytravelCreators.flyMyTravelRemoveFlightFailure(payload))
      failureCallback()
    } finally {
      dtAction.leaveAction()
    }
  }

  const apiCallJob = useRef(null)
  const isFocusedRef = useRef(true)

  const refreshInterval = env()?.FLIGHT_REFRESH_INTERVAL
  const startLoopApiCall = (callback: () => void) => {
    cancelLoopApiJob()
    apiCallJob.current = setTimeout(() => {
      if (isFocusedRef.current) {
        callback()
        startLoopApiCall(callback)
      }
    }, refreshInterval)
  }

  const cancelLoopApiJob = () => {
    clearTimeout(apiCallJob.current)
  }

  const getEarlierFlights = async (request: IGetFlightListRequest) => {
    const {
      direction,
      filterDate,
      filters,
      isLoadFlightAfter24h,
      filterAirline,
      filterCityAirport,
    } = request

    trackAction(AdobeTagName.CAppFlyFlightListEarlierFlights, {
      [AdobeTagName.CAppFlyFlightListEarlierFlights]: "1",
    })

    if (!isEmpty(sectionList) && !isEndEarlierFlights) {
      try {
        setIsLoadingEarlierFlights(true)

        let scheduledDate = getDateSingapore(filterDate)
        const currentDate = moment().format(DateFormats.YearMonthDay)
        const isPast = new Date(scheduledDate) < new Date(currentDate)
        if (isPast) {
          scheduledDate = currentDate
        }
        const body = {
          direction,
          next_token: previousToken,
          prev: "true",
          scheduled_date: handleScheduledDateForEarlierFly(isLoadFlightAfter24h, scheduledDate),
          scheduled_time: handleScheduledTimeForEarlierFly(isLoadFlightAfter24h),
          page_size: "5",
          ...getQueryFilter(filters),
          airline: filterAirline,
          airport: filterCityAirport,
        }

        const response = await requestApi({
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(getFlyEarlierListQuery, { ...body }),
          parameters: {},
          headers: {
            "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
          },
        })

        setNetworkError(false)
        setNetworkFailedAttempt(0)

        if (response?.data?.data?.getFlights) {
          const action = {
            payload: {
              data: {
                getFlights: {
                  flights: [...response?.data?.data?.getFlights?.flights?.reverse()],
                  previous_token: response?.data?.data?.getFlights?.next_token,
                },
              },
            },
          }

          const convertedData: any = new FlyList().success(
            action,
            store.getState().flyReducer?.flyCodesPayload,
            store.getState().mytravelReducer?.myTravelFlightsPayload,
          )

          let tempFlightList = []
          const mergeList = {
            data: [...convertedData.data, ...flyData?.payload],
          }
          tempFlightList = mergeToDateGroup(mergeList)

          dispatch(FlyCreators.flyArrivalListSuccessV2(tempFlightList))

          setPreviousToken(response?.data?.data?.getFlights?.next_token)
          setIsEndEarlierFlights(() => {
            if (response?.data?.data?.getFlights?.next_token) {
              return false
            } else {
              return true
            }
          })
          cancelLoopApiJob()
        } else {
          if (!isEmpty(sectionList)) {
            setNetworkFailedAttempt((s) => s + 1)
            setLastUpdated("")
          } else {
            setNetworkError(true)
          }
        }
      } catch (err) {
        setNetworkError(true)
      } finally {
        setIsLoadingEarlierFlights(false)
      }
    }
  }

  useEffect(() => {
    return cancelLoopApiJob
  }, [])

  return {
    sectionList,
    isLoading,
    lastUpdatedTime,
    isFocusedRef,
    isEndLoadMore,
    searchMoreEnabled,
    isNetworkError,
    setSectionList,
    startLoopApiCall,
    cancelLoopApiJob,
    getFlyArrivalList,
    searchArrivalFlight,
    setNetworkError,
    myTravelInsertFlightQuery,
    removeSavedFlight,
    getEarlierFlights,
    isLoadingEarlierFlights,
    isEndEarlierFlights,
    networkFailedAttempt,
    sectionListRef,
    canLoadMore,
    setCanLoadMore,
    setPreviousToken,
    isFirstLoadRef,
  }
}
