import { StyleSheet, Dimensions, Platform } from "react-native"
import { color, typography } from "app/theme"
import { presets } from "app/elements/text"

const { width } = Dimensions.get("window")
export const BG_HEIGHT = width * (516/750)

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  fixedBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    width: width,
    height: BG_HEIGHT,
    right: 0,
  },
  headerSafeArea: {
    position: "relative",
    top: 0,
    left: 0,
    width: width,
    zIndex: 3,
    flex: 1,
  },
  headerRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: 32,
    paddingHorizontal: 16,
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    color: color.palette.whiteGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
    lineHeight: 22,
    letterSpacing: 0,
  },
  realtimeCard: {
    marginBottom: 14,
  },
  liveText: {
    color: color.palette.lighterGrey,
    textAlign: "center",
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    fontFamily: typography.medium,
    fontSize: 11,
    lineHeight: 14,
    letterSpacing: 0,
  },
  realtimeTitle: {
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
    fontSize: 14,
    color: "#222",
  },
  flightContainer: {
    flex: 1,
  },
  loadingIndicator: { marginBottom: 10 },
  loadingIndicatorContainer: {
    alignItems: "center",
    opacity: 0,
    overflow: "hidden",
    position: "absolute",
    height: 48,
    alignSelf: "center",
    top: 40,
  },
})

export const tabScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  lottieLoading: {
    alignItems: "center",
    alignSelf: "center",
    backgroundColor: color.palette.transparent,
    marginLeft: 10,
    width: "70%",
    height: Dimensions.get("window").height,
  },
  lottieLoadingBackground: {
    backgroundColor: color.palette.whiteColorOpacity,
    bottom: 0,
    left: 0,
    position: "absolute",
    right: 0,
    top: 0,
  },
  listStyle: { backgroundColor: color.palette.almostWhiteGrey },
  sectionContainer: { paddingHorizontal: 20, marginBottom: 16 },
  filterContainer: {
    paddingTop: 20,
    marginBottom: 20,
  },
  feedBackToastStyle: {
    bottom: 24,
    paddingHorizontal: 16,
    width: "100%",
  },
  toastButtonStyle: {
    ...presets.textLink,
    alignItems: "flex-end",
    color: color.palette.lightBlue,
    fontWeight: "normal",
  },
  toastTextStyle: {
    ...presets.bodyTextRegular,
    color: color.palette.whiteGrey,
    width: "80%",
  },
  noEarlierFlights: { marginBottom: 20, marginHorizontal: 20 },
  errorContainer: {
    flex: 1,
    paddingHorizontal: 24,
    marginTop: 30,
  },
  noFlightsText: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  listContainer: {
    paddingBottom: 20,
  },
})
