import { color } from "app/theme/color"
import React, { useRef, useEffect, useCallback } from "react"
import {
  ViewStyle,
  StatusBar,
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  RefreshControl,
} from "react-native"
import { Search } from "./components/search"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import Animated from "react-native-reanimated"
import TrackTodayFlightsBanner from "./components/track-today-flights-banner"
import LinearGradient from "react-native-linear-gradient"
import { Text } from "app/elements/text"
import { NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { useSelector } from "react-redux"
import { typography } from "app/theme"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { PlaneSaved } from "assets/icons"
import { MytravelSelectors } from "app/redux/mytravelRedux"
import { getActiveRouteName } from "app/navigators/navigation-utilities"
import { useSharedValue } from "react-native-reanimated"
import { FlyLandingAnimatedHeader } from "./components/fly-landing-animated-header"
import { useHandleScroll } from "app/navigators/navigation-utilities"
import { SearchIndex } from "../search/tabs/searchIndex"
import { PartnershipSection, QuickLinksSection } from "./components/fly-landing-sections"
import { EmptyState } from "./components/empty-state"
import { useInternetConnection } from "./hooks/use-internet-connection"

const SCREEN_NAME = "FlyLanding"

const parentContainerStyle: ViewStyle = {
  flex: 1,
  backgroundColor: color.palette.whiteGrey,
}

export const FlyLanding = ({ navigation, route }) => {
  const insets = useSafeAreaInsets()
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const savedCount = useSelector(MytravelSelectors.countSavedFlight)
  const searchRef = useRef(null)
  const [refreshing, setRefreshing] = React.useState(false)
  const { isInternetConnected, checkInternetConnection } = useInternetConnection()

  const scrollY = useSharedValue(0)
  const { handleScroll, isTabVisible } = useHandleScroll()

  const onScroll = useCallback(
    (event) => {
      scrollY.value = event.nativeEvent.contentOffset.y
      handleScroll(event)
      route?.params?.setOptions?.({ tabBarVisible: isTabVisible.current })
    },
    [handleScroll, scrollY, route, isTabVisible],
  )

  const resetSearchIfNotFlyTab = useCallback(() => {
    try {
      const isFlyTab = getActiveRouteName(navigation.getState()) === "fly"
      if (!isFlyTab && searchRef.current?.reset) {
        searchRef.current.reset()
      }
    } catch (error) {
      console.error("Error parsing navigation state:", error)
    }
  }, [navigation])

  useEffect(() => {
    const unsubscribe = navigation.addListener("state", resetSearchIfNotFlyTab)

    return unsubscribe
  }, [navigation, resetSearchIfNotFlyTab])

  const handleNavigateToSavedFlights = () => {
    navigation?.navigate(NavigationConstants.saveFlightsScreen)
  }

  const onPressSavedFlight = () => {
    if (!isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.FLIGHTS,
        callBackAfterLoginSuccess: handleNavigateToSavedFlights,
      })
      return
    }

    handleNavigateToSavedFlights()
  }

  const handleNavigateToGlobalSearch = () => {
    navigation.navigate(NavigationConstants.search, {
      screen: SearchIndex.flights,
    })
  }

  const onRefresh = React.useCallback(() => {
    setRefreshing(true)

    checkInternetConnection()
    
    setTimeout(() => {
      setRefreshing(false)
    }, 1500) // mock refresh
  }, [])

  if (!isInternetConnected) {
    return (
      <View style={styles.noInternetContainer}>
        <EmptyState.NoInternet onPressReload={checkInternetConnection} />
        <StatusBar barStyle="dark-content" translucent backgroundColor="transparent" />
      </View>
    )
  }

  return (
    <View
      style={parentContainerStyle}
      testID={`${SCREEN_NAME}__FlyLandingScreen`}
      accessibilityLabel={`${SCREEN_NAME}__FlyLandingScreen`}
    >
      <StatusBar barStyle="dark-content" translucent backgroundColor="transparent" />
      <Animated.ScrollView
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{
          paddingTop: insets.top,
        }}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            progressViewOffset={insets.top}
          />
        }
      >
        <LinearGradient
          colors={["#FFFFFF", "#FFFFFF", "#F9F5FC", "#DEE6F6"]}
          locations={[0, 0.5, 0.75, 1]}
          style={{ alignItems: "center", flex: 1 }}
        >
          <View style={styles.headerContainer}>
            <Text preset="h6" tx="flyLandingV2.myFlight" />
            <View>
              {savedCount > 0 && isLoggedIn && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>{savedCount > 9 ? "9+" : savedCount}</Text>
                </View>
              )}
              <TouchableOpacity
                style={styles.savedFlightContainer}
                onPress={onPressSavedFlight}
                activeOpacity={0.8}
              >
                <View style={styles.buttonContent}>
                  <PlaneSaved style={styles.icon} />
                  <Text tx="flyLanding.saved" preset="textLink" style={styles.text} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <Search ref={searchRef} navigation={navigation} />
          <TrackTodayFlightsBanner />
          <View style={{ height: 40 }} />
        </LinearGradient>

        {/* <PartnershipSection /> */}

        {/* <QuickLinksSection /> */}

        <View style={{ height: 1000 }} />
      </Animated.ScrollView>

      <FlyLandingAnimatedHeader
        scrollY={scrollY}
        onSearchPress={handleNavigateToGlobalSearch}
        onSavedFlightPress={onPressSavedFlight}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  noInternetContainer: {
    flex: 1,
    alignItems: "stretch",
    justifyContent: "center",
    marginBottom: 90, // Bottom tab bar height
    paddingHorizontal: 24,
  },
  headerContainer: {
    alignSelf: "stretch",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 25,
    paddingVertical: 12,
  },
  savedFlightContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: color.palette.lightPurple,
    borderRadius: 40,
    width: 94,
    height: 32,
    borderColor: color.palette.purple8F58BE,
    borderStyle: "solid",
    borderWidth: 1,
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  icon: {
    backgroundColor: "transparent",
    position: "relative",
    left: 7,
    marginRight: 6,
  },
  text: {
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: 16,
  },
  badge: {
    position: "absolute",
    left: -6,
    top: -6,
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.lighterPurple,
    borderWidth: 1,
    width: 22,
    height: 22,
    borderRadius: 11,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 2,
  },
  badgeText: {
    color: color.palette.lightPurple,
    fontSize: 11,
    lineHeight: 14,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    fontFamily: typography.bold,
  },
})
