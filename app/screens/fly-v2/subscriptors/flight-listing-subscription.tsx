import {
  flightArrUpdatesSubscription,
  flightDepUpdatesSubscription,
} from "app/models/subscriptions"
import { FlyCreators } from "app/redux/flyRedux"
import { store } from "app/redux/store"
import { ifAllTrue } from "app/utils"
import { FlightDirection } from "app/utils/constants"
import { API } from "aws-amplify"
import { find, forEach, get, isEmpty, merge, reduce, values } from "lodash"
import React, { useEffect, useRef, useState } from "react"
import { AppState, InteractionManager } from "react-native"
import { useDispatch } from "react-redux"

interface FlightListingSubscriptionProps {
  direction: string
}

const mappingFLightData = ({
  isExisted,
  dataFlight,
  keyFlight,
  flightStatus,
  statusMapping,
  timeOfFlight,
  displayTimestamp,
}) => {
  if (dataFlight) {
    isExisted[keyFlight] = true
    dataFlight.flightStatus = flightStatus === "hide" ? "" : flightStatus
    dataFlight.flightStatusMapping = statusMapping?.listing_status_en
    dataFlight.statusColor = statusMapping?.status_text_color?.toLowerCase()
    dataFlight.showGate = statusMapping?.show_gate
    dataFlight.timeOfFlight = timeOfFlight
    dataFlight.beltStatusMapping = statusMapping?.belt_status_en
    dataFlight.displayTimestamp = displayTimestamp
  }
}

const INTERVAL_UPDATE_FLIGHT = 5000
const FlyListSubscription = (props: FlightListingSubscriptionProps) => {
  const { direction } = props
  const dispatch = useDispatch()
  const [toggleError, setToggleError] = useState(new Date().getTime())
  const subScriptionRef = useRef(null)
  const appState = useRef(AppState.currentState)
  const tempStoreFlight = useRef({})
  const intervalId = useRef(null)

  const checkFlightExisted = (
    data,
    flyDepartureLanding,
    flyDepartureListing,
    flightArrivalLanding,
    flightArrivalListing,
  ) => {
    const flyLandingDeparture = get(flyDepartureLanding, "data", [])
    const flyListingDeparture = get(flyDepartureListing, "payload", [])
    const flyLandingArrival = get(flightArrivalLanding, "data", [])
    const flyListingArrival = get(flightArrivalListing, "payload", [])

    let isExisted = {
      isExistedFlyLandingDeparture: false,
      isExistedFlyListingDeparture: false,
      isExistedFlyLandingArrival: false,
      isExistedFlyListingArrival: false,
    }

    !isEmpty(data) &&
      forEach(data, (rawData) => {
        const {
          flight_number: flightNumber,
          direction,
          flight_status: flightStatus,
          status_mapping: statusMapping,
          scheduled_time: timeOfFlight,
          scheduled_date: scheduledDate,
          display_timestamp: displayTimestamp,
        } = rawData
        if (direction === FlightDirection.Departure) {
          !isEmpty(flyLandingDeparture) &&
            forEach(flyLandingDeparture, (_element, index) => {
              const flyLandingDepartureData = get(flyLandingDeparture, `${index}.flightLanding`)
              const existedFlyLandingDeparture = find(
                flyLandingDepartureData,
                (el) =>
                  el.flightNumber === flightNumber &&
                  el.flightDate === scheduledDate &&
                  (el?.flightStatusMapping !== statusMapping?.listing_status_en ||
                    el?.statusColor?.toLowerCase() !==
                      statusMapping?.status_text_color?.toLowerCase() ||
                    el?.timeOfFlight !== timeOfFlight ||
                    el?.showGate !== statusMapping?.show_gate),
              )
              if (existedFlyLandingDeparture) {
                mappingFLightData({
                  isExisted,
                  dataFlight: existedFlyLandingDeparture,
                  keyFlight: "isExistedFlyLandingDeparture",
                  flightStatus,
                  statusMapping,
                  timeOfFlight,
                  displayTimestamp,
                })
              } else {
                delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`]
              }
            })
          !isEmpty(flyListingDeparture) &&
            forEach(flyListingDeparture, (_element, index) => {
              const flyListingDepartureData = get(flyListingDeparture, `${index}.flightListingData`)
              const existedFlyListingDeparture = find(
                flyListingDepartureData,
                (el) =>
                  el.flightNumber === flightNumber &&
                  el.flightDate === scheduledDate &&
                  (el?.flightStatusMapping !== statusMapping?.listing_status_en ||
                    el?.statusColor?.toLowerCase() !==
                      statusMapping?.status_text_color?.toLowerCase() ||
                    el?.timeOfFlight !== timeOfFlight ||
                    el?.showGate !== statusMapping?.show_gate),
              )
              if (existedFlyListingDeparture) {
                mappingFLightData({
                  isExisted,
                  dataFlight: existedFlyListingDeparture,
                  keyFlight: "isExistedFlyListingDeparture",
                  flightStatus,
                  statusMapping,
                  timeOfFlight,
                  displayTimestamp,
                })
              } else {
                delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`]
              }
            })
        }
        if (direction === FlightDirection.Arrival) {
          !isEmpty(flyLandingArrival) &&
            forEach(flyLandingArrival, (_element, index) => {
              const flyLandingArrivalData = get(flyLandingArrival, `${index}.flightLanding`)
              const existedFlyLandingArrival = find(
                flyLandingArrivalData,
                (el) =>
                  el.flightNumber === flightNumber &&
                  el.flightDate === scheduledDate &&
                  (el?.flightStatusMapping !== statusMapping?.listing_status_en ||
                    el?.statusColor?.toLowerCase() !==
                      statusMapping?.status_text_color?.toLowerCase() ||
                    el?.timeOfFlight !== timeOfFlight ||
                    el?.showGate !== statusMapping?.show_gate ||
                    el?.beltStatusMapping !== statusMapping?.belt_status_en),
              )
              if (existedFlyLandingArrival) {
                mappingFLightData({
                  isExisted,
                  dataFlight: existedFlyLandingArrival,
                  keyFlight: "isExistedFlyLandingArrival",
                  flightStatus,
                  statusMapping,
                  timeOfFlight,
                  displayTimestamp,
                })
              } else {
                delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`]
              }
            })
          !isEmpty(flyListingArrival) &&
            forEach(flyListingArrival, (_element, index) => {
              const flyListingArrivalData = get(flyListingArrival, `${index}.flightListingData`)
              const existedFlyListingArrival = find(
                flyListingArrivalData,
                (el) =>
                  el.flightNumber === flightNumber &&
                  el.flightDate === scheduledDate &&
                  (el?.flightStatusMapping !== statusMapping?.listing_status_en ||
                    el?.statusColor?.toLowerCase() !==
                      statusMapping?.status_text_color?.toLowerCase() ||
                    el?.timeOfFlight !== timeOfFlight ||
                    el?.showGate !== statusMapping?.show_gate ||
                    el?.beltStatusMapping !== statusMapping?.belt_status_en),
              )
              if (existedFlyListingArrival) {
                mappingFLightData({
                  isExisted,
                  dataFlight: existedFlyListingArrival,
                  keyFlight: "isExistedFlyListingArrival",
                  flightStatus,
                  statusMapping,
                  timeOfFlight,
                  displayTimestamp,
                })
              } else {
                delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`]
              }
            })
        }
      })
    const flyData: any = {}
    if (!isEmpty(flyDepartureLanding?.payload) && isExisted?.isExistedFlyLandingDeparture) {
      flyData.flyDeparturePayload = flyDepartureLanding
    }
    if (!isEmpty(flightArrivalLanding?.payload) && isExisted?.isExistedFlyLandingArrival) {
      flyData.flyArrivalPayload = flightArrivalLanding
    }
    if (!isEmpty(flyDepartureListing?.payload) && isExisted?.isExistedFlyListingDeparture) {
      flyData.flyPayload = flyDepartureListing
    }
    if (!isEmpty(flightArrivalListing?.payload) && isExisted?.isExistedFlyListingArrival) {
      flyData.flyArrivalResultPayload = flightArrivalListing
    }
    return Object.assign({}, flyData)
  }

  const updateDataToStore = () => {
    const data = [...values(Object.assign({}, tempStoreFlight.current))]
    if (!isEmpty(data)) {
      const { flyReducer } = store.getState()

      const flyDepartureLanding = flyReducer?.flyDeparturePayload
      const flyDepartureListing = flyReducer?.flyPayload
      const flightArrivalLanding = flyReducer?.flyArrivalPayload
      const flightArrivalListing = flyReducer?.flyArrivalResultPayload
      const updatedFlyData = checkFlightExisted(
        data,
        flyDepartureLanding,
        flyDepartureListing,
        flightArrivalLanding,
        flightArrivalListing,
      )
      if (Object.keys(updatedFlyData).length > 0) {
        InteractionManager.runAfterInteractions(() => {
          dispatch(
            FlyCreators.flyUpdateBySubscription({
              flyUpdatedTimeBySubscription: new Date().getTime(),
              ...updatedFlyData,
            }),
          )
        })
      }
    }
  }

  const setIntervalSubscription = () => {
    if (intervalId.current) return
    InteractionManager.runAfterInteractions(() => {
      intervalId.current = setInterval(() => {
        InteractionManager.runAfterInteractions(() => {
          updateDataToStore()
        })
      }, INTERVAL_UPDATE_FLIGHT)
    })
  }

  const clearIntervalSubscription = () => {
    clearInterval(intervalId.current)
    intervalId.current = null
  }

  useEffect(() => {
    setIntervalSubscription()
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (ifAllTrue([appState.current.match(/active/), nextAppState === "inactive"])) {
        clearIntervalSubscription()
      }
      if (ifAllTrue([appState.current.match(/inactive|background/), nextAppState === "active"])) {
        setIntervalSubscription()
      }
      appState.current = nextAppState
    })

    return () => {
      subscription.remove()
      clearIntervalSubscription()
    }
  }, [])

  useEffect(() => {
    subScriptionRef.current = (
      API.graphql({
        query:
          direction === FlightDirection.Departure
            ? flightDepUpdatesSubscription
            : flightArrUpdatesSubscription,
      }) as any
    ).subscribe({
      next: async (res) => {
        const data =
          direction === FlightDirection.Departure
            ? res?.value?.data?.flightDepUpdates
            : res?.value?.data?.flightArrUpdates
        const transformResponse = reduce(
          data,
          function (obj, param) {
            obj[`${param?.flight_number}_${param?.scheduled_date}`] = param
            return obj
          },
          {},
        )
        tempStoreFlight.current = merge(tempStoreFlight.current, transformResponse)
      },
      error: (_err) => {
        setToggleError(new Date().getTime())
      },
    })

    return () => {
      subScriptionRef?.current && subScriptionRef?.current.unsubscribe()
    }
  }, [toggleError])

  return <></>
}

export default React.memo(FlyListSubscription)
