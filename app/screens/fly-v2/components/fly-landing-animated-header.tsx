import React, { useState } from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme/color"
import { Plane, SearchIconV2 } from "assets/icons"
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  SharedValue,
  runOnJS,
  useAnimatedReaction,
} from "react-native-reanimated"

interface FlyLandingAnimatedHeaderProps {
  scrollY: SharedValue<number>
  onSearchPress: () => void
  onSavedFlightPress: () => void
}

const HEADER_HEIGHT = 100

export const FlyLandingAnimatedHeader = ({
  scrollY,
  onSearchPress,
  onSavedFlightPress,
}: FlyLandingAnimatedHeaderProps) => {
  const [canPress, setCanPress] = useState(false)

  useAnimatedReaction(
    () => scrollY.value,
    (value, prev) => {
      if (value !== prev) {
        runOnJS(setCanPress)(value > 100)
      }
    },
    [],
  )
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [0, 40, 50], [0, 0, 1], Extrapolation.CLAMP),
  }))

  const handleSearchPress = () => {
    onSearchPress && onSearchPress()
  }

  const handleSavedFlightPress = () => {
    onSavedFlightPress && onSavedFlightPress()
  }

  return (
    <Animated.View
      style={[flyHeaderStyles.headerContainer, headerAnimatedStyle]}
      pointerEvents={canPress ? "auto" : "none"}
    >
      <View style={flyHeaderStyles.headerRow}>
        <Text tx="flyLandingV2.fly" style={flyHeaderStyles.headerTitle} />
        <TouchableOpacity
          onPress={handleSearchPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <SearchIconV2 width={24} height={24} color={color.palette.darkestGrey} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleSavedFlightPress}
          style={flyHeaderStyles.savedFlightContainer}
        >
          <View style={{ transform: [{ rotate: "30deg" }] }}>
            <Plane width={24} height={24} color={color.palette.darkestGrey} />
          </View>
          <Text tx="flyLanding.saved" style={flyHeaderStyles.savedFlightText} />
        </TouchableOpacity>
      </View>
    </Animated.View>
  )
}

const flyHeaderStyles = StyleSheet.create({
  headerContainer: {
    height: HEADER_HEIGHT,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    shadowColor: "#121212",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    zIndex: 999,
    elevation: 5,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    marginTop: 61,
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: color.palette.darkestGrey,
    textAlign: "center",
    position: "absolute",
    left: 0,
    right: 0,
  },
  savedFlightContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  savedFlightText: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
    marginLeft: 4,
  },
})
