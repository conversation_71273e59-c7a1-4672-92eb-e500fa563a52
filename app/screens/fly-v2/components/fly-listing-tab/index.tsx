import React from "react"
import { View, Text, TouchableOpacity } from "react-native"
import LinearGradient from "react-native-linear-gradient"
import styles from "./styles"
import { useFlightListingContext } from "../../contexts/flight-listing-context"

function FlyListingTab({ state, descriptors, navigation }) {
  const { isLoading, setSelectedTab } = useFlightListingContext()

  return (
    <View style={styles.tabContainer}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key]
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name

        const isFocused = state.index === index

        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          })

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name)

            setSelectedTab(route.name)
          }
        }

        if (isFocused) {
          return (
            <LinearGradient
              key={route.key}
              colors={["#F6E9FF", "#FCFCFC"]}
              start={{ x: 0.5, y: 0 }}
              end={{ x: 0.5, y: 1 }}
              style={[
                styles.tabBtnActive,
                index === 0 ? styles.tabBtnActiveLeft : styles.tabBtnActiveRight,
              ]}
            >
              <TouchableOpacity
                style={styles.tabBtnTouch}
                onPress={onPress}
                activeOpacity={0.85}
                disabled={isLoading}
              >
                <Text style={styles.tabBtnTextActive}>{label}</Text>
              </TouchableOpacity>
            </LinearGradient>
          )
        }
        return (
          <TouchableOpacity
            key={route.key}
            style={[
              styles.tabBtnInactive,
              index === 0 ? styles.tabBtnInactiveLeft : styles.tabBtnInactiveRight,
            ]}
            onPress={onPress}
            activeOpacity={0.85}
            disabled={isLoading}
          >
            <Text style={styles.tabBtnTextInactive}>{label}</Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
}

export default FlyListingTab
