import { Platform, StyleSheet } from "react-native"
import { color, typography } from "app/theme"

export default StyleSheet.create({
  tabBtnInactive: {
    height: 32,
    backgroundColor: color.palette.lightestGrey,
    borderColor: color.palette.lighterGrey,
    borderWidth: 1,
    borderStyle: "solid",
    width: 102,
    justifyContent: "center",
    alignItems: "center",
  },
  tabBtnActive: {
    backgroundColor: "#f3eaff",
    width: 130,
    height: 38,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  tabBtnTextActive: {
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0,
  },
  tabBtnTextInactive: {
    color: color.palette.almostBlackGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    textAlign: "center",
  },
  tabBtnTouch: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  tabContainer: {
    alignItems: "flex-end",
    flexDirection: "row",
    justifyContent: "center",
  },
  tabBtnInactiveLeft: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    width: 102,
  },
  tabBtnInactiveRight: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 0,
    width: 121,
  },
  tabBtnActiveLeft: {
    width: 108,
  },
  tabBtnActiveRight: {
    width: 130,
  },
})
