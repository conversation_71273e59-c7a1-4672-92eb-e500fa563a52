import { useNavigation } from "@react-navigation/native"
import type { StackNavigationProp } from "@react-navigation/stack"
import { Text } from "app/elements/text/text"
import { ArrowRight, FlightTakeoff } from "assets/icons"
import React, { useCallback } from "react"
import { TouchableOpacity, View } from "react-native"
import styles from "./styles"

export type RootStackParamList = {
  flightResultLandingScreen: { screen: "ARR" | "DEP" }
}
type NavigationProp = StackNavigationProp<RootStackParamList, "flightResultLandingScreen">

const TrackTodayFlightsBanner = () => {
  const navigation = useNavigation<NavigationProp>()

  const handleNavigate = useCallback(
    (tab: "ARR" | "DEP") =>
      navigation.navigate("flightResultLandingScreen", {
        screen: tab,
      }),
    [],
  )

  return (
    <View style={styles.container}>
      <View style={styles.liveBadge}>
        <View style={styles.liveTextContainer}>
          <Text style={styles.liveText} tx="flightLanding.live" />
        </View>
        <FlightTakeoff />
      </View>
      <View style={styles.content}>
        <Text style={styles.subTitle} tx="flightLanding.realTimeUpdates" />
        <Text style={styles.title} tx="flightLanding.trackTodayFlights" />
      </View>
      <View style={styles.buttonGroup}>
        <TouchableOpacity style={styles.button} onPress={() => handleNavigate("ARR")}>
          <Text style={styles.buttonText} tx="flightLanding.arr" />
          <View style={styles.iconContainer}>
            <ArrowRight width={16} height={12} />
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={() => handleNavigate("DEP")}>
          <Text style={styles.buttonText} tx="flightLanding.dep" />
          <View style={styles.iconContainer}>
            <ArrowRight width={16} height={12} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default TrackTodayFlightsBanner
