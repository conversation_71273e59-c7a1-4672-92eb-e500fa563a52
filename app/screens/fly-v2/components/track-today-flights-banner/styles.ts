import { StyleSheet, Platform } from "react-native"
import { color, typography } from "app/theme"

const styles = StyleSheet.create({
  container: {
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginVertical: 16,
    ...Platform.select({
      ios: {
        shadowColor: color.palette.almostBlackGrey,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.08,
        shadowRadius: 20,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  liveBadge: {
    marginRight: 8,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  liveTextContainer: {
    backgroundColor: color.palette.basegreen,
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 4,
    height: 22,
    lineHeight: 22,
    position: "relative",
    right: -4,
    zIndex: 10,
    width: 37,
  },
  liveText: {
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: 11,
    lineHeight: 14,
    width: 37,
    fontFamily: typography.bold,
  },
  content: {
    flex: 1,
  },
  subTitle: {
    color: color.palette.darkestGrey,
    fontSize: 12,
    lineHeight: 16,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    fontFamily: typography.medium,
  },
  title: {
    color: color.palette.almostBlackGrey,
    fontSize: 12,
    lineHeight: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    fontFamily: typography.bold,
  },
  buttonGroup: {
    flexDirection: "row",
    gap: 8,
    marginLeft: 10,
  },
  button: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.palette.lightestPurple,
    paddingVertical: 8,
    paddingHorizontal: 12,
    width: 49,
    height: 48,
    gap: 4,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  buttonText: {
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    textAlign: "center",
    fontFamily: typography.bold,
    width: 25,
  },
  iconContainer: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: color.palette.memberTierIconBackground,
    alignItems: "center",
    justifyContent: "center",
  },
})

export default styles
