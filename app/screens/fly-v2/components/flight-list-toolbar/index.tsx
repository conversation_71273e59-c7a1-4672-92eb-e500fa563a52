import React, { useMemo } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/elements/text/text"
import styles from "./styles"
import { useFlightListingContext } from "../../contexts/flight-listing-context"
import { color } from "app/theme"

interface FlightListToolbarProps {
  onGetEarlierFlights?: () => void
  componentName?: string
}

function FlightListToolbar({ onGetEarlierFlights, componentName }: FlightListToolbarProps) {
  const { isLoading } = useFlightListingContext()

  const loadEarlierFlightsButtonStyle = useMemo<ViewStyle[]>(() => {
    return [styles.loadBtn, isLoading && { borderColor: color.palette.greyCCCCCC }]
  }, [isLoading])

  const loadEarlierFlightsTextStyle = useMemo<TextStyle[]>(() => {
    return [styles.loadBtnText, isLoading && { color: color.palette.darkGrey999 }]
  }, [isLoading])

  const getEarlierFlights = () => {
    if (onGetEarlierFlights && typeof onGetEarlierFlights === "function") {
      onGetEarlierFlights()
    }
  }

  return (
    <View>
      {/* Notice */}
      <Text style={styles.notice} tx="flightLanding.flightNotice" />

      <TouchableOpacity
        disabled={isLoading}
        onPress={getEarlierFlights}
        style={loadEarlierFlightsButtonStyle}
        testID={`${componentName ?? ""}__ButtonFlightsHandler`}
        accessibilityLabel={`${componentName ?? ""}__ButtonFlightsHandler`}
      >
        <Text preset={'caption1Bold'} style={loadEarlierFlightsTextStyle} tx="flightLanding.loadEarlierFlights" />
      </TouchableOpacity>
    </View>
  )
}
export default FlightListToolbar
