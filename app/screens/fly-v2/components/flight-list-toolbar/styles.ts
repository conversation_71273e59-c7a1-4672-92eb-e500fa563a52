import { StyleSheet, Dimensions, Platform } from "react-native"
import { color, typography } from "app/theme"

const { width } = Dimensions.get("window")

export default StyleSheet.create({
  notice: {
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    fontFamily: typography.regular,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0,
    paddingHorizontal: 20,
    color: color.palette.darkestGrey,
  },
  loadBtn: {
    alignSelf: "center",
    marginVertical: 20,
    width: width - 20 * 2,
    marginHorizontal: 20,
    borderRadius: 60,
    borderWidth: 1,
    borderColor: color.palette.purpleD5BBEA,
    backgroundColor: color.palette.whiteGrey,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadBtnText: {
    color: color.palette.lightPurple,
  },
})
