// COPIED FROM search-tab-flights/index.tsx (with modifications for new context)
import React, { useState, useCallback, forwardRef, useImperativeHandle } from "react"
import { View, Pressable, Keyboard, StyleSheet, Dimensions } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import { debounce } from "lodash"
import moment from "moment"

// COPIED FROM search-tab-flights/index.tsx (BottomSheet, Text, Cross, theme, etc. imports)
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { Text } from "app/elements/text/text"
import { Cross } from "assets/icons"
import { color, spacing } from "app/theme"
import { newPresets } from "app/elements/text"
import { palette } from "app/theme/palette"
import SearchBar from "app/sections/search-bar/search-bar"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import { translate } from "app/i18n"
import { REMOTE_FLAG_VALUE } from "app/services/firebase/remote-config"
import SearchActions, { SearchSelectors } from "app/redux/searchRedux"
import { FlightDirection } from "app/utils/constants"
import { useNavigation } from "@react-navigation/native"
import { NavigationConstants } from "app/utils/constants"
import { AdobeTagName, trackAction } from "app/services/adobe"
import FlightTabs from "app/screens/search-v2/tabs/search-tab-flights/flight-tabs"
import TabContent from "app/screens/search-v2/tabs/search-tab-flights/components/tab-content"
import AutocompleteSearch from "app/screens/search-v2/tabs/search-tab-flights/components/autocomplete-search"
import { BottomSheetType, SeachType } from "app/screens/search-v2/tabs/search-tab-flights/consts"
import Calendar from "app/screens/search-v2/tabs/search-tab-flights/components/calendar"
import { useCameraPermission } from "app/hooks"
import { useFlightListingContext } from "../../contexts/flight-listing-context"

// NEW/CHANGED IN search-bottom-sheet.tsx (constants for this component)
const SCREEN_HEIGHT = Dimensions.get('window').height
const BOTTOM_SHEET_HEIGHT = SCREEN_HEIGHT - 64

interface SearchBottomSheetProps {
}

export interface SearchBottomSheetRef {
  open: () => void
  close: () => void
  openWithDirection: (direction: FlightDirection) => void
}

export const SearchBottomSheet = forwardRef<SearchBottomSheetRef, SearchBottomSheetProps>((props, ref) => {
  // COPIED FROM search-tab-flights/index.tsx (redux, navigation, state, etc.)
  const dispatch = useDispatch()
  const navigation = useNavigation<any>()
  const autoCompleteKeywork = useSelector(SearchSelectors.searchKeyword)
  const [keySearch, setKeySearch] = useState(autoCompleteKeywork)
  const [selectedTab, setSelectedTab] = useState<FlightDirection>(FlightDirection.Arrival)
  const [selectedDate, setSelectedDate] = useState(moment())
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const { handleCameraPermission } = useCameraPermission()
  const { terminal, airline, airport, setShouldShowSearchBottomSheetOnFocus } = useFlightListingContext()

  // NEW/CHANGED IN search-bottom-sheet.tsx (imperative handle for modal open/close)
  useImperativeHandle(ref, () => ({
    open: () => setIsVisible(true),
    close: () => setIsVisible(false),
    openWithDirection: (direction: FlightDirection) => {
      setSelectedTab(direction)
      setIsVisible(true)
    },
  }))

  // COPIED FROM search-tab-flights/index.tsx (modal open/close logic)
  const openModal = () => {
    setIsModalVisible(true)
  }
  const closeModal = () => {
    setIsModalVisible(false)
  }
  const [bottomSheetType, setBottomSheetType] = useState(BottomSheetType.AutoComplete)

  // COPIED FROM search-tab-flights/index.tsx (autocomplete, debounce, search keyword logic)
  const onGetAutoCompleteKeyword = useCallback(
    (newKeyword: string) => {
      const param = { text: newKeyword.trim(), dataType: Object.values(SeachType).join(",") }
      dispatch(SearchActions.getAutoCompleteKeywordRequest(param))
      dispatch(SearchActions.setSearchKeyword(newKeyword))
    },
    [dispatch],
  )

  const onDebounceKeySearch = useCallback(debounce(onGetAutoCompleteKeyword, 200), [
    onGetAutoCompleteKeyword,
  ])

  const handleSearchKeywordChange = useCallback(
    (newKeyword: string) => {
      if (newKeyword.trim().length > 1) return onDebounceKeySearch(newKeyword)
      dispatch(SearchActions.resetListAutoCompleteKeyword())
      dispatch(SearchActions.setSearchKeyword(newKeyword))
    },
    [onDebounceKeySearch],
  )

  const updateKeySearch = useCallback(
    (value: string) => {
      setKeySearch(value)
      closeModal()
      dispatch(SearchActions.setSearchKeyword(value))
      handleSearchKeywordChange(value)
    },
    [handleSearchKeywordChange, dispatch],
  )

  // COPIED FROM search-tab-flights/index.tsx (onPressAutoCompleteItem, handleSearch, handleScanBoardingPass)
  const onPressAutoCompleteItem = (item, index) => {
    const itemPosition = `${index + 1}`
    const tabLabel = translate("search.tabTitles.flights")
    const flightDirectionLabel = selectedTab === FlightDirection.Arrival
      ? translate("flightLanding.arrivalTabTitle")
      : translate("flightLanding.departureTabTitle")

    trackAction(AdobeTagName.CAppSearchAutoComplete, {
      [AdobeTagName.CAppSearchAutoComplete]: `${tabLabel}(${flightDirectionLabel}) | ${autoCompleteKeywork} | ${item?.name} | ${itemPosition}`
    })

    updateKeySearch(item.name)
  }

  const handleSearch = () => {
    navigation.navigate(NavigationConstants.searchFlightsV2Result, {
      keyword: keySearch,
      date: selectedDate,
      direction: selectedTab,
      terminal: terminal.current,
      airline: airline.current,
      airport: airport.current,
      onGoBack: (params) => {
        if (!params) return
        params.direction && setSelectedTab(params.direction)
        params.date && setSelectedDate(params.date)
        params.keyword && setKeySearch(params.keyword)
      },
    })
    // We set this to true so that the search bottom sheet will be shown when the user returns to the flight listing screen.
    setShouldShowSearchBottomSheetOnFocus(true)
    setIsVisible(false)
  }

  const handleScanBoardingPass = () => {
    // We set this to true so that the search bottom sheet will be shown when the user returns to the flight listing screen.
    setShouldShowSearchBottomSheetOnFocus(true)
    setIsVisible(false)
    handleCameraPermission(() => {
      navigation.navigate("scanCode", { shouldTrackDetectedFlightNumber: true })
    })
  }
  // COPIED FROM search-tab-flights/index.tsx (renderTabContent)
  const renderTabContent = useCallback(
    () => (
      <TabContent
        searchTerm={keySearch}
        date={selectedDate}
        onSearchTermPress={() => {
          openModal()
          handleSearchKeywordChange(keySearch)
          setBottomSheetType(BottomSheetType.AutoComplete)
        }}
        onDatePress={() => {
          openModal()
          setBottomSheetType(BottomSheetType.DatePicker)
        }}
        onSearch={handleSearch}
        onScanBoardingPass={handleScanBoardingPass}
      />
    ),
    [keySearch, selectedDate, handleSearch],
  )

  // COPIED FROM search-tab-flights/index.tsx (JSX structure for BottomSheet, header, FlightTabs, etc.)
  return (
    <BottomSheet
      isModalVisible={isVisible}
      onClosedSheet={() => setIsVisible(false)}
      stopDragCollapse
      onBackPressHandle={() => setIsVisible(false)}
      containerStyle={styles.container}
      animationInTiming={300}
      animationOutTiming={300}
      openPendingModal
    >
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle} tx={`flightListingV2.searchFlights`} />
        <View style={styles.headerIcon}>
          <Cross width={24} height={24} onPress={() => setIsVisible(false)} />
        </View>
      </View>

      <FlightTabs
        selectedTab={selectedTab}
        onSelectTab={setSelectedTab}
        content={renderTabContent()}
      />

      <BottomSheet
        isModalVisible={isModalVisible}
        onClosedSheet={closeModal}
        stopDragCollapse
        onBackPressHandle={closeModal}
        containerStyle={bottomSheetStyles.container}
        animationInTiming={300}
        animationOutTiming={300}
        openPendingModal
      >
        <View style={bottomSheetStyles.headerContainer}>
          <Text
            style={bottomSheetStyles.headerTitle}
            tx={
              bottomSheetType === BottomSheetType.DatePicker
                ? "searchV2.flightsTab.datePickerTitle"
                : `searchV2.flightsTab.${selectedTab}.title`
            }
          />
          <View style={bottomSheetStyles.headerIcon}>
            <Cross width={24} height={24} onPress={closeModal} />
          </View>
        </View>

        {bottomSheetType === BottomSheetType.DatePicker ? (
          <Calendar
            selectedDate={selectedDate}
            onSelectDate={(date) => {
              setSelectedDate(date)
              closeModal()
            }}
          />
        ) : (
          <Pressable style={bottomSheetStyles.contentContainer} onPress={Keyboard.dismiss}>
            <SearchBar
              useInputFieldV2
              searchAutoCompleteFlag={REMOTE_FLAG_VALUE.ON}
              containerStyle={bottomSheetStyles.searchBar}
              inputContainerStyle={bottomSheetStyles.searchInput}
              inputProps={{
                placeholder: translate("searchV2.placeHolder.flights1"),
                placeholderList: [
                  translate("searchV2.placeHolder.flights1"),
                  translate("searchV2.placeHolder.flights2"),
                ],
              }}
              isShowBack={false}
              tab={SearchIndex.flights}
              keyword={keySearch}
              onChangeKeyword={handleSearchKeywordChange}
              onSearchClear={() => {
                dispatch(SearchActions.setSearchKeyword(""))
                handleSearchKeywordChange("")
              }}
              onSubmitLocal={() => {
                updateKeySearch(autoCompleteKeywork)
                Keyboard.dismiss()
              }}
              returnKeyType="previous"
              testID={""}
              accessibilityLabel={""}
            />
            <View style={bottomSheetStyles.textContainer}>
              <Text
                tx={`searchV2.flightsTab.${selectedTab}.information`}
                style={bottomSheetStyles.informationText}
              />
              <Text
                style={bottomSheetStyles.askText}
                tx={`searchV2.flightsTab.${selectedTab}.ask`}
                onPress={() =>
                  setSelectedTab(
                    selectedTab === FlightDirection.Arrival
                      ? FlightDirection.Departure
                      : FlightDirection.Arrival,
                  )
                }
              />
            </View>
            <AutocompleteSearch
              containerStyle={tabContentStyles.autocompleteContainerStyle}
              keySearch={autoCompleteKeywork.trim()}
              flightType={selectedTab}
              handleItemOnPress={onPressAutoCompleteItem}
            />
          </Pressable>
        )}
      </BottomSheet>
    </BottomSheet>
  )
})

// COPIED FROM search-tab-flights/index.tsx (styles, with possible tweaks for this file)
const styles = StyleSheet.create({
  container: {
    height: BOTTOM_SHEET_HEIGHT,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
    backgroundColor: color.palette.whiteGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    height: 64,
    position: "relative",
    width: "100%",
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: palette.almostBlackGrey,
    height: 22,
  },
  headerIcon: {
    position: "absolute",
    right: 20,
  },
})

const tabContentStyles = StyleSheet.create({
  autocompleteContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
})

const bottomSheetStyles = StyleSheet.create({
  container: {
    height: BOTTOM_SHEET_HEIGHT,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
    backgroundColor: color.palette.whiteGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    height: 64,
    position: "relative",
    width: "100%",
    backgroundColor: color.palette.transparent,
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: palette.almostBlackGrey,
    height: 22,
  },
  headerIcon: {
    position: "absolute",
    right: 20,
  },
  contentContainer: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
    width: "100%",
  },
  searchBar: {
    backgroundColor: color.palette.almostWhiteGrey,
    marginTop: 0,
    paddingLeft: 20,
    paddingRight: 20,
  },
  searchInput: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  textContainer: {
    marginTop: spacing[5],
  },
  informationText: {
    ...newPresets.caption1Regular,
    textAlign: "center",
    color: color.palette.darkestGrey,
  },
  askText: {
    ...newPresets.caption1Bold,
    color: color.palette.lightPurple,
    textAlign: "center",
  },
})

export default SearchBottomSheet
