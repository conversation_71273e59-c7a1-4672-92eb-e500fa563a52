import { ErrorCloudV2 } from "assets/icons"
import { Platform, StyleSheet, View, ViewStyle } from "react-native"
import { Text } from "app/elements/text"
import LinearGradient from "react-native-linear-gradient"
import { Button } from "app/elements/button/button"
import { color, typography } from "app/theme"
import { useSelector } from 'react-redux';
import { AemSelectors } from 'app/redux/aemRedux';
import { translate } from "app/i18n"

interface ApiErrorProps {
  containerStyle?: ViewStyle
  onPressReload?: () => void
}

export const ApiError = ({ onPressReload, containerStyle }: ApiErrorProps) => {
  const dataCommonAEM = useSelector(AemSelectors.getErrorsCommon) || []
  const ehr42: {
    header?: string,
    subHeader?: string,
    buttonLabel?: string,
  } = dataCommonAEM?.find((e) => e?.code === "EHR42")

  const title = ehr42?.header || translate("errors.EHR42.title")
  const description = ehr42?.subHeader || translate("errors.EHR42.description")
  const cta = ehr42?.buttonLabel || translate("errors.EHR42.cta")
  
  const handleReload = () => {
    onPressReload && onPressReload()
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <ErrorCloudV2 width={120} height={120} />
      <Text style={styles.title} text={title} />
      <Text style={styles.description} text={description} />
      <LinearGradient
        style={styles.buttonGradient}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
      >
        <Button
          onPress={handleReload}
          sizePreset="large"
          textPreset="buttonLarge"
          typePreset="primary"
          text={cta}
          backgroundPreset="light"
          statePreset="default"
        />
      </LinearGradient>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    marginTop: 16,
    textAlign: "center",
  },
  description: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    textAlign: "center",
    marginTop: 8,
  },
  buttonGradient: {
    borderRadius: 999,
    alignSelf: "stretch",
    marginTop: 16,
  },
})
