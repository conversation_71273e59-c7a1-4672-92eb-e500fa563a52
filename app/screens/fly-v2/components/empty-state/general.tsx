import { ErrorCloudV2 } from "assets/icons"
import { Platform, StyleSheet, View, ViewStyle } from "react-native"
import { Text } from "app/elements/text"
import LinearGradient from "react-native-linear-gradient"
import { Button } from "app/elements/button/button"
import { color, typography } from "app/theme"
import { translate } from "app/i18n"

interface GeneralProps {
  containerStyle?: ViewStyle
  callback?: () => void
  textTitle?: string
  textDescription?: string
  textButtonTitle?: string
}

export const General = ({
  callback,
  containerStyle,
  textTitle,
  textDescription,
  textButtonTitle = translate("errorOverlay.variant3.retry"),
}: GeneralProps) => {
  const handleCallback = () => {
    callback && callback()
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <ErrorCloudV2 width={120} height={120} />
      <Text style={styles.title} text={textTitle ?? ""} />
      <Text style={styles.description} text={textDescription ?? ""} />
      <LinearGradient
        style={styles.buttonGradient}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
      >
        <Button
          onPress={handleCallback}
          sizePreset="large"
          textPreset="buttonLarge"
          typePreset="primary"
          text={textButtonTitle ?? ''}
          backgroundPreset="light"
          statePreset="default"
        />
      </LinearGradient>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    marginTop: 24,
    textAlign: "center",
  },
  description: {
    fontFamily: typography.regular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    textAlign: "center",
    marginTop: 8,
  },
  buttonGradient: {
    borderRadius: 999,
    alignSelf: "stretch",
    marginTop: 24,
  },
})
