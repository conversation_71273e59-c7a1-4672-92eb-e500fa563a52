import { ErrorCloudNoResults } from "assets/icons"
import { Platform, StyleSheet, View, ViewStyle } from "react-native"
import { Text } from "app/elements/text"
import { color, typography } from "app/theme"

interface NoResultsProps {
  containerStyle?: ViewStyle
}

export const NoResults = ({ containerStyle }: NoResultsProps) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <ErrorCloudNoResults width={120} height={120} />
      <Text style={styles.title} tx={"errors.noResults.title"} />
      <Text style={styles.description} tx={"errors.noResults.description"} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    marginTop: 8,
    textAlign: "center",
  },
  description: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    textAlign: "center",
    marginTop: 8,
  },
})
