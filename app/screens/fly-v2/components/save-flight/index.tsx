import React, { useEffect, useRef, useState } from "react"
import { useNavigation, useIsFocused } from "@react-navigation/native"
import { translate } from "app/i18n"
import { useDispatch, useSelector } from "react-redux"
import { save, StorageKey } from "app/utils/storage"
import { size } from "lodash"
import moment from "moment"
import ConfirmSaveFlight from "app/components/flight-details-card/confirm-popup-save-flight"
import { handleImageUrl } from "app/utils/media-helper"
import { AemSelectors } from "app/redux/aemRedux"
import {
  getLastSavedFlightTime,
  setIsShowModalCheckRatingPopup,
  setLastSavedFlightTime,
} from "app/utils/storage/mmkv-storage"
import { FlightDirection, NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { DURATION, FeedBackToast, FeedBackToastType } from "app/components/feedback-toast"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { DateFormats } from "app/utils/date-time/date-time"
import { FlyCreators } from "app/redux/flyRedux"
import { FLIGHT_TYPE } from "app/screens/fly/flights/flight-details/useFlightDetailV2"
import { SCREEN_NAME } from "app/screens/dine-shop-v2/dine-shop-v2.constants"
import AddReturnCalendar from "app/screens/fly/flights/add-return-calendar"
import { TravelOption } from "app/screens/fly/flights/flight-details/flight-detail.props"
import SavedFlightTravelOptionsModal from "app/screens/fly/flights/save-flight-travel-option/save-flight-trave-option-wrap"
import { rootStyles } from "app/screens/search-v2/search-result/search-flights-result/styles"
import ModalManagerActions from "app/redux/modalManagerRedux"
import { useModal } from "app/hooks/useModal"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { useFlightListingContext } from "../../contexts/flight-listing-context"
import { ProfileSelectors } from "app/redux/profileRedux"
import { env } from "app/config/env-params"
import SearchActions from "app/redux/searchRedux"
import SearchDepartureFlight from "app/components/search-departure-flight"

const SaveFlight = () => {
  const dispatch = useDispatch()
  const navigation = useNavigation<any>()
  const isFocused = useIsFocused()
  const toastForSavedFlight = useRef(null)
  const {
    saveFlightPayload,
    isModalVisible,
    closeModal,
    selectedTravelOption,
    setSelectedTravelOption,
  } = useFlightListingContext()
  const [loadingSaveFlight, setLoadingSaveFlight] = useState(false)
  const [selectedFlightType, setSelectedFlightType] = useState(null)
  const [showCalendarModal, setShowCalendarModal] = useState(false)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const {
    isModalVisible: isModalVisibleConnectingFlight,
    openModal: openModalConnectingFlight,
    closeModal: closeModalConnectingFlight,
  } = useModal("saveConnectingFlightListingV2")

  const insertFlightPayload = useSelector(MytravelSelectors.insertFlightPayload)
  const dataCommonAEM = useSelector(AemSelectors.getMessagesCommon)
  const msg47 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG47")

  useEffect(() => {
    if (insertFlightPayload?.insertFlightData?.success || insertFlightPayload?.recordExist) {
      if (insertFlightPayload?.isInsertSuccessfully && isFocused) {
        setLoadingSaveFlight(false)
        const timeStamp = new Date().getTime()
        // if user hasnt save any flight within 24hours or user has only 1 saved flight
        // show save connecting flight modal
        // else show native share popup and finish save
        const showConnectingFlight =
          (getLastSavedFlightTime() + env()?.FLIGHT_SHOW_POPUP_ADD_RETURN < timeStamp ||
            size(myTravelFlightsPayload?.getMyTravelFlightDetails) === 1) &&
          insertFlightPayload?.flightData?.isPassenger

        if (!showConnectingFlight) {
          closeModal()
          toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
        } else {
          openModalConnectingFlight()
          setLastSavedFlightTime(0)
          dispatch(SearchActions.resetAutoCompleteFlight())
          save(StorageKey.isSaveFlightTriggered, true)
        }
      }
    }
    if (insertFlightPayload?.errorFlag) {
      setLoadingSaveFlight(false)
      closeModal()
    }
  }, [insertFlightPayload])

  const savedFlightOnPress = () => {
    setLoadingSaveFlight(true)
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: saveFlightPayload?.flightNumber,
      flightScheduledDate: saveFlightPayload?.scheduledDate,
      flightDirection: FlightDirection.Arrival,
      // check param
      flightPax: selectedTravelOption === TravelOption.iAmTravelling,
    }
    if (isLoggedIn) {
      dispatch(MytravelCreators.flyMyTravelInsertFlightRequest(data, { item: saveFlightPayload }))
    } else {
      //@ts-ignore
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.FLIGHTS,
      })
    }
  }

  const showToastForSaveFlightSuccess = () => {
    toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
  }

  const openSearchDepartureFlightModal = () => {
    dispatch(ModalManagerActions.openModal("searchDepartureFlight"))
  }

  const travelOptionTapped = (option) => {
    setSelectedTravelOption(option)
  }

  const savedFlightTravelOptionsOnModalHide = () => {
    setSelectedTravelOption(null)
    setIsShowModalCheckRatingPopup(false)
  }

  const onDateSelectedAddReturnCalendar = (dateString: string) => {
    const country = saveFlightPayload?.country || ""
    const date = moment(dateString)
    setShowCalendarModal(false)
    dispatch(FlyCreators.setFlightSearchDate(date.format("YYYY-MM-DD")))

    navigation.navigate(NavigationConstants.searchFlightsV2Result, {
      keyword: country,
      date,
      direction:
        saveFlightPayload?.direction === FlightDirection.Departure
          ? FlightDirection.Arrival
          : FlightDirection.Departure,
    })
  }

  const handleConnectingFlightOnPress = () => {
    const connectingFlightPayloadData = {
      isConnecting: true,
      flightConnecting: {
        ...insertFlightPayload?.flightData?.item,
        isPassenger: insertFlightPayload?.flightData?.isPassenger,
      },
    }
    dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData))
    setSelectedFlightType(FLIGHT_TYPE.CONNECTING)
    closeModalConnectingFlight()
  }

  return (
    <>
      <FeedBackToast
        ref={toastForSavedFlight}
        style={rootStyles.feedBackToastStyle}
        textButtonStyle={rootStyles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 8 }}
        textStyle={rootStyles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved")}
        onCallback={() => dispatch(MytravelCreators.flyClearInsertFlightPayload())}
      />
      <SavedFlightTravelOptionsModal
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={isModalVisible}
        onClosed={closeModal}
        loadingSaveFlight={loadingSaveFlight}
        onBackPressed={closeModal}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={savedFlightOnPress}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={saveFlightPayload?.direction}
      />
      <ConfirmSaveFlight
        imageUrl={handleImageUrl(msg47?.icon)}
        visible={isModalVisibleConnectingFlight}
        title={translate("flightDetails.newPopupConfirmSaveFlight.title")}
        messageText={translate("flightDetails.newPopupConfirmSaveFlight.arrivalMessage")}
        onClose={() => {
          closeModalConnectingFlight()
          save(StorageKey.isSaveFlightTriggered, true)
        }}
        onButtonPressed={() => {
          setSelectedFlightType(FLIGHT_TYPE.RETURN)
          closeModalConnectingFlight()
        }}
        textButtonConfirm={translate(
          "flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton",
        )}
        textButtonCancel={translate("flightDetails.newPopupConfirmSaveFlight.cancelButton")}
        onModalHide={() => {
          const timeStamp = new Date().getTime()
          setIsShowModalCheckRatingPopup(false)
          setLastSavedFlightTime(timeStamp)
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          if (selectedFlightType) {
            if (
              saveFlightPayload.direction === FlightDirection.Arrival &&
              selectedFlightType === FLIGHT_TYPE.CONNECTING
            ) {
              openSearchDepartureFlightModal()
            } else {
              setShowCalendarModal(true)
            }
            setSelectedFlightType(null)
          } else {
            toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
            dispatch(MytravelCreators.flyClearInsertFlightPayload())
          }
        }}
        isShowButtonConnection={saveFlightPayload?.direction === FlightDirection.Arrival}
        onButtonConnectionPressed={handleConnectingFlightOnPress}
        textButtonConnection={translate(
          "flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton",
        )}
        disableCloseButton={true}
        openPendingModal
      />
      <AddReturnCalendar
        isVisible={showCalendarModal}
        filterDate={moment(saveFlightPayload?.displayTimestamp).format(DateFormats.YearMonthDay)}
        initialMinDate={moment(saveFlightPayload?.displayTimestamp).format(
          DateFormats.YearMonthDay,
        )}
        onClosedCalendarModal={() => {
          setShowCalendarModal(false)
          toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          const connectingFlightPayloadToClear = {
            isConnecting: false,
            flightConnecting: null,
          }
          dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear))
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          save(StorageKey.isSaveFlightTriggered, true)
        }}
        onDateSelected={onDateSelectedAddReturnCalendar}
        testID={`${SCREEN_NAME}__AddReturnCalendar`}
        accessibilityLabel={`${SCREEN_NAME}__AddReturnCalendar`}
      />
      <SearchDepartureFlight
        handleOnClose={showToastForSaveFlightSuccess}
        displayTimestamp={saveFlightPayload?.displayTimestamp}
      />
    </>
  )
}

export default SaveFlight
