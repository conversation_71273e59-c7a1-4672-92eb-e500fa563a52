import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { color, spacing } from "app/theme"
import { StyleSheet, View, ViewStyle } from "react-native"
import FlightListToolbar from "../flight-list-toolbar"

const lightGreyLoadingColors = [color.palette.lightGrey, color.background, color.palette.lightGrey]
const lighterGreyLoadingColors = [
  color.palette.lightGrey,
  color.background,
  color.palette.lightGrey,
]
const skeletonLayout: ViewStyle[] = [
  {
    width: 48,
    height: 48,
    borderRadius: 8,
  },
  {
    width: 271,
    height: 12,
    marginBottom: spacing[3],
    borderRadius: 4,
  },
  {
    width: 148,
    height: 12,
    marginBottom: spacing[3],
    borderRadius: 4,
  },
  {
    width: 80,
    height: 12,
    borderRadius: 4,
  },
  {
    width: 110,
    height: 16,
    marginLeft: spacing[4],
    borderRadius: 4,
    marginBottom: spacing[5],
  },
]

export const LoadingSkeleton = () => {
  const renderItem = (_, i) => {
    return (
      <View key={i} style={styles.itemContainer}>
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lightGreyLoadingColors}
          shimmerStyle={skeletonLayout[0]}
        />
        <View style={styles.itemDetail}>
          <View>
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={lighterGreyLoadingColors}
              shimmerStyle={skeletonLayout[1]}
            />
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={lighterGreyLoadingColors}
              shimmerStyle={skeletonLayout[2]}
            />
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={lighterGreyLoadingColors}
              shimmerStyle={skeletonLayout[3]}
            />
          </View>
        </View>
      </View>
    )
  }

  return (
    <View>
      <FlightListToolbar />
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={lightGreyLoadingColors}
        shimmerStyle={skeletonLayout[4]}
      />
      {[...Array(6)].map(renderItem)}
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignSelf: "center",
    position: "relative",
    marginHorizontal: 20,
    marginBottom: spacing[5],
  },
  itemDetail: {
    flex: 1,
    marginBottom: spacing[0],
    borderBottomWidth: 1,
    borderColor: color.palette.lighterGrey,
    paddingBottom: 20,
    flexDirection: "row",
    marginLeft: spacing[4],
  },
})
