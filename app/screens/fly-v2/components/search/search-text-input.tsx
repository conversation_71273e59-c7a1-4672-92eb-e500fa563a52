import { FC, useRef, useState, useEffect } from "react"
import { StyleSheet, View, Keyboard, TextInput } from "react-native"
import PlaceholderCarousel from "app/sections/search-bar/components/placeholder-carousel"
import { translate } from "app/i18n"
import { color, spacing } from "app/theme"
import { presets, Text } from "app/elements/text"
import React from "react"
import { TextField } from "app/elements/text-field/text-field"

interface SearchTextInputProps {
  value: string
  onChangeText: (value: string) => void
  onFocus: () => void
  onBlur: () => void
}

export const SearchTextInput: FC<SearchTextInputProps> = ({
  value,
  onChangeText,
  onFocus,
  onBlur,
}) => {
  const [focused, setFocused] = useState(false)
  const currentPlaceholderIndex = useRef(0)
  const inputRef = useRef<TextInput>(null)

  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      inputRef.current?.blur()
    })

    return () => {
      keyboardDidHideListener?.remove()
    }
  }, [])

  const handleFocus = () => {
    onFocus && onFocus()
    setFocused(true)
  }

  const handleBlur = () => {
    onBlur && onBlur()
    setFocused(false)
  }

  return (
    <View style={{ marginBottom: 16 }}>
      <Text
        preset="bodyTextBold"
        tx="searchV2.flightsTab.searchTermLabel"
        style={styles.inputLabel}
      />
      <View
        style={[
          styles.inputContainerGlow,
          {
            backgroundColor: focused ? color.palette.lightestPurple : "transparent",
          },
        ]}
      >
        <View
          style={[
            styles.inputContainer,
            {
              borderColor: focused ? color.palette.lightPurple : color.palette.lightGrey,
            },
          ]}
        >
          <TextField
            forwardedRef={inputRef}
            style={{ flex: 1 }}
            inputStyle={styles.inputText}
            placeholderTextColor={color.palette.midGrey}
            secureTextEntry={false}
            preset="noMargin"
            editable
            allowFontScaling={false}
            onFocus={handleFocus}
            onBlur={handleBlur}
            value={value}
            onChangeText={onChangeText}
            maxLength={50}
            testID="FlyLandingV2__SearchTextInput__Input"
            accessibilityLabel="FlyLandingV2__SearchTextInput__Input"
          />

          {!value && (
            <PlaceholderCarousel
              isStop={focused}
              placeholderList={[
                translate("searchV2.placeHolder.flights1"),
                translate("searchV2.placeHolder.flights2"),
              ]}
              itemHeight={44}
              textStyle={styles.placeholderText}
              currentPlaceholderIndex={currentPlaceholderIndex}
            />
          )}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  inputLabel: {
    marginBottom: 10,
    color: color.palette.almostBlackGrey,
  },
  inputContainerGlow: {
    margin: -4,
    padding: 4,
    borderRadius: 10,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 36,
    backgroundColor: color.palette.whiteGrey,
  },
  inputText: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    backgroundColor: "transparent",
    textAlignVertical: "center",
    minHeight: 36,
    paddingTop: 0,
    paddingBottom: 0,
  },
  placeholderText: {
    paddingStart: spacing[3],
  },
})
