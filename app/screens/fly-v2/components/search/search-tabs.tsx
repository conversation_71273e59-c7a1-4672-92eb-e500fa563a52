import React, { memo, useCallback, useEffect, useMemo } from "react"
import { Dimensions, Keyboard, StyleSheet, TouchableOpacity, View } from "react-native"
import { FlightDirection } from "app/utils/constants"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  runOnJS,
} from "react-native-reanimated"
import {
  ActiveArrivalHeader,
  ActiveDepartureHeader,
  ORIGINAL_ACTIVE_HEADER,
} from "./search-svg-headers"
import { palette } from "app/theme/palette"
import { FlightArrivalIcon, FlightDepartureIcon } from "assets/icons"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"

// SEARCH TABS COMPONENT DOCUMENTATION
//
// This component implements a custom animated tab switcher for flight search (Arrival/Departure).
// It is visually complex, combining SVG headers (from Figma) and animated content transitions.
//
// STRUCTURE OVERVIEW:
// 1. Inactive Headers: Static UI showing both tabs (not interactive, for background only).
// 2. Active Headers: Two animated SVG headers (Arrival and Departure). Only one is visible at a time, and they animate horizontally when switching tabs. Each header has an invisible touch area to trigger tab switching.
// 3. Content: Contains the tab content area, which is animated horizontally to match the selected tab. The content area includes:
//    - Content Frame: The outer border/frame (with rounded corners and border).
//    - Content Background: The white background with rounded corners, offset to align with the SVG header.
//    - Content Row: A row containing two copies of the content (one for each tab), which slides horizontally during tab switch.
//
// ANIMATION & LAYOUT NOTES:
// - SVG headers are exported at a fixed size from Figma and scaled to fit the screen width.
// - The content and headers are offset and layered to create a seamless visual transition between tabs and headers.
// - The content for both tabs is rendered (cloned) and animated horizontally; only the active tab's content is visible at a time.
// - Tab switching is handled with reanimated transitions and invisible touchable areas over the headers.
//
// DEVELOPER NOTES:
// - The component uses several workarounds to align SVG and native content, including manual offsets and scaling factors.
// - All layout constants are calculated based on screen width and original SVG dimensions for responsiveness.
// - The component is memoized for performance.
//
// Props:
// - selectedTab: Which tab is currently active (FlightDirection.Arrival or Departure)
// - onSelectTab: Callback when a tab is selected
// - children: The content to render inside the tab area (will be cloned for both tabs)

const SCREEN_WIDTH = Dimensions.get("screen").width
const BORDER_WIDTH = 1
const CONTAINER_WIDTH = SCREEN_WIDTH - 2 * 16
const CONTENT_WIDTH = CONTAINER_WIDTH - 2 * BORDER_WIDTH
const SCALE_FACTOR = CONTAINER_WIDTH / ORIGINAL_ACTIVE_HEADER.WIDTH
// SVG Headers are exported with fixed size. Need to scale according to screen size
const SCALED_ACTIVE_HEADER = {
  WIDTH: Math.floor(CONTAINER_WIDTH),
  HEIGHT: Math.floor(ORIGINAL_ACTIVE_HEADER.HEIGHT * SCALE_FACTOR + 1), // need to add 1 to make header display properly with scaled size,
  CONTENT_OFFSET: Math.floor(ORIGINAL_ACTIVE_HEADER.CONTENT_OFFSET * SCALE_FACTOR),
}
const CONTENT_FRAME_OFFSET = 4
const CONTENT_BACKGROUND_OFFSET = SCALED_ACTIVE_HEADER.CONTENT_OFFSET - CONTENT_FRAME_OFFSET

type SearchTabsProps = {
  selectedTab: FlightDirection
  onSelectTab: (type: FlightDirection) => void
  children: React.ReactElement
}

export const SearchTabs = memo<SearchTabsProps>((props) => {
  const { selectedTab, onSelectTab, children } = props

  const arrivalHeaderX = useSharedValue(
    selectedTab === FlightDirection.Arrival ? 0 : CONTAINER_WIDTH,
  )

  const departureHeaderX = useSharedValue(
    selectedTab === FlightDirection.Arrival ? -CONTAINER_WIDTH : 0,
  )

  const contentX = useSharedValue(0)

  useEffect(() => {
    arrivalHeaderX.value = selectedTab === FlightDirection.Arrival ? 0 : CONTAINER_WIDTH
    departureHeaderX.value = selectedTab === FlightDirection.Arrival ? -CONTAINER_WIDTH : 0
    contentX.value = selectedTab === FlightDirection.Arrival ? 0 : -CONTAINER_WIDTH
  }, [selectedTab])

  const animatedArrivalStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: arrivalHeaderX.value }],
    position: "absolute",
  }))

  const animatedDepartureStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: departureHeaderX.value }],
    position: "absolute",
  }))

  const animatedContentRowStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: contentX.value }],
  }))

  const selectTab = useCallback(
    (direction: FlightDirection) => {
      Keyboard.dismiss()
      if (direction === FlightDirection.Arrival) {
        arrivalHeaderX.value = withSequence(
          withTiming(CONTAINER_WIDTH / 2, { duration: 0 }),
          withTiming(0, { duration: 300 }),
        )
        departureHeaderX.value = withTiming(-CONTAINER_WIDTH, { duration: 0 })
        contentX.value = withTiming(
          0,
          { duration: 300 },
          (finished) => finished && runOnJS(onSelectTab)(direction),
        )
      } else {
        arrivalHeaderX.value = withTiming(CONTAINER_WIDTH, { duration: 0 })
        departureHeaderX.value = withSequence(
          withTiming(-(CONTAINER_WIDTH / 2), { duration: 0 }),
          withTiming(0, { duration: 300 }),
        )
        contentX.value = withTiming(
          -CONTAINER_WIDTH,
          { duration: 300 },
          (finished) => finished && runOnJS(onSelectTab)(direction),
        )
      }
    },
    [arrivalHeaderX, departureHeaderX, onSelectTab, selectedTab],
  )

  const clonedContent = useMemo(() => React.cloneElement(children), [children])

  return (
    <Animated.View style={styles.container}>
      {/* Inactive Headers - Dummy UI */}
      <View style={styles.inactiveHeadersContainer}>
        <View style={styles.inactiveHeaderButton}>
          <FlightArrivalIcon width={24} height={24} color={color.palette.midGrey} />
          <Text tx={"flightLanding.arrivalTabTitle"} style={styles.inactiveHeaderTitle} />
        </View>
        <View style={styles.inactiveHeaderButton}>
          <FlightDepartureIcon width={24} height={24} color={color.palette.midGrey} />
          <Text tx={"flightLanding.departureTabTitle"} style={styles.inactiveHeaderTitle} />
        </View>
      </View>
      {/* Active Headers - SVG Headers */}
      <View style={styles.activeHeadersContainer}>
        <Animated.View style={animatedArrivalStyle}>
          <ActiveArrivalHeader
            width={SCALED_ACTIVE_HEADER.WIDTH}
            height={SCALED_ACTIVE_HEADER.HEIGHT}
          />
          {/* Pseudo Departure Header - Not visible but can be pressed to change to Departure Tab */}
          <TouchableOpacity
            style={styles.pseudoDepartureHeaderButton}
            onPress={() => selectTab(FlightDirection.Departure)}
            testID="FlyLandingV2__SearchTabs__DepartureTab"
            accessibilityLabel="FlyLandingV2__SearchTabs__DepartureTab"
          />
        </Animated.View>
        <Animated.View style={animatedDepartureStyle}>
          <ActiveDepartureHeader
            width={SCALED_ACTIVE_HEADER.WIDTH}
            height={SCALED_ACTIVE_HEADER.HEIGHT}
          />
          {/* Pseudo Arrival Header - Not visible but can be pressed to change to Departure Tab */}
          <TouchableOpacity
            style={styles.pseudoArrivalHeaderButton}
            onPress={() => selectTab(FlightDirection.Arrival)}
            testID="FlyLandingV2__SearchTabs__ArrivalTab"
            accessibilityLabel="FlyLandingV2__SearchTabs__ArrivalTab"
          />
        </Animated.View>
      </View>

      {/* Content Frame */}
      <Animated.View style={styles.contentFrame}>
        {/* Content Background */}
        <Animated.View style={styles.contentBackground}>
          {/* Content Row */}
          <Animated.View style={[animatedContentRowStyle, { flexDirection: "row" }]}>
            {/* Content (for Arrival tab) */}
            <Animated.View style={styles.content}>{props.children}</Animated.View>
            {/* Cloned Content (for Departure tab) */}
            <Animated.View style={styles.content}>{clonedContent}</Animated.View>
          </Animated.View>
        </Animated.View>
      </Animated.View>
    </Animated.View>
  )
})

const styles = StyleSheet.create({
  container: {
    shadowColor: "#121212",
    shadowOpacity: 0.08,
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowRadius: 20,
  },
  inactiveHeadersContainer: {
    position: "absolute",
    top: 4,
    left: 1,
    right: 1,
    height: SCALED_ACTIVE_HEADER.HEIGHT,

    flexDirection: "row",
    alignItems: "stretch",

    backgroundColor: palette.lightestGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    borderColor: palette.lighterGrey,
    borderWidth: 1,
    borderBottomWidth: 0,
  },
  inactiveHeaderButton: {
    flex: 1,
    flexDirection: "row",
    gap: 2,
    paddingLeft: 24,
    paddingTop: 6,
  },
  inactiveHeaderTitle: {
    ...newPresets.tabsSmall,
    color: palette.darkestGrey,
  },
  activeHeadersContainer: {
    width: SCALED_ACTIVE_HEADER.WIDTH,
    height: SCALED_ACTIVE_HEADER.HEIGHT,
    overflow: "hidden",
  },
  pseudoDepartureHeaderButton: {
    width: SCALED_ACTIVE_HEADER.WIDTH / 2,
    height: SCALED_ACTIVE_HEADER.HEIGHT,
    position: "absolute",
    top: 0,
    right: 0,
  },
  pseudoArrivalHeaderButton: {
    width: SCALED_ACTIVE_HEADER.WIDTH / 2,
    height: SCALED_ACTIVE_HEADER.HEIGHT,
    position: "absolute",
    left: 0,
    right: 0,
  },
  contentFrame: {
    width: Math.floor(CONTAINER_WIDTH),
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: "#D5BBEA",
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
    marginTop: -CONTENT_FRAME_OFFSET,
  },
  contentBackground: {
    width: Math.floor(CONTENT_WIDTH),
    overflow: "hidden",
    backgroundColor: "white",
    marginTop: -CONTENT_BACKGROUND_OFFSET,
    borderRadius: 16,
  },
  content: {
    padding: 20,
    width: Math.floor(CONTAINER_WIDTH),
  },
})
