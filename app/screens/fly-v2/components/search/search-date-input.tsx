import { FC, useMemo, useState } from "react"
import { StyleSheet, TouchableOpacity, View } from "react-native"
import { CalenderDob, Cross } from "assets/icons"
import { color, spacing } from "app/theme"
import { newPresets, presets, Text } from "app/elements/text"
import moment, { Moment } from "moment"
import { DateFormats } from "app/utils/date-time/date-time"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { palette } from "app/theme/palette"
import Calendar from "app/screens/search-v2/tabs/search-tab-flights/components/calendar"
import React from "react"

interface SearchDateInputProps {
  value: Moment
  onChangeDate: (date: Moment) => void
}

export const SearchDateInput: FC<SearchDateInputProps> = (props) => {
  const { value, onChangeDate } = props
  const [modalVisible, setModalVisible] = useState(false)
  const dateFormatted = useMemo(() => {
    const inputDate = moment(value)
    return inputDate.calendar(null, {
      sameDay: `[Today:] ${DateFormats.DayDateMonthYear}`,
      nextDay: `[Tomorrow:] ${DateFormats.DayDateMonthYear}`,
      nextWeek: DateFormats.DayDateMonthYear,
      lastDay: DateFormats.DayDateMonthYear,
      lastWeek: DateFormats.DayDateMonthYear,
      sameElse: DateFormats.DayDateMonthYear,
    })
  }, [value])

  const showModal = () => {
    setModalVisible(true)
  }

  const closeModal = () => {
    setModalVisible(false)
  }

  const handleSelectDate = (date) => {
    onChangeDate(date)
    closeModal()
  }

  return (
    <>
      <TouchableOpacity style={styles.opacityButton} onPress={showModal} testID="FlyLandingV2__SearchDateInput__Button" accessibilityLabel="FlyLandingV2__SearchDateInput__Button">
        <View style={{ marginBottom: 16 }}>
          <Text preset="bodyTextBold" tx="searchV2.flightsTab.date" style={styles.inputLabel} />
          <View style={styles.inputContainer}>
            <CalenderDob width={20} height={20} style={styles.calendarIcon} />
            <Text style={styles.inputText}>{dateFormatted}</Text>
          </View>
        </View>
      </TouchableOpacity>

      <BottomSheet
        isModalVisible={modalVisible}
        onClosedSheet={closeModal}
        stopDragCollapse
        onBackPressHandle={closeModal}
        containerStyle={bottomSheetStyles.container}
        animationInTiming={300}
        animationOutTiming={300}
        openPendingModal
      >
        <View style={bottomSheetStyles.headerContainer}>
          <Text style={bottomSheetStyles.headerTitle} tx={"searchV2.flightsTab.datePickerTitle"} />
          <View style={bottomSheetStyles.headerIcon}>
            <Cross width={24} height={24} onPress={closeModal} />
          </View>
        </View>

        <Calendar selectedDate={value} onSelectDate={handleSelectDate} />
      </BottomSheet>
    </>
  )
}

const styles = StyleSheet.create({
  inputLabel: {
    marginBottom: 10,
    color: color.palette.almostBlackGrey,
  },
  inputContainer: {
    borderColor: color.palette.lightGrey,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 36,
    backgroundColor: color.palette.whiteGrey,
  },
  inputText: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
  },
  opacityButton: {
    pointerEvents: "box-only",
  },
  textAlign: {
    paddingStart: spacing[3],
  },
  calendarIcon: {
    alignSelf: "center",
    marginRight: spacing[2],
  },
})

export const bottomSheetStyles = StyleSheet.create({
  container: {
    height: 749,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
    backgroundColor: color.palette.whiteGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    height: 64,
    position: "relative",
    width: "100%",
    backgroundColor: color.palette.transparent,
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: palette.almostBlackGrey,
    height: 22,
  },
  headerIcon: {
    position: "absolute",
    right: 20,
  },
})
