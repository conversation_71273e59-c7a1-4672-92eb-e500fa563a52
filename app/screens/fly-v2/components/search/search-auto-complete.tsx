import { color } from "app/theme"
import { FlightDirection } from "app/utils/constants"
import { memo, useCallback, useEffect, useMemo, useState, useRef } from "react"
import { StyleSheet, TouchableOpacity, View } from "react-native"
import { Text } from "app/elements/text"
import path from "app/services/api/apis.json"
import { env } from "app/config/env-params"
import restApi from "app/services/api/request"
import Responsive from "app/utils/responsive"
import { debounce } from "lodash"
import React from "react"

interface SearchAutoCompleteProps {
  search: string
  visible: boolean
  flightDirection: FlightDirection
  onKeywordPress: (keyword: string) => void
}

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

export const SearchAutoComplete = memo<SearchAutoCompleteProps>((props) => {
  const { search, onKeywordPress, visible, flightDirection } = props
  const [autoCompleteKeywords, setAutoCompleteKeywords] = useState([])

  const fetchAutoCompleteKeywordsRef = useRef(
    debounce(async (searchKeyword: string) => {
      const params = {
        text: searchKeyword,
        data_type: "flights,airlines,airports",
      }
      try {
        // NOTE: this code is copied from searchSaga
        const paramsArray = path.getAutoCompleteKeyword.split(" ")
        const method = paramsArray[0] || "GET"
        const url = env()?.SEARCH_GATEWAY_URL + paramsArray[1]
        const response = await restApi({ url, method, parameters: params })
        // @ts-ignore
        if (!response.success || response.errors) {
          setAutoCompleteKeywords([])
          return
        }
        const autoCompleteKeywords = response?.data?.list || []
        setAutoCompleteKeywords(autoCompleteKeywords)
      } catch (error) {
        setAutoCompleteKeywords([])
      }
    }, 300)
  )

  useEffect(() => {
    const trimmedSearch = search.trim()
    if (trimmedSearch.length <= 1) {
      setAutoCompleteKeywords([])
    } else {
      fetchAutoCompleteKeywordsRef.current(trimmedSearch)
    }

    return () => {
      fetchAutoCompleteKeywordsRef.current.cancel()
    }
  }, [search])

  const handleKeywordPress = (keyword: string) => {
    onKeywordPress && onKeywordPress(keyword)
  }

  const results = useMemo(() => {
    return autoCompleteKeywords
      .filter(
        (item) =>
          item.data_type?.toLowerCase() !== "flights" ||
          item.direction?.toLowerCase() === flightDirection.toLowerCase(),
      )
      .slice(0, 4)
  }, [autoCompleteKeywords, flightDirection])

  const renderAutoCompleteKeyword = useCallback(
    (searchKeyword: string, item: any, index: number) => {
      const regex = new RegExp(`(${escapeRegExp(searchKeyword)})`, "i") // case-insensitive
      const parts = item.name.split(regex)

      return (
        <TouchableOpacity
          style={styles.keywordContainer}
          onPress={() => handleKeywordPress(item.name)}
          testID={`FlyLandingV2__AutoComplete__Suggestion__${index}`}
          accessibilityLabel={`FlyLandingV2__AutoComplete__Suggestion__${index}`}
        >
          <Text style={styles.keywordText}>
            {parts.map((part: string, index: number) => {
              const isMatch = part.toLowerCase() === searchKeyword.toLowerCase()
              return (
                <Text
                  key={index}
                  style={
                    isMatch && {
                      color: color.palette.almostBlackGrey,
                    }
                  }
                  preset={isMatch ? "bodyTextBold" : undefined}
                >
                  {part}
                </Text>
              )
            })}
          </Text>
          {item.data_type?.toLowerCase() === "flights" && (
            <>
              <Text style={styles.keywordDotSeparator}>·</Text>
              <Text preset="caption2Regular">{item.text}</Text>
            </>
          )}
        </TouchableOpacity>
      )
    },
    [handleKeywordPress],
  )

  if (!visible || results.length === 0) return <View />

  return (
    <View style={styles.listKeywordsContainer} testID="FlyLandingV2__AutoComplete__Container" accessibilityLabel="FlyLandingV2__AutoComplete__Container">
      {results.map((item, index) => (
        <React.Fragment key={item.id || item.name}>
          {renderAutoCompleteKeyword(search.trim(), item, index)}
        </React.Fragment>
      ))}
    </View>
  )
})

const styles = StyleSheet.create({
  listKeywordsContainer: {
    position: "absolute",
    backgroundColor: "white",
    top: 66,
    left: 0,
    right: 0,
    borderWidth: 1,
    borderBottomWidth: 0,
    borderColor: color.palette.lighterGrey,
  },
  keywordContainer: {
    height: 54,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  keywordText: {
    color: color.palette.almostBlackGrey,
    fontSize: Responsive.getFontSize(16),
  },
  keywordDotSeparator: {
    marginHorizontal: 4,
    fontSize: 16,
    fontWeight: 700,
  },
})
