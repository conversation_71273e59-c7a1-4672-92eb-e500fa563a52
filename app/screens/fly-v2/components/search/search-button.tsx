import { But<PERSON> } from "app/elements/button/button"
import { color } from "app/theme"
import { FC } from "react"
import LinearGradient from "react-native-linear-gradient"
interface SearchButtonProps {
  disabled?: boolean
  onPress: () => void
}
export const SearchButton: FC<SearchButtonProps> = (props) => {
  const { disabled, onPress } = props

  const activeGradient = [color.palette.gradientColor1End, color.palette.gradientColor1Start]
  const disabledGradient = [color.palette.lighterGrey, color.palette.lighterGrey]
  const handlePress = () => {
    onPress && onPress()
  }
  return (
    <LinearGradient
      style={{
        borderRadius: 60,
        width: "100%",
      }}
      start={{ x: 1, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={disabled ? disabledGradient : activeGradient}
    >
      <Button
        sizePreset="large"
        textPreset="buttonLarge"
        typePreset="secondary"
        tx={"searchV2.flightsTab.searchFlights"}
        statePreset="default"
        backgroundPreset="light"
        onPress={handlePress}
        disabled={disabled}
        testID="FlyLandingV2__SearchButton__Button"
        accessibilityLabel="FlyLandingV2__SearchButton__Button"
        style={[
          {
            height: 40,
          },
          disabled && {
            borderColor: color.palette.greyCCCCCC,
            borderWidth: 1,
          },
        ]}
        textStyle={
          disabled && {
            color: color.palette.darkGrey999,
          }
        }
      />
    </LinearGradient>
  )
}
