import React, { useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useCallback } from "react"
import { Keyboard, StyleSheet, TouchableOpacity, View } from "react-native"
import { FlightDirection, NavigationConstants } from "app/utils/constants"
import { Scan } from "assets/icons"
import { color, spacing } from "app/theme"
import { Text } from "app/elements/text"
import moment from "moment"
import { useCameraPermission } from "app/hooks"
import { AdobeTagName, trackAction } from "app/services/adobe"
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated"
import { SearchButton } from "./search-button"
import { SearchAutoComplete } from "./search-auto-complete"
import { SearchTabs } from "./search-tabs"
import { SearchTextInput } from "./search-text-input"
import { SearchDateInput } from "./search-date-input"

const COLLAPSED_CONTENT_HEIGHT = 0
const EXPANED_CONTENT_HEIGHT = 132

interface SearchProps {
  navigation: any
}

export const Search = forwardRef((props: SearchProps, ref) => {
  const { navigation } = props

  const [selectedTab, setSelectedTab] = useState(FlightDirection.Arrival)
  const [search, setSearch] = useState("")
  const [searchAutoCompleteVisible, setSearchAutoCompleteVisible] = useState(false)
  const [date, setDate] = useState(moment())
  const { handleCameraPermission } = useCameraPermission()

  const contentHeight = useSharedValue(COLLAPSED_CONTENT_HEIGHT)
  const animatedContentStyle = useAnimatedStyle(() => ({
    height: contentHeight.value,
    overflow: "hidden",
  }))

  const collapse = () => {
    contentHeight.value = withTiming(COLLAPSED_CONTENT_HEIGHT, { duration: 300 })
    setSearchAutoCompleteVisible(false)
  }

  const reset = () => {
    collapse()
    setSearch("")
    setDate(moment())
    setSelectedTab(FlightDirection.Arrival)
  }

  useImperativeHandle(ref, () => ({
    collapse,
    reset,
  }))

  const handleScanBoardingPass = useCallback(() => {
    trackAction(AdobeTagName.CAppSearchResultFlyScanBoardingPass, {
      [AdobeTagName.CAppSearchResultFlyScanBoardingPass]: '1',
    })
    
    handleCameraPermission(() => {
      navigation.navigate("scanCode", { shouldTrackDetectedFlightNumber: true })
    })
  }, [handleCameraPermission, navigation])

  const handleFocus = () => {
    contentHeight.value = withTiming(EXPANED_CONTENT_HEIGHT, { duration: 300 })
    setSearchAutoCompleteVisible(true)
  }

  const handleBlur = () => {
    setSearchAutoCompleteVisible(false)
  }

  const handleSearchChange = (value: string) => {
    setSearchAutoCompleteVisible(true)
    setSearch(value)
  }

  const handleKeywordPress = (keyword: string) => {
    setSearchAutoCompleteVisible(false)
    setSearch(keyword)

    // If Keyboard.dismiss() is called immediately after setSearch,
    // TextInput's onChangeText may be triggered again with the old value.
    // We delay the dismiss slightly to ensure the new value is applied first.
    setTimeout(Keyboard.dismiss, 50)
  }

  const handleSearch = () => {
    navigation.navigate(NavigationConstants.searchFlightsV2Result, {
      keyword: search,
      date: date,
      direction: selectedTab,
      onGoBack: (params) => {
        if (!params) return
        params.direction && setSelectedTab(params.direction)
        params.date && setDate(params.date)
        params.keyword && setSearch(params.keyword)
      },
    })
  }

  return (
    <View style={{ flex: 1 }} testID="FlyLandingV2__SearchContainer" accessibilityLabel="FlyLandingV2__SearchContainer">
      <SearchTabs selectedTab={selectedTab} onSelectTab={setSelectedTab}>
        <View>
          <SearchTextInput
            value={search}
            onChangeText={handleSearchChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />

          <Animated.View style={animatedContentStyle}>
            <SearchDateInput value={date} onChangeDate={setDate} />
            <SearchButton disabled={!search} onPress={handleSearch} />
          </Animated.View>

          <View style={styles.bottomContainer}>
            <View style={styles.bottomCaption}>
              <View style={styles.divider} />
              <Text preset="caption2Bold" tx="searchV2.flightsTab.or" />
              <View style={styles.divider} />
            </View>
            <TouchableOpacity 
              style={styles.scanButtonContainer} 
              onPress={handleScanBoardingPass}
              testID="FlyLandingV2__ScanButton__Button"
              accessibilityLabel="FlyLandingV2__ScanButton__Button"
            >
              <Scan width={20} height={20} color={color.palette.lightPurple} />
              <Text
                preset="bodyTextBold"
                style={styles.textStyle}
                tx="searchV2.flightsTab.scanBoardingPass"
              />
            </TouchableOpacity>
          </View>

          <SearchAutoComplete
            search={search}
            visible={searchAutoCompleteVisible}
            flightDirection={selectedTab}
            onKeywordPress={handleKeywordPress}
          />
        </View>
      </SearchTabs>
    </View>
  )
})

const styles = StyleSheet.create({
  opacityButton: {
    pointerEvents: "box-only",
  },
  textAlign: {
    paddingStart: spacing[3],
  },
  calendarIcon: {
    alignSelf: "center",
    marginRight: spacing[2],
  },
  bottomContainer: {
    display: "flex",
    paddingTop: 8,
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 20,
  },
  bottomCaption: {
    flexDirection: "row",
    gap: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  divider: {
    borderColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
    flex: 1,
  },
  scanButtonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    alignContent: "center",
    width: "100%",
  },
  textStyle: {
    color: color.palette.lightPurple,
  },
})
