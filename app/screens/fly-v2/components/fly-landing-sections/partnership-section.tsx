import { Dimensions, StyleSheet, View } from "react-native"
import Svg, { Path } from "react-native-svg"
import { color } from "app/theme"

const SCREEN_WIDTH = Dimensions.get("window").width

export const PartnershipSection = () => {
  return (
    <View style={styles.container}>
      <View style={styles.cloudContainer}>
        <CloudSVG />
      </View>

      <View style={styles.skeleton} />
    </View>
  )
}

const CloudSVG = (props) => (
  <Svg
    style={{ width: SCREEN_WIDTH, height: (SCREEN_WIDTH / 375) * 95 }}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 375 95"
    fill="none"
    {...props}
  >
    <Path
      fill="#fff"
      d="M237 50.559c0-10.898-16.566-19.89-38.144-21.487-8.087-11.248-30.14-19.341-56.154-19.341-10.253 0-19.908 1.257-28.325 3.463C103.773 5.259 85.372 0 64.391 0c-20.98 0-37.731 4.81-48.5 12.146C8.588 10.599.543 9.73-7.936 9.73c-31.275 0-56.876 11.686-59.27 26.517C-89.876 38.352-107 47.984-107 59.56c0 13.114 21.97 23.742 49.078 23.742 3.363 0 6.643-.17 9.82-.479 8.252 5 20.918 8.204 35.153 8.204 10.748 0 20.588-1.826 28.324-4.87C26.205 91.597 41.37 95 58.223 95c16.855 0 32.822-3.593 43.694-9.291 10.005 3.692 22.466 5.908 36.019 5.908 26.035 0 48.109-8.104 56.175-19.371 23.848-.54 42.868-10.04 42.868-21.717l.021.03Z"
    />
    <Path
      fill="#fff"
      d="M470 50.559c0-10.898-16.566-19.89-38.144-21.487-8.087-11.248-30.14-19.341-56.154-19.341-10.253 0-19.908 1.257-28.325 3.463C336.773 5.259 318.372 0 297.391 0c-20.98 0-37.731 4.81-48.5 12.146-7.303-1.547-15.348-2.415-23.827-2.415-31.275 0-56.876 11.686-59.269 26.517C143.123 38.352 126 47.984 126 59.56c0 13.114 21.971 23.742 49.078 23.742 3.363 0 6.643-.17 9.82-.479 8.252 5 20.918 8.204 35.153 8.204 10.748 0 20.588-1.826 28.324-4.87C259.206 91.597 274.369 95 291.223 95c16.855 0 32.822-3.593 43.694-9.291 10.005 3.692 22.466 5.908 36.019 5.908 26.035 0 48.109-8.104 56.175-19.371 23.848-.54 42.868-10.04 42.868-21.717l.021.03Z"
    />
  </Svg>
)

const styles = StyleSheet.create({
  container: {
    position: "relative",
    height: 112,
    backgroundColor: color.palette.whiteGrey,
    width: "100%",
  },
  cloudContainer: {
    position: "absolute",
    top: -32,
    left: 0,
    right: 0,
  },
  skeleton: {
    marginHorizontal: 40,
    height: 76,
    borderRadius: 16,
    backgroundColor: color.palette.lighterGrey,
  }
})
