import { color } from "app/theme"
import { StyleSheet, View } from "react-native"

export const QuickLinksSection = () => {
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <View style={styles.skeleton} />
        <View style={styles.skeleton} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    height: 238,
    backgroundColor: color.palette.whiteGrey,
    marginTop: 20,
    marginHorizontal: 16,
  },
  headerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 4,
    gap: 16,
  },
  skeleton: {
    flex: 1,
    height: 24,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
  },
})
