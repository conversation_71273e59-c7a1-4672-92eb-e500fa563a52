import React, { createContext, useContext, useRef, ReactNode, useState } from "react"
import { SearchBottomSheetRef } from "../components/search-bottom-sheet"
import { FlightDirection } from "app/utils/constants"
import { flyModuleUpdatedTime } from "app/utils/date-time/date-time"
import { FlightListingProps } from "app/components/flight-listing-card/flight-listing-card.props"
import { useModal } from "app/hooks/useModal"
import { TravelOption } from "app/screens/fly/flights/flight-details/flight-detail.props"
import { SharedValue, useSharedValue } from "react-native-reanimated"

interface FlightListingContextType {
  openSearchBottomSheet: (direction?: FlightDirection) => void
  closeSearchBottomSheet: () => void
  searchBottomSheetRef: React.RefObject<SearchBottomSheetRef>
  shouldShowSearchBottomSheetOnFocus: React.MutableRefObject<boolean>
  setShouldShowSearchBottomSheetOnFocus: (val: boolean) => void
  terminal: React.MutableRefObject<string[] | undefined>
  setTerminal: (terminal: string[] | undefined) => void
  airline: React.MutableRefObject<string | undefined>
  setAirline: (airline: string | undefined) => void
  airport: React.MutableRefObject<string | undefined>
  setAirport: (airport: string | undefined) => void
  lastUpdated: string
  setLastUpdated: (val: string) => void
  isLoading: boolean
  setIsLoading: (val: boolean) => void
  setSaveFlightPayload: (fly: FlightListingProps) => void
  saveFlightPayload: FlightListingProps
  isModalVisible: boolean
  openModal: () => void
  closeModal: () => void,
  setSelectedTravelOption: (options: string) => void,
  selectedTravelOption: string,
  handleSelectTravelOptionAndOpenModal: (flight: FlightListingProps) => void,
  scrollY: SharedValue<number>
  isRefreshing: boolean
  setIsRefreshing: (val: boolean) => void
  selectedTab?: string
  setSelectedTab?: (tab: string) => void
  currentScrollPosition: SharedValue<number>
  positionStartRefresh: SharedValue<number>
  sharedRefreshing: SharedValue<number>
  contentPosition: SharedValue<number>
}

const FlightListingContext = createContext<FlightListingContextType | undefined>(undefined)

interface FlightListingContextProviderProps {
  children: ReactNode
}

export const FlightListingContextProvider: React.FC<FlightListingContextProviderProps> = ({ children }) => {
  const [lastUpdated, setLastUpdated] = useState(flyModuleUpdatedTime());
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(true);
  const [selectedTab, setSelectedTab] = useState<string>(FlightDirection.Arrival)
  
  const searchBottomSheetRef = useRef<SearchBottomSheetRef>(null)
  const [saveFlightPayload, setSaveFlightPayload] = useState(null)
  const [selectedTravelOption, setSelectedTravelOption] = useState(null)
  const {isModalVisible, openModal, closeModal} = useModal("saveFlightTravelOptionLightListingV2")
  
  // This ref is used to control whether the search bottom sheet should be shown when FlightListing regains focus.
  const shouldShowSearchBottomSheetOnFocus = useRef<boolean>(false)

  const terminal = useRef<string[] | undefined>(undefined)
  const airline = useRef<string | undefined>(undefined)
  const airport = useRef<string | undefined>(undefined)
  const scrollY = useSharedValue(0);
  const currentScrollPosition = useSharedValue(0);
  const positionStartRefresh = useSharedValue(0); //finger position when start pull to refresh
  const sharedRefreshing = useSharedValue(0);
  const contentPosition = useSharedValue(0);

  const setTerminal = (val: string[] | undefined) => { terminal.current = val }
  const setAirline = (val: string | undefined) => { airline.current = val }
  const setAirport = (val: string | undefined) => { airport.current = val }

  const openSearchBottomSheet = (direction?: FlightDirection) => {
    if (!direction) {
      return searchBottomSheetRef.current?.open()
    }

    searchBottomSheetRef.current?.openWithDirection(direction)
  }

  const closeSearchBottomSheet = () => {
    searchBottomSheetRef.current?.close()
  }

  const setShouldShowSearchBottomSheetOnFocus = (val: boolean) => {
    shouldShowSearchBottomSheetOnFocus.current = val
  }

  const handleSelectTravelOptionAndOpenModal = (saveFlight) => {
    const travelOption =
      saveFlight?.direction === FlightDirection.Departure
        ? TravelOption.iAmTravelling
        : TravelOption.iAmPicking

    setSelectedTravelOption(travelOption)
    setSaveFlightPayload(saveFlight)
    openModal()
  }

  const value: FlightListingContextType = {
    openSearchBottomSheet,
    closeSearchBottomSheet,
    searchBottomSheetRef,
    shouldShowSearchBottomSheetOnFocus,
    setShouldShowSearchBottomSheetOnFocus,
    terminal,
    setTerminal,
    airline,
    setAirline,
    airport,
    setAirport,
    lastUpdated,
    setLastUpdated,
    isLoading,
    setIsLoading,
    saveFlightPayload,
    setSaveFlightPayload,
    isModalVisible, 
    openModal,
    closeModal,
    selectedTravelOption,
    setSelectedTravelOption,
    handleSelectTravelOptionAndOpenModal, 
    scrollY,
    isRefreshing,
    setIsRefreshing,
    selectedTab,
    setSelectedTab,
    currentScrollPosition,
    positionStartRefresh,
    sharedRefreshing,
    contentPosition,
  }

  return (
    <FlightListingContext.Provider value={value}>
      {children}
    </FlightListingContext.Provider>
  )
}

export const useFlightListingContext = (): FlightListingContextType => {
  const context = useContext(FlightListingContext)
  if (context === undefined) {
    throw new Error("useFlightListingContext must be used within a FlightListingContextProvider")
  }
  return context
} 