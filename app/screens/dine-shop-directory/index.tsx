import React from "react"
import { View } from "react-native"
import { styles } from "./styles"
import {
  BottomSheetFilter,
  ItemContent,
  ViewFilter,
  ViewError,
  ViewLoading
} from "./component"
import { useFunction } from "./useFunction"
import CollapsibleHeader from "app/components/collapsible-header"
import { translate } from "app/i18n"
import { DineShopDirectoryBackground } from "assets/backgrounds"
import { SortBy } from "app/sections/staff-perk/filter-bottom-sheet/filter-bottom-sheet.constants"
import { ViewEmpty } from "./component/view-empty"

const DineShopDirectory = (props) => {
  const { route } = props

  const { filterData: initSelectedData } = route?.params || {}

  const {
    navigation,
    error,
    loading,
    data,
    handlePressReloadError,
    showFilterModal,
    openModalFilter,
    closeModalFilter,
    errorFilter,
    loadingFilter,
    dataFilter,
    setDataFilter,
    handlePressReloadFilter,
    dataFilterOriginal,
    rootListRef,
    setDataSubFilter,
    perkItemOffsetListRef,
    rootItemOffsetRef,
  } = useFunction(initSelectedData)

  const renderFilterBar = () => {
    return (
      <>
        {loading || error ? null : <ViewFilter
          containerStyle={styles.background}
          openModalFilter={openModalFilter}
          errorFilter={errorFilter}
          loadingFilter={loadingFilter}
          dataFilter={dataFilter}
          setDataFilter={setDataSubFilter}
        />}
      </>
    )
  }

  return (
    <View style={styles.container}>
      <CollapsibleHeader
        navigation={navigation}
        headerImageSource={DineShopDirectoryBackground}
        headerTitle={translate("dineShopDirectory.title")}
        renderFilter={renderFilterBar}
        hasError={!!error}
        renderError={() => (
          <ViewError typeError={error} handlePressReload={handlePressReloadError} />
        )}
        isLoading={loading}
        listData={data}
        perkItemOffsetListRef={perkItemOffsetListRef}
        rootItemOffsetRef={rootItemOffsetRef}
        rootListRef={rootListRef}
        data={data}
        renderItem={({ index, item }) => {
          return (
            <ItemContent
              item={item}
              index={index}
              perkItemOffsetListRef={perkItemOffsetListRef}
              dataLength={data?.length}
            />
          )
        }}
        nameField="title"
        sortBy={loading || error || data?.legnth <= 5 ? SortBy.LatestAddedDate : SortBy.AZ}
        keyExtractor={(item: any) => item?.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
        initialNumToRender={600}
        windowSize={50}
        scrollEnabled={data?.length > 0}
        customComponentLoading={<ViewLoading />}
        isScrollToIndex={true}
        ListEmptyComponent={<ViewEmpty />}
      />
      <BottomSheetFilter
        showFilterModal={showFilterModal}
        closeModalFilter={closeModalFilter}
        errorFilter={errorFilter}
        loadingFilter={loadingFilter}
        dataFilter={dataFilter}
        dataFilterOriginal={dataFilterOriginal}
        setDataFilter={(data) => setDataFilter(data)}
        handlePressReloadFilter={handlePressReloadFilter}
      />
    </View>
  )
}

export default DineShopDirectory
