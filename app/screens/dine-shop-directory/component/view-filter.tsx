import { Text } from "app/elements/text/text"
import { color, typography } from "app/theme"
import { ArrowDown, FilterV2, LocationOutline, SearchIconV2 } from "assets/icons"
import React from "react"
import { StyleSheet, View, ScrollView, TouchableOpacity, Platform } from "react-native"
import { useNavigation } from "@react-navigation/native"
import {
  findChildTagByNameInData,
  getLocationTagTitles,
  hasActiveTag,
  hasActiveTagDataRoot,
} from "../dine-shop-directory-until"
import { TypeSubFilter } from "../dine-shop-directory.constants"
import { CloseFilterMultipleNoti } from "ichangi-fe/assets/icons"

const ViewFilter = React.memo((props: any) => {
  const { containerStyle, openModalFilter, errorFilter, loadingFilter, dataFilter, setDataFilter } = props
  const navigation = useNavigation<any>()

  const isOnlyShopActive = dataFilter && findChildTagByNameInData(dataFilter, 'shop')?.isActive === true && !findChildTagByNameInData(dataFilter, 'dine')?.isActive
  const hasActive = dataFilter ? hasActiveTagDataRoot(dataFilter) : dataFilter
  const hasTerminalActive =
    dataFilter ? hasActiveTag(dataFilter[1]?.childTags) : false
  const txtLocation = dataFilter ? getLocationTagTitles(dataFilter[1]?.childTags) : ''
  const hasActivePublic = dataFilter ? findChildTagByNameInData(dataFilter, TypeSubFilter.Public)?.isActive : false
  const hasActiveHalal = dataFilter ? findChildTagByNameInData(dataFilter, TypeSubFilter.Halal)?.isActive : false
  const hasActiveTransit = dataFilter ? findChildTagByNameInData(dataFilter, TypeSubFilter.Transit)?.isActive : false

  const handleNavigateSearch = () => {
    navigation.navigate("search", {focusTextInput: true})
  }

  const filterItem = (type) => {
    setDataFilter(type)
  }

  const renderTxtLocation = () => {
    if (txtLocation?.length > 0 && hasTerminalActive) return txtLocation
    return "Location"
  }

  return (
    <ScrollView showsHorizontalScrollIndicator={false} horizontal={true} bounces={false} style={[styles.borderRadius, containerStyle]}>
      <View style={styles.container}>
        <TouchableOpacity style={styles.viewItem} onPress={handleNavigateSearch}>
          <SearchIconV2 width={16} height={16} color={color.palette.darkestGrey} />
        </TouchableOpacity>
        <TouchableOpacity
          style={hasActive ? styles.viewItemActive : styles.viewItem}
          onPress={openModalFilter}
        >
          <FilterV2
            width={16}
            height={16}
            color={hasActive ? color.palette.lightPurple : color.palette.darkestGrey}
          />
          {hasActive && <View style={styles.viewStatus} />}
        </TouchableOpacity>
        <TouchableOpacity
          style={[hasTerminalActive ? styles.viewItemActive : styles.viewItem, styles.viewItemRow]}
          onPress={openModalFilter}
        >
          <LocationOutline
            width={16}
            height={16}
            color={hasTerminalActive ? color.palette.lightPurple : color.palette.darkestGrey}
          />
          <Text style={hasTerminalActive ? styles.txtTitleActive : styles.txtTitleInActive}>
            {renderTxtLocation()}
          </Text>
          <ArrowDown
            width={16}
            height={16}
            color={hasTerminalActive ? color.palette.lightPurple : color.palette.darkestGrey}
          />
        </TouchableOpacity>
        {!errorFilter && !loadingFilter && (
          <>
            <TouchableOpacity
              style={hasActivePublic ? styles.viewItemActive : styles.viewItem}
              onPress={() => filterItem(TypeSubFilter.Public)}
            >
              <Text style={hasActivePublic ? styles.txtTitleActive : styles.txtTitleInActive}>
                Public
              </Text>
              {hasActivePublic && <CloseFilterMultipleNoti width='8' height='8' />}
            </TouchableOpacity>
            {!isOnlyShopActive && <TouchableOpacity
              style={hasActiveHalal ? styles.viewItemActive : styles.viewItem}
              onPress={() => filterItem(TypeSubFilter.Halal)}
            >
              <Text style={hasActiveHalal ? styles.txtTitleActive : styles.txtTitleInActive}>
                Halal
              </Text>
              {hasActiveHalal && <CloseFilterMultipleNoti width='8' height='8' />}
            </TouchableOpacity>}
            <TouchableOpacity
              style={hasActiveTransit ? styles.viewItemActive : styles.viewItem}
              onPress={() => filterItem(TypeSubFilter.Transit)}
            >
              <Text style={hasActiveTransit ? styles.txtTitleActive : styles.txtTitleInActive}>
                Transit
              </Text>
              {hasActiveTransit && <CloseFilterMultipleNoti width='8' height='8' />}
            </TouchableOpacity>
          </>
        )}
      </View>
    </ScrollView>
  )
})

const styles = StyleSheet.create({
  borderRadius: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  container: {
    padding: 20,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  viewItem: {
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 10,
    borderRadius: 99,
    borderColor: color.palette.lighterGrey,
    borderWidth: 1,
  },
  viewItemActive: {
    flexDirection: 'row',
    gap: 4,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 10,
    borderRadius: 99,
    borderColor: color.palette.purpleD5BBEA,
    borderWidth: 1,
    backgroundColor: color.palette.lightestPurple,
  },
  viewItemRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  txtTitleInActive: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  txtTitleActive: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  viewStatus: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: color.palette.lightPurple,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    position: "absolute",
    right: 0,
    top: 0,
  },
})

export { ViewFilter }
