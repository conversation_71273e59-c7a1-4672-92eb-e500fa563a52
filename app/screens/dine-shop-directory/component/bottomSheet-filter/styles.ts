import { color, typography } from 'app/theme';
import { Platform, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  bottomSheetContainer: {
    height: "90%",
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  container: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16
  },
  btnCloseStyles: {
    position: 'absolute',
    right: 24,
  },
  headerFilter: {
    flexDirection: "row",
    justifyContent: "center",
    marginVertical: 21,
  },
  filterTitle: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 20,
  },
  viewContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  buttonContainer: {
    borderTopWidth: 1,
    borderColor: color.palette.lighter<PERSON>rey,
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 40,
    backgroundColor: color.palette.whiteGrey,
  },
  flexRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center"
  },
  buttonGradient: {
    width: 176,
    height: 44,
    backgroundColor: color.palette.basePurple,
    borderRadius: 60,
    marginBottom: 7,
    justifyContent: 'center',
    alignItems: 'center'
  },
  txtApply: {
    fontFamily: typography.bold,
    color: color.palette.whiteGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 20,
  },
  txtClearAll: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  viewBottom: {
    height: 40
  },
  viewLoading: {
    paddingTop: 0,
    marginTop: 80
  }
});

export { styles };