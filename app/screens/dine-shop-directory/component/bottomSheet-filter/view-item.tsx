import { useState } from "react"
import { Checkbox } from "app/elements/checkbox/checkbox"
import { Chip } from "./Chip"
import { Text } from "app/elements/text"
import { color } from "app/theme/color"
import { typography } from "app/theme/typography"
import {
  AccordionUp,
  AccordionDown,
  Check,
  TransitAreaIcon,
  PublicAreaIcon,
  DineFilter,
  ShopFilter,
  TransitAreaFillIcon,
} from "assets/icons"
import React from "react"
import { Platform, StyleSheet, View, TouchableOpacity } from "react-native"
import { hasActiveTag } from "../../dine-shop-directory-until"

const myParamsChipUI = ["Area", "Availability", "Category", "Dietary Options"]
const myParamsCollapse = [
  "Availability",
  "Dietary Options",
  "Dine Category & Cuisine",
  "Shop Category",
  "Membership & Payment",
]

const ViewItem = React.memo(
  ({ data, item, index, handleOnClickItem, handleClearSubItem, handleClearLocation, handleClearCategory }: any) => {
    const [showCollapse, setShowCollapse] = useState(false)

    const renderHeaderTop = () => {
      const dataArea = data[0]?.childTags
      const dataLocation = data[1]?.childTags
      return (
        <View style={styles.header}>
          <View style={styles.viewRowHeader}>
            <Text style={styles.title}>
              {item?.tagTitle === "Area" ? "Location" : item?.tagTitle}
            </Text>
            {(hasActiveTag(dataArea) || hasActiveTag(dataLocation)) && (
              <TouchableOpacity style={styles.buttonClear} onPress={handleClearLocation}>
                <Text tx="common.clear" style={styles.txtClear} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )
    }

    const renderHeaderCategory = () => {
      const dataCategory = data[2]?.childTags
      return (
        <View style={styles.header}>
          <View style={styles.viewRowHeader}>
            <Text style={styles.title}>
              {item?.tagTitle === "Area" ? "Location" : item?.tagTitle}
            </Text>
            {(hasActiveTag(dataCategory)) && (
              <TouchableOpacity style={styles.buttonClear} onPress={handleClearCategory}>
                <Text tx="common.clear" style={styles.txtClear} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )
    }

    const renderHeader = () => {
      const stringMatch = myParamsCollapse.includes(item.tagTitle)
      if (item?.tagTitle === "Location") return null
      if (item?.tagTitle === "Area") return renderHeaderTop()
      if (item?.tagTitle === "Category") return renderHeaderCategory()
      return (
        <TouchableOpacity
          style={[!showCollapse ? styles.headerCollapse : styles.header]}
          onPress={() => setShowCollapse(!showCollapse)}
          disabled={!stringMatch}
        >
          <View style={styles.viewRowHeader}>
            <Text
              style={[styles.title, { minWidth: item?.tagTitle === "Shop Category" ? "30%" : 0 }]}
            >
              {item?.tagTitle === "Area" ? "Location" : item?.tagTitle}
            </Text>
            {hasActiveTag(item?.childTags) && (
              <TouchableOpacity style={styles.buttonClear} onPress={handleClearSubItem}>
                <Text tx="common.clear" style={styles.txtClear} />
              </TouchableOpacity>
            )}
          </View>
          {stringMatch && <>{showCollapse ? <AccordionUp /> : <AccordionDown />}</>}
        </TouchableOpacity>
      )
    }

    const renderCheckBoxList = (childTag) => {
      const isAll = childTag?.tagTitle === "All"
      return (
        <View key={childTag?.tagTitle}>
          <Checkbox
            value={!!childTag.isActive}
            onToggle={() => {
              handleOnClickItem(isAll ? "ALL" : childTag?.tagName)
            }}
            outlineStyle={styles.outlineStyle}
            fillStyle={styles.fillStyle}
            text={childTag.tagTitle}
            icon={<Check fill={color.palette.whiteGrey} width={16} height={16} />}
            textStyle={styles.txtCheckBox}
          />
          <View style={styles.lineGrey} />
        </View>
      )
    }

    const renderColorIcon = (active) => active ? color.palette.lightPurple : color.palette.darkestGrey

    const renderIconChip = (childTag, active) => {
      switch (childTag.tagTitle) {
        case "Public Area":
          return <PublicAreaIcon width={16} height={16} color={renderColorIcon(active)}/>
        case "Transit Area":
          return active ? <TransitAreaFillIcon width={16} height={16} color={color.palette.lightPurple}/> : <TransitAreaIcon width={16} height={16} />
        case "Dine":
          return <DineFilter width={16} height={16} color={renderColorIcon(active)}/>
        case "Shop":
          return <ShopFilter width={16} height={16} color={renderColorIcon(active)}/>
        default:
          return null
      }
    }

    const renderChip = (childTag) => {
      return (
        <View key={childTag?.tagTitle} style={styles.chipsContainer}>
          <Chip
            icon={renderIconChip(childTag, !!childTag.isActive)}
            isActive={!!childTag.isActive}
            onClick={() => handleOnClickItem(childTag?.tagName)}
            text={childTag.tagTitle}
          />
        </View>
      )
    }

    const renderDataFilter = () => {
      if (!item.childTags) {
        return null
      }

      return item.childTags?.map((childTag: any, ind: number) => {
        const stringMatch = myParamsChipUI.includes(item.tagTitle)
        return stringMatch ? renderChip(childTag) : renderCheckBoxList(childTag)
      })
    }

    const getContainerDineFilterStyle = (item) => {
      const stringMatch = myParamsChipUI.includes(item.tagTitle)
      return stringMatch ? styles.viewRow : styles.viewColumn
    }

    return (
      <View style={{ marginTop: index === 1 ? -34 : 0 }}>
        {renderHeader()}
        <View style={getContainerDineFilterStyle(item)}>
          {(showCollapse || item.tagTitle === "Category" || item.tagTitle === "Location" || item.tagTitle === "Area") && <>{renderDataFilter()}</>}
        </View>
      </View>
    )
  },
)

const styles = StyleSheet.create({
  header: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  headerCollapse: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomWidth: 1,
    borderColor: color.palette.lighterGrey,
    paddingBottom: 16,
    marginTop: -34
  },
  title: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    textAlignVertical: "center",
  },
  chipsContainer: {
    marginRight: 8,
    marginBottom: 8,
  },
  viewRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  viewRowHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewColumn: {
    width: "100%",
  },
  lineGrey: {
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
    marginVertical: 12,
  },
  outlineStyle: {
    width: 16,
    height: 16,
    borderRadius: 4,
    borderColor: color.palette.midGrey,
    borderWidth: 1,
  },
  fillStyle: {
    width: 16,
    height: 16,
    borderRadius: 4,
  },
  txtClear: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  },
  buttonClear: {
    marginLeft: 8,
  },
  txtCheckBox: {
    fontFamily: typography.regular,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
  }
})

export { ViewItem }
