import { Text } from 'app/elements/text/text';
import { color } from 'app/theme/color';
import { typography } from 'app/theme/typography';
import { CloseFilterMultipleNoti } from 'assets/icons';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity } from 'react-native';

const Chip = React.memo((props: any) => {
  const { text, onClick, isActive, icon } = props;
  return (
    <TouchableOpacity onPress={onClick} style={isActive ? styles.containerActive : styles.containerInActive}>
      {icon && icon}
      <Text style={isActive ? styles.txtActive : styles.txtInActive}>{text}</Text>
      {isActive && <CloseFilterMultipleNoti width={8} height={8} />}
    </TouchableOpacity>
  )
});

const styles = StyleSheet.create({
  containerInActive: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 99,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
    alignItems: 'center',
    gap: 4
  },
  containerActive: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 99,
    borderWidth: 1,
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
    alignItems: 'center',
    gap: 4
  },
  txtActive: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  txtInActive: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  }
})

export { Chip }