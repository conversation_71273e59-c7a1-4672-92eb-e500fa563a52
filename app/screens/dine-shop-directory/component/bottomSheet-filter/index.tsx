import React, { useEffect, useState } from "react"
import { TouchableOpacity, View, FlatList } from "react-native"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { styles } from "./styles"
import { Text } from "app/elements/text/text"
import { CloseCross } from "assets/icons"
import { color } from "app/theme/color"
import LinearGradient from "react-native-linear-gradient"
import { ViewItem } from "./view-item"
import {
  filterForDine,
  moveAreaAboveLocation,
  revertFilterForDine,
  revertFilterForShop,
  toggleChildTagActive,
  filterForShop,
  findChildTagByNameInData,
  revertFilterForDineAndShop,
  clearCategoryData,
} from "../../dine-shop-directory-until"
import { ViewError } from "../view-error"
import { ErrorType } from "../../dine-shop-directory.constants"
import { ViewLoading } from "../view-loading"

const BottomSheetFilter = React.memo((props: any) => {
  const {
    showFilterModal,
    closeModalFilter,
    errorFilter,
    loadingFilter,
    dataFilter,
    dataFilterOriginal,
    setDataFilter,
    handlePressReloadFilter,
  } = props

  const [data, setData] = useState([])

  useEffect(() => {
    if (dataFilter?.length > 0) {
      setData(moveAreaAboveLocation(dataFilter))
    }
  }, [showFilterModal, dataFilter])

  const closeModal = () => {
    closeModalFilter()
  }

  const renderItem = ({ item, index }) => {
    return (
      <ViewItem
        data={data}
        item={item}
        index={index}
        handleOnClickItem={(tagName) => handleOnClickItem(item, tagName)}
        handleClearSubItem={() => handleClearSubItem(item)}
        handleClearLocation={handleClearLocation}
        handleClearCategory={() => handleClearCategory(item)}
      />
    )
  }

  const handleClearLocation = () => {
    setData(prev => prev.map(i =>
      (i.tagTitle === 'Area' || i.tagTitle === 'Location')
        ? { ...i, childTags: i.childTags.map(tag => ({ ...tag, isActive: false })) }
        : i
    ))
  }

  const handleClearCategory = (item) => {
    if (item.tagTitle === "Category") {
      if (item?.childTags[0]?.isActive === true && item?.childTags[1]?.isActive === true) {
        const dataFilterForDine = clearCategoryData(data, dataFilterOriginal)
        setData(dataFilterForDine)
      } else if (item?.childTags[0]?.isActive === true) {
        const dataFilterForDine = revertFilterForDine(data, dataFilterOriginal)
        setData(dataFilterForDine)
      } else if (item?.childTags[1]?.isActive === true) {
        const dataFilterForShop = revertFilterForShop(data, dataFilterOriginal)
        setData(dataFilterForShop)
      }
    }
  }

  const handleClearSubItem = (item) => {
    setData(prev => prev.map(i =>
      i.tagTitle === item.tagTitle
        ? { ...i, childTags: i.childTags.map(tag => ({ ...tag, isActive: false })) }
        : i
    ))
  }

  const handleOnClickItem = (item, tagName) => {
    if (item.tagTitle === "Location") {
      const locationKeys = ["jewel", "terminal 1", "terminal 2", "terminal 3", "terminal 4"];
      if (locationKeys.includes(tagName)) {
        setData(prev => prev.map(i => {
          if (i.tagTitle !== "Location") return i;
          const toggled = i.childTags.map(tag =>
            tag.tagName === tagName ? { ...tag, isActive: !tag.isActive } : tag
          );
          const allActive = locationKeys.every(key => toggled.find(t => t.tagName === key)?.isActive);
          return {
            ...i,
            childTags: toggled.map(tag =>
              tag.tagName === "All" ? { ...tag, isActive: allActive } : tag
            ),
          };
        }));
        return;
      }
    }
    if (tagName === "ALL") {
      setData((prev) =>
        prev.map((i) =>
          i.tagTitle === item.tagTitle
            ? {
              ...i,
              childTags: i.childTags.map((tag) => ({
                ...tag,
                isActive: !i.childTags.every((t) => t.isActive),
              })),
            }
            : i,
        ),
      )
    } else if (tagName === "dine") {
      if (findChildTagByNameInData(data, 'shop')?.isActive === true && !item?.childTags[0]?.isActive) {
        const dataFilterForDine = revertFilterForDineAndShop(data, dataFilterOriginal)
        setData(dataFilterForDine)
      } else if (item?.childTags[0]?.isActive) {
        const dataFilterForDine = revertFilterForDine(data, dataFilterOriginal)
        setData(dataFilterForDine)
      } else {
        const dataFilterForDine = filterForDine(data)
        setData(toggleChildTagActive(dataFilterForDine, item.tagTitle, tagName))
      }
    } else if (tagName === "shop") {
      if (findChildTagByNameInData(data, 'dine')?.isActive === true && !item?.childTags[1]?.isActive) {
        const dataFilterForShop = revertFilterForDineAndShop(data, dataFilterOriginal)
        setData(dataFilterForShop)
      } else if (item?.childTags[1]?.isActive) {
        const dataFilterForShop = revertFilterForShop(data, dataFilterOriginal)
        setData(dataFilterForShop)
      } else {
        const dataFilterForShop = filterForShop(data)
        setData(toggleChildTagActive(dataFilterForShop, item.tagTitle, tagName))
      }
    } else {
      setData(toggleChildTagActive(data, item.tagTitle, tagName))
    }
  }

  const clearAllData = () => {
    setData(dataFilterOriginal);
  }

  const renderContent = () => {
    if (errorFilter) {
      return (
        <ViewError typeError={ErrorType.ErrorDefault} handlePressReload={handlePressReloadFilter} />
      )
    } else if (loadingFilter) {
      return <ViewLoading containerStyleProps={styles.viewLoading}/>
    } else {
      return (
        <>
          <View style={styles.viewContent}>
            <FlatList
              data={data}
              renderItem={renderItem}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => item?.tagTitle}
              contentContainerStyle={{ gap: 50 }}
              ListFooterComponent={() => <View style={styles.viewBottom} />}
            />
          </View>
          <View style={styles.buttonContainer}>
            <View style={styles.flexRow}>
              <TouchableOpacity onPress={clearAllData}>
                <Text tx={"dineShopFilter.clearAll-v2"} style={styles.txtClearAll} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setDataFilter(data)
                  closeModal()
                }}
              >
                <LinearGradient
                  style={styles.buttonGradient}
                  start={{ x: 1, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  colors={[color.palette.gradientColor1End, color.palette.gradientColor1Start]}
                >
                  <Text tx={"dineShopFilter.applyFilters-v2"} style={styles.txtApply} />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )
    }
  }

  return (
    <BottomSheet
      isModalVisible={showFilterModal}
      containerStyle={styles.bottomSheetContainer}
      onClosedSheet={closeModal}
      stopDragCollapse={true}
      onBackPressHandle={closeModal}
      animationInTiming={100}
      animationOutTiming={100}
      onModalHide={() => { }}
    >
      <View style={styles.container}>
        <View style={styles.headerFilter}>
          <Text tx={"dineShopFilter.titleHeader"} style={styles.filterTitle} />
          <TouchableOpacity onPress={closeModal} style={styles.btnCloseStyles}>
            <CloseCross width={24} height={24} />
          </TouchableOpacity>
        </View>
        <>{renderContent()}</>
      </View>
    </BottomSheet>
  )
})

export { BottomSheetFilter }
