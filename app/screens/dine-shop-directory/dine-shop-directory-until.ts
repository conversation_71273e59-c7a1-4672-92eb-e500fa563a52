const filterDataListByDataFilterV2 = (dataList, dataFilter) => {
  const areaGroup = dataFilter.find((g) => g.tagTitle === "Area")
  const locationGroup = dataFilter.find((g) => g.tagTitle === "Location")
  const areaActive = areaGroup?.childTags?.filter((tag) => tag.isActive)
  const locationActive = locationGroup?.childTags?.filter((tag) => tag.isActive)

  let filterMap = Object.fromEntries(
    dataFilter
      .map((group) => {
        if (!Array.isArray(group.childTags)) return null
        const activeTags = group.childTags.filter((tag) => tag.isActive).map((tag) => tag.tagName)
        let filterType = group.childTags[0]?.filterType
        if (["Dine Category & Cuisine", "Shop Category"].includes(group.tagTitle))
          filterType = "categories"
        return activeTags.length && filterType ? [filterType, activeTags] : null
      })
      .filter(Boolean),
  )

  if (areaActive?.length && locationActive?.length) {
    const { areas, locations, ...rest } = filterMap
    const combined = []
    for (const loc of locationActive) {
      for (const area of areaActive) {
        combined.push(`${(loc.tagName+"").trim().toLowerCase()}|${(area.tagName+"").trim().toLowerCase()}`)
      }
    }
    filterMap = { ...rest, location_areas: combined }
  }

  if (!Object.keys(filterMap).length) return dataList

  return dataList.filter(
    (item) =>
      Array.isArray(item.filter) &&
      Object.entries(filterMap).every(([filterType, activeTags]) => {
        const filterObj = item.filter.find((f) => f.main === filterType)
        return (
          filterObj &&
          Array.isArray(filterObj.child) &&
          filterObj.child.some((child) => {
            if (typeof child === "string") {
              return activeTags.includes((child+"").trim().toLowerCase())
            } else {
              return activeTags.includes((child.tagName+"").trim().toLowerCase())
            }
          })
        )
      }),
  )
}

const clearCategoryData = (currentData, originalData) => {
  return currentData.map((item) => {
    if (item.tagTitle === "Category") {
      return {
        ...item,
        childTags: item.childTags.map((child) =>
          child.tagName === "dine" || child.tagName === "shop"
            ? { ...child, isActive: false }
            : child,
        ),
      }
    }
    return item
  })
}

const moveAreaAboveLocation = (arr) => {
  const areaIdx = arr.findIndex((item) => item.tagTitle === "Area")
  const locationIdx = arr.findIndex((item) => item.tagTitle === "Location")
  if (areaIdx === -1 || locationIdx === -1 || areaIdx < locationIdx) return arr

  if (arr[locationIdx] && Array.isArray(arr[locationIdx].childTags)) {
    const hasAll = arr[locationIdx].childTags.some((tag) => tag.tagTitle === "All")
    if (!hasAll) {
      arr[locationIdx].childTags.unshift({
        tagTitle: "All",
        tagName: "All",
        filterType: "locations",
      })
    }
  }

  const [areaObj] = arr.splice(areaIdx, 1)
  arr.splice(locationIdx, 0, areaObj)
  return arr
}

const toggleChildTagActive = (data, parentTagTitle, childTagName) => {
  return data.map((item) => {
    if (item.tagTitle !== parentTagTitle) return item
    return {
      ...item,
      childTags: item.childTags.map((tag) =>
        tag.tagName === childTagName ? { ...tag, isActive: !tag.isActive } : tag,
      ),
    }
  })
}

const resetActiveAreaLocation = (data) => {
  return data.map((item) => {
    if (item.tagTitle === "Area" || item.tagTitle === "Location") {
      return {
        ...item,
        childTags: item.childTags.map((child) => ({
          ...child,
          isActive: false,
        })),
      }
    }
    return item
  })
}

const filterForDine = (data) => {
  return data
    .filter((item) => item.tagTitle !== "Shop Category")
    .map((item) =>
      item.tagTitle === "Availability" && Array.isArray(item.childTags)
        ? {
            ...item,
            childTags: item.childTags.filter((child) => child.tagTitle !== "Available on iShopChangi"),
          }
        : item
    )
}

const revertFilterForDine = (currentData, originalData) => {
  const isShopActive = currentData
    .find((i) => i.tagTitle === "Category")
    ?.childTags?.find((c) => c.tagName === "shop")?.isActive
  return originalData.reduce((acc, origItem) => {
    if (
      isShopActive &&
      (origItem.tagTitle === "Dine Category & Cuisine" || origItem.tagTitle === "Dietary Options")
    )
      return acc
    if (origItem.tagTitle === "Shop Category") {
      const currentItem = currentData.find((i) => i.tagTitle === "Shop Category")
      return acc.concat(currentItem || origItem)
    }
    if (origItem.tagTitle === "Availability") {
      const currentItem = currentData.find((i) => i.tagTitle === "Availability")
      let childTags = origItem.childTags
      if (isShopActive)
        childTags = childTags.filter((child) => child.tagTitle !== "Online Reservations")
      return acc.concat({
        ...origItem,
        childTags: childTags.map((child) => {
          const cur = currentItem?.childTags?.find((c) => c.tagTitle === child.tagTitle)
          return cur ? { ...child, isActive: cur.isActive } : child
        }),
      })
    }
    if (origItem.tagTitle === "Category") {
      const currentItem = currentData.find((i) => i.tagTitle === "Category")
      return acc.concat({
        ...origItem,
        childTags: origItem.childTags.map((child) =>
          child.tagName === "dine"
            ? { ...child, isActive: false }
            : {
                ...child,
                isActive:
                  currentItem?.childTags?.find((c) => c.tagName === child.tagName)?.isActive ??
                  child.isActive,
              },
        ),
      })
    }
    const currentItem = currentData.find((i) => i.tagTitle === origItem.tagTitle)
    return acc.concat(currentItem || origItem)
  }, [])
}

const filterForShop = (data) =>
  data
    .filter(
      (item) => item.tagTitle !== "Dietary Options" && item.tagTitle !== "Dine Category & Cuisine",
    )
    .map((item) =>
      item.tagTitle === "Availability" && Array.isArray(item.childTags)
        ? {
            ...item,
            childTags: item.childTags.filter((child) => child.tagTitle !== "Online Reservations"),
          }
        : item,
    )

const revertFilterForShop = (currentData, originalData) => {
  const isDineActive = currentData
    .find((i) => i.tagTitle === "Category")
    ?.childTags?.find((c) => c.tagName === "dine")?.isActive
  return originalData.reduce((acc, origItem) => {
    if (isDineActive && origItem.tagTitle === "Shop Category") return acc
    if (
      origItem.tagTitle === "Dietary Options" ||
      origItem.tagTitle === "Dine Category & Cuisine"
    ) {
      const currentItem = currentData.find((i) => i.tagTitle === origItem.tagTitle)
      return acc.concat(currentItem || origItem)
    }
    if (origItem.tagTitle === "Availability") {
      const currentItem = currentData.find((i) => i.tagTitle === "Availability")
      let childTags = origItem.childTags
      if (isDineActive) {
        childTags = childTags.filter((child) => child.tagTitle !== "Available on iShopChangi")
      }
      return acc.concat({
        ...origItem,
        childTags: childTags.map((child) => {
          const cur = currentItem?.childTags?.find((c) => c.tagTitle === child.tagTitle)
          return cur ? { ...child, isActive: cur.isActive } : child
        }),
      })
    }
    if (origItem.tagTitle === "Category") {
      const currentItem = currentData.find((i) => i.tagTitle === "Category")
      return acc.concat({
        ...origItem,
        childTags: origItem.childTags.map((child) =>
          child.tagName === "shop"
            ? { ...child, isActive: false }
            : {
                ...child,
                isActive:
                  currentItem?.childTags?.find((c) => c.tagName === child.tagName)?.isActive ??
                  child.isActive,
              },
        ),
      })
    }
    const currentItem = currentData.find((i) => i.tagTitle === origItem.tagTitle)
    return acc.concat(currentItem || origItem)
  }, [])
}

const revertFilterForDineAndShop = (currentData, originalData) => {
  const cat = currentData.find((i) => i.tagTitle === "Category")
  const isShopActive = cat?.childTags?.find((c) => c.tagName === "shop")?.isActive
  const isDineActive = cat?.childTags?.find((c) => c.tagName === "dine")?.isActive
  return originalData.map((origItem) => {
    if (
      isDineActive && isShopActive && origItem.tagTitle === "Availability"
    ) {
      const cur = currentData.find((i) => i.tagTitle === "Availability")
      return {
        ...origItem,
        childTags: origItem.childTags.map((child) => {
          const c = cur?.childTags?.find((x) => x.tagName === child.tagName)
          return c && typeof c.isActive !== "undefined" ? { ...child, isActive: c.isActive } : child
        }),
      }
    }
    if (
      isDineActive &&
      ["Dine Category & Cuisine", "Dietary Options"].includes(origItem.tagTitle)
    ) {
      return currentData.find((i) => i.tagTitle === origItem.tagTitle) || origItem
    }
    if (isShopActive && origItem.tagTitle === "Shop Category") {
      return currentData.find((i) => i.tagTitle === "Shop Category") || origItem
    }
    if (origItem.tagTitle === "Category") {
      const cur = currentData.find((i) => i.tagTitle === "Category")
      return {
        ...origItem,
        childTags: origItem.childTags.map((child) =>
          ["dine", "shop"].includes(child.tagName)
            ? { ...child, isActive: true }
            : {
                ...child,
                isActive:
                  cur?.childTags?.find((c) => c.tagName === child.tagName)?.isActive ??
                  child.isActive,
              },
        ),
      }
    }
    const cur = currentData.find((i) => i.tagTitle === origItem.tagTitle)
    if (!cur || !Array.isArray(origItem.childTags)) return origItem
    return {
      ...origItem,
      childTags: origItem.childTags.map((child) => {
        const c = cur.childTags?.find((x) => x.tagName === child.tagName)
        return c && typeof c.isActive !== "undefined" ? { ...child, isActive: c.isActive } : child
      }),
    }
  })
}

const hasActiveTag = (childTags) => {
  return childTags.some((tag) => tag.isActive === true)
}

const hasActiveTagDataRoot = (data) => {
  return data.some(
    (item) =>
      Array.isArray(item.childTags) && item.childTags.some((child) => child.isActive === true),
  )
}

const getLocationTagTitles = (childTags) => {
  const filtered = childTags.filter((tag) => tag.tagTitle !== "All" && tag.isActive === true)
  // Custom sort: T* first, others, Jewel last
  const sorted = filtered.sort((a, b) => {
    const isT = (x) => /^T\d$/i.test(x.tagTitle.trim())
    if (isT(a) && !isT(b)) return -1
    if (!isT(a) && isT(b)) return 1
    if (a.tagTitle === "Jewel") return 1
    if (b.tagTitle === "Jewel") return -1
    return 0
  })
  return sorted.map((tag) => tag.tagTitle).join(", ")
}

const findChildTagByNameInData = (dataFilter, name) => {
  for (const item of dataFilter) {
    if (Array.isArray(item.childTags)) {
      const found = item.childTags.find((tag) => tag.tagName === name)
      if (found) return found
    }
  }
  return undefined
}

const getInitialDataFilter = (queryFilterData, initSelectedData) => {
  if (!Array.isArray(queryFilterData) || queryFilterData?.length === 0) return queryFilterData
  if (!Array.isArray(initSelectedData) || initSelectedData.length === 0) return queryFilterData
  const initialDataFilter = queryFilterData.map((item) => {
    if (["Area", "Location", "Category"].includes(item?.tagTitle) ) {
      return {
        ...item,
        childTags: item?.childTags?.map?.((child) => ({
          ...child,
          isActive: !!initSelectedData?.find?.(i => i?.tagName === child?.tagName && i?.filterType === child?.filterType),
        })),
      }
    }
    return item
  })
  return initialDataFilter
}

export {
  moveAreaAboveLocation,
  toggleChildTagActive,
  hasActiveTag,
  resetActiveAreaLocation,
  filterForDine,
  revertFilterForDine,
  filterForShop,
  revertFilterForShop,
  revertFilterForDineAndShop,
  hasActiveTagDataRoot,
  getLocationTagTitles,
  findChildTagByNameInData,
  clearCategoryData,
  filterDataListByDataFilterV2,
  getInitialDataFilter,
}
