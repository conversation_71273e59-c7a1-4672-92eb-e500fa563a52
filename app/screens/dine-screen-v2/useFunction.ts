import { useEffect, useState } from "react"
import NetInfo from "@react-native-community/netinfo"
import requestApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { graphqlOperation } from "aws-amplify"
import { getDealsPromos, listAEMDine } from "app/models/queries"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { useSelector } from "react-redux"
import {
  adobeRetrieveLocationContent,
  DEFAULT_LOCATION_CONTENT,
} from "app/services/adobe"
import { ProfileSelectors } from "app/redux/profileRedux"
import sha256 from "crypto-js/sha256"
import { simpleCondition } from "app/utils"
import { ShopSelectors } from "app/redux/shopRedux"
import { useHandleNavigation } from "app/utils/navigation-helper"

const useFunctions = () => {
  const { handleNavigation } = useHandleNavigation()
  const isShopDineV2JustForYou = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2_JUSTFORYOU)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const recommendedProductsPayload = useSelector(ShopSelectors.recommendedProductsPayload)
  const isJustForYouV2 = isShopDineV2JustForYou && isLoggedIn
  const [dataDealsPromos, setDataDealsPromos] = useState(null)
  const [isLoadingDealsPromos, setIsLoadingDealsPromos] = useState(false)
  const [isErrorDealsPromos, setIsErrorDealsPromos] = useState(false)

  const [dataJustForYou, setDataJustForYou] = useState(null)
  const [isLoadingJustForYou, setIsLoadingJustForYou] = useState(false)
  const [isErrorJustForYou, setIsErrorJustForYou] = useState(false)

  const [dataDineGuides, setDataDineGuides] = useState(null)
  const [isLoadingDineGuides, setIsLoadingDineGuides] = useState(false)
  const [isErrorDineGuides, setIsErrorDineGuides] = useState(false)

  const [showNoInternetError, setShowNoInternetError] = useState<boolean>(false)

  useEffect(() => {
    checkInternet()
  }, [])

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (!isConnected) {
      setShowNoInternetError(true)
      // stop loading indicators if any in progress
      setIsLoadingDealsPromos(false)
      setIsLoadingJustForYou(false)
      setIsLoadingDineGuides(false)
      return
    }
    setShowNoInternetError(false)
    // Fetch all data when internet is available
    getDataDealsPromos()
    getDataJustForYou()
    getDataDineGuides()
  }

  const getDataJustForYou = async () => {
    if (isJustForYouV2) {
      setIsErrorJustForYou(false)
      setIsLoadingJustForYou(true)
      const response = await adobeRetrieveLocationContent(
        {
          login_status: profilePayload ? 0 : 1,
          uid: profilePayload?.id,
          hashed_email: profilePayload?.email
            ? sha256(profilePayload?.email?.toLowerCase())?.toString()
            : undefined,
          ...simpleCondition({
            condition: recommendedProductsPayload?.profile_parameters,
            ifValue: recommendedProductsPayload?.profile_parameters,
            elseValue: {},
          }),
        },
        simpleCondition({
          condition: recommendedProductsPayload?.parameters,
          ifValue: recommendedProductsPayload?.parameters,
          elseValue: {},
        }),
        recommendedProductsPayload?.m_box_name,
      )
      if (response && response !== DEFAULT_LOCATION_CONTENT) {
        try {
          const jsonObject = JSON.parse(response)
          setIsLoadingJustForYou(false)
          if (jsonObject?.code) {
            setIsErrorJustForYou(true)
          } else {
            setDataJustForYou(jsonObject)
          }
        } catch (error) {
          setIsLoadingJustForYou(false)
          setIsErrorJustForYou(true)
        }
      } else {
        setIsLoadingJustForYou(false)
        setDataJustForYou(null)
      }
    }
  }

  const getDataDealsPromos = async () => {
    try {
      setIsLoadingDealsPromos(true)
      const input = {
        "page_number": 1,
        "page_size": 8
      }
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getDealsPromos, { input }),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      setIsLoadingDealsPromos(false)
      if (response?.data) {
        setDataDealsPromos(response?.data?.data?.getDealsPromosListing)
      } else {
        setIsErrorDealsPromos(true)
      }
    } catch (error) {
      setIsLoadingDealsPromos(false)
      setIsErrorDealsPromos(true)
    }
  }

  const getDataDineGuides = async () => {
    try {
      setIsLoadingDineGuides(true)
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(listAEMDine),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      setIsLoadingDineGuides(false)
      if (response?.data?.data?.listAEMDine) {
        setDataDineGuides(response?.data?.data?.listAEMDine)
      } else {
        setIsErrorDineGuides(true)
      }
    } catch (error) {
      setIsLoadingDineGuides(false)
      setIsErrorDineGuides(true)
    }
  }

  const handleClickBrandOffer = (item, index) => {
    const { navigationType, navigationValue, redirect } = item || {}
    if (!navigationType || !navigationValue) return
    handleNavigation(navigationType, navigationValue, redirect)
  }

  return {
    dataDealsPromos,
    isLoadingDealsPromos,
    isErrorDealsPromos,
    getDataDealsPromos,
    dataDineGuides,
    isLoadingDineGuides,
    isErrorDineGuides,
    isJustForYouV2,
    dataJustForYou,
    isLoadingJustForYou,
    isErrorJustForYou,
    handleClickBrandOffer,
    showNoInternetError,
    checkInternet,
  }
}

export { useFunctions }
