import { QLdineShop, QLnonTraveller, <PERSON><PERSON><PERSON><PERSON>, QLtraveller } from "assets/icons"
import React, { useEffect, useState } from "react"
import { View, StyleSheet, Platform, TouchableOpacity, Linking } from "react-native"
import { translate } from "app/i18n"
import { Text } from "app/elements/text"
import { color, typography } from "app/theme"
import { useNavigation } from "@react-navigation/native"
import { NavigationConstants, StateCode, PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { getExperienceCloudId } from "app/services/adobe"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"

enum idData {
  QLdineShop = 1,
  QLShopping = 2,
  QLtraveller = 3,
  QLnonTraveller = 4
}

const lightGreyLoadingColors = [color.palette.lightGrey, color.background, color.palette.lightGrey]
const lighterGreyLoadingColors = [
  color.palette.lighterGrey,
  color.background,
  color.palette.lighterGrey,
]

const data = [
  {
    id: idData.QLdineShop,
    icon: <QLdineShop />,
    title: translate("dineScreenV2.quickLink.dsDirectory"),
  },
  {
    id: idData.QLShopping,
    icon: <QLShopping />,
    title: translate("dineScreenV2.quickLink.spConcierge"),
    value: "https://changi.me/sccaf",
  },
  {
    id: idData.QLtraveller,
    icon: <QLtraveller />,
    title: translate("dineScreenV2.quickLink.traveller"),
    value: "https://www.ishopchangi.com/en?cmode=TRDEP",
  },
  {
    id: idData.QLnonTraveller,
    icon: <QLnonTraveller />,
    title: translate("dineScreenV2.quickLink.nonTraveller"),
    value: "https://www.ishopchangi.com/en?cmode=NTDEL",
  },
]

const QuickLink = React.memo(() => {
  const navigation = useNavigation<any>()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 2000)
    return () => clearTimeout(timer)
  }, [])

  const navigateFromCSM = async (type, item) => {
    try {
      const payload = {
        stateCode: StateCode?.ISHOPCHANGI,
        input: {
          redirectTarget: type,
          ecid: await getExperienceCloudId(),
        },
      }
      const getLink = await getDeepLinkV2(payload, true)
      navigation.navigate(NavigationConstants.webview, {
        uri: getLink?.redirectUri,
      })
    } catch (error) {
      navigation.navigate(NavigationConstants.webview, {
        uri: item?.value,
      })
    }
  }

  const onPress = (item) => {
    if (item?.id === idData.QLdineShop) {
      navigation.navigate(NavigationConstants.DineShopDirectory)
    } else if (item?.id === idData.QLShopping) {
      Linking.openURL(item?.value)
    } else {
      navigateFromCSM(item?.id === idData.QLtraveller ? `/en?cmode=TRDEP` : `/en?cmode=NTDEL`, item)
    }
  }

  if (isLoading) {
    const placeholders = new Array(5).fill(0)
    return (
      <View style={styles.skeletonContainer}>
        {placeholders.map((_, index) => (
          <View key={index} style={styles.skeletonItemContainer}>
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={lightGreyLoadingColors}
              shimmerStyle={styles.skeletonIcon}
            />
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={lighterGreyLoadingColors}
              shimmerStyle={styles.skeletonText}
            />
          </View>
        ))}
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {data.map((item) => {
        return (
          <TouchableOpacity
            key={item.id}
            style={{ alignItems: "center" }}
            onPress={() => onPress(item)}
          >
            {item.icon}
            <Text style={styles.txtTitle}>{item.title}</Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    paddingHorizontal: 25,
    justifyContent: "space-around",
  },
  skeletonContainer: {
    width: "100%",
    flexDirection: "row",
    paddingHorizontal: 25,
    justifyContent: "space-around",
  },
  skeletonItemContainer: {
    alignItems: "center",
  },
  skeletonIcon: {
    width: 24,
    height: 24,
    borderRadius: 8,
  },
  skeletonText: {
    width: 48,
    height: 12,
    borderRadius: 4,
    marginTop: 8,
  },
  txtTitle: {
    fontFamily: typography.regular,
    color: color.palette.almostBlackGrey,
    fontSize: 10,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 12,
    textAlign: "center",
    marginTop: 8,
  },
})

export { QuickLink }
