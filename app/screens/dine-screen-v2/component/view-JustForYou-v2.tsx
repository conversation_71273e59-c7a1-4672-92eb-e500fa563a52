import { Text } from 'app/elements/text';
import { color } from 'app/theme/color';
import { typography } from 'app/theme/typography';
import { ArrowRightV2 } from 'assets/icons';
import React from 'react';
import { StyleSheet, View, TouchableOpacity, Platform } from 'react-native';
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { LOADING_COLORS } from "app/screens/for-you/components/miffy-gamification-banner/miffy-gamification-banner.styles"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from 'app/utils/constants';

const ViewJustForYouV2Loading = React.memo(() => {
  return (
    <View style={styles.viewLoading}>
      {
        [1, 2, 3].map((item) => {
          return (
            <View key={item} style={styles.viewItemLoading}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.imageLoading}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.loadingFirst}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.loadingFirst}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={[styles.loadingFirst, { width: 60 }]}
              />
            </View>
          )
        })
      }
    </View>
  )
})

const ViewJustForYouV2 = (props) => {

  const { dataJustForYou, isLoadingJustForYou, isErrorJustForYou } = props;
  
  if (isErrorJustForYou || (!isLoadingJustForYou && !dataJustForYou?.length)) {
    return null
  }
  
  const renderContent = () => {
    if (isLoadingJustForYou) {
      return <ViewJustForYouV2Loading />
    }
    return <></>
  }

  return (
    <View style={styles.container}>
      {(dataJustForYou || isLoadingJustForYou) && <View style={styles.viewTitle}>
        <Text tx="dineScreenV2.justForYou.title" style={styles.txtTitle} />
        <TouchableOpacity style={styles.viewRow} onPress={() => { }}>
          <Text tx="dineScreenV2.justForYou.seeAll" style={styles.txtButton} />
          <ArrowRightV2 color={color.palette.lightPurple} />
        </TouchableOpacity>
      </View>}
      {renderContent()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  viewTitle: {
    paddingHorizontal: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 50
  },
  viewRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  txtTitle: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  },
  txtButton: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
    marginRight: 8,
  },
  viewLoading: {
    width: '100%',
    marginTop: 12,
    flexDirection: 'row',
    gap: 8,
    paddingLeft: 20
  },
  viewItemLoading: {
    height: 220,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: color.palette.lightGrey,
    padding: 12
  },
  imageLoading: {
    width: 96,
    height: 80,
    marginBottom: 8
  },
  loadingFirst: {
    width: 96,
    height: 12,
    borderRadius: 4,
    marginBottom: 12
  }
})

export { ViewJustForYouV2 }