import BaseImage from "app/elements/base-image/base-image"
import { Text } from "app/elements/text"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { useFocusEffect } from "@react-navigation/native"
import { LOADING_COLORS } from "app/screens/for-you/components/miffy-gamification-banner/miffy-gamification-banner.styles"
import { color } from "app/theme/color"
import { typography } from "app/theme/typography"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { ArrowRightV2 } from "assets/icons"
import React, { useMemo, useRef } from "react"
import { Dimensions, Platform, StyleSheet, TouchableOpacity, View } from "react-native"
import Carousel from "react-native-snap-carousel"
import { NavigationConstants } from "app/utils/constants"
import StaffPerkPromotionDetailController from "app/components/staff-perk-promotion-detail/staff-perk-promotion-detail-controller"
import { ErrorSectionV2 } from "app/components/error-section-v2"
import { translate } from "app/i18n"

const SnapCarousel = Carousel as any

const { width: screenWidth } = Dimensions.get("window")
const ITEM_WIDTH = 194.4
const ITEM_HEIGHT = 129.6

const Loading = () => {
  return (
    <View style={styles.viewContent}>
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={LOADING_COLORS}
        shimmerStyle={[styles.loadingDescriptionStyle, { marginLeft: 20 }]}
      />
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={LOADING_COLORS}
        shimmerStyle={styles.loadingDescriptionStyle}
      />
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={LOADING_COLORS}
        shimmerStyle={styles.loadingDescriptionStyle}
      />
    </View>
  )
}

const DealsPromos = React.memo((props: any) => {
  const { data, isLoading, isError, navigation, onReload } = props
  const carouselRef = useRef<Carousel<any>>(null)

  const calculateDataValue = (promos: any[]) => {
    if (!promos?.length) return []
    
    const length = promos.length
    
    if (length === 1) {
      return promos // Keep 1 item
    } else if (length === 2) {
      return [...promos, ...promos, ...promos] // Multiply 3 times = 6 items
    } else if (length >= 3 && length <= 5) {
      return [...promos, ...promos]
    } else {
      return promos.slice(0, 8) // Keep the rest (maximum 8)
    }
  }

  // Fix: Crash https://github.com/meliorence/react-native-snap-carousel/issues/586 
  const dataValue = useMemo(() => calculateDataValue(data?.promos), [data?.promos])

  useFocusEffect(
    React.useCallback(() => {
      carouselRef.current?.startAutoplay()
      return () => carouselRef.current?.stopAutoplay()
    }, []),
  )

  // Pause auto scroll when user interacts
  const handleScrollBeginDrag = () => {
    carouselRef.current?.stopAutoplay()
  }

  // Resume auto scroll after user stops interacting
  const handleScrollEndDrag = () => {
    carouselRef.current?.startAutoplay()
  }

  const handleClickItem = (item: any) => {
    StaffPerkPromotionDetailController.showModal(navigation, { item })
  }

  const renderCarouselItem = ({ item, index }: { item: any; index: number }) => {
    return (
      <TouchableOpacity style={styles.carouselItemContainer} onPress = {() => {handleClickItem(item)}}>
        <BaseImage style={styles.image} source={{ uri: item?.imageUrl }} resizeMode="cover" />
      </TouchableOpacity>
    )
  }

  const renderConetent = () => {
    if (isLoading) {
      return <Loading />
    } else if (isError) {
      return (
        <ErrorSectionV2
          reload
          onReload={onReload}
          extendCode="EHR1.1"
          containerStyle={styles.errorContainer}
          reloadButtonStyleOverride={styles.reloadButtonStyle}
          reloadButtonTextStyleOverride={styles.reloadButtonTextStyle}
          textContainerStyle={styles.textContainerStyle}
          reloadButtonInnerStyleOverride={styles.reloadButtonInnerStyle}
          title={translate("errorOverlay.variant1.title")}
          message={translate("errorOverlay.variant1.message")}
          reloadText={translate("errorOverlay.variant1.reload")}
          iconSize={120}
        />
      )
    } else {
      return (
        <View style={styles.carouselContainer}>
          <SnapCarousel
            ref={carouselRef}
            data={dataValue}
            renderItem={renderCarouselItem}
            sliderWidth={screenWidth}
            itemWidth={ITEM_WIDTH}
            autoplay={dataValue?.length > 1}
            autoplayInterval={3000}
            autoplayDelay={1000}
            loop={dataValue?.length > 1}
            inactiveSlideScale={0.833}
            loopClonesPerSide={10}
            enableMomentum={false}
            lockScrollWhileSnapping={true}
            decelerationRate={0.9}
            removeClippedSubviews={false}
            scrollEventThrottle={16}
            onScrollBeginDrag={handleScrollBeginDrag}
            onScrollEndDrag={handleScrollEndDrag}
            keyExtractor={(item: any, index: number) => `${item.id}-${index}`}
          />
        </View>
      )
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.viewTitle}>
        <Text tx="dineScreenV2.dealsPromo.title" preset={isError ? "bodyTextBlack" : "caption1BoldSmall"} style={{
          color: isError ? color.palette.almostBlackGrey : color.palette.darkestGrey,
        }} />
        {!isError && <TouchableOpacity style={styles.viewRow} onPress={() => navigation.navigate(NavigationConstants.dealsPromosListing)}>
          <Text tx="dineScreenV2.dealsPromo.seeAll" style={styles.txtButton} />
          <ArrowRightV2 color={color.palette.lightPurple} />
        </TouchableOpacity>}
      </View>
      {renderConetent()}
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginTop: 50,
    gap: 12,
  },
  viewTitle: {
    paddingHorizontal: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  viewRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  txtButton: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
    marginRight: 8,
  },
  viewContent: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  carouselContainer: {
    height: ITEM_HEIGHT,
  },
  carouselItemContainer: {
    width: ITEM_WIDTH,
    height: ITEM_HEIGHT,
    borderRadius: 8,
    overflow: "hidden",
  },
  image: {
    width: ITEM_WIDTH,
    height: ITEM_HEIGHT,
  },
  loadingDescriptionStyle: {
    width: 162,
    height: 108,
    borderRadius: 4,
  },
  errorContainer: {
    marginTop: 24,
  },
  reloadButtonStyle: {
    paddingLeft: 0,
    paddingRight: 0,
  },
  reloadButtonTextStyle: {
    fontSize: 14,
    lineHeight: 18,
    color: color.palette.whiteGrey,
  },
  textContainerStyle: {
    paddingHorizontal: 20,
  },
  reloadButtonInnerStyle: {
    height: undefined,
    paddingVertical: 5,
    paddingHorizontal: 12,
  },
})

export { DealsPromos }
