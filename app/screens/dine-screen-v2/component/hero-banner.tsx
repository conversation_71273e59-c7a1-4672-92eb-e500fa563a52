import React, { useState, useEffect } from "react"
import { StyleSheet, Dimensions, InteractionManager } from "react-native"

import { ImageCarousel } from "./image-carousel/image-carousel"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { NavigationType } from "app/components/main-promo/main-promo"
import { NavigationConstants } from "app/utils/constants"
import { color } from "app/theme"
import { env } from "app/config/env-params"
import restApi from "app/services/api/request"
import { graphqlOperation } from "aws-amplify"
import { mainPromoQuery } from "app/models/queries"
import { useNavigation } from "@react-navigation/native"

const { width } = Dimensions.get("window")
const PADDING_VERTICAL = 16
const IMAGE_RATIO = 344 / 218
const imageWidth = width - 2 * PADDING_VERTICAL

const HeroBanner = () => {
  const navigation = useNavigation()
  const { handleNavigation } = useHandleNavigation()

  const [isLoading, setIsLoading] = useState(true)
  const [promotions, setPromotions] = useState([])
  const [hasError, setHasError] = useState(false)

  const carouselType: any = isLoading ? "loading" : "default"

  const onPress = (item) => {
    const { navigationType, navigationValue, redirect } = item || ""
    if (!navigationType || !navigationValue) return null
    if (item.navigationType === NavigationType.inapp && item.navigationValue === "offerDetails") {
      navigation.navigate(NavigationConstants.dineShopOfferDetailsScreen, {
        offerId: item?.offerId,
      })
      return
    }
    handleNavigation(navigationType, navigationValue, redirect)
  }

  useEffect(() => {
    const getMainPromo = async () => {
      setIsLoading(true)
      try {
        const response = await restApi({
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(mainPromoQuery, { type: "" }),
          parameters: {},
          headers: {
            "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
          },
        })
        const data = response?.data?.data?.getMainPromoDineShop || []
        if (response?.data?.data?.getMainPromoDineShop) {
          const sortedPromotions = data.sort((a, b) => a.orderId - b.orderId)
          setPromotions(sortedPromotions)
        }
      } catch (error) {
        setHasError(true)
      } finally {
        setIsLoading(false)
      }
    }

    InteractionManager.runAfterInteractions(() => {
      getMainPromo()
    })
  }, [])

  if (hasError || (!isLoading && !promotions?.length)) {
    return null
  }

  return (
    <ImageCarousel
      onPressed={onPress}
      type={carouselType}
      promotions={promotions}
      containerStyle={styles.carouselContainer}
      backgroundImageStyle={styles.backgroundImageStyle}
      carouselContainerStyle={styles.carouselContainerStyle}
    />
  )
}

export { HeroBanner }

const styles = StyleSheet.create({
  carouselContainer: {
    marginTop: 32,
    backgroundColor: color.palette.whiteGrey,
  },
  carouselContainerStyle: {
    paddingHorizontal: 16,
    backgroundColor: color.palette.whiteGrey,
  },
  backgroundImageStyle: {
    width: imageWidth,
    height: imageWidth / IMAGE_RATIO,
  },
})
