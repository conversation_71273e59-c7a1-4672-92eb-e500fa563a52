import { useNavigation } from '@react-navigation/native';
import BaseImage from 'app/elements/base-image/base-image';
import { Text } from 'app/elements/text';
import ShimmerPlaceholder from 'app/helpers/shimmer-placeholder';
import { LOADING_COLORS } from 'app/screens/for-you/components/miffy-gamification-banner/miffy-gamification-banner.styles';
import { color } from 'app/theme/color';
import { typography } from 'app/theme/typography';
import { NavigationConstants, PLACEHOLDER_ANIMATION_SPEED_IN_MS } from 'app/utils/constants';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View, ScrollView } from 'react-native';

const DineGuidesLoading = () => {
  return (
    <View style={styles.viewLoading}>
      {
        [1, 2, 3].map((item) => {
          return (
            <View style={styles.viewItemLoading} key={item}>
              <View style={styles.viewContentLoading}>
                <View style={styles.viewTxt}>
                  <ShimmerPlaceholder
                    duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                    shimmerColors={LOADING_COLORS}
                    shimmerStyle={styles.contentTitleLoading}
                  />
                  <ShimmerPlaceholder
                    duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                    shimmerColors={LOADING_COLORS}
                    shimmerStyle={styles.contentTitleLoading}
                  />
                  <ShimmerPlaceholder
                    duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                    shimmerColors={LOADING_COLORS}
                    shimmerStyle={styles.contentLoading}
                  />
                  <ShimmerPlaceholder
                    duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                    shimmerColors={LOADING_COLORS}
                    shimmerStyle={styles.contentTitleLoading}
                  />
                </View>
              </View>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.imageLoading}
              />
            </View>
          )
        })
      }
    </View>
  )
}

const DineGuides = React.memo((props: any) => {
  const navigation = useNavigation<any>()
  const {
    dataDineGuides,
    isLoadingDineGuides,
    isErrorDineGuides
  } = props;

  if (isErrorDineGuides || (!isLoadingDineGuides && !dataDineGuides?.length)) return null;

  const onClickItemGuide = (item) => {
    navigation.navigate(NavigationConstants.webview, {
      uri: item?.link
    })
  }

  return (
    <View style={styles.container}>
      {(dataDineGuides?.length > 0 || isLoadingDineGuides) && <View style={styles.viewTitle}>
        <Text tx="dineScreenV2.dineGuides.title" preset="caption1BoldSmall" style={styles.txtTitle} />
      </View>}
      {isLoadingDineGuides ? (
        <DineGuidesLoading />
      ) : (
        <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
          <View style={styles.viewScroll}>
            {dataDineGuides?.map((item, index) => {
              return (
                <View key={`${item?.title}-${index}`} style={[styles.viewItemLoading, { marginLeft: index === 0 ? 20 : 0 }]}>
                  <TouchableOpacity style={[styles.viewContentLoading, { paddingRight: 12 }]} onPress={() => { onClickItemGuide(item) }}
                    activeOpacity={0.7}  
                  >
                    <View style={styles.viewTxtContent}>
                      <Text style={styles.txtTitleItem} numberOfLines={3}>{item?.title}</Text>
                      <Text style={styles.txtSubTitle} numberOfLines={1}>{item?.source}</Text>
                    </View>
                    <BaseImage source={{ uri: item?.image }} style={styles.imageLoading} />
                  </TouchableOpacity>
                </View>
              )
            })}
          </View>
        </ScrollView>
      )}
    </View>
  );
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginTop: 50,
  },
  viewScroll: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 12,
    marginBottom: 30
  },
  viewLoading: {
    width: '100%',
    marginTop: 12,
    flexDirection: 'row',
    gap: 12,
    paddingLeft: 20,
  },
  viewItemLoading: {
    width: 200,
    height: 213,
    justifyContent: 'flex-end',
  },
  viewContentLoading: {
    height: 184,
    width: '100%',
    borderRadius: 16,
    backgroundColor: color.palette.whiteGrey,
    shadowColor: "#121212",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.08,
    shadowRadius: 20,
    elevation: 4,
    justifyContent: 'flex-end',
    paddingBottom: 12,
    paddingLeft: 12,
    paddingRight: 37
  },
  imageLoading: {
    width: 176,
    height: 97,
    marginHorizontal: 12,
    borderRadius: 12,
    position: 'absolute',
    top: -29,
  },
  viewTitle: {
    paddingHorizontal: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  viewRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  txtTitle: {
    color: color.palette.darkestGrey,
  },
  contentTitleLoading: {
    width: '100%',
    height: 13,
    borderRadius: 4
  },
  viewTxt: {
    gap: 12
  },
  contentLoading: {
    width: 57,
    height: 13,
    borderRadius: 4
  },
  viewTxtContent: {
    width: '100%',
    height: 94,
    justifyContent: 'space-between'
  },
  txtTitleItem: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 20,
  },
  txtSubTitle: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
  }
})

export { DineGuides }