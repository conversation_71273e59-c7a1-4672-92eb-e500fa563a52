import React from "react"
import { <PERSON>, ScrollView } from "react-native"
import { styles } from "./styles"
import {
  <PERSON>Link,
  DealsPromos,
  HeroBanner,
  <PERSON>e<PERSON><PERSON><PERSON>,
  ViewJustForYouV2,
} from "./component"
import { ErrorSectionV2 } from "app/components/error-section-v2"
import { useFunctions } from "./useFunction"
import { useNavigation } from "@react-navigation/native"
import { handleCondition } from "app/utils"
import { BrandOffer } from "app/sections/brand-offer/brand-offer"
import { useSafeAreaInsets } from "react-native-safe-area-context"

const DineScreenV2 = () => {
  const navigation = useNavigation()
  const { bottom } = useSafeAreaInsets()
  const {
    dataDealsPromos,
    isLoadingDealsPromos,
    isErrorDealsPromos,
    getDataDealsPromos,
    isJustForYouV2,
    dataJustForYou,
    isLoadingJustForYou,
    isErrorJustForY<PERSON>,
    dataDineGuides,
    isLoadingDineGuides,
    isErrorDineGuides,
    handleClick<PERSON>randOffer,
    showNoInternetError,
    checkInternet,
  } = useFunctions()

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.background}>
      <View style={styles.container}>
        {showNoInternetError ? (
          <ErrorSectionV2
            onReload={checkInternet}
            containerStyle={{ paddingBottom: 120 + bottom }}
            reloadButtonStyleOverride={styles.noInternetReloadButtonStyle}
            reloadButtonTextStyleOverride={styles.reloadButtonTextStyle}
          />
        ) : (
          <>
            <QuickLink />
            <HeroBanner />
            <DealsPromos
              data={dataDealsPromos}
              isLoading={isLoadingDealsPromos}
              isError={isErrorDealsPromos || (!isLoadingDealsPromos && !dataDealsPromos?.promos?.length)}
              navigation={navigation}
              onReload={getDataDealsPromos}
            />
            {handleCondition(
              isJustForYouV2,
              <ViewJustForYouV2
                dataJustForYou={dataJustForYou}
                isLoadingJustForYou={isLoadingJustForYou}
                isErrorJustForYou={isErrorJustForYou}
              />,
              <View style={styles.viewJustForYou}>
                <BrandOffer styleContainerProps={styles.marginBottom}
                  handleClickProps = {handleClickBrandOffer}
                  isShowError={false}
                />
              </View>,
            )}
            <DineGuides
              dataDineGuides={dataDineGuides}
              isLoadingDineGuides={isLoadingDineGuides}
              isErrorDineGuides={isErrorDineGuides}
            />
            <View style={styles.viewBottom} />
          </>
        )}
      </View>
    </ScrollView>
  )
}

export { DineScreenV2 }
