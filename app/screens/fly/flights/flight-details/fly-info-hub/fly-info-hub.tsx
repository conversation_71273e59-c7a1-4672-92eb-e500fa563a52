import React, { useContext } from "react"
import { TouchableOpacity } from "react-native"
import { styles } from "./fly-info-hub.styles"
import { Text } from "app/elements/text/text"
import { useNavigation } from "@react-navigation/native"
import {
  FlightDineIcon,
  FlightFacilitiesIcon,
  FlightShoppingBag,
  FlightTravelIcon,
} from "ichangi-fe/assets/icons"
import { FlightInformationHubProps } from "./fly-info-hub.props"
import { TypeFocusInformationHub } from "../flight-detail.props"
import { useDispatch } from "react-redux"
import { NavigationConstants, NavigationInAppValueSpecial } from "app/utils/constants"
import { TravelOption } from "../travel-options/travel-options"
import { handleNavigation } from "app/utils/screen-helper"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { ScrollView } from "react-native-gesture-handler"
import { FLY_CONTEXT } from "app/services/context/fly"
import { getFeatureFlagInit, isFlagOnCondition, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { trackAction, AdobeTagName, AdobeValueByTagName } from "app/services/adobe"
import { translate } from "app/i18n"
import { ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME } from "app/services/firebase"
import useFlightDetailClickEvent from "app/helpers/flight-detail/useFlightDetailClickEvent"
import { FlightDirection } from "app/screens/fly/flights/flight-props"

const getInitialFilterData = ({terminal, isIAmPicking, navigationValue}) => {
  const filterData = []
  if (terminal) {
    filterData.push({
      filterType: "locations",
      tagName: `terminal ${terminal}`,
      tagTitle: `T${terminal}`,
    })
  }
  if (navigationValue === NavigationInAppValueSpecial.shop) {
    filterData.push({
      filterType: "type",
      tagName: "shop",
      tagTitle: "Shop",
    })
  }
  if (navigationValue === NavigationInAppValueSpecial.dine) {
    filterData.push({
      filterType: "type",
      tagName: "dine",
      tagTitle: "Dine",
    })
  }
  if (isIAmPicking) {
    filterData.push({
      filterType: "areas",
      tagName: "public",
      tagTitle: "Public Area",
    })
  }
  return filterData
}

const FlightInformationHub = ({
  typeFocusInformationHub,
  setTypeFocusInformationHub,
  terminal,
  selectedTravelOption,
  direction,
  flightNumber,
}: FlightInformationHubProps) => {
  const navigation = useNavigation()
  const dispatch = useDispatch()

  const { flyDetailsFirstFlag } = useContext(FLY_CONTEXT)?.Handlers
  const isFlightDetailsFirst = isFlagOnCondition(flyDetailsFirstFlag)
  
  const isTravellerLocal = selectedTravelOption === TravelOption.iAmTravelling
  const isDepartureLocal = direction === FlightDirection.departure
  const {logClickEvent} = useFlightDetailClickEvent({isDeparture: isDepartureLocal, isTraveller: isTravellerLocal})
  const isIAmPicking = selectedTravelOption === TravelOption.iAmPicking
  const trackingModeValue = isIAmPicking ? AdobeValueByTagName.FlightDetailsPagePickingSomeone : AdobeValueByTagName.FlightDetailsPageTravelling
  const isShopDineV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)

  const travelTabLabelTx = "flightDetailV2.flightInformationHub.menu.travel"
  const shopTabLabelTx = "flightDetailV2.flightInformationHub.menu.shop"
  const dineTabLabelTx = "flightDetailV2.flightInformationHub.menu.dine"
  const facilitiesTabLabelTx = "flightDetailV2.flightInformationHub.menu.facilities"

  const onPressTravel = () => {
    setTypeFocusInformationHub(TypeFocusInformationHub.TRAVEL)
    trackAction(AdobeTagName.FlyFlightDetailsPageBottomDetails, {
      [AdobeTagName.FlyFlightDetailsPageBottomDetails]: `${flightNumber} | ${trackingModeValue} | ${direction} | ${translate(travelTabLabelTx)} | Tabs`,
    })
  }

  const onPressShopAndDine = ({
    navigationValue,
  }: {
    navigationValue: NavigationInAppValueSpecial,
  }) => {
    if (isShopDineV2) {
      const filterData = getInitialFilterData({terminal, isIAmPicking, navigationValue})
      navigation.navigate(NavigationConstants.DineShopDirectory, {
        filterData,
      })
      return
    }

    let filterTitles = []
    if (terminal) {
      filterTitles.push({
        filterType: 'locations',
        tagName: `terminal ${terminal}`,
        tagTitle: `Terminal ${terminal}`,
      })
    }
    if (isIAmPicking) {
      filterTitles.push({
        filterType: 'areas',
        tagName: 'public',
        tagTitle: 'Public',
      })
    }
    handleNavigation(
      NavigationTypeEnum.inScreen,
      navigationValue,
      navigation,
      dispatch,
      {
        filters: filterTitles,
        isFocusRedirect: true,
      }
    )
  }

  const onPressFacilities = () => {
    setTypeFocusInformationHub(TypeFocusInformationHub.FACILITIES)
    logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_TAB)
    trackAction(AdobeTagName.FlyFlightDetailsPageBottomDetails, {
      [AdobeTagName.FlyFlightDetailsPageBottomDetails]: `${flightNumber} | ${trackingModeValue} | ${direction} | ${translate(facilitiesTabLabelTx)} | Tabs`,
    })
  }

  const onPressShopTab = () => {
    onPressShopAndDine({ navigationValue: NavigationInAppValueSpecial.shop })
    logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SHOP_TAB)
    trackAction(AdobeTagName.FlyFlightDetailsPageBottomDetails, {
      [AdobeTagName.FlyFlightDetailsPageBottomDetails]: `${flightNumber} | ${trackingModeValue} | ${direction} | ${translate(shopTabLabelTx)} | Tabs`,
    })
  }

  const onPressDineTab = () => {
    onPressShopAndDine({ navigationValue: NavigationInAppValueSpecial.dine })
    logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.DINE_TAB)
    trackAction(AdobeTagName.FlyFlightDetailsPageBottomDetails, {
      [AdobeTagName.FlyFlightDetailsPageBottomDetails]: `${flightNumber} | ${trackingModeValue} | ${direction} | ${translate(dineTabLabelTx)} | Tabs`,
    })
  }

  return (
    <ScrollView
      horizontal
      nestedScrollEnabled
      contentContainerStyle={[
        styles.scrollContent,
        {
          paddingHorizontal: isFlightDetailsFirst ? 16 : 24,
        },
      ]}
      showsHorizontalScrollIndicator={false}
    >
      <TouchableOpacity
        style={
          typeFocusInformationHub === TypeFocusInformationHub.TRAVEL
            ? styles.menuButtonFocus
            : styles.menuButton
        }
        onPress={onPressTravel}
      >
        <FlightTravelIcon />
        <Text tx={travelTabLabelTx} style={styles.menuText} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.menuButton} onPress={onPressShopTab}>
        <FlightShoppingBag />
        <Text tx={shopTabLabelTx} style={styles.menuText} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.menuButton} onPress={onPressDineTab}>
        <FlightDineIcon />
        <Text tx={dineTabLabelTx} style={styles.menuText} />
      </TouchableOpacity>

      <TouchableOpacity
        style={
          typeFocusInformationHub === TypeFocusInformationHub.FACILITIES
            ? styles.menuButtonFocus
            : styles.menuButton
        }
        onPress={onPressFacilities}
      >
        <FlightFacilitiesIcon />
        <Text tx="flightDetailV2.flightInformationHub.menu.facilities" style={styles.menuText} />
      </TouchableOpacity>
    </ScrollView>
  )
}

export { FlightInformationHub }
