import React from "react"
import { View, TouchableOpacity } from "react-native"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { color } from "app/theme"
import { Text } from "app/elements/text"
import { Radio } from "app/elements/radio"
import { styles } from "./styles"
import { CrossPurple, FlightSave, TickPurpleIcon } from "assets/icons"
import BaseImage from "app/elements/base-image/base-image"
import LinearGradient from "react-native-linear-gradient"
import LottieView from "lottie-react-native"
import { Button } from "app/elements/button/button"
import { FlightDirection } from "../../flight-props"
import { TravelOption } from "../travel-options/travel-options"

const activeGradient = [color.palette.gradientColor1Start, color.palette.gradientColor1End]

const ModalSaveAndShare = (props: any) => {
  const {
    visible,
    onClosed,
    onModalHide,
    selectedOption,
    onPress,
    loadingSaveFlight,
    flightDirection,
    savedFlightOnPress,
    onShareOnlyPress
  } = props

  const renderSelectOption = () => {
    if (flightDirection === FlightDirection.departure) {
      return (
        <>
          <Radio
            tx={TravelOption.iAmTravelling}
            isChecked={selectedOption === TravelOption.iAmTravelling}
            onChecked={() => onPress(TravelOption.iAmTravelling)}
            style={styles.travellingText}
            isLoadingToProcess={loadingSaveFlight}
          />
          <Radio
            tx={TravelOption.iAmPicking}
            isChecked={selectedOption === TravelOption.iAmPicking}
            onChecked={() => onPress(TravelOption.iAmPicking)}
            style={styles.pickingText}
            isLoadingToProcess={loadingSaveFlight}
          />
        </>
      )
    } else {
      return (
        <>
          <Radio
            tx={"dropdownSelectCard.imPickingSomeone"}
            isChecked={selectedOption === TravelOption.iAmPicking}
            onChecked={() => onPress(TravelOption.iAmPicking)}
            style={styles.travellingText}
            isLoadingToProcess={loadingSaveFlight}
          />
          <Radio
            tx={TravelOption.iAmTravelling}
            isChecked={selectedOption === TravelOption.iAmTravelling}
            onChecked={() => onPress(TravelOption.iAmTravelling)}
            style={styles.pickingText}
            isLoadingToProcess={loadingSaveFlight}
          />
        </>
      )
    }
  }

  const renderTitle = (title: string) => (
    <View style={styles.wrapTitle}>
      <TickPurpleIcon />
      <Text tx={title} preset="caption1Regular" style={styles.title} />
    </View>
  )

  const renderInfo = () => (
    <View style={styles.parentContainer}>
      <TouchableOpacity style={styles.containerCloseIcon} onPress={onClosed}>
        <CrossPurple />
      </TouchableOpacity>
      <BaseImage source={FlightSave} style={styles.img} />
      <Text
        tx={"flightDetailV2.titleModalSaveAndShare"}
        preset={"bodyTextBlack"}
        style={styles.headerTitle}
      />
      <View style={styles.containerTitle}>
        <View style={styles.containerTitleLeft}>
          {renderTitle("flightDetailV2.saveFlightPopup.flightJourney")}
          {renderTitle("flightDetailV2.saveFlightPopup.destinationWeather")}
        </View>
        <View style={styles.containerTitleRight}>
          {renderTitle("flightDetailV2.saveFlightPopup.destionationAirportDetails")}
        </View>
      </View>
    </View>
  )

  const renderSelectYourProfileContent = () => (
    <View style={styles.containerSelectYourProfile}>
      <View style={styles.wrapSelectYourProfile}>
        <View style={styles.wrapHeaderBottomSheet}>
          <Text
            tx={"dropdownSelectCard.selectYourProfile"}
            preset={"h2"}
            style={styles.selectYourProfile}
          />
        </View>
        <Text
          tx="flightDetailV2.saveFlightPopup.descSelectProfile"
          preset="caption1Regular"
          style={styles.descriptionHeader}
        />
        {renderSelectOption()}
      </View>
    </View>
  )

  const renderButtonSave = () => (
    <View style={styles.bottomContainer}>
      {loadingSaveFlight ? (
        <View style={styles.loadingButtonStyles}>
          <LottieView
            source={require("../../save-flight-travel-option/dots-loading-saved.json")}
            autoPlay
            loop
            style={styles.lottieView}
          />
        </View>
      ) : (
        <LinearGradient
          style={styles.buttonLinearStyle}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 0 }}
          colors={activeGradient}
        >
          <Button
            tx={"flightDetailV2.saveAndShare"}
            sizePreset="large"
            textPreset="buttonLarge"
            typePreset="secondary"
            statePreset="default"
            backgroundPreset="light"
            onPress={savedFlightOnPress}
            textStyle={styles.textButtonStyleas}
          />
        </LinearGradient>
      )}
    </View>
  )

  const renderSubButton = () => (
    <View style={styles.viewSubButton}>
      <TouchableOpacity onPress={onShareOnlyPress}>
        <Text tx="flightDetailV2.shareOnly" style={styles.txtSubButton} />
      </TouchableOpacity>
    </View>
  )

  return (
    <BottomSheet
      isModalVisible={visible}
      onModalHide={onModalHide}
      onClosedSheet={onClosed}
      containerStyle={styles.bottomSheetStyle}
      stopDragCollapse={true}
      onBackPressHandle={onClosed}
      animationInTiming={500}
      animationOutTiming={500}
      openPendingModal
    >
      <View style={styles.modalContainer}>
        <View>
          {renderInfo()}
          {renderSelectYourProfileContent()}
        </View>
        {renderButtonSave()}
        {renderSubButton()}
      </View>
    </BottomSheet>
  )
}

export default ModalSaveAndShare