import { StyleSheet, Dimensions, Platform } from "react-native"
import { color, typography } from "app/theme"
import { presets } from "app/elements/text"

const { height } = Dimensions.get("screen")

export const styles = StyleSheet.create({
  bottomSheetStyle: {
    height: height,
    backgroundColor: "transparent",
  },
  animatedContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    backgroundColor: color.palette.transparent,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  topModalContainer: {
    backgroundColor: color.palette.transparent,
    flex: 1,
  },
  parentContainer: {
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingHorizontal: 24,
    width: "100%",
  },
  wrapHeaderBottomSheet: {
    alignItems: "center",
  },
  selectYourProfile: {
    ...presets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  descriptionHeader: {
    marginBottom: 16,
    marginTop: 8,
    paddingHorizontal: 20,
    textAlign: "center",
    color: color.palette.almostBlackGrey,
  },
  travellingText: {
    marginBottom: 12,
  },
  pickingText: {
    marginTop: 12,
  },
  containerTitle: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 18,
  },
  containerTitleLeft: {
    marginRight: 12,
    flex: 1,
  },
  containerTitleRight: {
    flex: 1,
  },
  wrapTitle: {
    flexDirection: "row",
    marginBottom: 5,
  },
  title: {
    marginLeft: 2,
    color: color.palette.almostBlackGrey,
    flexShrink: 1, // allow text to wrap when it exceeds the container width
  },
  containerSelectYourProfile: {
    backgroundColor: color.palette.whiteGrey,
    paddingHorizontal: 16,
    width: "100%",
  },
  wrapSelectYourProfile: {
    borderWidth: 1,
    borderRadius: 4,
    borderColor: color.palette.lighterGrey,
    padding: 16,
    marginBottom: 10,
  },
  headerTitle: {
    color: color.palette.almostBlackGrey,
    textAlign: "center",
    marginVertical: 12,
  },
  img: {
    width: 136,
    height: 56,
    alignSelf: "center",
    marginBottom: 11,
    marginTop: 16,
  },
  containerCloseIcon: {
    position: "absolute",
    right: 6,
    top: 0,
    padding: 10,
  },
  containerFlightJourney: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: color.palette.almostBlackGrey,
    opacity: 0.7,
  },
  bottomContainer: {
    paddingHorizontal: 24,
    paddingTop: 12,
    paddingBottom: 16,
    backgroundColor: color.palette.whiteGrey,
  },
  buttonLinearStyle: {
    borderRadius: 60,
    width: "100%",
  },
  loadingButtonStyles: {
    alignItems: "center",
    backgroundColor: color.palette.lightGrey,
    borderRadius: 60,
    height: 44,
    justifyContent: "center",
    width: "100%",
  },
  lottieView: {
    height: 24,
    width: "100%",
  },
  textButtonStyleas: {
    color: color.palette.almostWhiteGrey,
  },
  viewSubButton: {
    width: '100%',
    paddingBottom: 36,
    backgroundColor: color.palette.whiteGrey,
  },
  txtSubButton: {
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 22,
    textAlign: "center"
  },
})
