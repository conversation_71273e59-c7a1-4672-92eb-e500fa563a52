import { FlightDetailsCardState } from "app/components/flight-details-card/flight-details-card.props";
import { ReactNode } from "react";
import { ViewStyle } from "react-native";
import { FlightDetailsPayloadProps, IntoCityOrAirportProps } from "../flight-detail.props";
import { Inf22Props } from "../../flight-props";

export interface ITimelineItem {
  dashHeight?: number;
  date: string;
  time: string;
  dayAdded?: string;
  oldTime?: string;
  customStyle?: ViewStyle
  customTextViewStyle?: ViewStyle
  displayDirection?: EDisplayDirection
}

export interface ILocationPlace {
  shortName: string
  fullName: string
  type: FlightDetailsCardState
  showDetails?: boolean;
  renderDetails?(): ReactNode
}

export interface IFlightTimeLine {
  data: Array<ITimelineItem>,
  displayDirection?: EDisplayDirection
}

export enum EDisplayDirection {
  topToBottom = "topToBottom",
  bottomToTop = "bottomToTop",
}

export interface IFly {
  logo: string;
  flightNumber: string;
  departingCode: string;
  destinationCode: string;
  flightDate: string;
  scheduledDate: string;
  state: string;
  destinationPlace: string;
  departingPlace: string;
  timeOfFlight: string;
  codeShare: Array<string>;
  flightStatus: string;
  flightStatusMapping: string;
  beltStatusMapping: string;
  statusColor: string;
  showGate: boolean;
  isSaved: boolean;
  isMSError?: string | undefined;
  transits?: string | undefined;
  flightUniqueId: string;
  estimatedTimestamp: string;
  actualTimestamp: string;
  direction: string;
  terminal: number;
  checkInRow?: string | null;
  displayBelt: number;
  displayTimestamp: string;
  viaAirportDetails?: string | null;
  country: string;
  isFirstFlight: boolean;
  upcomingStatusMapping: string;
  technicalFlightStatus1: string;
  technicalFlightStatus2: string;
}

export interface FlightInfoProps {
  flyFlightDetailsPayload: FlightDetailsPayloadProps
  flyLastUpdatedTimeStamp: string
  shouldShowSQArrivalTerminalInfo: boolean
  inf22: Inf22Props
  handleMap: (type: string, item?: string) => void
  isLoadingDetailFlight: boolean
  intoCityOrAirportPayload: IntoCityOrAirportProps
  marginTop?: number
  isSaved: boolean
  onSaveFlight?: (label?: string) => void
  getMyTripData: GetMyTripProps
  isLoadingGetMyTripData: boolean,
  isErrorGetMyTrip: boolean,
  onRetryGetMyTripData: () => void
  directionFromParams: string
}

export enum FlightJourneyEnumStatus {
  DEPARTED = "DEPARTED",
  NOT_STARTED = "NOT_STARTED",
  LANDED = "LANDED",
}

export interface GetMyTripProps {
  id: string;
  departurePlace: string;
  scheduledDepartureLocalDate: string;
  actualDepartureLocalDate: string;
  actualArrivalLocalDate: string;
  departureTerminal: string;
  departureGate: string;
  departureCheckInDesk: string;
  estimatedDepartureLocalDate: string;
  arrivalPlace: string;
  scheduledArrivalLocalDate: string;
  arrivalTerminal: string;
  arrivalGate: string;
  arrivalBaggageClaim: string;
  estimatedArrivalLocalDate: string;
  departureTimezone: string,
  arrivalTimezone: string,
  flightStatus: FlightJourneyEnumStatus;
  updateFields: string[];
  transit?: GetMyTripProps; // Recursive structure for getMyTrip data
  title?: string,
  subtitle?: string,
}

export enum TypeTimelineHeader {
  DepDateTimeMiddle = 'dep-datetime-middle',
  DepDateTimeEnd = 'dep-datetime-end',
  DepCircleMiddle = 'dep-circle-middle',
  DepCircleEnd = 'dep-circle-end',
  DepDateTimeStart = 'dep-datetime-start',
  
  ArrDateTimeMiddle = 'arr-datetime-middle',
  ArrDateTimeEnd = 'arr-datetime-end',
  ArrCircleMiddle = 'arr-circle-middle',
  ArrCircleEnd = 'arr-circle-end',
  ArrDateTimeStart = 'arr-datetime-start',
}

export interface ITimelineHeaderItem {
  type: TypeTimelineHeader;
  height: number;
  flightScheduledDate?: string;
  mainTime?: string;
  reTimeFlag?: boolean;
  numberDaysDiff?: string | number;
  scheduledTime?: string;
  gapEnd?: number;
  needCircleEnd?: boolean;
  needCircleDepDateTimeEnd?: boolean;
  isHasTransitDataAndDestinationEmpty?: boolean;
}