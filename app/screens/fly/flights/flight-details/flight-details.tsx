import React, { useRef, useState, useEffect, useMemo, useContext, Fragment } from "react"
import {
  RefreshControl,
  ScrollView,
  TouchableOpacity,
  View,
  Alert,
  ViewStyle,
  TextStyle,
  InteractionManager,
} from "react-native"
import { FlightHeroImage } from "app/components/flight-hero-image/flight-hero-image"
import { BackButton, ArrowU<PERSON>, <PERSON><PERSON>rOutlined, Share as ShareIcon } from "ichangi-fe/assets/icons"
import DropdownSelectCard from "app/components/dropdown-select-card/dropdown-select-card"
import { handleNavigationFlightDetail } from "app/components/flight-details-card/flight-details-card"
import { FlightDetailsCardState } from "app/components/flight-details-card/flight-details-card.props"
import { styles } from "./flight-details-styles"
import { FabBackground } from "ichangi-fe/assets/backgrounds"
import { useDispatch, useSelector } from "react-redux"
import { FlySelectors, FlyCreators, Fly } from "app/redux/flyRedux"
import { GetIntoAirport } from "./get-into-airport/get-into-airport"
import { TimelineSection } from "./timeline-section/timeline-section"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import SaveFlightButton from "./save-flight-button/save-flight-button"
import { NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { ProfileSelectors } from "app/redux/profileRedux"
import { FlightDetailsCrt } from "app/sections/flight-details-crt/flight-details-crt"
import {
  CustomerEligibility,
  FlightDirection,
  FlightNavigationType,
  FlightRequestType,
} from "../flight-props"
import { translate } from "app/i18n"
import { flyModuleUpdatedTime } from "app/utils/date-time/date-time"
import TravelOptions from "./travel-options/travel-options"
import FeedBackToast from "app/components/feedback-toast/feedback-toast"
import { DURATION, FeedBackToastType } from "app/components/feedback-toast/feedback-toast-props"
import { env } from "app/config/env-params"
import NetInfo from "@react-native-community/netinfo"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import FlightPerks from "./flight-perks/flight-perks"
import FacilitiesAndServices from "../facilities-services/facilities-services"
import ConfirmSaveFlight from "app/components/flight-details-card/confirm-popup-save-flight"
import AemActions, { AemSelectors, AEM_PAGE_NAME } from "app/redux/aemRedux"
import { handleImageUrl } from "app/utils/media-helper"
import { useIsFocused } from "@react-navigation/native"
import moment from "moment-timezone"
import TickerBand from "app/components/ticker-band/ticker-band"
import { get as _get, isEmpty, size } from "lodash"
import { trackAction, AdobeTagName, commonTrackingScreen } from "app/services/adobe"
import { Text, presets } from "app/elements/text"
import { color } from "app/theme"
import { putFlightInfoToAdobeAnalyticAfterSaveUnSave } from "app/utils/screen-helper"
import { getLocationMapping } from "app/sagas/pageConfigSaga"
import { LoadingOverlay } from "app/components/loading-modal"
import { handleCondition, simpleCondition } from "app/utils"
import { save } from "app/utils/storage"
import { StorageKey } from "app/utils/storage/storage-key"
import { AlertApp } from "app/components/alert-app/alert-app"
import { AlertTypes } from "app/components/alert-app/alert-app.props"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import { BottomSheetUnableLoadLocation } from "app/components/bottom-sheet-unable-load-location/bottom-sheet-unable-load-location"
import AppscapadeBannerFlightDetail from "./appscapade-flight-detail"
import { FlightDetailsCardV2 } from "app/components/flight-details-card/flight-details-card-v2"
import { BottomSheetMapUnavailable } from "app/components/bottom-sheet-map-unavailable/bottom-sheet-map-unavailable"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS, isFlagON, isFlagOnCondition } from "app/services/firebase/remote-config"
import SavedFlightTravelOptions, {
  TravelOption,
} from "../save-flight-travel-option/save-flight-travel-option"
import AddReturnCalendar from "../add-return-calendar"
import { MaintananceTickerBandType, useTickerbandMaintanance } from "app/hooks/useTickerbandMaintanence"
import Share, { ShareOptions } from 'react-native-share'
import { FLY_CONTEXT } from "app/services/context/fly"
import { useFlightSaveErrorHandling } from "app/hooks/useFlightSaveErrorHandling"
import { SingleTapButton } from "app/elements/single-tap-button"
import { useFlightDetail } from "./useFlightDetail"
import { ErrorOverlayApp } from "app/components/error-overlay/error-overlay-app"
import { getPreviousScreen, setCurrentScreenActive } from "app/utils/screen-hook"
import { ErrorCloudComponent } from "app/components/error-cloud/error-cloud"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { DirectionTagNameEnum, FlightDetailSectionCFProps, ITravelChecklistAEM, UserProfileTagNameEnum } from "./flight-detail.props"
import { InformationHubCard } from "./information-hub-card/information-hub-card"
import { RootState } from "app/redux/store"
import { FE_LOG_PREFIX, dtManualActionEvent, ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME } from "app/services/firebase"
import { handleUpdateUpcomingAndSavedFlight } from "app/utils/flight-detail-v2-helper"
import { useOnlineCheckIn } from "./flight-details.hooks"
import useFlightDetailClickEvent from "app/helpers/flight-detail/useFlightDetailClickEvent"
import { useModal } from "app/hooks/useModal"
import { getLastSavedFlightTime, setLastSavedFlightTime } from "app/utils/storage/mmkv-storage"
import ModalSaveAndShare from "./modal-saveAndShare"

const SCREEN_NAME = "FlightDetails"

const toastButtonStyleAdded: TextStyle = {
  ...presets.textLink,
  fontWeight: "normal",
  color: color.palette.lightBlue,
  alignItems: "flex-end",
}

const toastTextStyleAdded: TextStyle = {
  ...presets.bodyTextRegular,
  color: color.palette.whiteGrey,
  width: "80%",
}

export const feedBackToastStyle: ViewStyle = {
  alignItems: "flex-start",
  backgroundColor: color.palette.black,
  borderRadius: 8,
  height: 60,
  marginBottom: 20,
  marginHorizontal: 16,
  width: "95%",
}

export const toastButtonStyle: TextStyle = {
  ...presets.textLink,
  fontWeight: "normal",
  color: color.palette.lightBlue,
  alignItems: "flex-end",
}

export const toastTextStyle: TextStyle = {
  ...presets.bodyTextRegular,
  color: color.palette.whiteGrey,
  width: "80%",
}

const toastStyleAddedFlight: ViewStyle = {
  width: "100%",
  height: 60,
  marginBottom: 24,
  alignItems: "flex-start",
}

export enum TypePressDetailFlightCard {
  CHECK_IN_ROW = "CHECK_IN_ROW",
  GATE = "GATE",
  BAGGAGE_BELT = "BAGGAGE_BELT",
  TERMINAL = "TERMINAL",
  SKYTRAIN = "SKYTRAIN"
}

export enum TypeTransport {
  PUBLIC = 'public',
  TRANSIT = 'transit',
}

export enum TypeGetIntoAirport {
  LINK1 = "LINK1",
  LINK2 = "LINK2",
  LINK3 = "LINK3",
}

const wrapHeader: ViewStyle = {
  paddingHorizontal: 14,
  position: "absolute",
  zIndex: 10,
}

const getValue = (value, defaultValue) => value || defaultValue

let intervalRefreshFlight: any
const FLY_APPSCAPADE_TYPE = "general_entry_point"
const FlightDetails = ({ navigation, route }) => {
  const { payload, direction, isFromScanBoardingPass, referrer, isFromUpcomingEvent } = route.params
  const { item: flyItem } = payload
  const dispatch = useDispatch()
  const isFocused = useIsFocused()
  const enableFlySavePrompt = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLIGHT_SAVEPROMPT)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const toastForRefresh = useRef(null)
  const toastForRemoveFlight = useRef(null)
  const toastForSavedFlight = useRef(null)
  const unableToLoadLocationRef = useRef(null)
  const mapUnavailable = useRef(null)
  const [loadingFlightMap, setLoadingFlightMap] = useState(false)
  const [heightTickerBand, setHeightTickerBand] = useState(0)
  const [travelChecklistAEM, setTravelChecklistAEM] = useState<ITravelChecklistAEM>(null);
  const [isTravelChecklistAEMLoading, setIsTravelChecklistAEMLoading] = useState<boolean>(true);

  const loadingShareRef = useRef(false)

  const flyFlightDetailsPayload: any = useSelector(FlySelectors.flyFlightDetailsPayload)
  const flyFlightDetailsFetching: any = useSelector(FlySelectors.flyFlightDetailsFetching)
  const flyFlightDetailsFetchingFirst = useSelector(FlySelectors.flyFlightDetailsFetchingFirst)
  const getIntoCityOrAirportPayload: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.getIntoCityOrAirportPayload(state),
  )

  const flyFlightDetailsError: any = useSelector(FlySelectors.flyFlightDetailsError)
  const insertFlightPayload = useSelector(MytravelSelectors.insertFlightPayload) //list of saved flights
  const removeFlightPayload = useSelector(MytravelSelectors.removeFlightPayload) //info of flight is unsaved
  const flyLastUpdatedTimeStamp: any = useSelector(FlySelectors.flyLastUpdatedTimeStamp)
  const callAEMData = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.FLY_APPSCAPADE))
  const flyAppscapadeData = callAEMData?.data

  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const msg47 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG47")
  const msg48 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG48")
  const msg58 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG58")
  const msg65 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG65")
  const iconUrl = handleImageUrl(msg47?.icon)

  const inf22 = dataCommonAEM?.data?.informatives?.find((e) => e?.code === "INF22")

  const initSelectedTravelOption = direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking

  const tickerbandMaintananceHook = useTickerbandMaintanance(MaintananceTickerBandType.FLY_DETAILS)

  const scrollRef = useRef(null)
  const alertApp = useRef(null)
  const [fabVisibility, setFabVisibility] = useState(false)
  const { isModalVisible, openModal, closeModal } = useModal("saveConnectingFlightDetail")
  const {
    isModalVisible: isModalVisibleTravelOption,
    openModal: openModalTravelOption,
    closeModal: closeModalTravelOption,
  } = useModal("flightDetailSaveTravelOption")
  const [showModalTravelOption, setShowModalTravelOption] = useState(false)
  const [selectedTravelOption, setSelectedTravelOption] = useState(initSelectedTravelOption)
  const [showNoInternetError, setShowNoInternetError] = useState(false)
  const [isGoToListingFlight, setIsGoToListingFlight] = useState(false)
  const [isRefereePopupVisible, setRefereePopupVisible] = useState(false)
  const flyShowTickerBand = useSelector(FlySelectors.flyShowTickerBand)
  const {
    onPressCTA,
    onCloseTickerBand,
    tickerBand,
    tickerBandDescription,
    tickerBandButtonText,
    isShowMaintenance,
    isShowTickerband,
    fetchTickerbandMaintanance,
    errorData,
  } = useTickerbandMaintanance(MaintananceTickerBandType.FLY_DETAILS)

  const [statusSaveAndShare, setStatusSaveAndShare] = useState<"START FOLLOW" | "SAVE SUCCESS" | "STARTING SHARE" | null>(null)
  const {
    isModalVisible: isModalSaveAndShare,
    openModal: openModalSaveAndShare,
    closeModal: closeModalSaveAndShare,
  } = useModal("modalSaveAndShare")

  const isShowModalConfirmSaveFly = useSelector(FlySelectors.flyShowModalConfirmSaveFly)
  const isCheckInOnlineLoadFailed = useSelector(FlySelectors.flyCheckInOnlineLoadFailed)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const [mapRMFlag, setMapRMFlag] = useState(false)
  const [selectedTopTravelOption, setSelectedTopTravelOption] = useState(
    direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking,
  )
  const isTravellerLocal = selectedTopTravelOption === TravelOption.iAmTravelling
  const isDepartureLocal = direction === FlightDirection.departure
  const { logClickEvent } = useFlightDetailClickEvent({ isTraveller: isTravellerLocal, isDeparture: isDepartureLocal })
  const [loadingSaveFlight, setLoadingSaveFlight] = useState(false)
  const [showCalendarModal, setShowCalendarModal] = useState(false)
  const connectingFlightPayload = useSelector(FlySelectors.connectingFlightPayload)
  const isSharing = useRef(false)
  const { fly_eci_dynamic_display, flyDetailP1Flag } = useContext(FLY_CONTEXT)?.Handlers
  const enableEciDynamicDisplay = isFlagOnCondition(fly_eci_dynamic_display)
  const isFlightDetailP1 = isFlagOnCondition(flyDetailP1Flag)
  const [showSaveFlightWhenOnlineCheckIn, setShowSaveFlightWhenOnlineCheckIn] =
    useState<boolean>(false)
  const { handleCheckInOnline, handleSaveFlightWhenCheckInOnline } = useOnlineCheckIn({
    direction,
    flyItem,
    navigation,
    setShowSaveFlightWhenOnlineCheckIn,
  })

  const flightTerminalDisclaimerText = useMemo(
    () => flyFlightDetailsPayload?.flightDetailsData?.displayTerminalDisclaimer,
    [flyFlightDetailsPayload],
  )
  const shouldShowSQArrivalTerminalInfo = useMemo(
    () =>
      !!flightTerminalDisclaimerText &&
      !!inf22?.informativeText,
    [inf22?.informativeText, flightTerminalDisclaimerText],
  )

  const flightHeroImageStyle = shouldShowSQArrivalTerminalInfo ? { marginBottom: 32 } : {}
  const dropDownSelectCardContainerStyle = shouldShowSQArrivalTerminalInfo ? { bottom: -40 } : {}
  const flightDetailsCardDisclaimerText = flightTerminalDisclaimerText ? (
    <Text text={`(${flightTerminalDisclaimerText})`} style={styles.terminalDisclaimerText} />
  ) : null

  const { getDeeplinkShare, getTravelChecklistAEM } = useFlightDetail()
  const [isRefereeModalEverShown, setRefereeModalEverShown] = useState(false)
  const [isNoInternetConnection, setNoInterConnection] = useState(false)
  const [showRemoveFlightAlert, setShowRemoveFlightAlert] = useState(false)

  const isFlightAfter24h = useMemo(() => {
    if (flyFlightDetailsPayload?.flightDetailsData) {
      const { scheduledDate, scheduledTime, actualTimestamp, displayTimestamp } = flyFlightDetailsPayload?.flightDetailsData
      const priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime}`
      if (priorityTime) {
        const formatedScheduledTime = moment.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
        const currentTimeToUTC = moment().tz("Asia/Singapore")
        const isFlighterAfter24h = moment(currentTimeToUTC).add(1, "day") <= formatedScheduledTime
        return isFlighterAfter24h
      }
      return false
    }
    return false
  }, [
    flyFlightDetailsPayload?.flightDetailsData
  ])

  const shouldShowShareButton = flyFlightDetailsPayload?.flightDetailsData?.flightNumber && !isShowMaintenance

  const { isSaved, isPassenger } = useMemo(() => {
    let isPassenger = false
    let isSaved = false
    const data = flyFlightDetailsPayload?.flightDetailsData || {}
    if (myTravelFlightsPayload?.getMyTravelFlightDetails) {
      const idx = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(savedFlight => {
        return savedFlight.flightNumber === data.flightNumber &&
          savedFlight.scheduledDate === data.scheduledDate &&
          (savedFlight.flightDirection || savedFlight.direction) === (data.flightDirection || data.direction)
      })
      if (idx >= 0) {
        isSaved = true
        isPassenger = myTravelFlightsPayload.getMyTravelFlightDetails[idx].isPassenger
      }
    }
    return {
      isSaved: isSaved,
      isPassenger: isPassenger
    }
  }, [flyFlightDetailsPayload?.flightDetailsData, myTravelFlightsPayload?.getMyTravelFlightDetails])

  const isButtonSaveHidden = useMemo(() => {
    if (!isLoggedIn) {
      return false
    }
    if (!flyFlightDetailsPayload?.flightDetailsData || !myTravelFlightsPayload?.getMyTravelFlightDetails) {
      return true
    }
    if (isShowMaintenance) {
      return false
    }
    return flyItem?.isMSError || isSaved
  }, [
    isLoggedIn,
    flyItem?.isMSError,
    isSaved,
    myTravelFlightsPayload?.getMyTravelFlightDetails,
    flyFlightDetailsPayload?.flightDetailsData,
  ])


  const refRetryAction = useRef(null)

  useEffect(() => {
    if (flyFlightDetailsError) {
      toastForRefresh?.current?.show(DURATION.FOREVER)
      setIsTravelChecklistAEMLoading(false)
    }
  }, [flyFlightDetailsError])

  useFlightSaveErrorHandling(isSharing.current, statusSaveAndShare ? true : false)

  const wrapHeaderStyles = useMemo(() => {
    return {
      flexDirection: "row",
      width: "100%",
      top: simpleCondition({
        condition: isShowTickerband,
        ifValue: 25,
        elseValue: 58,
      }),
      justifyContent: "center",
      paddingHorizontal: 14,
      position: "absolute",
      zIndex: 99999,
      alignItems: "center",
    }
  }, [isShowTickerband])

  const getPositionBackButton = useMemo(() => {
    return {
      top: isShowTickerband ? heightTickerBand + 25 : 58,
    }
  }, [isShowTickerband, heightTickerBand])

  useEffect(() => {
    setCurrentScreenActive(`Flight_Detail_${flyItem?.flightNumber}`)
    commonTrackingScreen(
      `Flight_Detail_${flyItem?.flightNumber}`,
      getPreviousScreen(),
      isLoggedIn,
    )
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    getFlightDetailsApiFirst()
    setIntervalRefeshFlight()

    return () => {
      clearInterval(intervalRefreshFlight)
    }
  }, [route.params, isLoggedIn])

  const filterDataForFly = (data) => {
    if (isEmpty(data?.list)) return null
    return data?.list?.find((e) => e?.type === FLY_APPSCAPADE_TYPE)
  }
  useEffect(() => {
    if (!callAEMData?.data) {
      dispatch(
        AemActions.getAemConfigData({
          name: AEM_PAGE_NAME.FLY_APPSCAPADE,
          pathName: "getFlyAppscapade",
          callBackAfterSuccess: filterDataForFly,
        }),
      )
    }
  }, [callAEMData?.data])

  const handleGetTravelChecklistAEM = async () => {
    setIsTravelChecklistAEMLoading(true)
    const isDeparture = direction === FlightDirection.departure;
    const isTraveller = selectedTopTravelOption === TravelOption.iAmTravelling;
    const { flightNumber, airline, displayTerminal, destinationCode, departingCode } = flyFlightDetailsPayload.flightDetailsData
    const res = await getTravelChecklistAEM({
      flight_no: flightNumber,
      user_profile: isTraveller ? UserProfileTagNameEnum.TRAVELLER : UserProfileTagNameEnum.MEETERS_AND_GREETERS,
      direction: isDeparture ? DirectionTagNameEnum.DEPARTURE : DirectionTagNameEnum.ARRIVAL,
      airport: isDeparture ? destinationCode : departingCode,
      airline: airline,
      terminal: displayTerminal,
    })
    setTravelChecklistAEM(res)
    setIsTravelChecklistAEMLoading(false)
  }

  const checkReloadTravelChecklistAEM = useMemo(() => {
    if (!flyFlightDetailsPayload?.flightDetailsData) {
      return ""
    }
    return `${flyFlightDetailsPayload?.flightDetailsData?.flightNumber}-${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate}-${direction}`
  }, [
    flyFlightDetailsPayload?.flightDetailsData?.flightNumber,
    flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
    direction,
  ])

  useEffect(() => {
    if (isFlightDetailP1 && checkReloadTravelChecklistAEM) {
      handleGetTravelChecklistAEM();
    }
  }, [checkReloadTravelChecklistAEM, selectedTopTravelOption, isFlightDetailP1]);

  const flightDetailSectionData: FlightDetailSectionCFProps = useMemo(() => {
    if (!isFlightDetailP1 || !travelChecklistAEM?.success || !travelChecklistAEM?.data?.sections?.length) return null
    return {
      ...travelChecklistAEM.data,
      sections: travelChecklistAEM.data.sections.sort((a, b) => Number(a.sequenceNumber) - Number(b.sequenceNumber)),
    };
  }, [travelChecklistAEM, isFlightDetailP1]);

  /**
   * Display referree popup
   * if flight details is loaded and flight is not saved
   * flight number from referrer link must be the same as in screen
   * flow is from referrer link and referrer is different from referree
   * has appscapade data ready
   * flight is eligible (DEP + after 24h)
   * shown for the first time
   */
  useEffect(() => {
    if (flyFlightDetailsPayload?.flightDetailsData?.flightNumber
      && flyItem?.flightNumber === flyFlightDetailsPayload?.flightDetailsData?.flightNumber
      && referrer
      && referrer !== profilePayload?.id
      && flyAppscapadeData
      && flyFlightDetailsPayload?.flightDetailsData && !isSaved
      && isFlightAfter24h
      && !isRefereePopupVisible
      && !isRefereeModalEverShown
    ) {
      setRefereePopupVisible(true)
      setRefereeModalEverShown(true)
    }
  }, [
    flyFlightDetailsPayload?.flightDetailsData?.flightNumber,
    isSaved,
    flyItem?.flightNumber,
    referrer,
    flyAppscapadeData,
    isFlightAfter24h
  ])

  const setIntervalRefeshFlight = () => {
    const refreshInterval = env()?.FLIGHT_REFRESH_INTERVAL
    clearInterval(intervalRefreshFlight)
    intervalRefreshFlight = setInterval(() => {
      if (!isFocused) {
        return;
      }
      if (!showNoInternetError) {
        InteractionManager.runAfterInteractions(() => {
          refreshFlightDetails()
        })
      }
    }, refreshInterval)
  }

  const getTickerBand = () => {
    dispatch(
      AemActions.getAemConfigData({
        name: AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true,
      }),
    )
  }

  const clearFlyDetailData = () => {
    dispatch(FlyCreators.flyClearFlightDetailsPayload())
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
    dispatch(FlyCreators.flyCheckInOnlineLoadFailed(false))
    dispatch(FlyCreators.getAppscapadeBannerReset())
  }

  useEffect(() => {
    return () => {
      clearFlyDetailData()
    }
  }, [])

  const checkInternetConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  useEffect(() => {
    checkInternetConnection().then((isConnection) => {
      if (!isConnection) {
        setShowNoInternetError(true)
      }
    })
  }, [])

  useEffect(() => {
    if (!isSaved) return
    if (!isPassenger) {
      setSelectedTopTravelOption(TravelOption.iAmPicking)
      setSelectedTravelOption(TravelOption.iAmPicking)
    } else {
      setSelectedTopTravelOption(TravelOption.iAmTravelling)
      setSelectedTravelOption(TravelOption.iAmTravelling)
    }
  }, [flyFlightDetailsPayload, isSaved, isPassenger])

  useEffect(() => {
    if (isFromUpcomingEvent) {
      const flightDetailsResult = flyFlightDetailsPayload?.flightDetailsData
      handleUpdateUpcomingAndSavedFlight(flightDetailsResult)
    }
  }, [flyFlightDetailsPayload?.flightDetailsData, isFromUpcomingEvent])

  /**
   * Save flight success response
   */
  useEffect(() => {
    if (insertFlightPayload?.insertFlightData?.success || insertFlightPayload?.recordExist) {
      putFlightInfoToAdobeAnalyticAfterSaveUnSave({
        data: insertFlightPayload,
        isSuccess: true,
        tag: AdobeTagName.CAppFlyFlightDetailSaveFlight,
        flyProfile: "flying",
        pageName: AdobeTagName.CAppFlyFlightDetail,
        isSaveFlight: true,
      })
      if (insertFlightPayload?.isInsertSuccessfully && isFocused) {
        setLoadingSaveFlight(false)
        const timeStamp = new Date().getTime()
        // if user hasnt save any flight within 24hours or user has only 1 saved flight
        // show save connecting flight modal
        // else show native share popup and finish save
        const showConnectingFlight = (
          getLastSavedFlightTime() + env()?.FLIGHT_SHOW_POPUP_ADD_RETURN < timeStamp ||
          size(myTravelFlightsPayload?.getMyTravelFlightDetails) === 1
        ) && insertFlightPayload?.flightData?.isPassenger
        if (!showConnectingFlight) {
          if (statusSaveAndShare) {
            setStatusSaveAndShare("SAVE SUCCESS")
            closeModalSaveAndShare()
          } else {
            closeModalTravelOption()
            toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          }
        } else {
          if (statusSaveAndShare) {
            setStatusSaveAndShare("SAVE SUCCESS")
            closeModalSaveAndShare()
          } else {
            openModal()
            setLastSavedFlightTime(0)
          }
        }
        save(StorageKey.isSaveFlightTriggered, true)
      }
    }
    if (insertFlightPayload?.errorFlag) {
      setLoadingSaveFlight(false)
      closeModalTravelOption()
    }
  }, [insertFlightPayload, statusSaveAndShare])

  useEffect(() => {
    if (removeFlightPayload?.isRemovedSuccessFully) {
      putFlightInfoToAdobeAnalyticAfterSaveUnSave({
        data: removeFlightPayload,
        isSuccess: true,
        tag: AdobeTagName.CAppFlyFlightDetailRemoveFlight,
        flyProfile: "flying",
        pageName: AdobeTagName.CAppFlyFlightDetail,
        isSaveFlight: false,
      })
      toastForSavedFlight?.current?.closeNow()
      toastForRemoveFlight?.current?.show(DURATION.LENGTH_SHORT)
      setSelectedTravelOption(direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking,)
      setSelectedTopTravelOption(direction === FlightDirection.departure ? TravelOption.iAmTravelling : TravelOption.iAmPicking,)
      dispatch(MytravelCreators.flyClearInsertFlightPayload())
    }
  }, [removeFlightPayload])

  useEffect(() => {
    return () => setIsGoToListingFlight(false)
  }, [])

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const getTitleDropdownTravelOption = useMemo(() => {
    if (
      direction === FlightDirection.departure ||
      selectedTopTravelOption === TravelOption.iAmTravelling
    ) {
      return selectedTopTravelOption
    }
    if (selectedTopTravelOption === TravelOption.iAmPicking) {
      return "dropdownSelectCard.imPickingSomeone"
    }
  }, [selectedTopTravelOption, direction])

  const filterDateAddReturnCalendar = useMemo(() => {
    if (flyFlightDetailsPayload?.flightDetailsData?.type === "loading") {
      return moment().format("YYYY-MM-DD")
    } else {
      return (
        moment(flyFlightDetailsPayload?.flightDetailsData?.displayTimestamp).format("YYYY-MM-DD") ||
        moment().format("YYYY-MM-DD")
      )
    }
  }, [flyFlightDetailsPayload])

  const getFlightDetailsApiFirst = () => {
    dispatch(
      FlyCreators.flyFlightDetailsRequestFirst(
        direction,
        flyItem?.flightNumber,
        flyItem?.flightDate,
        FlightRequestType.FlightDefault,
        flyItem?.flightNumber?.substring(0, 2),
        flyItem?.flightStatus,
        isFromScanBoardingPass,
      ),
    )
    dispatch(FlyCreators.flightDetailPerkRequest())
  }

  const getFlightDetailsApi = (
    flightRequest: FlightRequestType = FlightRequestType.FlightDefault,
  ) => {
    dispatch(
      FlyCreators.flyFlightDetailsRequest(
        direction,
        flyItem?.flightNumber,
        flyItem?.flightDate,
        flightRequest,
        flyItem?.flightNumber?.substring(0, 2),
        flyItem?.flightStatus,
        isFromScanBoardingPass,
      ),
    )
    dispatch(FlyCreators.flightDetailPerkRequest())
  }

  useEffect(() => {
    if (flyItem?.flightNumber && flyItem?.flightDate) {
      dispatch(
        FlyCreators.flyGetIntoCityOrAirportRequest(
          direction,
          `${flyItem?.flightNumber}_${flyItem?.flightDate}`,
        ),
      )
    }
  }, [flyItem?.flightNumber, flyItem?.flightDate])

  const refreshFlightDetails = () => {
    checkInternetConnection().then((isConnection) => {
      if (isConnection) {
        if (!isEmpty(refRetryAction.current)) {
          handleMap(refRetryAction.current?.type, refRetryAction.current?.item)
          return
        }
        //prevent data collision when use save/unsave flight at the same time as interval flight details api
        if (
          isModalVisibleTravelOption || //when use is selecting travel option
          isModalVisible ||
          showRemoveFlightAlert ||
          insertFlightPayload?.loading ||
          removeFlightPayload?.loading
        ) {
          return
        }
        toastForRefresh?.current?.closeNow()
        dispatch(MytravelCreators.flyClearInsertFlightPayload())
        getTickerBand()
        getFlightDetailsApi(FlightRequestType.FlightRefresh)
        dispatch(
          FlyCreators.flyGetIntoCityOrAirportRequest(
            direction,
            `${flyItem?.flightNumber}_${flyItem?.flightDate}`,
          ),
        )
        setShowNoInternetError(false)
      } else {
        toastForRefresh?.current?.show(DURATION.FOREVER)
      }
    })
    dispatch(FlyCreators.flyLastUpdatedTimeStamp(flyModuleUpdatedTime()))
  }

  const onPressTouch = () => {
    scrollRef.current?.scrollTo({
      y: 0,
      animated: true,
    })
  }

  const handleScroll = (event) => {
    if (event.nativeEvent.contentOffset.y > 200) {
      setFabVisibility(true)
    } else if (event.nativeEvent.contentOffset.y === 0) {
      setFabVisibility(false)
    }
  }

  if (!flyFlightDetailsPayload) {
    return <></>
  }

  const showErrorFeedBackToastMessage = () => {
    return (
      <FeedBackToast
        ref={toastForRefresh}
        style={styles.feedBackToastStyle}
        textButtonStyle={styles.toastButtonStyle}
        position={"bottom"}
        textStyle={styles.toastTextStyle}
        type={FeedBackToastType.fullWidthFeedBack}
        text={translate("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp}
      />
    )
  }

  const showToastForRemoveFlight = () => {
    return (
      <FeedBackToast
        ref={toastForRemoveFlight}
        style={[styles.feedBackToastStyle, isFlightDetailP1 && { zIndex: 1000 }]} // zIndex = 1000 to overlap ShowNoInternetConnection (zIndex = 999) if isFlightDetailP1 = true
        textButtonStyle={styles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 100 }}
        textStyle={styles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flyLanding.removeFlightNew")}
      />
    )
  }

  const onClosedSheet = () => {
    setShowModalTravelOption(false)
  }

  const onCloseConfirmPopUpSavedFlight = () => {
    closeModal()
  }

  const travelOptionTapped = (option) => {
    const flyProfile = option === TravelOption.iAmTravelling ? "flying" : "non-flying"
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightDate
    trackAction(AdobeTagName.CAppFlyFlightDetailFlyProfile, {
      [AdobeTagName.CAppFlyFlightDetailFlyProfile]: `${flyProfile}|${flightNumber}|${flightDate}`,
    })
    setShowModalTravelOption(false)
    setSelectedTravelOption(option)
    setSelectedTopTravelOption(option)
  }

  const handleMessage58 = (message) => {
    if (message) {
      let status = flyFlightDetailsPayload?.flightDetailsData?.flightStatus?.toLowerCase()
      if (status?.includes("cancelled")) {
        status = `been ${status}`
      }
      return message
        .replace("<Flight No.>", flyItem?.flightNumber)
        .replace("<departed/landed/been cancelled>", status)
    }
    return message
  }
  const handleMessage48 = (message, number, place) => {
    if (message) {
      return message.replace("<Flight No.>", number).replace("<country>", place)
    }
    return message
  }

  const notAbleToSaveAlert = () => {
    const temp =
      flyFlightDetailsPayload?.flightDetailsData?.flightStatus?.split(" ") ||
      flyItem?.flightStatus?.split(" ")
    const status = temp?.length > 0 ? temp[0] : ""
    const message =
      handleMessage58(msg58?.message) ||
      `${translate("flightLanding.flight")} ${flyItem?.flightNumber} ${translate(
        "flightLanding.has",
      )} ${status} ${translate("flightLanding.notSaveMessage")}`
    alertApp?.current?.show({
      title: msg58?.title || translate("flightLanding.alert"),
      description: message,
      labelAccept: msg58?.firstButton || translate("flightLanding.okay"),
      onAccept: () => null,
      type: AlertTypes.ALERT,
    })
  }

  const checkFlightCanSave = (statusTag: string, newDirection: string) => {
    const status = statusTag?.toLowerCase()
    const priorityTime =
      flyFlightDetailsPayload?.flightDetailsData?.actualTimestamp ||
      flyFlightDetailsPayload?.flightDetailsData?.estimatedTimestamp ||
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate} ${flyFlightDetailsPayload?.flightDetailsData?.scheduledTime}`
    const currentTimeToUTC = moment().tz("Asia/Singapore")
    const flightTime = moment.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
    if (newDirection === FlightDirection.departure) {
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
          return false
        default:
          return true
      }
    } else {
      switch (true) {
        case /cancelled/gim.test(status):
        case /landed/gim.test(status) &&
          moment(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") <
          currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
          return false
        default:
          return true
      }
    }
  }

  const onSaveFlight = async (isRemove: boolean, statusPassenger) => {
    logClickEvent(isRemove ?
      ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.UNSAVE_FLIGHT_BUTTON :
      ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SAVE_FLIGHT_BUTTON)
    if (
      !checkFlightCanSave(
        flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
        direction,
      ) &&
      !isRemove
    ) {
      notAbleToSaveAlert()
      return null
    }
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate:
        flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: statusPassenger,
    }
    if (isRemove && isLoggedIn) {
      setShowRemoveFlightAlert(true)
      Alert.alert(
        msg48?.title,
        handleMessage48(
          msg48?.message,
          flyItem?.flightNumber,
          flyFlightDetailsPayload?.flightDetailsData?.destinationPlace,
        ),
        [
          {
            text: msg48?.firstButton,
            onPress: () => setShowRemoveFlightAlert(false)
          },
          {
            text: msg48?.secondButton,
            style: "cancel",
            onPress: () => {
              const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detail-unsave`)
              dtAction.reportStringValue(
                "flight-detail-unsave-press-flightNumber",
                `${flyItem?.flightNumber}`,
              )
              dtAction.reportStringValue(
                "flight-detail-unsave-press-scheduledDate",
                `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate ||
                flyItem?.scheduledDate
                }`,
              )
              dtAction.reportStringValue("flight-detail-unsave-press-direction", `${direction}`)
              dtAction.reportStringValue(
                "flight-detail-unsave-press-statusPassenger",
                `${statusPassenger}`,
              )
              setShowRemoveFlightAlert(false)
              const payloadRemove = { ...payload }
              payloadRemove.item.scheduledDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
              dispatch(MytravelCreators.flyMyTravelRemoveFlightRequest(data, payloadRemove))
              dtAction.leaveAction()
            },
          },
        ],
      )
    } else {
      if (isLoggedIn) {
        openModalTravelOption()
      } else {
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem,
          callBackAfterLoginSuccess: () => {
            getFlightDetailsApi()
            openModalTravelOption()
          },
          callBackAfterLoginCancel: () => null,
        })
      }
    }
  }

  const savedFlightOnPress = async (_, travelOption?: TravelOption) => {
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detail-save`)
    dtAction.reportStringValue("flight-detail-save-press-flightNumber", `${flyItem?.flightNumber}`)
    dtAction.reportStringValue(
      "flight-detail-save-press-scheduledDate",
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate}`,
    )
    dtAction.reportStringValue("flight-detail-save-press-direction", `${direction}`)
    dtAction.reportStringValue("flight-detail-save-press-travelOption", `${travelOption}`)
    // call save
    setLoadingSaveFlight(true)
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate:
        flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: travelOption ? (travelOption === TravelOption.iAmTravelling) : (selectedTravelOption === TravelOption.iAmTravelling),
    }

    if (isLoggedIn) {
      let payloadForSave = payload
      if (!payloadForSave?.item?.scheduledTime) {
        payloadForSave = {
          ...payloadForSave,
          item: {
            ...payloadForSave.item,
            scheduledTime: flyFlightDetailsPayload?.flightDetailsData?.scheduledTime,
            flightDate: flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
            scheduledDate: flyFlightDetailsPayload?.flightDetailsData?.scheduledDate,
          },
        }
      }
      if (referrer) {
        payloadForSave.referrer = referrer
      }
      // enter flight details via FDL
      if (!payloadForSave?.item?.departingCode) {
        payloadForSave.item.departingCode = flyFlightDetailsPayload?.flightDetailsData?.departingCode
      }
      if (!payloadForSave?.item?.destinationCode) {
        payloadForSave.item.destinationCode = flyFlightDetailsPayload?.flightDetailsData?.destinationCode
      }
      if (!payloadForSave?.item?.direction) {
        payloadForSave.item.direction = direction
      }
      if (!payloadForSave?.item?.terminal) {
        payloadForSave.item.terminal = flyFlightDetailsPayload?.flightDetailsData?.displayTerminal
      }
      if (!payloadForSave?.item?.flightNavigationType) {
        payloadForSave.flightNavigationType = flyFlightDetailsPayload?.flightDetailsData?.flightNavigationType || FlightNavigationType.FLightSearchDetail
      }
      dispatch(MytravelCreators.flyMyTravelInsertFlightRequest(data, payloadForSave))
    } else {
      navigation.navigate(NavigationConstants.authScreen, { sourceSystem })
    }
    dtAction.leaveAction()
  }

  let fabContainerBottom = { bottom: 120 }
  let sectionSeparator = { height: 90 }
  if (isButtonSaveHidden) {
    fabContainerBottom = { bottom: 40 }
    sectionSeparator = { height: 40 }
  }
  const showFlightAddedFeedBackToastMessage = () => {
    return (
      <FeedBackToast
        ref={toastForSavedFlight}
        style={toastStyleAddedFlight}
        textButtonStyle={toastButtonStyleAdded}
        position={"bottom"}
        textStyle={toastTextStyleAdded}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved")}
        onCallback={() => dispatch(MytravelCreators.flyClearInsertFlightPayload())}
      />
    )
  }

  const onPressFlightCardLinks = (titleCardLink: string) => {
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightDate
    trackAction(AdobeTagName.CAppFlyFlightDetailFlightCardLinks, {
      [AdobeTagName.CAppFlyFlightDetailFlightCardLinks]: `${titleCardLink}|${flightNumber}|${flightDate}`,
    })
  }

  const onTrackingTimeline = (value: string, sectionKey?: string) => {
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightDate
    trackAction(AdobeTagName.CAppFlyFlightDetailTimelineTiles, {
      [AdobeTagName.CAppFlyFlightDetailTimelineTiles]: `${value}|${flightNumber}|${flightDate}`,
    })
    logClickEvent(sectionKey)
  }

  const handleMap = async (type, item = null, skytrainItem?: { type: TypeTransport, terminal: number }) => {
    const isConnection = await checkInternetConnection()
    if (!isConnection) {
      refRetryAction.current = {
        type,
        item,
      }
      setShowNoInternetError(true)
      return
    }
    setShowNoInternetError(false)
    refRetryAction.current = null
    if (!mapRMFlag) {
      return mapUnavailable?.current.show()
    }
    const { flightDetailsData } = flyFlightDetailsPayload
    setLoadingFlightMap(true)
    const input = {
      category: "",
      terminal: flightDetailsData.displayTerminal,
      direction: flyItem?.direction === "DEP" ? "Departure" : "Arrival",
      name: "",
      gate: undefined,
    }
    let isFocusToArea = false; // For navigating to Terminal in ATOM Map
    switch (type) {
      case TypePressDetailFlightCard.GATE:
        input.category = TypePressDetailFlightCard.GATE
        input.name = flightDetailsData.displayGate
        break
      case TypePressDetailFlightCard.CHECK_IN_ROW:
        input.category = TypePressDetailFlightCard.CHECK_IN_ROW
        input.name = flightDetailsData.checkInRow
        break
      case TypePressDetailFlightCard.BAGGAGE_BELT:
        input.category = TypePressDetailFlightCard.BAGGAGE_BELT
        input.name = flightDetailsData.baggageBelt
        break
      case TypePressDetailFlightCard.TERMINAL:
        input.category = TypePressDetailFlightCard.TERMINAL
        input.name = ""
        isFocusToArea = true
        break
      case TypePressDetailFlightCard.SKYTRAIN:
        input.category = TypePressDetailFlightCard.SKYTRAIN
        input.name = `T${skytrainItem.terminal} ${skytrainItem?.type}`
        input.gate = flightDetailsData.displayGate
        break
      case TypeGetIntoAirport.LINK1:
        input.category = flyItem?.direction === "DEP" ? "Drop-off" : "Pick-up"
        input.name = item
        break
      case TypeGetIntoAirport.LINK3:
        input.category =
          flyItem?.direction === "DEP" ? "Accessible Drop-off" : "Accessibility Pick-up"
        input.name = item
        break
      case TypeGetIntoAirport.LINK2:
        input.category = "Nearest Car Park"
        input.name = flightDetailsData.nearestCarpark
        break
    }
    trackAction(AdobeTagName.CAppNavigationMapsEnter, {
      [AdobeTagName.CAppNavigationMapsEnter]: `${AdobeTagName.CAppFlyFlightDetail}|Fly`,
    })

    if (type !== TypePressDetailFlightCard.TERMINAL) {
      logClickEvent(skytrainItem ? ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.GETTING_AROUND_CHANGI_AIRPORT : ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FLIGHT_INFO_DETAILS)
    }

    const fetchData = await getLocationMapping({ input: input })

    trackAction(AdobeTagName.CAppATOMSEntryClick, {
      [AdobeTagName.CAppATOMSEntryClick]: `${input.direction} Detail Page|${input.name}|${fetchData.local_ref}`,
    })
    navigation.navigate(NavigationConstants.changiMap, { localRef: fetchData.local_ref, isFocusToArea })
    setLoadingFlightMap(false)
  }

  const handleOnLayout = (event) => {
    const { height } = event.nativeEvent.layout
    setHeightTickerBand(height)
  }
  const renderTickerBand = () => (
    <TickerBand
      urgent={false}
      title={flyShowTickerBand && tickerBand}
      description={flyShowTickerBand && tickerBandDescription}
      buttonText={flyShowTickerBand && tickerBandButtonText}
      onCTAPress={onPressCTA}
      onClose={() => onCloseTickerBand()}
      isLanding={true}
      onLayout={handleOnLayout}
    />
  )

  const trackAAWhenBack = () => {
    const flightDirection = flyItem?.direction
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightNumber
    trackAction(AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, {
      [AdobeTagName.CAppFlyFlightDetailTopNavigationMenu]: `${flightDirection}|${flightNumber}|${flightDate}`,
    })
  }

  const onClosedTravelOptionSheet = () => {
    if (!loadingSaveFlight) {
      closeModalTravelOption()
    }
  }

  const savedFlightTravelOptionsOnModalHide = () => {
    setSelectedTravelOption(initSelectedTravelOption)
  }

  const handleConnectingFlightOnPress = () => {
    const connectingFlightPayloadData = {
      isConnecting: true,
      flightConnecting: {
        ...insertFlightPayload?.flightData?.item,
        isPassenger: insertFlightPayload?.flightData?.isPassenger,
      },
    }
    dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadData))
    setIsGoToListingFlight(true)
    onCloseConfirmPopUpSavedFlight()
  }

  const backOnPressed = () => {
    trackAAWhenBack()
    navigation.goBack()
  }
  const sourceSystem = SOURCE_SYSTEM.FLIGHTS

  const onOpenSaveAndShareModal = () => {
    openModalSaveAndShare()
    setStatusSaveAndShare("START FOLLOW")
  }

  const checkSaveAndShare = () => {
    if(enableFlySavePrompt && checkFlightCanSave(
      flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
      direction,
    ) && !tickerbandMaintananceHook.isShowMaintenance) {
      onOpenSaveAndShareModal()
    } else {
      onSharePress()
    }
  }

  const handleSharePress = () => {
    logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.SHARE_FLIGHT_BUTTON)
    if (isLoggedIn) {
      !isSaved ? checkSaveAndShare() : onSharePress()
    } else {
      clearInterval(intervalRefreshFlight)
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: checkSaveAndShare,
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const onSharePress = () => {
    onOpenFlightShareSheet()
    trackAction(AdobeTagName.CAppFlyFlightDetailTopNavigationMenu, {
      [AdobeTagName.CAppFlyFlightDetailTopNavigationMenu]: `${direction} | ${flyItem?.flightNumber} | Share Button`,
    })
  }

  const onOpenFlightShareSheet = async (isSavedSuccess?: boolean) => {
    if (loadingFlightMap || loadingShareRef.current) {
      return;
    }
    loadingShareRef.current = true
    setLoadingFlightMap(true)
    if (!isSavedSuccess) {
      const isConnected = await checkInternetConnection()
      setNoInterConnection(!isConnected)
      if (!isConnected) {
        setLoadingFlightMap(false)
        loadingShareRef.current = false
        return;
      }
    }
    const {
      flightNumber,
      departingCode,
      destinationCode,
      scheduledDate,
      scheduledTime,
      actualTimestamp,
      displayTimestamp
    } = flyFlightDetailsPayload.flightDetailsData
    const isDeparture = direction === FlightDirection.departure
    const saved = isSavedSuccess ?? (isSaved && isLoggedIn)

    let options: ShareOptions = {
      title: "I would like to share a flight's information with you...",
      message: "I would like to share a flight's information with you...",
      failOnCancel: true
    }
    const shareLinkResponse = await getDeeplinkShare({
      mode: "flight_details",
      flightNo: flightNumber,
      direction: direction,
      scheduledDate: scheduledDate,
      scheduledTime: scheduledTime,
      promoCode: "",
      isPassenger: isPassenger,
    })

    setLoadingFlightMap(false)
    loadingShareRef.current = false
    if (!shareLinkResponse.success) {
      return;
    }

    const priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime}`
    const formatedScheduledTime = moment.tz(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
    const currentTimeToUTC = moment().tz("Asia/Singapore")
    const isFlightEligible = moment(currentTimeToUTC).add(1, "day") <= formatedScheduledTime

    if (isDeparture) {
      if (isFlightEligible && isPassenger && saved) {
        options = {
          title: "I would like to share a flight's information with you...",
          message: translate("flightDetails.share.shareMessageEligible", {
            flightNumber: flightNumber,
            departingCode: "SIN",
            destinationCode: destinationCode,
            scheduledDate: `${moment(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${moment(scheduledTime, 'HH:mm').format("HH:mm")}`,
            actualTimestamp: ` (Updated ${moment().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
            flightDetailFDL: shareLinkResponse.data
          })
        }
      } else {
        options = {
          title: "I would like to share a flight's information with you...",
          message: translate("flightDetails.share.shareMessageNonEligible", {
            flightNumber: flightNumber,
            departingCode: "SIN",
            destinationCode: destinationCode,
            scheduledDate: `${moment(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${moment(scheduledTime, 'HH:mm').format("HH:mm")}`,
            actualTimestamp: ` (Updated ${moment().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
            flightDetailFDL: shareLinkResponse.data
          })
        }
      }
    } else {
      options = {
        title: "I would like to share a flight's information with you...",
        message: translate("flightDetails.share.shareMessageNonEligible", {
          flightNumber: flightNumber,
          departingCode: departingCode,
          destinationCode: destinationCode,
          scheduledDate: `${moment(`${scheduledDate}`, "YYYY-MM-DD", "Asia/Singapore").format("MMM DD, YYYY")}, at ${moment(scheduledTime, 'HH:mm').format("HH:mm")}`,
          actualTimestamp: ` (Updated ${moment().tz("Asia/Singapore").format("HH:mm, DD MMM YYYY")})`,
          flightDetailFDL: shareLinkResponse.data
        })
      }
    }
    try {
      const shareResponse = await Share.open(options)
      isSharing.current = false
      loadingShareRef.current = false
      if (shareResponse.success) {
        statusSaveAndShare && finishThreadSaveAndShareModal()
      }
    } catch (error) {
      statusSaveAndShare && finishThreadSaveAndShareModal()
    }
  }

    const onCloseModalSaveAndShare = () => {
    closeModalSaveAndShare()
    setStatusSaveAndShare(null)
  }

  const onShareOnlyPress = () => {
    closeModalSaveAndShare()
    setStatusSaveAndShare("STARTING SHARE")
    onSharePress()
  }

  const finishThreadSaveAndShareModal = () => {
    if (statusSaveAndShare === "SAVE SUCCESS") {
      toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
    }
    setStatusSaveAndShare(null)
  }

  useEffect(() => {
    if(statusSaveAndShare === "SAVE SUCCESS"){
      onSharePress();
    }
  },[statusSaveAndShare])

  useEffect(() => {
    if (statusSaveAndShare && insertFlightPayload?.errorFlag) {
      onShareOnlyPress()
    }
  }, [insertFlightPayload?.errorFlag])

  const closeBottomSheetError = () => {
    dispatch(FlyCreators.flyCheckInOnlineLoadFailed(false))
  }
  const onButtonPressBottomSheetError = () => {
    if (isLoggedIn) {
      handleNavigationFlightDetail(
        flyFlightDetailsPayload?.flightDetailsData?.onlineCheckIn?.link,
        isButtonSaveHidden,
        navigation,
        dispatch,
      )
      return null
    }
    dispatch(FlyCreators.flyPendingCheckInOnline(true))
    navigation.navigate(NavigationConstants.authScreen, { sourceSystem })
  }

  const onButtonPressedConfirmSaveFlight = () => {
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flyItem?.flightNumber,
      flightScheduledDate: flyItem?.scheduledDate,
      flightDirection: direction,
      flightPax: selectedTravelOption === TravelOption.iAmTravelling,
    }
    const savePayload = { ...payload }
    savePayload.item.flightDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
    savePayload.item.scheduledDate = flyFlightDetailsPayload?.flightDetailsData?.scheduledDate
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-detail-save-confirm`)
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-flightNumber",
      `${flyItem?.flightNumber}`,
    )
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-scheduledDate",
      `${flyFlightDetailsPayload?.flightDetailsData?.scheduledDate || flyItem?.scheduledDate
      }`,
    )
    dtAction.reportStringValue("flight-detail-save-confirm-press-direction", `${direction}`)
    dtAction.reportStringValue(
      "flight-detail-save-confirm-press-selectedTravelOption",
      `${selectedTravelOption}`,
    )
    dispatch(MytravelCreators.flyMyTravelInsertFlightRequest(data, savePayload))
    dispatch(FlyCreators.flyPendingSaveFlight(true))
    dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
    dtAction.leaveAction()
  }

  const onSelectCard = () => {
    logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.USER_PROFILE_SELECTOR)
    setShowModalTravelOption(true)
  }

  const renderBottomSheetError = () => {
    return (
      <BottomSheetError
        iconUrl={msg65?.icon}
        icon={<ErrorOutlined width="70" height="70" />}
        visible={isCheckInOnlineLoadFailed}
        title={getValue(msg65?.title, translate("popupError.somethingWrong"))}
        errorMessage={getValue(msg65?.message, translate("popupError.networkErrorMessage"))}
        onClose={closeBottomSheetError}
        buttonText={getValue(msg65?.firstButton, translate("popupError.retry"))}
        onButtonPressed={onButtonPressBottomSheetError}
        testID={`${SCREEN_NAME}__BottomSheetError`}
        accessibilityLabel={`${SCREEN_NAME}__BottomSheetError`}
      />
    )
  }

  const renderFlightDetails = () => {
    return (
      <>
        <FlightDetailsCardV2
          timeStamp={translate("flightLanding.updatedSystemTime") + " " + flyLastUpdatedTimeStamp}
          {...flyFlightDetailsPayload?.flightDetailsData}
          state={handleCondition(
            direction === FlightDirection.departure,
            FlightDetailsCardState.departure,
            FlightDetailsCardState.arrival,
          )}
          type={flyFlightDetailsPayload?.flightDetailsData?.type}
          navigation={navigation}
          isFlightSaved={isButtonSaveHidden && isLoggedIn}
          removeFlightLoading={removeFlightPayload?.loading}
          onButtonPressed={() =>
            onSaveFlight(true, selectedTravelOption === TravelOption.iAmTravelling)
          }
          showGate={flyFlightDetailsPayload?.flightDetailsData?.statusMapping?.show_gate}
          isMSError={flyItem?.isMSError}
          selectedTravelOption={selectedTravelOption}
          selectedTopTravelOption={selectedTopTravelOption}
          route={route}
          flyShowTickerBand={isShowTickerband}
          onPressFlightCardLinks={onPressFlightCardLinks}
          onPressCheckInRow={() => handleMap(TypePressDetailFlightCard.CHECK_IN_ROW)}
          onPressGate={() => handleMap(TypePressDetailFlightCard.GATE)}
          onPressBaggageBelt={() => handleMap(TypePressDetailFlightCard.BAGGAGE_BELT)}
          onPressNearestCarPark={() => handleMap(TypeGetIntoAirport.LINK2)}
          onPressPickupOrDropOff={() =>
            handleMap(
              TypeGetIntoAirport.LINK1,
              flyFlightDetailsPayload?.flightDetailsData?.dropOffDoor ||
              getIntoCityOrAirportPayload?.getIntoCityOrAirport?.text1,
            )
          }
          onPressAccessiblePickupOrDropOff={() =>
            handleMap(
              TypeGetIntoAirport.LINK3,
              getIntoCityOrAirportPayload?.getIntoCityOrAirport?.text3,
            )
          }
          testID={`${SCREEN_NAME}FlightDetailsCard`}
          accessibilityLabel={`${SCREEN_NAME}FlightDetailsCard`}
          profilePayload={profilePayload}
          direction={direction}
          disclaimerText={flightDetailsCardDisclaimerText}
          enableEciDynamicDisplay={enableEciDynamicDisplay}
          saveFlightWhenCheckInOnline={handleSaveFlightWhenCheckInOnline}
          isFlightDetailP1={isFlightDetailP1}
        />
        {isFlightDetailP1 &&
          <InformationHubCard
            direction={direction}
            handleMap={handleMap}
            travelChecklistAEM={travelChecklistAEM}
            flightDetailSectionData={flightDetailSectionData}
            flyFlightDetailsPayload={flyFlightDetailsPayload}
            flyFlightDetailsError={flyFlightDetailsError}
            selectedTopTravelOption={selectedTopTravelOption}
            isFlightSaved={isButtonSaveHidden && isLoggedIn}
            enableEciDynamicDisplay={enableEciDynamicDisplay}
            onPressFlightCardLinks={onPressFlightCardLinks}
            customerEligibility={handleCondition(
              direction === FlightDirection.departure,
              CustomerEligibility.FlyingDep,
              CustomerEligibility.FlyingArr,
            )}
            onPressReloadTravelAEM={handleGetTravelChecklistAEM}
            isTravelChecklistAEMLoading={isTravelChecklistAEMLoading}
            onPressReloadFlightDetails={refreshFlightDetails}
            saveFlightWhenCheckInOnline={handleSaveFlightWhenCheckInOnline}
          />
        }
        {!isFlightDetailP1 &&
          <>
            <GetIntoAirport
              direction={direction}
              flyFlightDetailsPayload={flyFlightDetailsPayload}
              flightUniqueId={
                flyItem?.flightUniqueId || `${flyItem?.flightNumber}_${flyItem?.flightDate}`
              }
              onPressed={(type, item) => handleMap(type, item)}
            />
            <AppscapadeBannerFlightDetail
              isTravelling={selectedTravelOption === TravelOption.iAmTravelling}
            />
            <FlightDetailsCrt flightDirection={direction} navigation={navigation} />
            <FlightPerks
              testID={`${SCREEN_NAME}__FlightPerks`}
              accessibilityLabel={`${SCREEN_NAME}__FlightPerks`}
              flightDetail={flyFlightDetailsPayload?.flightDetailsData}
              flightDirection={direction}
              isSavedFlight={isButtonSaveHidden && isLoggedIn}
            />
            <TimelineSection
              flyItem={flyItem}
              direction={direction}
              flyFlightDetailsPayload={flyFlightDetailsPayload}
              onTrackingTimeline={onTrackingTimeline}
              testID={`${SCREEN_NAME}__TimeLineSection`}
              accessibilityLabel={`${SCREEN_NAME}__TimeLineSection`}
            />
            <FacilitiesAndServices
              testID={`${SCREEN_NAME}__FacilitiesAndServices`}
              accessibilityLabel={`${SCREEN_NAME}__FacilitiesAndServices`}
              customerEligibility={handleCondition(
                direction === FlightDirection.departure,
                CustomerEligibility.FlyingDep,
                CustomerEligibility.FlyingArr,
              )}
              onPressItem={(item) => {
                logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_SERVICES)
                trackAction(AdobeTagName.CAppFlyFlightDetailFacilitiesServices, {
                  [AdobeTagName.CAppFlyFlightDetailFacilitiesServices]: item?.title || "",
                })
              }}
              onPressAll={() => {
                logClickEvent(ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME.FACILITIES_SERVICES_VIEW_ALL)
                trackAction(AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll, {
                  [AdobeTagName.CAppFlyFlightDetailFacilitiesServicesViewAll]: "1",
                })
              }}
            />
          </>
        }
        <View style={sectionSeparator} />
      </>
    )
  }

  if (isNoInternetConnection) {
    return (
      <ErrorOverlayApp reload onReload={onOpenFlightShareSheet} />
    )
  }

  return (
    <Fragment>
      {isShowTickerband && renderTickerBand()}
      {showNoInternetError && (
        <ErrorOverlayNoConnection
          reload
          hideScreenHeader={false}
          headerBackgroundColor="transparent"
          visible={showNoInternetError}
          onBack={() => {
            navigation.goBack()
          }}
          testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          onReload={refreshFlightDetails}
        />
      )}
      <View style={[wrapHeader, getPositionBackButton, { left: 0 }]}>
        <TouchableOpacity
          style={{ zIndex: 10 }}
          onPress={backOnPressed}
          testID={`${SCREEN_NAME}__BackButton`}
          accessibilityLabel={`${SCREEN_NAME}__BackButton`}
        >
          <BackButton />
        </TouchableOpacity>
      </View>
      {shouldShowShareButton && (
        <View style={[wrapHeader, getPositionBackButton, { right: 0 }]}>
          <SingleTapButton
            disabled={loadingFlightMap}
            onPress={handleSharePress}
            testID={`${SCREEN_NAME}__ShareButton`}
            accessibilityLabel={`${SCREEN_NAME}__ShareButton`}
          >
            <ShareIcon />
          </SingleTapButton>
        </View>
      )}
      <ScrollView
        style={styles.scrollViewContainerStyle}
        showsVerticalScrollIndicator={false}
        ref={scrollRef}
        onScroll={handleScroll}
        refreshControl={<RefreshControl refreshing={false} onRefresh={refreshFlightDetails} />}
        scrollEnabled
      >
        <View style={flightHeroImageStyle}>
          <FlightHeroImage
            imageUrl={flyFlightDetailsPayload?.heroImageData?.imageUrl}
            state={direction}
            travelInfo={flyFlightDetailsPayload?.heroImageData?.travelInfo}
            type={flyFlightDetailsPayload?.heroImageData?.type}
            flightNumber={flyItem?.flightNumber}
            airportCode={handleCondition(
              flyItem?.direction === "DEP",
              flyFlightDetailsPayload?.flightDetailsData?.destinationCode,
              "SIN",
            )}
            wrapHeaderStyles={wrapHeaderStyles}
          />
          <View
            style={[
              styles.dropDownSelectCardContainerStyle,
              dropDownSelectCardContainerStyle,
              {
                left: isFlightDetailP1 ? 16 : 24,
                right: isFlightDetailP1 ? 16 : 24,
              },
            ]}
          >
            {shouldShowSQArrivalTerminalInfo && (
              <Text text={inf22?.informativeText} style={styles.informativeTextStyle} />
            )}
            <DropdownSelectCard
              style={[styles.dropDownSelectCardStyle, { marginTop: 20 }]}
              onPressed={onSelectCard}
              title={getTitleDropdownTravelOption}
              type={flyFlightDetailsPayload?.heroImageData?.type}
            />
          </View>
        </View>
        {isShowMaintenance ? (
          <View style={styles.maintenanceErrorContainer}>
            <ErrorCloudComponent
              skipStatusbar
              style={{ backgroundColor: "transparent" }}
              titleStyle={{ marginTop: 16 }}
              buttonStyle={{ width: "auto" }}
              errorData={errorData}
              onPress={fetchTickerbandMaintanance}
            />
          </View>
        ) : (
          renderFlightDetails()
        )}
      </ScrollView>
      {showErrorFeedBackToastMessage()}
      {handleCondition(
        isButtonSaveHidden ||
        (flyFlightDetailsFetching && !insertFlightPayload?.loading),
        null,
        <SaveFlightButton
          loading={insertFlightPayload?.loading}
          onPress={() => onSaveFlight(false, selectedTravelOption === TravelOption.iAmTravelling)}
          disabled={isShowMaintenance}
          isFlightCanSave={
            isShowMaintenance
              ? false
              : checkFlightCanSave(
                flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
                direction,
              )
          }
        />,
      )}
      {fabVisibility && (
        <TouchableOpacity
          style={[styles.fabContainerViewStyle, fabContainerBottom]}
          onPress={() => onPressTouch()}
          testID={`${SCREEN_NAME}__Button_GotoTop`}
          accessibilityLabel={`${SCREEN_NAME}__Button_GotoTop`}
        >
          <ArrowUp style={styles.fabArrowStyle} height={24} width={24} />
          <FabBackground />
        </TouchableOpacity>
      )}
      <TravelOptions
        visible={showModalTravelOption}
        onClosed={onClosedSheet}
        onBackPressed={onClosedSheet}
        selectedOption={selectedTopTravelOption}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      {showToastForRemoveFlight()}
      <SavedFlightTravelOptions
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={isModalVisibleTravelOption}
        onClosed={onClosedTravelOptionSheet}
        loadingSaveFlight={loadingSaveFlight}
        onBackPressed={onClosedTravelOptionSheet}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={(e) => {
          isSharing.current = false
          savedFlightOnPress(e)
        }}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      <ModalSaveAndShare
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={isModalSaveAndShare}
        onClosed={onCloseModalSaveAndShare}
        loadingSaveFlight={loadingSaveFlight}
        onShareOnlyPress={onShareOnlyPress}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={(e) => {
          isSharing.current = false
          savedFlightOnPress(e)
        }}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      <ConfirmSaveFlight
        imageUrl={handleImageUrl(msg47?.icon)}
        visible={isModalVisible && isFocused}
        title={translate("flightDetails.newPopupConfirmSaveFlight.title")}
        messageText={simpleCondition({
          condition: direction === FlightDirection.arrival,
          ifValue: translate("flightDetails.newPopupConfirmSaveFlight.arrivalMessage"),
          elseValue: translate("flightDetails.newPopupConfirmSaveFlight.departureMessage"),
        })}
        onClose={() => {
          onCloseConfirmPopUpSavedFlight()
        }}
        onButtonPressed={() => {
          setIsGoToListingFlight(true)
          onCloseConfirmPopUpSavedFlight()
        }}
        onModalHide={() => {
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          const timeStamp = new Date().getTime()
          setLastSavedFlightTime(timeStamp)
          if (isGoToListingFlight) {
            setShowCalendarModal(true)
            setIsGoToListingFlight(false)
          } else {
            toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          }
        }}
        textButtonConfirm={translate(
          "flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton",
        )}
        textButtonCancel={translate("flightDetails.newPopupConfirmSaveFlight.cancelButton")}
        isShowButtonConnection={direction === FlightDirection.arrival}
        onButtonConnectionPressed={handleConnectingFlightOnPress}
        textButtonConnection={translate(
          "flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton",
        )}
        disableCloseButton={true}
        openPendingModal
      />
      <AddReturnCalendar
        isVisible={showCalendarModal}
        filterDate={filterDateAddReturnCalendar}
        initialMinDate={filterDateAddReturnCalendar}
        onClosedCalendarModal={() => {
          toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          setShowCalendarModal(false)
          const connectingFlightPayloadToClear = {
            isConnecting: false,
            flightConnecting: null,
          }
          dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear))
        }}
        onDateSelected={(dateString) => {
          const country = connectingFlightPayload.isConnecting
            ? "Singapore"
            : flyFlightDetailsPayload?.flightDetailsData?.country || ""
          const date = moment(dateString).format("YYYY-MM-DD")
          setShowCalendarModal(false)
          dispatch(FlyCreators.setFlightSearchDate(date))
          navigation.navigate("flightResultLandingScreen", {
            screen: "Tabs",
            selectedDate: date,
            country: country,
            params: {
              screen: direction === FlightDirection.departure ? "arrivalResultScreen" : "departureResultScreen",
              selectedDate: date,
              country: country,
            }
          })
        }}
        testID={`${SCREEN_NAME}__AddReturnCalendar`}
        accessibilityLabel={`${SCREEN_NAME}__AddReturnCalendar`}
      />
      {showFlightAddedFeedBackToastMessage()}
      <LoadingOverlay visible={loadingFlightMap || flyFlightDetailsFetchingFirst} />
      <AlertApp ref={alertApp} />
      <ConfirmSaveFlight
        imageUrl={iconUrl}
        visible={isShowModalConfirmSaveFly}
        messageText={translate("flightDetails.popupConfirmSaveFlight.message")}
        onClose={() => {
          dispatch(FlyCreators.flyShowModalConfirmSaveFly(false))
        }}
        onButtonPressed={onButtonPressedConfirmSaveFlight}
      />
      <ConfirmSaveFlight
        imageUrl={iconUrl}
        visible={showSaveFlightWhenOnlineCheckIn}
        messageText={translate("flightDetails.popupConfirmSaveFlight.messageOnlineCheckIn")}
        onClose={() => setShowSaveFlightWhenOnlineCheckIn(false)}
        onButtonPressed={onButtonPressedConfirmSaveFlight}
        textButtonCancel={translate("flightDetails.popupConfirmSaveFlight.onlineCheckInOnly")}
        onSecondaryBtnPressed={handleCheckInOnline}
      />
      {renderBottomSheetError()}
      <BottomSheetUnableLoadLocation ref={unableToLoadLocationRef} />
      <BottomSheetMapUnavailable ref={mapUnavailable} />
    </Fragment>
  )
}

export { FlightDetails }
