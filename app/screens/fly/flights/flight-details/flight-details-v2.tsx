import {
  TouchableOpacity,
  View,
  Platform,
  RefreshControl,
  Dimensions,
  InteractionManager,
} from "react-native"
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import Animated, {
  Extrapolate,
  interpolate,
  runOnJS,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated"
import { FlightInfo } from "./flight-info/flight-info"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import OnboardingOverlay from "./onboarding-overlay"
import { BackgroundImage } from "./background-image/background-image"
import { LoadingOverlay } from "app/components/loading-modal"
import { BackButton, ErrorOutlined, Share as ShareIcon } from "assets/icons"
import { SingleTapButton } from "app/elements/single-tap-button"
import { useFlightDetailV2Styles } from "./flight-details-v2.styles"
import TickerBand from "app/components/ticker-band/ticker-band"
import { useFlightDetailV2 } from "./useFlightDetailV2"
import { translate } from "app/i18n"
import { FeedBackToast, FeedBackToastType } from "app/components/feedback-toast"
import { ErrorOverlayApp } from "app/components/error-overlay/error-overlay-app"
import { CustomerEligibility, FlightDirection } from "../flight-props"
import { getNavBarHeight, handleCondition, simpleCondition } from "app/utils"
import SaveFlightButton from "./save-flight-button/save-flight-button"
import { InformationHubCard } from "./information-hub-card/information-hub-card"
import { BottomSheetUnableLoadLocation } from "app/components/bottom-sheet-unable-load-location/bottom-sheet-unable-load-location"
import { BottomSheetMapUnavailable } from "app/components/bottom-sheet-map-unavailable/bottom-sheet-map-unavailable"
import FlightInformationHub from "./flight-information-hub/flight-information-hub"
import TravelOptions from "./travel-options/travel-options"
import { AdobeTagName, trackAction } from "app/services/adobe"
import FlightBanner from "./flight-banner/flight-banner"
import { FlyProfileEnum, IBannerAEMResponse, TravelOption } from "./flight-detail.props"
import { AlertApp } from "app/components/alert-app/alert-app"
import ConfirmSaveFlight from "app/components/flight-details-card/confirm-popup-save-flight"
import { handleImageUrl } from "app/utils/media-helper"
import AddReturnCalendar from "../add-return-calendar"
import moment from "moment"
import { Text } from "app/elements/text"
import SavedFlightTravelOptionsV2 from "../save-flight-travel-option/save-flight-travel-option-v2"
import ModalSaveAndShare from "./modal-saveAndShare";
import { useSelector } from "react-redux"
import { FlySelectors } from "app/redux/flyRedux"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { ErrorCloudComponent } from "app/components/error-cloud/error-cloud"
import _get from "lodash/get"
import { useOnlineCheckIn } from "./flight-details.hooks"
import FlightJourney from "./flight-journey/flight-journey"
import { ProfileSelectors } from "app/redux/profileRedux"
import { ENUM_STORAGE_MMKV, ENUM_STORAGE_TYPE, getMMKVdata } from "app/utils/storage/mmkv-storage"
import SearchDepartureFlight from "app/components/search-departure-flight"

const SCREEN_NAME = "FlightDetails"
const getValue = (value, defaultValue) => value || defaultValue
const PADDING_BOTTOM_SAVE_FLIGHT = 36

const FlightDetailsV2 = ({ navigation, route }) => {
  const { top, bottom } = useSafeAreaInsets()

  const { styles, SCREEN_HEIGHT, LAYOUT_1_HEIGHT, LAYOUT_3_TOP_OFFSET, ANIMATED_TIME_DURATION } =
    useFlightDetailV2Styles()
  const INITIAL_TOP_LAYOUT_2_VALUE = LAYOUT_1_HEIGHT
  const INITIAL_TOP_LAYOUT_3_VALUE = INITIAL_TOP_LAYOUT_2_VALUE + LAYOUT_3_TOP_OFFSET
  const coverHeight = SCREEN_HEIGHT * 0.4

  const toastForSavedFlight = useRef<FeedBackToast>(undefined)
  const alertApp = useRef(null)
  const toastForRemoveFlight = useRef(null)

  const isShowModalConfirmSaveFly = useSelector(FlySelectors.flyShowModalConfirmSaveFly)
  const isCheckInOnlineLoadFailed = useSelector(FlySelectors.flyCheckInOnlineLoadFailed)

  const priorActionRef = useRef("null")
  const scrollY = useSharedValue(0)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const [isFlightJourneyFullScreen, setIsFlightJourneyFullScreen] = useState(false)
  const [isStartShowFlightJourney, setIsStartShowFlightJourney] = useState(false)
  const animatedTopLayout2Position = useSharedValue(INITIAL_TOP_LAYOUT_2_VALUE)
  const animatedTopLayout3Position = useSharedValue(INITIAL_TOP_LAYOUT_3_VALUE)
  const borderTopRightRadius = useSharedValue(30)
  const borderTopLeftRadius = useSharedValue(30)
  const heightTickerBand = useSharedValue(0)
  const [heightLayout1, setHeightLayout1] = useState(0)
  const [heightLayout3, setHeightLayout3] = useState(0)
  const [heightScrollView, setHeightScrollView] = useState(SCREEN_HEIGHT)

  const [showModalTravelOption, setShowModalTravelOption] = useState(false)
  const [bannerAEMResponse, setBannerAEMResponse] = useState<IBannerAEMResponse>(null)
  const [isBannerAEMLoading, setIsBannerAEMLoading] = useState<boolean>(false)
  const [isFinishedJourneyOnboarding, setIsFinishedJourneyOnboarding] = useState(false)
  const [showSaveFlightWhenOnlineCheckIn, setShowSaveFlightWhenOnlineCheckIn] =
    useState<boolean>(false)

  const { payload, direction, isFromScanBoardingPass, referrer, isFromUpcomingEvent } = route.params
  const { item: flyItem } = payload

  const {
    trackAAWhenBack,
    shouldShowShareButton,
    flyShowTickerBand,
    tickerbandMaintananceHook,
    flyFlightDetailsPayload,
    flyLastUpdatedTimeStamp,
    shouldShowSQArrivalTerminalInfo,
    inf22,
    handleSharePress,
    loadingSaveFlight,
    insertFlightPayload,
    flightDetailV1Hook,
    isNoInternetConnection,
    onOpenFlightShareSheet,
    loadingFlightMap,
    handleMap,
    isLoadingDetailFlight,
    intoCityOrAirportPayload,
    getBackgroundAirportAEM,
    isSaved,
    isButtonSaveHidden,
    checkFlightCanSave,
    isLoggedIn,
    travelChecklistAEM,
    isTravelChecklistAEMLoading,
    handleGetTravelChecklistAEM,
    unableToLoadLocationRef,
    mapUnavailable,
    refreshFlightDetails,
    toastForRefresh,
    flyFlightDetailsError,
    enableEciDynamicDisplay,
    flightDetailSectionData,
    onPressFlightCardLinks,
    setSelectedTravelOption,
    selectedTravelOption,
    onSaveFlight,
    savedFlightTravelOptionsOnModalHide,
    modalTravelOptionVisible,
    onClosedTravelOptionSheet,
    isModalVisible,
    isFocused,
    onCloseConfirmPopUpSavedFlight,
    msg47,
    onPressSavedFlightOnPress,
    onButtonPressedConfirmConnectingFlight,
    onModalHideConfirmConnectingFlight,
    handleConnectingFlightOnPress,
    showCalendarModal,
    onClosedCalendarModal,
    onDateSelectedAddReturnCalendar,
    removeFlightPayload,
    isMSError,
    getMyTripData,
    onRetryGetMyTripData,
    isLoadingGetMyTripData,
    isErrorGetMyTrip,
    iconUrl,
    onButtonPressedConfirmSaveFlight,
    onCloseConfirmSaveFlight,
    closeBottomSheetError,
    msg65,
    onButtonPressBottomSheetError,
    showNoInternetError,
    enableFlightJourney,
    pendingShowFlightJourney,
    setPendingShowFlightJourney,
    showToastForSaveFlightSuccess,  
    isModalSaveAndShare,
    onCloseModalSaveAndShare,
    onShareOnlyPress,
    isFlyLandingEnabled
  } = useFlightDetailV2({
    flyItem,
    direction,
    isFromScanBoardingPass,
    referrer,
    toastForSavedFlight,
    toastForRemoveFlight,
    isFromUpcomingEvent,
    priorActionRef,
  })

  const {
    onPressCTA,
    onCloseTickerBand,
    tickerBand,
    tickerBandDescription,
    tickerBandButtonText,
    isShowTickerband,
    fetchTickerbandMaintanance,
    errorData,
    isShowMaintenance,
  } = tickerbandMaintananceHook

  const { handleCheckInOnline, handleSaveFlightWhenCheckInOnline } = useOnlineCheckIn({
    direction,
    flyItem,
    navigation,
    refreshFlightDetails,
    setShowSaveFlightWhenOnlineCheckIn,
  })

  // Layout 2 Style
  const layout2Style = useAnimatedStyle(() => {
    return {
      top: animatedTopLayout2Position.value,
      borderTopRightRadius: borderTopRightRadius.value,
      borderTopLeftRadius: borderTopLeftRadius.value,
    }
  })

  // Layout 3 Style
  const layout3Style = useAnimatedStyle(() => {
    return {
      top: animatedTopLayout3Position.value,
    }
  })

  const onPressLayout2 = () => {
    // Check if flight is saved before allowing fullscreen view
    // If flight is not saved, prompt user to save it first
    if (!isSaved) {
      priorActionRef.current = translate("flightDetailV2.flightJourney.header.title")
      setPendingShowFlightJourney("pending")
      onSaveFlight(false, selectedTravelOption === TravelOption.iAmTravelling, alertApp, payload)
      return
    }

    handleShowFlightJourney()
  }

  const initTopLayout3Position = useMemo(() => {
    return heightLayout1 + LAYOUT_3_TOP_OFFSET
  }, [heightLayout1])
  const initTopLayout2Position = useMemo(() => {
    return heightLayout1
  }, [heightLayout1])

  const handleShowFlightJourney = useCallback(() => {
    InteractionManager.runAfterInteractions(() => {
      // set the top position of layout 2 is top, and the top position of layout 3 is bottom
      const newTopLayout3Position = Dimensions.get("window").height - bottom + scrollY.value // Layout2 cover Layout3 (fullscreen)
      const newTopLayout2Position = 0 + scrollY.value
      animatedTopLayout3Position.value = withTiming(
        newTopLayout3Position,
        {
          duration: ANIMATED_TIME_DURATION,
        },
        () => {
          runOnJS(setIsFlightJourneyFullScreen)(true)
        },
      )
      animatedTopLayout2Position.value = withTiming(newTopLayout2Position, {
        duration: ANIMATED_TIME_DURATION,
      })
      borderTopRightRadius.value = withTiming(0, {
        duration: ANIMATED_TIME_DURATION,
      })
      borderTopLeftRadius.value = withTiming(0, {
        duration: ANIMATED_TIME_DURATION,
      })
      setIsStartShowFlightJourney(true)
    })
  }, [scrollY.value])

  const checkReloadFlyItem = useMemo(() => {
    if (!flyItem?.flightNumber) {
      return ""
    }
    return `${flyItem?.flightNumber}-${flyItem?.scheduledDate}-${direction}`
  }, [flyItem?.flightNumber, flyItem?.scheduledDate, direction])

  const handleHideFlightJourney = () => {
    InteractionManager.runAfterInteractions(() => {
      if (initTopLayout3Position && initTopLayout2Position) {
        // set the top position of layout 2 and layout 3 to the initial values
        const newTopLayout3Position = initTopLayout3Position
        const newTopLayout2Position = initTopLayout2Position
        animatedTopLayout3Position.value = withTiming(
          newTopLayout3Position,
          {
            duration: ANIMATED_TIME_DURATION,
          },
          () => {
            runOnJS(setIsFlightJourneyFullScreen)(false)
          },
        )
        animatedTopLayout2Position.value = withTiming(newTopLayout2Position, {
          duration: ANIMATED_TIME_DURATION,
        })
        borderTopRightRadius.value = withTiming(30, {
          duration: ANIMATED_TIME_DURATION,
        })
        borderTopLeftRadius.value = withTiming(30, {
          duration: ANIMATED_TIME_DURATION,
        })
        setIsStartShowFlightJourney(false)
      }
    })
  }

  useEffect(() => {
    if (
      pendingShowFlightJourney === "finished" &&
      isSaved &&
      !isLoadingDetailFlight &&
      !isLoadingGetMyTripData
    ) {
      // First clear the pending state
      setPendingShowFlightJourney(null)
      handleShowFlightJourney()
    }
  }, [
    pendingShowFlightJourney,
    isSaved,
    isLoadingDetailFlight,
    isLoadingGetMyTripData,
    handleShowFlightJourney,
  ])

  useEffect(() => {
    // handle hide Flight Journey in case open flight detail from notification
    if (checkReloadFlyItem) {
      // set the top position of layout 2 and layout 3 to the initial values
      handleHideFlightJourney()
    }
  }, [checkReloadFlyItem])

  const backOnPressed = () => {
    trackAAWhenBack()
    navigation.goBack()
  }

  const onClosedSheet = () => {
    setShowModalTravelOption(false)
  }

  const travelOptionTapped = (option) => {
    const flyProfile =
      option === TravelOption.iAmTravelling ? FlyProfileEnum.flying : FlyProfileEnum.nonFlying
    const flightNumber = flyItem?.flightNumber
    const flightDate = flyItem?.flightDate
    trackAction(AdobeTagName.CAppFlyFlightDetailFlyProfile, {
      [AdobeTagName.CAppFlyFlightDetailFlyProfile]: `${flyProfile}|${flightNumber}|${flightDate}`,
    })
    setShowModalTravelOption(false)
    setSelectedTravelOption(option)
    // setSelectedTopTravelOption(option) //do late
  }

  const handleGetBannerAEM = async () => {
    setIsBannerAEMLoading(true)
    const res = await flightDetailV1Hook.getBannerAEM()
    setBannerAEMResponse(res)
    setIsBannerAEMLoading(false)
  }

  useEffect(() => {
    handleGetBannerAEM()
  }, [])

  const handleOnLayout = (event) => {
    const { height } = event.nativeEvent.layout
    heightTickerBand.value = height
  }

  const handleLayout1 = (event) => {
    setHeightLayout1(event.nativeEvent.layout.height)
    // Only update position when not in fullscreen mode
    if (!isStartShowFlightJourney) {
      if (enableFlightJourney) {
        animatedTopLayout2Position.value = event.nativeEvent.layout.height
        animatedTopLayout3Position.value = event.nativeEvent.layout.height + LAYOUT_3_TOP_OFFSET
      } else {
        animatedTopLayout3Position.value = event.nativeEvent.layout.height
      }
    }
  }

  const handleLayout3 = (event) => {
    setHeightLayout3(event.nativeEvent.layout.height)
  }

  const headerPositionStyle = useAnimatedStyle(() => {
    return {
      top: isShowTickerband ? heightTickerBand.value + 25 : 58,
    }
  })

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y
    },
  })

  const threshold = heightScrollView - SCREEN_HEIGHT

  const footerAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [threshold + heightTickerBand.value, threshold + heightTickerBand.value + coverHeight],
      [coverHeight, 0],
      Extrapolate.CLAMP,
    )
    return { transform: [{ translateY }] }
  })

  const onOpenSaveFlightModal = (priorActionLabel) => {
    priorActionRef.current = priorActionLabel || ""
    onSaveFlight(false, selectedTravelOption === TravelOption.iAmTravelling, alertApp, payload)
  }

  const onUnsaveFlight = () => {
    onSaveFlight(true, selectedTravelOption === TravelOption.iAmTravelling, alertApp, payload)

    const savedFlightPriorActions = getMMKVdata(
      ENUM_STORAGE_MMKV.SAVED_FLIGHT_PRIOR_ACTIONS,
      ENUM_STORAGE_TYPE.string,
    ) as string

    try {
      let priorActionText = "null"
      if (savedFlightPriorActions) {
        const priorAction = JSON.parse(savedFlightPriorActions).find(
          (action) => action?.uid === profilePayload?.id && action?.flightNumber === flyItem?.flightNumber && action?.direction === flyItem?.direction,
        )
        if (priorAction) {
          priorActionText = priorAction?.priorAction || "null"
        }
      }

      const directionText = flyItem?.direction === FlightDirection.departure ? "Departure" : "Arrival"
      trackAction(AdobeTagName.CAppFlightPersonalizationSaveFlight, {
        [AdobeTagName.CAppFlightPersonalizationSaveFlight]: `${flyItem?.flightNumber} | ${directionText} | ${translate(selectedTravelOption)} | ${priorActionText} | Unsave`,
      })
    } catch (error) {
      console.error("Error parsing saved flight prior actions:", error)
    }
  }

  useEffect(() => {
    setHeightScrollView(
      heightLayout1 + heightLayout3 + PADDING_BOTTOM_SAVE_FLIGHT - getNavBarHeight() + 24,
    )
  }, [heightLayout3, heightLayout1])

  const filterDateAddReturnCalendar = useMemo(() => {
    if (isLoadingDetailFlight) {
      return moment().format("YYYY-MM-DD")
    } else {
      return (
        moment(flyFlightDetailsPayload?.flightDetailsData?.displayTimestamp).format("YYYY-MM-DD") ||
        moment().format("YYYY-MM-DD")
      )
    }
  }, [flyFlightDetailsPayload?.flightDetailsData?.displayTimestamp, isLoadingDetailFlight])

  const isHideSaveFlight = useMemo(() => {
    return (
      !isFinishedJourneyOnboarding ||
      isButtonSaveHidden ||
      (isLoadingDetailFlight && !insertFlightPayload?.loading) ||
      isStartShowFlightJourney
    )
  }, [
    isButtonSaveHidden,
    isLoadingDetailFlight,
    insertFlightPayload?.loading,
    isStartShowFlightJourney,
    isFinishedJourneyOnboarding,
  ])

  const isFlightSaved = useMemo(() => {
    return isButtonSaveHidden && isSaved && isLoggedIn
  }, [isButtonSaveHidden, isSaved, isLoggedIn])

  const isShowUnSaveFlight = useMemo(() => {
    if (isLoadingDetailFlight) {
      return false
    }
    return isFlightSaved && !removeFlightPayload?.loading && !isMSError
  }, [isFlightSaved, removeFlightPayload?.loading, isMSError, isLoadingDetailFlight])
  
  const onCloseTickerBandView = () => {
    onCloseTickerBand()
    heightTickerBand.value = 0
  }

  const renderTickerBand = () => (
    <TickerBand
      urgent={false}
      title={flyShowTickerBand && tickerBand}
      description={flyShowTickerBand && tickerBandDescription}
      buttonText={flyShowTickerBand && tickerBandButtonText}
      onCTAPress={onPressCTA}
      onClose={onCloseTickerBandView}
      isLanding={true}
      onLayout={handleOnLayout}
    />
  )

  const renderHeader = () => {
    if (isStartShowFlightJourney) {
      return null
    }
    return (
      <>
        <Animated.View style={[styles.wrapHeader, headerPositionStyle, { left: 0 }]}>
          <TouchableOpacity
            style={{ zIndex: 10 }}
            onPress={backOnPressed}
            testID={`${SCREEN_NAME}__BackButton`}
            accessibilityLabel={`${SCREEN_NAME}__BackButton`}
          >
            <BackButton />
          </TouchableOpacity>
        </Animated.View>
        {shouldShowShareButton && (
          <Animated.View style={[styles.wrapHeader, headerPositionStyle, { right: 0 }]}>
            <SingleTapButton
              disabled={loadingFlightMap || !flyFlightDetailsPayload?.flightDetailsData?.flightNumber}
              onPress={handleSharePress}
              testID={`${SCREEN_NAME}__ShareButton`}
              accessibilityLabel={`${SCREEN_NAME}__ShareButton`}
            >
              <ShareIcon />
            </SingleTapButton>
          </Animated.View>
        )}
      </>
    )
  }

  const showErrorFeedBackToastMessage = () => {
    return (
      <FeedBackToast
        ref={toastForRefresh}
        style={styles.feedBackToastStyle}
        textButtonStyle={styles.toastButtonStyle}
        position={"bottom"}
        textStyle={styles.toastTextStyle}
        type={FeedBackToastType.fullWidthFeedBack}
        text={translate("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp}
      />
    )
  }

  const showFlightAddedFeedBackToastMessage = () => {
    return (
      <FeedBackToast
        ref={toastForSavedFlight}
        style={styles.toastStyleAddedFlight}
        textButtonStyle={styles.toastButtonStyleAdded}
        position={"bottom"}
        textStyle={styles.toastTextStyleAdded}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved")}
      />
    )
  }

  const showToastForRemoveFlight = () => {
    return (
      <FeedBackToast
        ref={toastForRemoveFlight}
        style={styles.feedBackToastStyle}
        textButtonStyle={styles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 100 }}
        textStyle={styles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flyLanding.removeFlightNew")}
      />
    )
  }

  const renderBottomSheetError = () => {
    return (
      <BottomSheetError
        iconUrl={msg65?.icon}
        icon={<ErrorOutlined width="70" height="70" />}
        visible={isCheckInOnlineLoadFailed}
        title={getValue(msg65?.title, translate("popupError.somethingWrong"))}
        errorMessage={getValue(msg65?.message, translate("popupError.networkErrorMessage"))}
        onClose={closeBottomSheetError}
        buttonText={getValue(msg65?.firstButton, translate("popupError.retry"))}
        onButtonPressed={onButtonPressBottomSheetError}
        testID={`${SCREEN_NAME}__BottomSheetError`}
        accessibilityLabel={`${SCREEN_NAME}__BottomSheetError`}
      />
    )
  }

  if (isNoInternetConnection) {
    return (
      <ErrorOverlayApp
        reload
        onReload={onOpenFlightShareSheet}
      />
    )
  }

  if (isShowMaintenance) {
    return (
      <View style={styles.maintenanceErrorContainer}>
        <ErrorCloudComponent
          skipStatusbar
          style={{ backgroundColor: "transparent" }}
          titleStyle={{ marginTop: 16 }}
          buttonStyle={{ width: "auto" }}
          errorData={errorData}
          onPress={fetchTickerbandMaintanance}
        />
      </View>
    )
  }

  return (
    <>
      <LoadingOverlay visible={loadingFlightMap || removeFlightPayload?.loading} />
      {isShowTickerband && renderTickerBand()}
      {showNoInternetError && (
        <ErrorOverlayNoConnection
          reload
          hideScreenHeader={false}
          headerBackgroundColor="transparent"
          visible={showNoInternetError}
          onBack={() => {
            navigation.goBack()
          }}
          testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          onReload={refreshFlightDetails}
          noInternetOverlayStyle={styles.errorOverLay}
        />
      )}
      <BackgroundImage
        direction={direction}
        getBackgroundAirportAEM={getBackgroundAirportAEM}
        isSaved={isSaved}
        airport={
          direction === FlightDirection.departure
            ? flyFlightDetailsPayload?.flightDetailsData?.destinationCode
            : flyFlightDetailsPayload?.flightDetailsData?.departingCode
        }
      />
      <Animated.ScrollView
        style={styles.containerScrollView}
        showsVerticalScrollIndicator={false}
        onScroll={scrollHandler}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => (!isStartShowFlightJourney ? refreshFlightDetails(true) : undefined)}
          />
        }
        scrollEnabled={!isStartShowFlightJourney}
        bounces={!isStartShowFlightJourney}
      >
        <View
          style={[
            styles.container,
            {
              minHeight: heightScrollView,
            },
          ]}
        >
          {/* Layout 1 */}
          <View style={styles.layout1} onLayout={(e) => handleLayout1(e)}>
            <FlightInfo
              directionFromParams={direction}
              flyFlightDetailsPayload={flyFlightDetailsPayload}
              flyLastUpdatedTimeStamp={flyLastUpdatedTimeStamp}
              shouldShowSQArrivalTerminalInfo={shouldShowSQArrivalTerminalInfo}
              inf22={inf22}
              handleMap={handleMap}
              isLoadingDetailFlight={isLoadingDetailFlight}
              intoCityOrAirportPayload={intoCityOrAirportPayload}
              marginTop={isShowTickerband ? -top + 25 : 58 - top} // TODO: handler when show TickerBand later
              isSaved={isSaved}
              onSaveFlight={onOpenSaveFlightModal}
              getMyTripData={getMyTripData}
              isLoadingGetMyTripData={isLoadingGetMyTripData}
              isErrorGetMyTrip={isErrorGetMyTrip}
              onRetryGetMyTripData={() =>
                onRetryGetMyTripData({
                  isHideLoadingDetail: false,
                })
              }
            />
            <FlightBanner
              bannerAEMResponse={bannerAEMResponse}
              isBannerAEMLoading={isBannerAEMLoading}
              onPressReloadBanner={handleGetBannerAEM}
            />
          </View>
          {/* Layout 2 */}
          {enableFlightJourney && (
            <Animated.View style={[styles.layout2, layout2Style]}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={onPressLayout2}
                disabled={
                  isStartShowFlightJourney || isLoadingGetMyTripData || isLoadingDetailFlight
                }
              >
                <FlightJourney
                  isFlightJourneyFullScreen={isFlightJourneyFullScreen}
                  isStartShowFlightJourney={isStartShowFlightJourney}
                  handleHide={handleHideFlightJourney}
                  flightDetails={flyFlightDetailsPayload?.flightDetailsData}
                  isLoadingData={isLoadingDetailFlight || isLoadingGetMyTripData}
                  getMyTripData={getMyTripData}
                />
              </TouchableOpacity>
            </Animated.View>
          )}
          {/* Layout 3 */}
          <Animated.View style={[styles.layout3, layout3Style]} onLayout={(e) => handleLayout3(e)}>
            <FlightInformationHub
              setShowModalTravelOption={setShowModalTravelOption}
              selectedTravelOption={selectedTravelOption}
              loadingFlightDetail={isLoadingDetailFlight}
              isFlightSaved={isSaved}
              isFlightJourneyFullScreen={isStartShowFlightJourney}
              handleHideFlightJourney={handleHideFlightJourney}
            >
              <InformationHubCard
                direction={direction}
                handleMap={handleMap}
                travelChecklistAEM={travelChecklistAEM}
                flightDetailSectionData={flightDetailSectionData}
                flyFlightDetailsPayload={flyFlightDetailsPayload}
                flyFlightDetailsError={flyFlightDetailsError}
                selectedTopTravelOption={selectedTravelOption}
                isFlightSaved={isButtonSaveHidden && isLoggedIn}
                enableEciDynamicDisplay={enableEciDynamicDisplay}
                onPressFlightCardLinks={onPressFlightCardLinks}
                customerEligibility={handleCondition(
                  direction === FlightDirection.departure,
                  CustomerEligibility.FlyingDep,
                  CustomerEligibility.FlyingArr,
                )}
                onPressReloadTravelAEM={handleGetTravelChecklistAEM}
                isTravelChecklistAEMLoading={isTravelChecklistAEMLoading}
                onPressReloadFlightDetails={refreshFlightDetails}
                disableSaveFlight={
                  tickerbandMaintananceHook.isShowMaintenance
                    ? true
                    : !checkFlightCanSave(
                        flyFlightDetailsPayload?.flightDetailsData?.flightStatus ||
                          flyItem?.flightStatus,
                        direction,
                      )
                }
                onSaveFlight={onOpenSaveFlightModal}
                saveFlightWhenCheckInOnline={handleSaveFlightWhenCheckInOnline}
              />
              {handleCondition(
                isShowUnSaveFlight,
                <View style={styles.containerUnsaveFlight}>
                  <TouchableOpacity
                    onPress={onUnsaveFlight}
                    style={styles.containerUnsaveFlightBtn}
                  >
                    <Text
                      tx="flightDetailV2.unsaveFlight"
                      preset="caption1Bold"
                      style={styles.unsaveFlightLabel}
                    />
                  </TouchableOpacity>
                </View>,
                null,
              )}
            </FlightInformationHub>
          </Animated.View>
        </View>
      </Animated.ScrollView>
      {/* Animated footer view cover background when overscroll */}
      {Platform.OS === "ios" && !!flyFlightDetailsPayload?.flightDetailsData && (
        <Animated.View
          style={[
            styles.coverFooterCointainer,
            {
              height: coverHeight,
            },
            footerAnimatedStyle,
          ]}
          pointerEvents="none"
        />
      )}
      {showErrorFeedBackToastMessage()}
      {handleCondition(
        !modalTravelOptionVisible && isHideSaveFlight,
        null,
        <SaveFlightButton
          loading={insertFlightPayload?.loading}
          onPress={() => onOpenSaveFlightModal("null")}
          disabled={tickerbandMaintananceHook.isShowMaintenance}
          isFlightCanSave={
            tickerbandMaintananceHook.isShowMaintenance
              ? false
              : checkFlightCanSave(
                  flyFlightDetailsPayload?.flightDetailsData?.flightStatus || flyItem?.flightStatus,
                  direction,
                )
          }
        />,
      )}
      <AddReturnCalendar
        isVisible={showCalendarModal}
        filterDate={filterDateAddReturnCalendar}
        initialMinDate={filterDateAddReturnCalendar}
        onClosedCalendarModal={onClosedCalendarModal}
        onDateSelected={(dateString) => onDateSelectedAddReturnCalendar(dateString)}
        testID={`${SCREEN_NAME}__AddReturnCalendar`}
        accessibilityLabel={`${SCREEN_NAME}__AddReturnCalendar`}
      />
      {showFlightAddedFeedBackToastMessage()}
      <AlertApp ref={alertApp} />
      {renderHeader()}
      <TravelOptions
        visible={showModalTravelOption}
        onClosed={onClosedSheet}
        onBackPressed={onClosedSheet}
        selectedOption={selectedTravelOption}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      {showToastForRemoveFlight()}
      <SavedFlightTravelOptionsV2
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={modalTravelOptionVisible}
        onClosed={onClosedTravelOptionSheet}
        loadingSaveFlight={loadingSaveFlight}
        onBackPressed={onClosedTravelOptionSheet}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={(e) => onPressSavedFlightOnPress(e)}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      {isFlyLandingEnabled && (
        <SearchDepartureFlight
          handleOnClose={showToastForSaveFlightSuccess}
          displayTimestamp={flyFlightDetailsPayload?.flightDetailsData?.displayTimestamp}
        />
      )}
      <ModalSaveAndShare
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={isModalSaveAndShare}
        onClosed={onCloseModalSaveAndShare}
        loadingSaveFlight={loadingSaveFlight}
        onShareOnlyPress={onShareOnlyPress}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={(e) => onPressSavedFlightOnPress(e)}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={direction}
      />
      <ConfirmSaveFlight
        imageUrl={handleImageUrl(msg47?.icon)}
        visible={isModalVisible && isFocused}
        title={translate("flightDetails.newPopupConfirmSaveFlight.title")}
        messageText={simpleCondition({
          condition: direction === FlightDirection.arrival,
          ifValue: translate("flightDetails.newPopupConfirmSaveFlight.arrivalMessage"),
          elseValue: translate("flightDetails.newPopupConfirmSaveFlight.departureMessage"),
        })}
        onClose={() => {
          onCloseConfirmPopUpSavedFlight()
        }}
        onButtonPressed={onButtonPressedConfirmConnectingFlight}
        onModalHide={onModalHideConfirmConnectingFlight}
        textButtonConfirm={translate(
          "flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton",
        )}
        textButtonCancel={translate("flightDetails.newPopupConfirmSaveFlight.cancelButton")}
        isShowButtonConnection={direction === FlightDirection.arrival}
        onButtonConnectionPressed={handleConnectingFlightOnPress}
        textButtonConnection={translate(
          "flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton",
        )}
        disableCloseButton={true}
      />
      <ConfirmSaveFlight
        imageUrl={iconUrl}
        visible={isShowModalConfirmSaveFly}
        messageText={translate("flightDetails.popupConfirmSaveFlight.message")}
        onClose={onCloseConfirmSaveFlight}
        onButtonPressed={() => onButtonPressedConfirmSaveFlight(payload)}
      />
      <ConfirmSaveFlight
        imageUrl={iconUrl}
        visible={showSaveFlightWhenOnlineCheckIn}
        messageText={translate("flightDetails.popupConfirmSaveFlight.messageOnlineCheckIn")}
        onClose={() => setShowSaveFlightWhenOnlineCheckIn(false)}
        onButtonPressed={() => onButtonPressedConfirmSaveFlight(payload)}
        textButtonCancel={translate("flightDetails.popupConfirmSaveFlight.onlineCheckInOnly")}
        onSecondaryBtnPressed={handleCheckInOnline}
      />
      {renderBottomSheetError()}
      <BottomSheetUnableLoadLocation ref={unableToLoadLocationRef} />
      <BottomSheetMapUnavailable ref={mapUnavailable} />
      <OnboardingOverlay
        direction={direction}
        isLoadingDetailFlight={isLoadingDetailFlight}
        setIsFinishedJourneyOnboarding={setIsFinishedJourneyOnboarding}
      />
    </>
  )
}

export { FlightDetailsV2 }
