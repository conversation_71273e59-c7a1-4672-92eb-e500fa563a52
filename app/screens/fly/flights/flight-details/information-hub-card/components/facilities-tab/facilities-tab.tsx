import React, { useContext } from "react"
import { FlatList, View } from "react-native"
import { styles } from "./facilities-tab.styles"
import { Text } from "app/elements/text/text"
import { useNavigation } from "@react-navigation/native"
import { FacilitiesTabProps } from "./facilities-tab.props"
import { translate } from "app/i18n"
import { AttractionsFacilitiesServices } from "app/components/attractions-facilities-services/attractions-facilities-services"
import { ExploreItemTypeEnum } from "app/redux/types/explore/explore-item-type"
import { Button } from "app/elements/button/button"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { SHIMMER_FLATLIST_DATA, NavigationConstants } from "app/utils/constants"
import { ErrorCloudComponentV2 } from "app/components/error-cloud-v2/error-cloud-v2"
import { FLY_CONTEXT } from "app/services/context/fly"
import { getFeatureFlagInit, isFlagOnCondition, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"

const FacilitiesTab = ({
  facilitiesServicesData,
  loading,
  testID,
  accessibilityLabel,
  terminal,
  onPressItem,
  onPressAll,
  selectedTravelOption,
  onPressReloadFacilities,
  facilitiesError,
}: FacilitiesTabProps) => {
  const navigation = useNavigation()
  const { handleNavigation } = useHandleNavigation("FLIGHT_LANDING_FACILITIES_SERVICES")

  const { flyDetailsFirstFlag } = useContext(FLY_CONTEXT)?.Handlers
  const isFlightDetailsFirst = isFlagOnCondition(flyDetailsFirstFlag)
  const { flyLandingFeatureFlag } = useContext(AccountContext)

  const bottomList = () => {
    return (
      <Button
        textStyle={styles.textBottomListButton}
        style={[
          styles.styleBottomListButton,
          {
            marginHorizontal: isFlightDetailsFirst ? 16 : 0,
          },
        ]}
        tx={"flightDetailV2.facilitiesTab.seeAll"}
        testID={`${testID}__ButtonViewAllFacilitiesAndServices`}
        accessibilityLabel={`${accessibilityLabel}__ButtonViewAllFacilitiesAndServices`}
        onPress={() => {
          if (onPressAll) {
            onPressAll()
          }
          const isFlyLandingV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag)
          if (isFlyLandingV2) {
            // @ts-ignore
            navigation.navigate(NavigationConstants.facilitiesServices)
          } else {
            // @ts-ignore
            navigation.navigate("fly", {
              screen: "airport",
              params: {
                moveToElement: "facilitiesServices",
                rand: Math.random(),
                terminal,
                selectedTravelOption,
              },
            })
          }
        }}
      />
    )
  }

  const onPress = (navigate) => {
    if (!navigate) {
      return
    }
    const { type, value } = navigate?.navigation || ""
    const { redirect } = navigate || {}
    handleNavigation(type, value, redirect)
  }

  const renderTitle = () => {
    if (!terminal) {
      return (
        <View style={styles.containerTitle}>
          <Text
            text={translate("flightDetailV2.facilitiesTab.allTerminal").toUpperCase()}
            preset="caption2Bold"
            style={styles.textAlmostBackColor}
          />
          <Text
            tx={"flightDetailV2.facilitiesTab.descAllTerminal"}
            preset="bodyTextRegular"
            style={styles.descAllTerminalLabel}
          />
        </View>
      )
    }
    return (
      <View style={styles.containerTitle}>
        <Text
          text={translate("flightDetailV2.facilitiesTab.titleWithTerminal", {
            terminal,
          }).toUpperCase()}
          preset="caption2Bold"
          style={styles.textAlmostBackColor}
        />
        <Text
          tx={"flightDetailV2.facilitiesTab.descWithTerminal"}
          preset="bodyTextRegular"
          style={styles.descAllTerminalLabel}
        />
      </View>
    )
  }

  if (!!facilitiesError) {
    return (
      <ErrorCloudComponentV2
        title={translate("screenError.oop")}
        content={translate("screenError.somethingWrong")}
        buttonText={translate("screenError.reload")}
        onPress={onPressReloadFacilities}
      />
    )
  }

  return (
    <View style={styles.container}>
      {renderTitle()}
      <FlatList
        data={!loading ? facilitiesServicesData : SHIMMER_FLATLIST_DATA}
        renderItem={({ item, index }) => (
          <AttractionsFacilitiesServices
            type={loading ? ExploreItemTypeEnum.loading : ExploreItemTypeEnum.default}
            title={item?.title}
            imageUrl={item?.image_url}
            attractionId={undefined}
            locationDisplayText={item?.locationText}
            onPressed={() => {
              if (onPressItem) {
                onPressItem(item)
              }
              onPress(item)
            }}
            testID={`${testID}__AttractionsFacilitiesServices__${index}`}
            accessibilityLabel={`${accessibilityLabel}__AttractionsFacilitiesServices__${index}`}
            isFlex1Fixed
          />
        )}
        keyExtractor={(_, index) => `key_travelling_item_${index}`}
        numColumns={2}
        style={styles.flatListStyle}
        contentContainerStyle={[
          styles.contentContainerStyle,
          {
            marginHorizontal: isFlightDetailsFirst ? 0 : 16,
            paddingHorizontal: isFlightDetailsFirst ? 8 : 0,
          },
        ]}
        scrollEnabled={false}
        ListFooterComponent={bottomList}
        ListFooterComponentStyle={styles.footerComponentStyle}
        testID={`${testID}__FlatListFacilitiesAndServices`}
        accessibilityLabel={`${accessibilityLabel}__FlatListFacilitiesAndServices`}
      />
    </View>
  )
}

export { FacilitiesTab }
