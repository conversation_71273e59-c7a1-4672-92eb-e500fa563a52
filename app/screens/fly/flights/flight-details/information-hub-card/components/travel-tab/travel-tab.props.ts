import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { FlightDetailSectionCFProps, FlightDetailsPayloadProps, ITravelChecklistAEM } from "../../../flight-detail.props"
import { TypePressDetailFlightCard, TypeTransport } from "../../../flight-details"
import { TravelOption } from "../../../travel-options/travel-options"

export interface TravelTabProps {
  direction: FlightDirection
  handleMap: (type: TypePressDetailFlightCard, item?, skytrainItem?: { type: TypeTransport, terminal: number }) => void
  flightDetailSectionData: FlightDetailSectionCFProps | null
  travelChecklistAEM: ITravelChecklistAEM
  flyFlightDetailsError: any
  flyFlightDetailsPayload: FlightDetailsPayloadProps
  selectedTopTravelOption: TravelOption
  isFlightSaved: boolean
  enableEciDynamicDisplay: boolean
  onPressFlightCardLinks: (titleCardLink: string) => void
  onPressReloadTravelAEM: () => void
  isTravelChecklistAEMLoading: boolean
  onPressReloadFlightDetails: () => void
  disableSaveFlight?: boolean
  onSaveFlight?: (label?: string) => void
  saveFlightWhenCheckInOnline?: (isFlightSaved?: boolean) => any
}
