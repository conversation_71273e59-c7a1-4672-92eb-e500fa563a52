import { CustomerEligibility, FlightDirection } from "../../flight-props"
import { FlightDetailSectionCFProps, FlightDetailsPayloadProps, IBannerAEM, IBannerAEMResponse, ITravelChecklistAEM } from "../flight-detail.props"
import { TypePressDetailFlightCard, TypeTransport } from "../flight-details"
import { TravelOption } from "../travel-options/travel-options"

export interface FacilitiesTabProps {
  direction: FlightDirection
  handleMap: (type: TypePressDetailFlightCard, item?, skytrainItem?: { type: TypeTransport, terminal: number }) => void
  travelChecklistAEM: ITravelChecklistAEM
  flightDetailSectionData: FlightDetailSectionCFProps | null
  flyFlightDetailsError: any
  flyFlightDetailsPayload: FlightDetailsPayloadProps
  selectedTopTravelOption: TravelOption
  isFlightSaved: boolean
  enableEciDynamicDisplay: boolean
  onPressFlightCardLinks: (titleCardLink: string) => void
  customerEligibility: CustomerEligibility
  onPressReloadTravelAEM: () => void
  isTravelChecklistAEMLoading: boolean
  onPressReloadFlightDetails: () => void
  disableSaveFlight?: boolean
  onSaveFlight?: (label?: string) => void
  saveFlightWhenCheckInOnline?: (isFlightSaved?: boolean) => any
}
