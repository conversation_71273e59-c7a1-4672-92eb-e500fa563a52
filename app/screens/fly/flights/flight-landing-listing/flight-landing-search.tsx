import React, { useCallback } from "react"
import { Alert, Platform, StyleSheet, TouchableOpacity, View } from "react-native"
import { Text } from "app/elements/text/text"
import { color } from "app/theme/color"
import NetInfo from "@react-native-community/netinfo"
import { openSettings, PERMISSIONS, request, RESULTS } from "react-native-permissions"
import { handleCondition } from "app/utils"
import { AemSelectors } from "app/redux/aemRedux"
import { translate } from "app/i18n/translate"
import { useDispatch, useSelector } from "react-redux"
import { FlySelectors } from "app/redux/flyRedux"
import { ScanFlight } from "ichangi-fe/assets/icons"
import { SearchBarVariations } from "app/elements/search/search.props"
import { Search } from "app/elements/search/search"
import FilterByDate from "../filter-date-landing"

const styles = StyleSheet.create({
  searchBarWrap: {
    backgroundColor: color.palette.lightestGrey,
  },
  searchBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 24,
  },
  searchBarTopContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 24,
    marginBottom: 12,
  },
  searchBarText: {
    flex: 1,
  },
  scanIconContainer: {
    marginLeft: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  scanIconText: {
    marginLeft: 2,
  },
  searchStyle: {
    flex: 1,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
})

const FlightLandingSearch = (props) => {
  const { testID, setNoConnection, navigation, onSearchPress } = props
  const dispatch = useDispatch()

  const flyLandingSelectedTab = useSelector(FlySelectors.flyLandingSelectedTab)
  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const msg61 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG61")
  const msg62 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG62")

  const Rationale = {
    title: msg61?.title || translate("requestPermission.camera.title"),
    message: msg61?.message || translate("requestPermission.camera.message"),
    buttonPositive: msg61?.secondButton || translate("requestPermission.camera.buttonPositive"),
    buttonNegative: msg61?.firstButton || translate("requestPermission.camera.buttonNegative"),
  }

  const onScanPress = useCallback(() => {
    const checkConnection = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setNoConnection(true)
      } else {
        request(
          handleCondition(
            Platform.OS === "ios",
            PERMISSIONS.IOS.CAMERA,
            PERMISSIONS.ANDROID.CAMERA,
          ),
          Rationale,
        ).then((result) => {
          if (result === RESULTS.BLOCKED) {
            Alert.alert(
              msg62?.title || translate("scanCode.needAccessPermission.title"),
              msg62?.message || translate("scanCode.needAccessPermission.description"),
              [
                {
                  text:
                    msg62?.firstButton || translate("scanCode.needAccessPermission.firstButton"),
                  style: "cancel",
                  onPress: () => {
                    openSettings()
                  },
                },
                {
                  text:
                    msg62?.secondButton || translate("scanCode.needAccessPermission.secondButton"),
                  onPress: () => null,
                },
              ],
            )
          } else if (result === RESULTS.GRANTED) {
            navigation.navigate("scanCode")
          }
        })
      }
    }
    checkConnection()
  }, [])

  return (
    <View style={styles.searchBarWrap}>
      <View style={styles.searchBarTopContainer}>
        <Text preset="h4" text="find your flight" style={styles.searchBarText} />
        <TouchableOpacity
          style={styles.scanIconContainer}
          onPress={onScanPress}
          testID={`${testID}__TouchableScan`}
          accessibilityLabel={`${testID}__TouchableScan`}
        >
          <ScanFlight width={25} height={25} />
          <Text preset="textLink" text={"Scan"} style={styles.scanIconText} />
        </TouchableOpacity>
      </View>
      <View style={styles.searchBarContainer}>
        <FilterByDate
          flightScreen={"Landing"}
          direction={flyLandingSelectedTab}
          dispatch={dispatch}
          navigation={navigation}
          buttonStyles={null}
        />
        <Search
          type={SearchBarVariations.flightSearchBar}
          style={styles.searchStyle}
          placeholderTx={"departureSection.searchBarText"}
          onPressed={onSearchPress}
          testID={`${testID}__Search`}
          accessibilityLabel={`${testID}__Search`}
        />
      </View>
    </View>
  )
}

export default React.memo(FlightLandingSearch)
