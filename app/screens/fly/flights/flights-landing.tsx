/* eslint-disable @typescript-eslint/no-unused-vars */
import { color } from "app/theme"
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react"
import {
  ViewStyle,
  View,
  RefreshControl,
  TextStyle,
  Platform,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Animated,
  InteractionManager,
} from "react-native"
import DepartureLandingScreen from "./departure/departure-landing"
import { useHandleScroll } from "../../../navigators/navigation-utilities"
import { translate } from "../../../i18n/translate"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import ArrivalLandingScreen from "./arrival/arrival-landing"
import {
  FlightDirection,
  FlightLandingContext,
  FlightRequestType,
  IFilterPillItem,
} from "./flight-props"
import { useSelector, useDispatch } from "react-redux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { Fly, FlySelectors, FlyCreators } from "app/redux/flyRedux"
import { RootState } from "app/redux/store"
import { ProfileSelectors } from "app/redux/profileRedux"
import { flyModuleUpdatedTime } from "app/utils/date-time/date-time"
import { FeedBackToastType } from "app/components/feedback-toast/feedback-toast-props"
import { presets } from "app/elements/text"
import { FlightListingState } from "app/components/flight-listing-card/flight-listing-card.props"
import ShortCutLinkFlySection from "./short-link/short-link"
import NetInfo from "@react-native-community/netinfo"
import AemActions, { AemSelectors, AEM_PAGE_NAME } from "app/redux/aemRedux"
import { AirportLandingSelectors } from "app/redux/airportLandingRedux"
import { LoadingOverlay } from "app/components/loading-modal"
import { StorageKey } from "app/utils/storage/storage-key"
import { load } from "app/utils/storage"
import isEmpty from "lodash/isEmpty"
import { get } from "lodash"
import { trackAction, AdobeTagName, commonTrackingScreen } from "app/services/adobe"
import { handleCondition, simpleCondition } from "app/utils"
import { defaultFlyLandingPageConfiguration } from "app/screens/dine-shop/json/flightLanding"
import { isCloseToBottom } from "app/utils/screen-helper"
import StickyHeader from "./sticky-header"
import { useFocusEffect, useIsFocused } from "@react-navigation/native"
import { FLY_CONTEXT } from "app/services/context/fly"
import { TOAST_MESSAGE_DURATION, TrackingScreenName } from "app/utils/constants"
import FlyAppscapadeBanner from "app/sections/appscapade-flight"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs"
import { FAB } from "app/components/fab"
import { useSharedValue } from "react-native-reanimated"
import FlightLandingListingTab from "./flight-landing-listing/flight-landing-listing-tab"
import FlightLandingListing from "./flight-landing-listing/flight-landing-listing"
import FlightLandingSearch from "./flight-landing-listing/flight-landing-search"
import { getPreviousScreen, useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import Suspend from "app/components/suspend"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { PageConfigSelectors } from "app/redux/pageConfigRedux"
import { AnimatedFeedBackToast } from "app/components/feedback-toast/animated-feedback-toast"
import { BottomNavContext } from "app/navigators/bottom-navigator"

const parentContainerStyle: ViewStyle = {
  flex: 1,
  backgroundColor: color.palette.lightestGrey,
}

export const topTabTouchableOpacityStyle: ViewStyle = {
  alignItems: "center",
  marginEnd: 24,
}

const toastButtonStyle: TextStyle = {
  ...presets.textLink,
  fontWeight: "normal",
  color: color.palette.lightBlue,
  alignItems: "flex-end",
}

const toastTextStyle: TextStyle = {
  ...presets.bodyTextRegular,
  color: color.palette.whiteGrey,
  width: "80%",
}

const searchBarMarginBottom = 20
const searchBarContainerWithSeparator: ViewStyle = {
  marginBottom: searchBarMarginBottom,
}

const feedBackToastStyle: ViewStyle = { width: "100%", paddingHorizontal: 16, bottom: 8 }

const flightLandingContainer: ViewStyle = {
  paddingBottom: 10,
}

const stickyHeaderStyle: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
}

enum FlightComponentTypes {
  shortcutLink = "shortcutLink",
  search = "search",
  flightList = "flightList",
  listTravelling = "listTravelling",
  listTransiting = "listTransiting",
  facilitiesAndServices = "facilitiesAndServices",
  listSavedFlight = "listSavedFlight",
  appscapadeBanner = "appscapadeBanner",
}

const COMPONENT_NAME = "FlightsLanding"
const FlightsLanding = ({ navigation, route }) => {
  const {initFlightListTabKey} = route?.params || {}

  const isFocused = useIsFocused()
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const dispatch = useDispatch()
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const [isNoConnection, setNoConnection] = useState(false)
  const [loadingLoadMoreLocal, setLoadingLoadMore] = useState<boolean>(false);
  const { handleScroll, scrollDirection } = useHandleScroll()
  const toast = useRef(null)
  const scrollViewRef = useRef(null)
  const isTriggerFlightRequestRef = useRef(false)
  const flyContentShortLinkFetching = useSelector(FlySelectors.flyContentShortLinkFetching)
  const dataFlyLandingPageConfiguration = useSelector(PageConfigSelectors.flyPagePayload)
  const configPage = handleCondition(
    !isEmpty(dataFlyLandingPageConfiguration),
    dataFlyLandingPageConfiguration,
    defaultFlyLandingPageConfiguration,
  )
  const positionScrollRef = useRef({
    contentOffsetY: 0,
    contentOffsetX: 0,
  })
  const searchBarRef = useRef(null)
  const [locationSearchBar, setLocationSearchBar] = useState(0)
  const [pullToRefreshTimeStamp, setPullToRefreshTimeStamp] = useState(new Date().getTime())
  const FLY_CONTEXT_HANDLERS = React.useContext(FLY_CONTEXT).Handlers
  const animatedScrollYValue = useRef(new Animated.Value(0)).current
  const flyPayload: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.flyArrivalLanding(state),
  )
  const isScrollToTopAfterShowTooltip = useSelector(FlySelectors.isScrollToTopAfterShowTooltip)
  const callAEMData = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.FLY_APPSCAPADE))
  const getAppCapadeLoading = useMemo(() => {
    return get(callAEMData, "loading")
  }, [callAEMData])

  const bottomTabHeight = useBottomTabBarHeight()
  const fabOpacity = useSharedValue(0)
  const { bottomTabsPosition } = useContext(BottomNavContext)

  const getOpenFlyScreenBefore = () => {
    let openFlyScreenBefore = false
    load(StorageKey.needDisableToolTipFlyScreen).then((value) => {
      if (value) {
        openFlyScreenBefore = value
      }
    })
    return openFlyScreenBefore
  }

  const getStateGetArrFlight = useMemo(() => {
    return flyPayload?.type === FlightListingState.loading
  }, [flyPayload])

  const checkAllowScrollEnabled = useMemo(() => {
    const isOpenFlyScreenBefore = getOpenFlyScreenBefore()
    if (isOpenFlyScreenBefore) {
      if (flyPayload?.type === FlightListingState.loading) return true
      if (isEmpty(flyPayload?.data) || flyPayload?.errorFlag) return false
      return true
    } else {
      return !getStateGetArrFlight
    }
  }, [getStateGetArrFlight, getOpenFlyScreenBefore, flyPayload])


  useCurrentScreenActiveAndPreviousScreenHook(TrackingScreenName.FlyLanding)
  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        FLY_CONTEXT_HANDLERS.fly_listing_departure_focus_search_bar = null
        FLY_CONTEXT_HANDLERS.fly_listing_arrival_focus_search_bar = null
        commonTrackingScreen(
          TrackingScreenName.FlyLanding,
          getPreviousScreen(),
          isLoggedIn,
        )
      })
    }, []),
  )

  const flyLandingSelectedTab = useSelector(FlySelectors.flyLandingSelectedTab)
  const getChangiGameUrlLoading = useSelector(AirportLandingSelectors.getChangiGameUrlLoading)
  const flyLastUpdatedTimeStamp: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.flyLastUpdatedTimeStamp(state),
  )
  const contentContainerStyle: ViewStyle = {
    paddingTop: 16,
    paddingBottom: (useSafeAreaInsets().bottom > 0 ? 50 : 18) + bottomTabHeight,
  }
  const flyDepartureLandingState: any = useSelector<RootState, Fly>(
    FlySelectors.flyDepartureLandingState,
  )
  const flyArrivalLandingState: any = useSelector<RootState, Fly>(
    FlySelectors.flyArrivalLandingState,
  )
  const flyDepartureLanding: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.flyDepartureLanding(state),
  )
  const flyArrivalLanding: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.flyArrivalLanding(state),
  )
  const departureLandingLoading: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.departureLandingLoading(state),
  )
  const arrivalLandingLoading: any = useSelector<RootState, Fly>((state) =>
    FlySelectors.arrivalLandingLoading(state),
  )
  const isEndEarlierFlightLanding = useSelector(FlySelectors.isEndEarlierFlightLanding)
  const filterPillList = useSelector(FlySelectors.filterPillList) || []
 
  const isLoadingTickerBandRef = useRef(false)

  const getTickerBand = () => {
    isLoadingTickerBandRef.current = true
    dispatch(
      AemActions.getAemConfigData({
        name: AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true,
      }),
    )
    setTimeout(() => {
      isLoadingTickerBandRef.current = false
    }, 2000)
  }

  useEffect(() => {
    if (isScrollToTopAfterShowTooltip) {
      scrollViewRef.current?.scrollTo({
        y: 0,
        animated: true,
      })
      dispatch(FlyCreators.setScrollToTopAfterShowTooltip(false))
    }
  }, [isScrollToTopAfterShowTooltip])

  const checkOpenedScreen = async () => {
    const openFlyScreenBefore = await load(StorageKey.needDisableToolTipFlyScreen)
    const appscapadeCompomentIsConfig = configPage?.find(
      (element) => element.sectionComponent === FlightComponentTypes.appscapadeBanner,
    )
    const isFeatureFlagON = isFlagOnCondition(
      FLY_CONTEXT_HANDLERS.fly_appscapade_flight_landing,
    )
    const checkAppCapadeCondition =
      !isEmpty(appscapadeCompomentIsConfig) && isFeatureFlagON
        ? getAppCapadeLoading !== undefined && !getAppCapadeLoading
        : true
    if (!openFlyScreenBefore && !flyContentShortLinkFetching && checkAppCapadeCondition) {
      scrollViewRef?.current?.scrollTo({ y: locationSearchBar })
      Platform.OS === "android" &&
        setTimeout(() => {
          dispatch(FlyCreators.setShowTooltipAfterScroll(true))
        }, 500)
    }
  }
  useFocusEffect(
    useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        if (!isLoadingTickerBandRef.current) {
          getTickerBand()
        }
        checkOpenedScreen()
      })
    }, [locationSearchBar, flyContentShortLinkFetching, getAppCapadeLoading, configPage]),
  )
  useEffect(() => {
    if (
      flyDepartureLandingState === FlightListingState.loading ||
      flyArrivalLandingState === FlightListingState.loading ||
      flyDepartureLandingState === undefined ||
      flyArrivalLandingState === undefined
    ) {
      toast?.current?.closeNow()
    }
  }, [flyDepartureLandingState, flyArrivalLandingState])


  const checkInternetConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  const handleRefresh = (callAllDataPage = false) => {
    checkInternetConnection().then((isConnection) => {
      if (isConnection) {
        toast?.current?.closeNow()
        getTickerBand()
        getLandingData()
        if (isLoggedIn) {
          dispatch(MytravelCreators.flyMyTravelFlightsRequest(profilePayload?.email))
        }
        if (callAllDataPage) {
          // call shortLinks and F&S
          dispatch(FlyCreators.flyContentShortLinkRequest())
        }
      } else {
        if (flyLandingSelectedTab === FlightDirection.departure) {
          if (flyDepartureLandingState === FlightListingState.default) {
            toast?.current?.show(TOAST_MESSAGE_DURATION)
          }
        } else {
          if (flyArrivalLandingState === FlightListingState.default) {
            toast?.current?.show(TOAST_MESSAGE_DURATION)
          }
        }
      }
    })
  }

  const getLandingData = () => {
    const filterPillItemChecked = filterPillList?.find((item: IFilterPillItem) => {
      return item.isSelected
    })
    const filters = filterPillItemChecked.tagCode === "all" ? [] : [filterPillItemChecked.tagName]
    if (flyLandingSelectedTab === FlightDirection.departure) {
      dispatch(
        FlyCreators.flyLandingDepartureRequest(
          FlightDirection.departure,
          FlightRequestType.FlightRefresh,
          filters,
        ),
      )
    } else {
      dispatch(
        FlyCreators.flyLandingArrivalRequest(
          FlightDirection.arrival,
          FlightRequestType.FlightRefresh,
          filters,
        ),
      )
    }
    isEndEarlierFlightLanding && dispatch(FlyCreators.resetEndEarlierFlightLanding())
    dispatch(FlyCreators.flyLastUpdatedTimeStamp(flyModuleUpdatedTime()))
  }

  const isFlyDepartureLandingRequestType: any = useSelector<RootState, Fly>(
    FlySelectors.isFlyDepartureLandingRequestType,
  )
  const isFlyDepartureLandingError: any = useSelector<RootState, Fly>(
    FlySelectors.isFlyDepartureLandingError,
  )

  const isFlyArrivalLandingRequestType: any = useSelector<RootState, Fly>(
    FlySelectors.isFlyArrivalLandingRequestType,
  )

  const isFlyArrivalLandingError: any = useSelector<RootState, Fly>(
    FlySelectors.isFlyArrivalLandingError,
  )

  const showFeedBackToastMessage = () => {
    return (
      <AnimatedFeedBackToast
        ref={toast}
        style={feedBackToastStyle}
        textButtonStyle={toastButtonStyle}
        position={"custom"}
        textStyle={toastTextStyle}
        type={FeedBackToastType.fullWidthFeedBack}
        text={translate("flightLanding.feedBackToastErrorMessage") + flyLastUpdatedTimeStamp}
        testID={`${COMPONENT_NAME}__FeedBackToastErrorMessage`}
        accessibilityLabel={`${COMPONENT_NAME}__FeedBackToastErrorMessage`}
        positionBottom={bottomTabsPosition}
      />
    )
  }

  useEffect(() => {
    toast?.current?.closeNow()
  }, [flyLandingSelectedTab])

  useEffect(() => {
    if (
      isFlyDepartureLandingError &&
      flyLandingSelectedTab === FlightDirection.departure &&
      isFlyDepartureLandingRequestType === FlightRequestType.FlightRefresh
    ) {
      toast?.current?.show(TOAST_MESSAGE_DURATION)
    } else {
      toast?.current?.closeNow()
    }
  }, [
    isFlyDepartureLandingError,
    isFlyDepartureLandingRequestType === FlightRequestType.FlightRefresh,
  ])

  useEffect(() => {
    if (
      isFlyArrivalLandingError &&
      flyLandingSelectedTab === FlightDirection.arrival &&
      isFlyArrivalLandingRequestType === FlightRequestType.FlightRefresh
    ) {
      toast?.current?.show(TOAST_MESSAGE_DURATION)
    } else {
      toast?.current?.closeNow()
    }
  }, [isFlyArrivalLandingError, isFlyArrivalLandingRequestType === FlightRequestType.FlightRefresh])

  useEffect(() => {
    ;(async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setNoConnection(true)
      }
    })()
  }, [])

  const onLayoutTab = (_params: any) => null

  const showUnableMessage = () => {
    toast?.current?.closeNow()
    toast?.current?.show(TOAST_MESSAGE_DURATION)
  }
  const hideUnableMessage = () => {
    toast?.current?.closeNow()
  }

  const triggerFlightRequest = () => {
    isTriggerFlightRequestRef.current = true
    const filterPillItemChecked = filterPillList?.find((item: IFilterPillItem) => {
      return item.isSelected
    })
    const filters = filterPillItemChecked.tagCode === "all" ? [] : [filterPillItemChecked.tagName]
    const tab = flyLandingSelectedTab || FlightDirection.departure
    if (tab === FlightDirection.arrival && flyArrivalLanding?.nextToken) {
      setLoadingLoadMore(true)
      dispatch(
        FlyCreators.flyLandingPaginationArrivalRequest(tab, flyArrivalLanding?.nextToken, filters),
      )
    } else if (tab === FlightDirection.departure && flyDepartureLanding?.nextToken) {
      setLoadingLoadMore(true)
      dispatch(
        FlyCreators.flyLandingPaginationDepartureRequest(
          tab,
          flyDepartureLanding?.nextToken,
          filters,
        ),
      )
    }
    setTimeout(() => {
      isTriggerFlightRequestRef.current = false
    }, 2000)
  }

  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if(event.nativeEvent.contentOffset.y > locationSearchBar + 105){
      fabOpacity.value = 1
    } else {
      fabOpacity.value = 0
    }
    if (event.nativeEvent.contentOffset.y > 0 && FLY_CONTEXT_HANDLERS?.flyRefreshRef) {
      clearInterval(FLY_CONTEXT_HANDLERS?.flyRefreshRef)
    }
    handleScroll(event)
    positionScrollRef.current.contentOffsetX = event.nativeEvent.contentOffset.x
    positionScrollRef.current.contentOffsetY = event.nativeEvent.contentOffset.y

    if (scrollDirection?.current === "up") return
    if (departureLandingLoading || arrivalLandingLoading) return
    const threshHold = simpleCondition({
      condition: event.nativeEvent.contentSize.height,
      ifValue: {
        condition: event.nativeEvent.contentSize.height > 7000,
        ifValue: 700,
        elseValue: event.nativeEvent.contentSize.height * 0.1,
      },
      elseValue: 400,
    })
    if (isCloseToBottom(event, threshHold) && !isTriggerFlightRequestRef.current) {
      triggerFlightRequest()
    }
  }

  const searchBarOnLayout = (event) => {
    setLocationSearchBar(event.nativeEvent.layout.y)
  }

  const [tabKey, setTabKey] = useState(initFlightListTabKey || FlightDirection.arrival)
  const stickyOnPress = (event) => {
    setLoadingLoadMore(false)
    const { value } = event
    setTimeout(() => {
      scrollViewRef.current?.scrollTo({
        y: locationSearchBar + 50 + searchBarMarginBottom,
        animated: true,
      })
      setTabKey(value)
    }, 0)
  }

  const tabOnClickCallback = ({ index }) => {
    setLoadingLoadMore(false)
    let value = FlightDirection.arrival
    if (index === 1) value = FlightDirection.departure
    setTimeout(() => {
      setTabKey(value)
    }, 0)
    dispatch(FlyCreators.flyLandingSelectedTab(value))
  }

  const scrollToTop = useCallback(() => {
    scrollViewRef.current?.scrollTo({
      y: 0,
      animated: true,
    })
    handleRefresh()
  }, [flyLandingSelectedTab, filterPillList, profilePayload, isLoggedIn, isEndEarlierFlightLanding])

  const onSearchPress = () => {
    trackAction(AdobeTagName.CAppFlyFlightSearchSearchBar, {
      [AdobeTagName.CAppFlyFlightSearchSearchBar]: AdobeTagName.CAppFlightLanding,
    })
    if (flyLandingSelectedTab === FlightDirection.arrival) {
      FLY_CONTEXT_HANDLERS.fly_listing_arrival_focus_search_bar = true
    } else {
      FLY_CONTEXT_HANDLERS.fly_listing_departure_focus_search_bar = true
    }
    navigation.navigate("flightResultLandingScreen", {
      screen: flyLandingSelectedTab,
      sourcePage: AdobeTagName.CAppFlightLanding,
    })
  }

  const hideLoadingLoadMore = useCallback(() => {
    setLoadingLoadMore(false);
  },[])

  const renderFlightTab = useMemo(() => {
    return {
      tabList: [
        {
          component: ArrivalLandingScreen,
          props: {
            showUnableMessage: showUnableMessage,
            hideUnableMessage: hideUnableMessage,
            onLayoutTab: onLayoutTab,
            pullToRefreshTimeStamp,
            loadingLoadMoreLocal: loadingLoadMoreLocal,
            hideLoadingLoadMore: hideLoadingLoadMore
          },
        },
        {
          component: DepartureLandingScreen,
          props: {
            showUnableMessage: showUnableMessage,
            hideUnableMessage: hideUnableMessage,
            onLayoutTab: onLayoutTab,
            pullToRefreshTimeStamp,
            loadingLoadMoreLocal: loadingLoadMoreLocal,
            hideLoadingLoadMore: hideLoadingLoadMore
          },
        },
      ],
    }
  }, [pullToRefreshTimeStamp, loadingLoadMoreLocal, hideLoadingLoadMore])

  const pullToRefresh = () => {
    setPullToRefreshTimeStamp(new Date().getTime())
    handleRefresh()
  }
  const headerOpacity = animatedScrollYValue.interpolate({
    inputRange: [locationSearchBar + 10, locationSearchBar + 15],
    outputRange: [0, 1],
    extrapolate: "clamp",
  })

  const headerZIndex = animatedScrollYValue.interpolate({
    inputRange: [locationSearchBar + 9, locationSearchBar + 10],
    outputRange: [-1, 1],
    extrapolate: "clamp",
  })
  
  const animatedStickyHeader = useMemo(() => {
    if (!locationSearchBar) {
      return <></>
    }
    return (
      <Animated.View style={[stickyHeaderStyle, { opacity: headerOpacity, zIndex: headerZIndex }]}>
        <StickyHeader
          onPress={stickyOnPress}
          navigation={navigation}
          onSearchPress={onSearchPress}
          dispatch={dispatch}
        />
      </Animated.View>
    )
  }, [locationSearchBar, headerOpacity, headerZIndex, flyLandingSelectedTab])

  const showContent = useMemo(() => {
    return configPage?.map((element, index) => {
      const needSeparator = index !== configPage?.length - 1
      switch (element?.sectionComponent) {
        case FlightComponentTypes.shortcutLink:
          return (
            <ShortCutLinkFlySection
              setNoConnection={(isCheckConnect) => setNoConnection(isCheckConnect)}
              testID={`${COMPONENT_NAME}__Search`}
              accessibilityLabel={`${COMPONENT_NAME}__Search`}
              key={`${element?.sectionComponent}-${index}`}
              index={index}
              needSeparator={needSeparator}
            />
          )
        case FlightComponentTypes.listSavedFlight:
          return <View key={`${element?.sectionComponent}-${index}`}/>
        case FlightComponentTypes.search:
          return (
            <View
              ref={searchBarRef}
              style={handleCondition(needSeparator, searchBarContainerWithSeparator, null)}
              key={`${element?.sectionComponent}-${index}`}
              onLayout={searchBarOnLayout}
            >
              <FlightLandingSearch
                testID={COMPONENT_NAME}
                setNoConnection={setNoConnection}
                navigation={navigation}
                onSearchPress={onSearchPress}
              />
            </View>
          )
        case FlightComponentTypes.flightList:
          return (
            <Suspend freeze={!isFocused}>
              <View style={flightLandingContainer} key={`${element?.sectionComponent}-${index}`}>
                <FlightLandingListingTab tabOnClickCallback={tabOnClickCallback} tabKey={tabKey}/>
                <FlightLandingContext.Provider value={{ screenDirection: tabKey  }}>
                  <FlightLandingListing renderFlightTab={renderFlightTab} />
                </FlightLandingContext.Provider>
              </View>
            </Suspend>
          )
        case FlightComponentTypes.facilitiesAndServices:
          return <View key={`${element?.sectionComponent}-${index}`}/>
        case FlightComponentTypes.listTravelling:
          return <View key={`${element?.sectionComponent}-${index}`}/>
        case FlightComponentTypes.listTransiting:
          return <View key={`${element?.sectionComponent}-${index}`}/>
        case FlightComponentTypes.appscapadeBanner:
          return <FlyAppscapadeBanner key={`${element?.sectionComponent}-${index}`}/>
        default:
          return null
      }
    })
  }, [tabKey, isFocused, renderFlightTab])

  useEffect(() => {
    const isArrivalTabActived = tabKey === FlightDirection.arrival
    const isInitTabKeyIsDeparture = initFlightListTabKey === FlightDirection.departure
    if (isInitTabKeyIsDeparture && isArrivalTabActived) {
      setTabKey(FlightDirection.departure)
    }
  }, [initFlightListTabKey])

  return (
    <>
      {showFeedBackToastMessage()}
      {animatedStickyHeader}
      <Animated.ScrollView
        keyboardShouldPersistTaps={"always"}
        refreshControl={<RefreshControl refreshing={false} onRefresh={pullToRefresh} />}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: animatedScrollYValue } } }],
          { listener: onScroll, useNativeDriver: true },
        )}
        scrollEnabled={checkAllowScrollEnabled}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={contentContainerStyle}
        testID={`${COMPONENT_NAME}__ScrollViewFlightLanding`}
        accessibilityLabel={`${COMPONENT_NAME}__ScrollViewFlightLanding`}
        style={parentContainerStyle}
        ref={scrollViewRef}
        onMomentumScrollEnd={async () => {
          const openFlyScreenBefore = await load(StorageKey.needDisableToolTipFlyScreen)
          if (!openFlyScreenBefore) {
            dispatch(FlyCreators.setShowTooltipAfterScroll(true))
          }
        }}
      >
        {showContent}
      </Animated.ScrollView>
      <FAB opacity={fabOpacity} onPress={scrollToTop}/>

      <ErrorOverlayNoConnection
        reload={true}
        header={false}
        headerBackgroundColor="transparent"
        visible={isNoConnection}
        testID={`${COMPONENT_NAME}__ErrorOverlayNoConnection`}
        onReload={() => {
          const checkConnection = async () => {
            const { isConnected } = await NetInfo.fetch()
            if (isConnected) {
              handleRefresh(true)
              setNoConnection(false)
            }
          }
          checkConnection()
        }}
      />
      <LoadingOverlay visible={getChangiGameUrlLoading} />
    </>
  )
}

export { FlightsLanding }
