import { Text } from "app/elements/text"
import { DineShopEpicDefaultBanner, DineShopEpicDefaultBg } from "assets/backgrounds"
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react"
import {
  Dimensions,
  DimensionValue,
  InteractionManager,
  RefreshControl,
  ScrollView,
  StatusBarStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native"
import {
  BACKGROUND_COLOR,
  calculateBannerImageSize,
  calculateImageSize,
  INITIAL_VISIBLE_TAB_CONTENT_HEIGHT,
  MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT,
  STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT,
  styles,
  TOP_BAR_VISIBLE_TRIGGER_POINT,
} from "./dine-shop-v2.styles"
import { useInitializeData } from "./dine-shop-v2.hooks"
import { useSelector } from "react-redux"
import { ifAllTrue, ifOneTrue } from "app/utils"
import { NativeAuthSelectors } from "app/redux/nativeAuthRedux"
import { translate } from "app/i18n"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import { DownArrowGrey } from "assets/icons"
import LinearGradient from "react-native-linear-gradient"
import Animated, {
  Easing,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated"
import DineShopV2TabNavigator, { BOTTOM_SPACING } from "./containers/dine-shop-v2-tab-navigator"
import { useFocusEffect } from "@react-navigation/native"
import DineShopStaticTileList from "./components/dine-shop-static-tile-list"
import DineShopOtherAvailable from "./components/dine-shop-other-available"
import { DineShopPlayPass } from "./components/dine-shop-playpass-list"
import { DineShopActiveParking } from "./components/dine-shop-active-parking"
import { useHandleScroll } from "app/navigators"
import DineImage from "./components/dine-image"
import DineImageBackground from "./components/dine-background-image"
import { useRewardTier } from "app/hooks/useRewardTier"
import { Tier } from "app/components/changi-rewards-member-card"
import { useGeneratePlayPassUrl } from "app/utils/screen-hook"
import DineShopChangiMillionaireExperiences from "./components/dine-shop-cme"
import { getFeatureFlagInit, isFlagOnCondition, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import DineShopEarnedForToday from "./components/dine-shop-earned-for-today"
import DineShopTabBar, { NO_PADDING_TAB_BAR_HEIGHT, TAB_BAR_PADDING_N_REFINEMENT } from "./components/dine-shop-tab-bar"
import { SCREEN_NAME, TAB_BAR_CONFIG } from "./dine-shop-v2.constants"
import _isNumber from "lodash/isNumber"
import { FocusAwareStatusBar } from "app/components/focus-status-bar"
import { color } from "app/theme"
import DineShopLoadingTiles from "./components/dine-shop-loading-tiles"
import { DineError } from "./components/dine-error"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { NavigationConstants, StateCode } from "app/utils/constants"
import { PlayPassEntryPoint } from "app/redux/types/explore/explore-item-type"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { ImageManagerSelectors } from "app/redux/imageManagerRedux"
import { isEmpty } from "lodash"
import { AemGroupTwoSelectors } from "app/redux/aemGroupTwo"
import { AdobeTagName, trackAction } from "app/services/adobe"
import LuckyDraw from "./components/lucky-draw"

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient)
const { height: screenHeight, width: screenWidth } = Dimensions.get("window")

const DineShopScreenV2 = ({ navigation, route }) => {
  const { screen } = route?.params || {}
  const dineShopV2Banner = useSelector(ImageManagerSelectors.dineShopV2Banner)
  const dineShopV2Background = useSelector(ImageManagerSelectors.dineShopV2Background)
  const isShopDineV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_V2)
  const [bannerImageStyle, setBannerImageStyle] = useState<ViewStyle>(
    styles.bannerImageDefaultStyle,
  )
  const [bgImageStyle, setBgImageStyle] = useState<ViewStyle>(styles.bgImageDefaultStyle)
  const [measuredPromoHeight, setMeasuredPromoHeight] = useState<number>(0)
  const [statusBarStyle, setStatusBarStyle] = useState<StatusBarStyle>("light-content")
  const { loggedInTextCopy, nonLoggedInTextCopy } = useSelector(AemGroupTwoSelectors.dineShopEpicLandingPagePayload) || {}
  const { memberIconInfo } = useRewardTier()
  const isLoggedIn = useSelector(NativeAuthSelectors.isLoggedIn)
  const animatedHeight = useSharedValue<DimensionValue>(INITIAL_VISIBLE_TAB_CONTENT_HEIGHT)
  const animatedTabContentOpacity = useSharedValue<number>(0)
  const animatedScrollX = useSharedValue<number>(0)
  const animatedViewAllPromosOpacity = useSharedValue<number>(1)
  const contentHeight = useSharedValue(screenHeight + 100)
  const dineHeight = useSharedValue(0)
  const shopHeight = useSharedValue(0)
  const marketHeight = useSharedValue(0)
  const animatedTabBarHeight = useSharedValue<DimensionValue>(0)
  const tabContentPy = useSharedValue<number>(0)
  const isFirstTimeFocus = useRef<boolean>(true)
  const isMounted = useRef<boolean>(false)
  const overallScrollRef = useRef<any>(null)
  const contentRef = useRef<View>(null)
  const tabContentScrollRef = useRef<any>(null)
  const { getPlayPassUrl } = useGeneratePlayPassUrl("dineShopEpic")
  const isAccountV2CM24 = isFlagOnCondition(useContext(AccountContext)?.accountCM24FeatureFlag)
  const { handleScroll, isTabVisible } = useHandleScroll()
  const { handleNavigation } = useHandleNavigation("STATIC_PARKING_CM_TILE")
  const pageOffsetY = useSharedValue(0)
  const topBarStyleOpacity = useSharedValue(0)

  // Initiate all dine & shop screen requests
  const {
    isRefreshFinished,
    setIsRefresh,
    setShowRefreshIndicator,
    showRefreshIndicator,

    data,
    isLoading,
    isError
  } = useInitializeData()
  const [loadedScreen, setLoadedScreen] = useState([])

  const transactionSummaryData = data?.transactionSummary
  const isActiveLandingPage = transactionSummaryData?.isLogin && data?.transactionSummary.totalNrOfTransactions > 0 

  const isAEM = data?.parkingPerks?.aemParkingTile
  const isPP = data?.playpassPackages
  const isCM = data?.cmPerks
  const isCPMS = data?.parkingPerks?.cpmsParkingTile

  const isTilesFetching = isLoading

  const showError =
    !isTilesFetching &&
    ifOneTrue([
      isError,
      isActiveLandingPage ? ifAllTrue([!isPP, !isAEM, !isCM, !isCPMS]) : ifAllTrue([!isPP, !isAEM]),
    ])

  const isShowText = isTilesFetching || !showError 

  const promoListStyle = useAnimatedStyle(() => {
    return {
      height: animatedHeight.value,
    }
  }, [measuredPromoHeight, animatedHeight.value])

  const tabContentStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedTabContentOpacity.value,
    }
  }, [animatedTabContentOpacity.value])

  const viewAllPromosStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedViewAllPromosOpacity.value,
    }
  })

  const topBarStyle = useAnimatedStyle(() => {
    if (pageOffsetY.value >= TOP_BAR_VISIBLE_TRIGGER_POINT) {
      topBarStyleOpacity.value = withTiming(1, { duration: 150 })
    } else {
      topBarStyleOpacity.value = withTiming(0, { duration: 150 })
    }

    return {
      opacity: topBarStyleOpacity.value
    }
  }, [pageOffsetY.value])

  const tabBarStyle = useAnimatedStyle(() => {
    return {
      height: animatedTabBarHeight.value,
      overflow: "hidden",
    }
  }, [animatedTabBarHeight.value])

  const bgStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      tabContentPy.value,
      [0, STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT],
      [color.palette.lightestGrey, BACKGROUND_COLOR],
    )
    return {
      backgroundColor,
    }
  }, [tabContentPy.value])

  const textCopy = useMemo(() => {
    if (!transactionSummaryData || isActiveLandingPage) {
      return ""
    }
    if (!isLoggedIn) {
      return nonLoggedInTextCopy || translate("dineShopScreen.nonLoggedInTextCopy")
    }
    return loggedInTextCopy || translate("dineShopScreen.loggedInTextCopy")
  }, [
    isLoggedIn,
    loggedInTextCopy,
    nonLoggedInTextCopy,
    isActiveLandingPage,
  ])

  const imageBgCoverStyle = useMemo(() => {
    return {
      backgroundColor: BACKGROUND_COLOR,
      height: measuredPromoHeight,
      left: 0,
      marginTop: Number(bannerImageStyle.height),
      position: "absolute",
      top: 0,
      width: screenWidth,
    } as ViewStyle
  }, [measuredPromoHeight, bannerImageStyle.height])

  const handlePromoPerkLayout = (evt) => {
    let newHeight = evt?.nativeEvent?.layout?.height
    newHeight =
      newHeight > MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT
        ? newHeight
        : MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT
    setMeasuredPromoHeight((oldValue) => {
      return newHeight !== oldValue ? newHeight : oldValue
    })
  }

  const handleToggleExpandBtnVisible = (isEnable) => {
    if (!isEnable) {
      animatedViewAllPromosOpacity.value = withTiming(0)
      return
    }
    animatedViewAllPromosOpacity.value = 1
  }

  const handleExpandPromoPerk = () => {
    trackAction(AdobeTagName.EPICDineAndShop, {
      [AdobeTagName.EPICDineAndShop]: "View All Promotions",
    })
    if (animatedViewAllPromosOpacity.value !== 1) return
    handleToggleExpandBtnVisible(false)
    animatedHeight.value = withTiming(measuredPromoHeight)
  }

  const resetScrollingState = () => {
    setTimeout(() => {
      animatedHeight.value = withTiming(MAXIMUM_INITIAL_PROMO_PERK_VISIBLE_HEIGHT, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      })
      animatedTabContentOpacity.value = withTiming(1, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      })
    }, 500)
  }

  const onScroll = (e) => {
    const offsetY = e?.nativeEvent?.contentOffset?.y
    handleScroll(e)
    route?.params?.setOptions?.({ tabBarVisible: isTabVisible.current })
    if (offsetY >= TOP_BAR_VISIBLE_TRIGGER_POINT) {
      setStatusBarStyle("dark-content")
    } else {
      setStatusBarStyle("light-content")
    }
    tabContentScrollRef.current?.measure?.((_fx, _fy, _width, _height, _px, py) => {
      tabContentPy.value = py
      if (py <= STICKY_TAB_BAR_VISIBLE_TRIGGER_POINT) {
        animatedTabBarHeight.value = NO_PADDING_TAB_BAR_HEIGHT
      } else {
        animatedTabBarHeight.value = 0
      }
    })
    pageOffsetY.value = offsetY
  }
  const isFirstMountDone = useRef(false)
  const populatePosition = () => {
    if (!isFirstMountDone.current) {
      isFirstMountDone.current = true
      if (!screen) return
    }
    contentRef?.current?.measureLayout?.(
      overallScrollRef?.current?.getInnerViewNode(),
      (x: number, y: number) => {
        // Scroll to the position of the ref
        overallScrollRef.current.scrollTo({ y: y - NO_PADDING_TAB_BAR_HEIGHT - TAB_BAR_PADDING_N_REFINEMENT, animated: true })
      },
    )
  }

  const handleScrollToTab = (newTab) => {
    const indexToScroll = TAB_BAR_CONFIG.findIndex((config) => config.name === newTab)
    if (_isNumber(indexToScroll)) {
      tabContentScrollRef?.current?.scrollTo?.({
        animated: true,
        x: indexToScroll * screenWidth,
      })
    }
    if (!_isNumber(indexToScroll)) {
      return
    }
    if (indexToScroll === 0) {
      contentHeight.value = withSpring(dineHeight.value + BOTTOM_SPACING)
    } else if (indexToScroll === 1) {
      contentHeight.value = withSpring(shopHeight.value + BOTTOM_SPACING)
    } else {
      contentHeight.value = withSpring(marketHeight.value + BOTTOM_SPACING)
    }
    populatePosition()
    indexTab(newTab)
    if (TAB_BAR_CONFIG?.[indexToScroll]?.labelTx) {
      trackAction(AdobeTagName.CAppShopDineTopMenuToggle, {
        [AdobeTagName.CAppShopDineTopMenuToggle]: translate(TAB_BAR_CONFIG?.[indexToScroll]?.labelTx),
      })
    }
  }

  const indexTab = (name: string) => {
    console.log(`indexTab: ${name}`)
    if (loadedScreen.includes(name)) {
      return;
    }
    InteractionManager.runAfterInteractions(() => {
      setLoadedScreen(loaded => {
        const after = [...loaded, name]
        return after
      })
    })
  }

  const handlePressParkingCMTile = (item) => {
    trackAction(AdobeTagName.EPICDineAndShop, {
      [AdobeTagName.EPICDineAndShop]: `Pre-spend | ${item?.title} | ${item?.tag} | Null`,
    })
    const navigationData = item?.cta?.navigationLink
    if (navigationData?.value) {
      handleNavigation(navigationData?.type, navigationData?.value, item?.cta?.aemUtmRedirect)
    }
  }

  const handlePressPPTile = (item) => {
    trackAction(AdobeTagName.EPICDineAndShop, {
      [AdobeTagName.EPICDineAndShop]: `Pre-spend | ${item?.name} | ${item?.header} | Null`,
    })
    if (!item?.code) return
    GlobalLoadingController.showLoading(true)
    getPlayPassUrl(StateCode.PPEVENT, item?.code, {
      entryPoint: PlayPassEntryPoint.DINE_SHOP_EPIC,
      eventName: item?.name,
    })
  }

  const handlePullToRefresh = () => {
    setIsRefresh(true)
    setShowRefreshIndicator(true)
    resetScrollingState()
    handleToggleExpandBtnVisible(true)
  }

  useFocusEffect(
    useCallback(() => {
      if (isFirstTimeFocus?.current) {
        resetScrollingState()
        isFirstTimeFocus.current = false
      }
    }, []),
  )

  useEffect(() => {
    if (!dineShopV2Banner?.uri) return
    const imageSize = calculateBannerImageSize(dineShopV2Banner?.width, dineShopV2Banner?.height)
    setBannerImageStyle((oldStyles) => {
      return {
        ...oldStyles,
        ...imageSize,
      }
    })
  }, [dineShopV2Banner?.uri])

  useEffect(() => {
    if (!dineShopV2Background?.uri) return
    setBgImageStyle((oldStyles) => {
      return {
        ...oldStyles,
        ...calculateImageSize(dineShopV2Background?.width, dineShopV2Background?.height)
      }
    })
  }, [dineShopV2Background?.uri])

  useEffect(() => {
    if (isMounted?.current) {
      isFirstTimeFocus.current = true
      handleToggleExpandBtnVisible(true)
      overallScrollRef?.current?.scrollTo?.({ animated: true, y: 0 })
      animatedHeight.value = INITIAL_VISIBLE_TAB_CONTENT_HEIGHT
    } else {
      isMounted.current = true
    }
    isFirstMountDone.current = false
  }, [isLoggedIn])

  useEffect(() => {
    if (isRefreshFinished && showError) {
      animatedHeight.value = "auto"
      animatedViewAllPromosOpacity.value = 0
    }
  }, [isRefreshFinished, showError])

  const renderLoading = () => {
    return (
      <>
        <DineShopLoadingTiles />
      </>
    )
  }

  const renderParkingPromotion = () => {
    const isMonarch = memberIconInfo?.title === Tier.Monarch || memberIconInfo?.title === Tier.StaffMonarch
    const disclaimer = data?.parkingPerks?.disclaimer
  
    if (isMonarch) return null
    if (!isEmpty(disclaimer)) {
      return (
        <Text
          style={styles.noPromotionDisclaimer}
          text={disclaimer}
        />
      )
    }
    return <DineShopActiveParking data={data} />
  }

  const renderContentActive = () => {
    return (
      <View style={styles.promotionPerkContainerNoPaddingTopStyle}>
        {isActiveLandingPage ? (
          <>
            {renderParkingPromotion()}
            <DineShopPlayPass data={data} />
            {isAccountV2CM24 && isCM && <DineShopChangiMillionaireExperiences data={data} />}
            {isAccountV2CM24 && isLoggedIn && <LuckyDraw data={data?.luckyDrawPerks} />}
            <DineShopOtherAvailable data={data} />
          </>
        ) : (
          <>
            <DineShopStaticTileList
              onPressParkingCMTile={handlePressParkingCMTile}
              onPressPPTile={handlePressPPTile}
              data={data}
            />
            {isAccountV2CM24 && isCM && <DineShopChangiMillionaireExperiences data={data} />}
            {isAccountV2CM24 && isLoggedIn && <LuckyDraw data={data?.luckyDrawPerks} />}
            <View style={{ height: 44 }} />
          </>
        )}
      </View>
    )
  }

  const renderContent = () => {
    if (isTilesFetching) {
      return renderLoading()
    } else {
      //Scenario 2 and 11
      if (showError) {
        return <DineError />
      } else {
        return renderContentActive()
      }
    }
  }

  return (
    <>
      <FocusAwareStatusBar translucent backgroundColor="transparent" barStyle={statusBarStyle} />
      <Animated.View style={[styles.topBarContainerStyle, topBarStyle]}>
        <Text style={styles.topBarTitleTextStyle} tx="dineShopScreen.title" />
        {!isShopDineV2 && <Animated.View style={tabBarStyle}>
          <DineShopTabBar
            animatedScrollX={animatedScrollX}
            handleScrollToTab={handleScrollToTab}
            noPaddingTop
          />
        </Animated.View>}
      </Animated.View>
      <Animated.View style={bgStyle}>
        <View style={styles.bannerImageContainerStyle}>
          <DineImage
            alternativeImageSource={dineShopV2Banner?.uri ? dineShopV2Banner?.uri : DineShopEpicDefaultBanner}
            resizeMode="contain"
            source={dineShopV2Banner?.uri ? { uri: dineShopV2Banner?.uri } : DineShopEpicDefaultBanner}
            style={[bannerImageStyle]}
          />
        </View>
        <ScrollView
          alwaysBounceVertical={false}
          onScroll={onScroll}
          ref={overallScrollRef}
          showsVerticalScrollIndicator={false}
          style={styles.containerStyle}
          refreshControl={
            <RefreshControl
              colors={[color.palette.whiteGrey]}
              onRefresh={handlePullToRefresh}
              progressBackgroundColor="transparent"
              progressViewOffset={52}
              refreshing={showRefreshIndicator}
              tintColor={color.palette.whiteGrey}
            />
          }
        >
          <View>
            <View style={imageBgCoverStyle} />
            <DineImageBackground
              alternativeImageSource={dineShopV2Background?.uri ? dineShopV2Background?.uri : DineShopEpicDefaultBg}
              imageStyle={bgImageStyle}
              resizeMode="contain"
              source={dineShopV2Background?.uri ? { uri: dineShopV2Background?.uri } : DineShopEpicDefaultBg}
              style={styles.bgImageContainerStyle}
              trailing
            >
              {isActiveLandingPage
                ? !showError && <DineShopEarnedForToday isRefreshFinished={isRefreshFinished} data={data} />
                : isShowText && <Text style={styles.textCopyTextStyle} text={textCopy} />}
              <Animated.View style={[styles.promotionPerkWrapperStyle, promoListStyle]}>
                <View onLayout={handlePromoPerkLayout} style={styles.promotionPerkContainerStyle}>
                  {renderContent()}
                </View>
              </Animated.View>
            </DineImageBackground>
          </View>
          <View>
            {!showError && (
              <AnimatedLinearGradient
                colors={["rgba(18, 18, 18, 0)", "rgba(18, 18, 18, 0.5)"]}
                end={{ x: 0, y: 1 }}
                start={{ x: 0, y: 0 }}
                style={[styles.viewAllPromosContainerStyle, viewAllPromosStyle]}
              >
                <MultimediaTouchableOpacity
                  accessibilityLabel={`${SCREEN_NAME}_Btn_ViewAllPromotions`}
                  androidRippleColor="transparent"
                  disabled={!isRefreshFinished}
                  onPress={handleExpandPromoPerk}
                  style={styles.viewAllPromosBtnStyle}
                  testID={`${SCREEN_NAME}_Btn_ViewAllPromotions`}
                >
                  <Text style={styles.viewAllPromosBtnLabelStyle} text="View All Promotions" />
                  <DownArrowGrey />
                </MultimediaTouchableOpacity>
              </AnimatedLinearGradient>
            )}
            <Animated.View ref={contentRef} style={[styles.dineShopPageContent, tabContentStyle]}>
              <DineShopV2TabNavigator
                animatedScrollX={animatedScrollX}
                contentHeight={contentHeight}
                dineHeight={dineHeight}
                handleScrollToTab={handleScrollToTab}
                marketHeight={marketHeight}
                navigation={navigation}
                route={route}
                shopHeight={shopHeight}
                tabContentScrollRef={tabContentScrollRef}
                loadedScreen={loadedScreen}
                indexTab={indexTab}
                overallScrollRef={overallScrollRef}
                isShopDineV2={isShopDineV2}
              />
            </Animated.View>
          </View>
          <View style={{
            backgroundColor: color.palette.lightestGrey,
            width: screenWidth,
            height: screenHeight / 3,
            position: 'absolute',
            bottom: 0,
            left: 0,
            transform: [{
              translateY: screenHeight / 3
            }]
          }}/>
        </ScrollView>
      </Animated.View>
    </>
  )
}

export default DineShopScreenV2
