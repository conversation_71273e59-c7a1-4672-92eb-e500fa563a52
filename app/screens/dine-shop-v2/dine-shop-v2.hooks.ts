import { useDispatch, useSelector } from "react-redux"
import DineActions, { DineSele<PERSON>, landingPageAemRequest } from "app/redux/dineRedux"
import { useContext, useEffect, useMemo, useRef, useState } from "react"
import { ifAllTrue, ifOneTrue } from "app/utils"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { REMOTE_CONFIG_FLAGS, getFeatureFlagInit } from "app/services/firebase/remote-config"
import { DineShopContext } from "app/services/context/dine-shop"
import { useIsFocused } from "@react-navigation/native"
import { useRewardTier } from "app/hooks/useRewardTier"
import { env } from "app/config/env-params"

import path from "app/services/api/apis.json"
import restApi from "app/services/api/request"
import { requestLandingPageAemData } from "app/services/api/page-config"
import AemGroupTwoCreators, { AemGroupTwoSelectors } from "app/redux/aemGroupTwo"

export const useDineShopFlags = () => {
  const { shopDineEpicFFlag } = useContext(DineShopContext)
  const isShopDineEpicV2On = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.SHOPDINE_EPIC, shopDineEpicFFlag)
  return {
    isShopDineEpicV2On,
  }
}

export const useFetchData = () => {
  const dispatch = useDispatch()

  const fetchLandingPageAemData = () => {
    dispatch(AemGroupTwoCreators.dineShopEpicLandingPageFetching())
    requestLandingPageAemData()
  }

  return {
    fetchLandingPageAemData,
  }
}

export const useInitializeData = () => {
  const { memberIconInfo } = useRewardTier()
  const [isRefresh, setIsRefresh] = useState<boolean>(false)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const [showRefreshIndicator, setShowRefreshIndicator] = useState<boolean>(false)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const dineShopEpicLandingPagePayload = useSelector(AemGroupTwoSelectors.dineShopEpicLandingPagePayload)
  const dineShopEpicLandingPageLoading = useSelector(AemGroupTwoSelectors.dineShopEpicLandingPageLoading)
  const dineShopEpicLandingPageError = useSelector(AemGroupTwoSelectors.dineShopEpicLandingPageError)
  const [data, setData] = useState(null);
  const [isLoading, setLoading] = useState(false)
  const [isError, setIsError] = useState(false)
  const dispatch = useDispatch()
  const isFocused = useIsFocused()
  const { fetchLandingPageAemData } = useFetchData()
  const isFirstTimeLoading = useRef<boolean>(true)

  const isRefreshFinished = useMemo(() => {
    const isRequestLandingPageAemFinished =
      !dineShopEpicLandingPageLoading && (dineShopEpicLandingPagePayload || dineShopEpicLandingPageError)
    const isRequestPlayPassTilesFinished =
      !isLoading && (ifOneTrue([data]) || isError)

    return (
      ifAllTrue([
        isRequestLandingPageAemFinished,
        isRequestPlayPassTilesFinished,
      ])
    )
  }, [
    JSON.stringify(dineShopEpicLandingPagePayload),
    dineShopEpicLandingPageLoading,
    dineShopEpicLandingPageError,
    data,
    isError,
    isLoading
  ])

  const initializeData = (refreshing?: boolean) => {
    if (refreshing) {
      fetchLandingPageAemData()
    }
    getData()
  }

  const resetAllData = () => {
    setData(null)
    setLoading(false)
    setIsError(false)
  }

  useEffect(() => {
    setIsRefresh(true)
    return () => {
      resetAllData()
    }
  }, [isLoggedIn])

  useEffect(() => {
    if (isFocused && isRefresh) {
      if (!isFirstTimeLoading.current) {
        initializeData(true)
      } else {
        initializeData()
        isFirstTimeLoading.current = false
      }
    }
  }, [isFocused, isRefresh])

  const getData = async () => {
    setLoading(true)
    try {
      const inputData = {
        vehicleIU: profilePayload?.vehicleIU,
        tierCode: memberIconInfo?.title
      }
      const paramsArray = path.getEpicPerkV2.split(" ")
      const url = env()?.API_GATEWAY_URL + paramsArray[1]
      const response = await restApi({
        url,
        method: paramsArray[0],
        data: inputData,
        headers: { "x-api-key": env()?.X_API_KEY },
      })
      setData(response?.data)
      setIsError(false)
    } catch (error) {
      setIsError(true)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (ifAllTrue([isRefresh, isRefreshFinished])) {
      setIsRefresh(false)
      setShowRefreshIndicator(false)
    }
  }, [isRefreshFinished])

  return {
    isRefreshFinished,
    setIsRefresh,
    setShowRefreshIndicator,
    showRefreshIndicator,
    data,
    isLoading,
    isError
  }
}
