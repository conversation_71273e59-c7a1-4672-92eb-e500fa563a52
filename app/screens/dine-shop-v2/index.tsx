import { memo } from "react"
import { DineShopScreenWrapper } from "../dine-shop"
import DineShopScreenV2 from "./dine-shop-v2"
import { useDineShopFlags } from "./dine-shop-v2.hooks"

const DineShopWrapper = (props) => {
  const {isShopDineEpicV2On} = useDineShopFlags()

  return isShopDineEpicV2On ? <DineShopScreenV2 {...props} /> : <DineShopScreenWrapper {...props} />
}

export default memo(DineShopWrapper)
