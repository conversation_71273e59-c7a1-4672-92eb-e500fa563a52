import { newPresets } from "app/elements/text"
import { Dimensions, StyleSheet, Text, View } from "react-native"
import { SCREEN_NAME, TAB_BAR_CONFIG } from "../dine-shop-v2.constants"
import _get from "lodash/get"
import _isNumber from "lodash/isNumber"
import { color } from "app/theme"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import Animated, {
  Extrapolation,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
} from "react-native-reanimated"
import { memo } from "react"
import { TAB_CONTENT_GAP } from "../containers/dine-shop-v2-tab-navigator"
import { translate } from "app/i18n"

interface PropsType {
  animatedScrollX?: any
  handleScrollToTab?: Function
  noPaddingTop?: boolean
  isShopDineV2?: boolean
}

const AnimatedText = Animated.createAnimatedComponent(Text)

const DineShopTabBar = (props: PropsType) => {
  const { animatedScrollX, handleScrollToTab, noPaddingTop, isShopDineV2 } = props
  const tabNavItemWidth = screenWidth / 3
  const animatedLeft = useSharedValue((screenWidth * 2) / 3)

  const underlineStyle = useAnimatedStyle(() => {
    animatedLeft.value = interpolate(
      animatedScrollX?.value,
      [0, screenWidth * 2],
      [0, (screenWidth * 2) / 3],
      Extrapolation.CLAMP,
    )
    return {
      backgroundColor: color.palette.lightPurple,
      bottom: -1,
      height: 2,
      left: 0,
      position: "absolute",
      width: tabNavItemWidth,
      transform: [{ translateX: animatedLeft.value }],
    }
  }, [animatedScrollX?.value])

  if(isShopDineV2) {
    return null
  }

  return (
    <View style={noPaddingTop ? styles.noPaddingContainerStyle : styles.containerStyle}>
      <View style={styles.tabBarContainerStyle}>
        {TAB_BAR_CONFIG.map((config, index) => {
          const onPress = () => {
            handleScrollToTab(config.name)
          }

          const animatedStyle = useAnimatedStyle(() => {
            const titleColor = interpolateColor(
              animatedScrollX?.value,
              [0, screenWidth + TAB_CONTENT_GAP, (screenWidth + TAB_CONTENT_GAP) * 2],
              TAB_BAR_CONFIG.map((_config, configIdx) =>
                configIdx === index ? color.palette.almostBlackGrey : color.palette.darkGrey999,
              ),
            )
            return {
              color: titleColor,
            }
          })

          return (
            <MultimediaTouchableOpacity
              accessibilityLabel={`${SCREEN_NAME}_TabBar__TouchableNavItem`}
              androidRippleColor="transparent"
              key={config?.name}
              onPress={onPress}
              style={styles.tabNavItemStyle}
              testID={`${SCREEN_NAME}_TabBar__TouchableNavItem`}
            >
              <AnimatedText style={[styles.tabNavItemLabelStyle, animatedStyle]}>
                {translate(config.labelTx)}
              </AnimatedText>
            </MultimediaTouchableOpacity>
          )
        })}
        <Animated.View style={underlineStyle} />
      </View>
    </View>
  )
}

const { width: screenWidth } = Dimensions.get("window")
export const NO_PADDING_TAB_BAR_HEIGHT = 40
// The padding and refinement of the tab bar
export const TAB_BAR_PADDING_N_REFINEMENT = 16 + 16 + 8
const styles = StyleSheet.create({
  containerStyle: {
    height: NO_PADDING_TAB_BAR_HEIGHT + 16,
    paddingTop: 16,
  },
  noPaddingContainerStyle: {
    height: NO_PADDING_TAB_BAR_HEIGHT,
  },
  tabBarContainerStyle: {
    height: 40,
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  tabNavItemStyle: {
    alignItems: "center",
    height: 40,
    justifyContent: "center",
    width: screenWidth / 3,
  },
  tabNavItemLabelStyle: {
    ...newPresets.caption2Bold,
    color: color.palette.darkGrey999,
  },
  tabNavItemLabelActiveStyle: {
    color: color.palette.almostBlackGrey,
  },
})

export default memo(DineShopTabBar)
