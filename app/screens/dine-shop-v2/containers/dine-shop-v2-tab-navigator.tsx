import { Dimensions, LayoutChangeEvent, StyleSheet } from "react-native"
import DineShopTabBar from "../components/dine-shop-tab-bar"
import { memo, MutableRefObject, useEffect, useRef } from "react"
import { NavigationConstants } from "app/utils/constants"
import _get from "lodash/get"
import { <PERSON>lers, DineShopContext } from "app/screens/dine-shop/dine-shop-context"
import Animated, { SharedValue, useAnimatedScrollHandler, useAnimatedStyle, withSpring } from "react-native-reanimated"
import { useSelector } from "react-redux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { DineScreenV2 } from "app/screens/dine-screen-v2"
import { DineShopV2TabContent } from "./dine-shop-v2-tab-navigator-content"

interface PropsType {
  animatedScrollX?: any
  contentHeight: SharedValue<number>
  dineHeight: SharedValue<number>
  handleScrollToTab: Function
  marketHeight: SharedValue<number>
  navigation: any
  route: any
  shopHeight: SharedValue<number>
  tabContentScrollRef: MutableRefObject<any>
  loadedScreen: string[]
  indexTab: (name: string) => void
  overallScrollRef: MutableRefObject<any>
  isShopDineV2: boolean
}

export const BOTTOM_SPACING = 72

const DineShopV2TabNavigator = (props: PropsType) => {
  const {
    animatedScrollX,
    contentHeight,
    dineHeight,
    handleScrollToTab,
    marketHeight,
    route,
    shopHeight,
    tabContentScrollRef,
    loadedScreen,
    indexTab,
    isShopDineV2
  } = props
  const { screen, timestamp } = _get(route, "params", {})
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const lastTimestampRef = useRef(null)

  // layout measure:
  const onDineLayout = (e: LayoutChangeEvent) => {
    const newHeight = e.nativeEvent.layout.height
    if(newHeight && marketHeight.value !== newHeight){
      dineHeight.value = newHeight
      contentHeight.value = withSpring(newHeight + BOTTOM_SPACING)
    }
  }
  const onShopLayout = (e: LayoutChangeEvent) => {
    const newHeight = e.nativeEvent.layout.height
    if(newHeight && marketHeight.value !== newHeight){
      shopHeight.value = newHeight
      contentHeight.value = withSpring(newHeight + BOTTOM_SPACING)
    }
  }
  const onMarketLayout = (e: LayoutChangeEvent) => {
    const newHeight = e.nativeEvent.layout.height
    if(newHeight && marketHeight.value !== newHeight){
      marketHeight.value = newHeight
      contentHeight.value = withSpring(newHeight + BOTTOM_SPACING)
    }
  }

  const scrollHandler = useAnimatedScrollHandler((event) => {
    animatedScrollX.value = event?.contentOffset?.x
  });

  const scrollViewAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: Math.max(contentHeight.value, screenHeight)
    }
  }) 

  useEffect(() => {
    const hasRouteScreen = screen && timestamp && timestamp !== lastTimestampRef.current
    const timeoutValue = hasRouteScreen ? 2100 : 1
    setTimeout(() => {
      if (hasRouteScreen) {
        lastTimestampRef.current = timestamp
        handleScrollToTab(screen)
        indexTab(screen)
      } else {
        handleScrollToTab(NavigationConstants.marketplace)
        indexTab(NavigationConstants.marketplace)
      }
    }, timeoutValue)
  }, [screen, timestamp, isLoggedIn])

  return (
    <DineShopContext.Provider value={{ Handlers }}>
      <DineShopTabBar animatedScrollX={animatedScrollX} handleScrollToTab={handleScrollToTab} isShopDineV2={isShopDineV2} />
      {isShopDineV2 ? <DineScreenV2 /> : <Animated.ScrollView
        horizontal
        onScroll={scrollHandler}
        scrollEnabled={false}
        pagingEnabled
        ref={tabContentScrollRef}
        style={[styles.tabBarWrapperViewStyle, scrollViewAnimatedStyle]}
        showsHorizontalScrollIndicator={false}
      >
        <DineShopV2TabContent
          loadedScreen={loadedScreen}
          props={props}
          onDineLayout={onDineLayout}
          onShopLayout={onShopLayout}
          onMarketLayout={onMarketLayout}
          styles={styles}
        />
      </Animated.ScrollView>}
    </DineShopContext.Provider>
  )
}

const { height: screenHeight, width: screenWidth } = Dimensions.get("window")
export const TAB_CONTENT_GAP = 8
const styles = StyleSheet.create({
  tabBarWrapperViewStyle: {
    minHeight: screenHeight - 114,
    gap: TAB_CONTENT_GAP,
    width: screenWidth,
  },
  tabContentStyle: {
    width: screenWidth,
  },
  emptyContentStyle: {
    height: screenHeight - 114,
    width: screenWidth,
  },
})

export default memo(DineShopV2TabNavigator)
