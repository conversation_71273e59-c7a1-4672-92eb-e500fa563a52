import { View } from "react-native"
import { NavigationConstants } from "app/utils/constants"
import DineScreen from "app/screens/dine-shop/dine-screen"
import ShopScreen from "app/screens/dine-shop/shop-screen"
import MarketPlaceScreen from "app/screens/dine-shop/market-place-screen"

export const DineShopV2TabContent = ({
  loadedScreen,
  props,
  onDineLayout,
  onShopLayout,
  onMarketLayout,
  styles,
}) => (
  <>
    <View style={styles.tabContentStyle}>
      {loadedScreen.includes(NavigationConstants.dine) && (
        <DineScreen {...props} onLayout={onDineLayout} />
      )}
    </View>
    <View style={styles.tabContentStyle}>
      {loadedScreen.includes(NavigationConstants.shop) && (
        <ShopScreen {...props} onLayout={onShopLayout} />
      )}
    </View>
    <View style={styles.tabContentStyle}>
      {loadedScreen.includes(NavigationConstants.marketplace) && (
        <MarketPlaceScreen {...props} onLayout={onMarketLayout} />
      )}
    </View>
  </>
)
