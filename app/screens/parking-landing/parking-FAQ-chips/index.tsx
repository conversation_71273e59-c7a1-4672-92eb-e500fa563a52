import React from 'react';
import { View, StyleSheet, TouchableOpacity, Platform, Image } from 'react-native';
import MarqueeViewRevert from 'app/components/marquee-view-revert'
import MarqueeView from 'app/components/marquee-view';
import { color, typography } from 'app/theme';
import { Text } from 'app/elements/text';
import { mappingUrlAem } from 'app/utils';
import { COMPONENT_NAME } from '../parking-landing.constants';
import { FAQLoading } from './loading';
import { NavigationConstants } from 'app/utils/constants';
import { AdobeTagName, trackAction } from 'app/services/adobe';

const timeDefault = 40000
const timeEachItem = 8000

const ParkingFAQChips = React.memo((props: any) => {
  const { chipsError, listChipsFetching, chipsPayload, navigation, parkingLandingTrackValue } = props;

  if (listChipsFetching) {
    return <FAQLoading />
  }

  const handleClickItem = (item) => {
    const dataToBeSent = `${parkingLandingTrackValue} | FAQs | ${item?.question}`
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: dataToBeSent,
    })
    navigation.navigate(NavigationConstants.FAQLanding, { url: `${item?.cta?.navigation?.value}` })
  }

  const renderTime = (data) => {
    if (data) {
      return data?.length * timeEachItem
    } else {
      return timeDefault
    }
  }

  return (
    <>
      {chipsError || !chipsPayload ? null : <View style={styles.container}>
        {chipsPayload?.dataTop?.length > 0 && <MarqueeViewRevert duration={renderTime(chipsPayload?.dataTop)}
        >
          <View style={styles.viewContentMarquee}>
            {chipsPayload?.dataTop?.map((item) => {
              return (
                <TouchableOpacity style={styles.itemMarquee} key={`MarqueeItem_${item?.sequenceNumber}`}
                  onPress={() => handleClickItem(item)}
                >
                  {item?.icon && <Image
                    accessibilityLabel={`${COMPONENT_NAME}__MarqueeItem___Icon`}
                    source={{ uri: mappingUrlAem(item?.icon) }}
                    style={styles.itemIconStyle}
                    testID={`${COMPONENT_NAME}__MarqueeItem___Icon`}
                    resizeMode='contain'
                  />}
                  <Text style={styles.txtQuestion} testID={`${COMPONENT_NAME}__MarqueeItem___Question`}>{item?.question}</Text>
                </TouchableOpacity>
              )
            })}
          </View>
        </MarqueeViewRevert>}
        {chipsPayload?.dataBottom?.length > 0 && <MarqueeView duration={renderTime(chipsPayload?.dataBottom)}>
          <View style={styles.viewContentMarquee}>
            {chipsPayload?.dataBottom?.map((item) => {
              return (
                <TouchableOpacity style={styles.itemMarquee} key={`MarqueeItem_${item?.sequenceNumber}`}
                  onPress={() => handleClickItem(item)}
                >
                  {item?.icon && <Image
                    accessibilityLabel={`${COMPONENT_NAME}__MarqueeItem___Icon`}
                    source={{ uri: mappingUrlAem(item?.icon) }}
                    style={styles.itemIconStyle}
                    testID={`${COMPONENT_NAME}__MarqueeItem___Icon`}
                    resizeMode='contain'
                  />}
                  <Text style={styles.txtQuestion} testID={`${COMPONENT_NAME}__MarqueeItem___Question`}>{item?.question}</Text>
                </TouchableOpacity>
              )
            })}
          </View>
        </MarqueeView>}
      </View>}
    </>
  )
});

const styles = StyleSheet.create({
  container: {
    marginTop: 50,
    gap: 12
  },
  viewContentMarquee: {
    height: 32,
    flexDirection: 'row'
  },
  itemMarquee: {
    flexDirection: 'row',
    height: 32,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
    borderRadius: 99,
    marginHorizontal: 4,
    alignItems: 'center',
    paddingHorizontal: 16
  },
  txtQuestion: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 16,
  },
  itemIconStyle: {
    width: 16,
    height: 16,
    marginRight: 8
  }
})

export { ParkingFAQChips }