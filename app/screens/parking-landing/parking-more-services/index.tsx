import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TitleCopy } from './title-copy';
import { MoreServicesError } from './more-services-error';
import { MoreServicesItem } from './more-services-item';
import MoreServicesSkeleton from "./more-services-loading";
import GlobalLoadingController from "app/components/global-loading/global-loading-controller";
import { PARKING_HEADER_HEIGHT } from '../constants';

const ParkingMoreServices = React.memo((props: any) => {
  const {
    moreServicesFetching,
    moreServicesPayload,
    moreServicesError,
    fetchMoreServices,
    parkingMoreServicesSectionOffsetY,
    parkingLandingTrackValue,
  } = props

  const reTryCallApi = () => {
    GlobalLoadingController.showLoading(true)
    fetchMoreServices(GlobalLoadingController.hideLoading, true)
  }

  return (
    <View
      onLayout={evt => {
        parkingMoreServicesSectionOffsetY.value = evt.nativeEvent.layout.y - PARKING_HEADER_HEIGHT
      }}
      style={styles.container}
    >
      {(moreServicesPayload?.data?.length > 0 || moreServicesFetching || moreServicesError) && <TitleCopy isError={moreServicesError} />}
      <View style={moreServicesError ? styles.viewContent : styles.viewContentMargin}>
        {moreServicesFetching && <MoreServicesSkeleton />}
        {!moreServicesFetching && moreServicesPayload?.data?.map((item, index) => {
          return (
            <MoreServicesItem
              item={item}
              index={index}
              key={`moreServicesPayload_${index}`}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
          )
        })}
        {moreServicesError && !moreServicesFetching && <MoreServicesError reTryCallApi={reTryCallApi} />}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginTop: 50,
  },
  viewContent: {
    paddingHorizontal: 16,
  },
  viewContentMargin: {
    paddingHorizontal: 16,
    marginTop: 16
  }
})

export { ParkingMoreServices }