import { Text } from 'app/elements/text';
import { color, typography } from 'app/theme';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { COMPONENT_NAME, MoreServicesStatus, MoreServicesType } from '../parking-landing.constants';
import BaseImage from 'app/elements/base-image/base-image';
import { useNavigation } from '@react-navigation/native';
import { NavigationConstants } from 'app/utils/constants';
import { useHandleNavigation } from 'app/utils/navigation-helper';
import { AdobeTagName, trackAction } from 'app/services/adobe';

const MoreServicesItem = React.memo((props: any) => {
  const navigation = useNavigation<any>();
  const { handleNavigation } = useHandleNavigation()
  const { item, index, parkingLandingTrackValue } = props

  const handleOnclick = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | More Services | ${item?.title}`,
    })
    switch (item?.type) {
      case MoreServicesType.AEM:
        handleNavigation(item?.navigation?.type, item?.navigation?.value)
        break
      case MoreServicesType.SHOP:
        navigation.navigate(NavigationConstants.shopDetailsScreen, {
          tenantId: item?.id,
          name: "",
        })
        break
      case MoreServicesType.DINE:
        navigation.navigate(NavigationConstants.restaurantDetailScreen, {
          tenantId: item?.id,
          name: "",
        })
        break
    }
  }

  const renderStatus = () => {
    if (item?.openingStatus === MoreServicesStatus.Open) {
      return <Text style={styles.txtStatusGreen} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Status_Open`}>{item?.openingStatus}</Text>
    } else if (item?.openingStatus === MoreServicesStatus.OpenSomeOutlets) {
      return <View style={styles.viewSomeOutLet}>
        <Text style={styles.txtStatusGreen}>Open</Text>
        <Text style={[styles.txtStatusGray, { color: color.palette.darkGrey999 }]}>Some Outlets</Text>
      </View>
    } else {
      return <Text style={styles.txtStatusGray} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Status_Closed`}>{item?.openingStatus}</Text>
    }
  }

  return (
    <TouchableOpacity style={styles.container} onPress={handleOnclick}>
      <BaseImage
        accessibilityLabel={`${COMPONENT_NAME}__MoreServices_Item__${index}__Icon`}
        source={{ uri: item?.image }}
        style={styles.image}
        testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Icon`}
      />
      <View style={styles.viewContent}>
        <View style={styles.viewText}>
          <Text style={styles.txtTitle} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Title`}>{item?.title}</Text>
          {item?.locationDisplayText?.length > 0 && <View style={styles.viewRow}>
            <Text style={styles.txtGate} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Gate`}>{item?.locationDisplayText}</Text>
            {item?.area?.length > 0 && <Text style={styles.txtStatusGate} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__GateStatus`}> · {item?.area}</Text>}
          </View>}
          {item?.category && <Text style={styles.txtService} testID={`${COMPONENT_NAME}__MoreServices_Item__${index}__Service`}>{item?.category}</Text>}
        </View>
        {item?.openingStatus?.length > 0 && renderStatus()}
      </View>
    </TouchableOpacity>
  )
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    marginTop: 12,
  },
  image: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 16
  },
  viewContent: {
    flex: 1,
    flexDirection: 'row',
    paddingBottom: 24,
    borderColor: color.palette.lighterGrey,
    borderBottomWidth: 1
  },
  viewText: {
    flex: 1,
  },
  txtStatusGreen: {
    fontFamily: typography.bold,
    color: color.palette.basegreen,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 15,
    marginLeft: 16
  },
  txtStatusGray: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 15,
    marginLeft: 16
  },
  txtTitle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
  },
  viewRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4
  },
  txtGate: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  txtStatusGate: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
  },
  txtService: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
    marginTop: 12
  },
  viewSomeOutLet: {
    alignItems: 'flex-end',
    gap: 2
  }
})

export { MoreServicesItem }