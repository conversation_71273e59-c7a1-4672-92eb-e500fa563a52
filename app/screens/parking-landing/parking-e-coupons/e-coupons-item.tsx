import BaseImage from 'app/elements/base-image/base-image';
import React, { useRef, useEffect } from 'react';
import { Platform, StyleSheet, View, TouchableOpacity } from 'react-native';
import { COMPONENT_NAME } from '../parking-landing.constants';
import { Text } from 'app/elements/text';
import { color, typography } from 'app/theme';
import { ECouponIcon } from 'assets/icons';
import { NavigationValueDeepLink, useHandleNavigation } from 'app/utils/navigation-helper';
import { NavigationTypeEnum } from 'app/redux/types/explore/navigation-type';
import { useSelector } from 'react-redux';
import { ProfileSelectors } from 'app/redux/profileRedux';
import { useModal } from 'app/hooks/useModal';
import { AdobeTagName, trackAction } from 'app/services/adobe';

const EcouponsItem = React.memo((props: any) => {
  const { item, index, list, refreshData, eCouponStateCodeRef, parkingLandingTrackValue } = props

  const { handleNavigation } = useHandleNavigation()
  const { openModal: openEditVehicleIUModal } = useModal("parkingEntitlements")

  const profilePayload = useSelector(ProfileSelectors.profilePayload)

  const onPressEcouponItem = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Parking eCoupon | ${item?.title}`,
    })

    if (profilePayload && !profilePayload?.vehicleIU) {
      eCouponStateCodeRef.current = item?.state_code
      openEditVehicleIUModal()
      return
    }

    handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.carpass, {
      refreshData,
      stateCode: item?.state_code,
      noVehicleIUcallback: () => {
        eCouponStateCodeRef.current = item?.state_code
        openEditVehicleIUModal()
      },
    })
  }

  useEffect(() => {
    if (profilePayload?.vehicleIU && eCouponStateCodeRef.current) {
      handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.carpass, {
        refreshData,
        stateCode: eCouponStateCodeRef.current,
      })
      eCouponStateCodeRef.current = ""
    }
  }, [profilePayload?.vehicleIU])

  return (
    <>
      <TouchableOpacity onPress={onPressEcouponItem} style={styles.container}>
        <BaseImage
          accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__Icon`}
          resizeMode="contain"
          source={{ uri: item?.image }}
          style={styles.iconStyle}
          testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__Icon`}
        />
        <View style={styles.viewContent}>
          <Text
            accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy1`}
            style={styles.titleTextStyle}
            testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy1`}
            text={item?.highlightCopy1}
          />
          <Text
            accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__Title`}
            style={styles.highlightCopy1}
            testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__Title`}
            text={item?.title}
          />
          <View style={styles.viewRow}>
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__current_price`}
              style={styles.current_price}
              testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__current_price`}
              text={item?.current_price}
            />
            {item?.actual_price && <Text
              accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__actual_price`}
              style={styles.actual_price}
              testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__actual_price`}
              text={item?.actual_price}
            />}
          </View>
          {item?.highlightCopy2 && <View style={[styles.viewRow, { marginTop: 4 }]}>
            <ECouponIcon />
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy2`}
              style={styles.highlightCopy2}
              testID={`${COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy2`}
              text={item?.highlightCopy2}
            />
          </View>}
        </View>
      </TouchableOpacity>
      {list?.length - 1 !== index && <View style={styles.viewBottom} />}
    </>
  );
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    gap: 16
  },
  iconStyle: {
    width: 80,
    height: 80
  },
  viewContent: {
    flex: 1,
  },
  titleTextStyle: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  },
  highlightCopy1: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    marginTop: 4,
    marginBottom: 16
  },
  viewRow: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  current_price: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
    marginRight: 4
  },
  actual_price: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
    textDecorationLine: 'line-through',
    marginBottom: 1
  },
  highlightCopy2: {
    fontFamily: typography.bold,
    color: color.palette.lightOrange,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 14,
    marginLeft: 4
  },
  viewBottom: {
    width: '100%',
    height: 1,
    backgroundColor: color.palette.lighterGrey,
    marginVertical: 16
  }
});

export { EcouponsItem }