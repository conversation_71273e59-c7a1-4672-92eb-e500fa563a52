import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TitleCopy } from './title-copy';
import ECouponsLoadingSkeleton from './e-coupons-loading';
import { EcouponsItem } from './e-coupons-item';
import { ECouponsError } from './e-coupons-error';
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { PARKING_HEADER_HEIGHT } from '../constants';

const ParkingEcoupon = React.memo((props: any) => {
  const {
    refreshData,
    eCouponsFetching,
    eCouponsPayload,
    eCouponsPayloadError,
    fetchEcoupons,
    eCouponStateCodeRef,
    parkingECouponSectionOffsetY,
    parkingLandingTrackValue,
  } = props

  const reTryCallApi = () => {
    GlobalLoadingController.showLoading(true)
    fetchEcoupons(GlobalLoadingController.hideLoading, true)
  }

  return (
    <View
      onLayout={(evt) => {
        parkingECouponSectionOffsetY.value = evt.nativeEvent.layout.y - PARKING_HEADER_HEIGHT
      }}
      style={styles.container}
    >
      {(eCouponsPayload?.data?.length > 0 || eCouponsFetching || eCouponsPayloadError) && <TitleCopy isError={eCouponsPayloadError} />}
      <View style={eCouponsPayloadError ? styles.viewContent : styles.viewContentMargin}>
        {eCouponsFetching && <ECouponsLoadingSkeleton />}
        {!eCouponsFetching && eCouponsPayload?.data?.map((item, index) => {
          return (
            <EcouponsItem
              item={item}
              index={index}
              refreshData={refreshData}
              list={eCouponsPayload?.data}
              key={`eCouponsPayload_${index}`}
              eCouponStateCodeRef={eCouponStateCodeRef}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
          )
        })}
        {!eCouponsFetching && eCouponsPayloadError && <ECouponsError reTryCallApi={reTryCallApi} />}
      </View>
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    marginTop: 50,
  },
  viewContent: {
    paddingHorizontal: 16,
  },
  viewContentMargin: {
    paddingHorizontal: 16,
    marginTop: 16
  }
})

export { ParkingEcoupon }