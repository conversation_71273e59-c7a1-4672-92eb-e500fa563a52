import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import BaseImage from "app/elements/base-image/base-image"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"
import { DotWoColor, ParkingRebate, ParkNFlyECouponIcon, ParkNWorkECouponIcon } from "assets/icons"
import { StyleSheet, View } from "react-native"
import DashedLine from "react-native-dashed-line"
import { COMPONENT_NAME } from "../parking-landing.constants"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { useSelector } from "react-redux"
import { useNavigation } from "app/utils/navigation-helper"
import { NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { ProfileSelectors } from "app/redux/profileRedux"
import { MutableRefObject, useEffect } from "react"
import Animated, {
  Easing,
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated"
import { ParkingECouponType } from "../parking-landing.constants"
import { toNumber } from "app/utils"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { trackAction, AdobeTagName } from "app/services/adobe"
import { translate } from "app/i18n"

interface PropsType {
  openEditVehicleIUModal?: () => void
  parkingECouponSectionOffsetY?: SharedValue<number>
  parkingLandingCardPayload?: any
  refreshData?: () => void
  scrollRef?: MutableRefObject<any>
  parkingLandingCardFetching?: boolean
  isError?: boolean
  parkingLandingTrackValue?: string
}

const LoadingSkeleton = () => {
  return (
    <View style={styles.parkNXContainerStyle}>
      <View style={styles.parkNFlyContainerStyle}>
        <BaseImage source={ParkingRebate} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerStyle={styles.parkNFlyValueShimmerStyle}
            shimmerColors={color.shimmerPlacholderColorLighter}
          />
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNFlyLabel"
          />
        </View>
      </View>
      <DashedLine
        axis="vertical"
        dashColor={color.palette.lightestPurple}
        dashGap={3}
        dashThickness={1}
        dashLength={2}
        style={styles.dividerStyle}
      />
      <View style={[styles.parkNFlyContainerStyle, styles.parkNWorkContainerStyle]}>
        <BaseImage source={ParkingRebate} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerStyle={styles.parkNFlyValueShimmerStyle}
            shimmerColors={color.shimmerPlacholderColorLighter}
          />
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNWorkLabel"
          />
        </View>
      </View>
    </View>
  )
}

const ErrorOverlay = () => {
  return (
    <View style={styles.parkNXContainerStyle}>
      <View style={styles.parkNFlyContainerStyle}>
        <BaseImage source={ParkingRebate} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <Text
              style={styles.refreshToReload}
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              testID={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              tx="parkingLanding.errors.refreshToReload"
            />
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNFlyLabel"
          />
        </View>
      </View>
      <DashedLine
        axis="vertical"
        dashColor={color.palette.lightestPurple}
        dashGap={3}
        dashThickness={1}
        dashLength={2}
        style={styles.dividerStyle}
      />
      <View style={[styles.parkNFlyContainerStyle, styles.parkNWorkContainerStyle]}>
        <BaseImage source={ParkingRebate} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <Text
              style={styles.refreshToReload}
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              testID={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              tx="parkingLanding.errors.refreshToReload"
            />
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNWorkLabel"
          />
        </View>
      </View>
    </View>
  )
}

const ParkNXECoupons = (props: PropsType) => {
  const {
    openEditVehicleIUModal,
    parkingECouponSectionOffsetY,
    parkingLandingCardPayload,
    refreshData,
    scrollRef,
    parkingLandingCardFetching,
    isError,
    parkingLandingTrackValue,
  } = props
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const profileData = useSelector(ProfileSelectors.profilePayload)
  const navigation = useNavigation()
  const parkNFlyData = parkingLandingCardPayload?.coupons?.find?.(
    (item) => item?.couponType === ParkingECouponType.ParkNFly,
  )
  const parkNWorkData = parkingLandingCardPayload?.coupons?.find?.(
    (item) => item?.couponType === ParkingECouponType.ParkNWork,
  )
  const shouldShowError = isError || (!parkingLandingCardPayload?.coupons?.length && isLoggedIn)
  const totalParkNFlyECoupons =
    toNumber(parkNFlyData?.numActivatedCoupons) + toNumber(parkNFlyData?.numDeactivatedCoupons)
  const hasActiveParkNFlyECoupons = !!parkNFlyData?.activated
  const totalParkNWorkECoupons =
    toNumber(parkNWorkData?.numActivatedCoupons) + toNumber(parkNWorkData?.numDeactivatedCoupons)
  const hasActiveParkNWorkECoupons = !!parkNWorkData?.activated
  const opacityValue = useSharedValue(1)
  const blingAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacityValue.value,
    }
  }, [opacityValue.value])

  const handlePressParkNXECoupons = (eCouponType?: string) => {
    const ctaTrackingValue = eCouponType === ParkingECouponType.ParkNFly ? translate("parkingLanding.parkNFlyLabel") : translate("parkingLanding.parkNWorkLabel")
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Top section | ${ctaTrackingValue}`,
    })

    if (!isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => {
          refreshData?.()
        },
        sourceSystem: SOURCE_SYSTEM.OTHERS,
      })
      return
    }
    if (!profileData?.vehicleIU) {
      openEditVehicleIUModal()
      return
    }
    let screen = ""
    if (eCouponType === ParkingECouponType.ParkNFly) {
      if (hasActiveParkNFlyECoupons) {
        screen = NavigationConstants.ParkingLandingBenefitActive
      } else if (totalParkNFlyECoupons > 0) {
        screen = NavigationConstants.ParkingLandingBenefitInActive
      }
      if (screen) {
        navigation.navigate(NavigationConstants.ParkingLandingBenefit, {
          screen,
        })
        return
      }
    } else if (eCouponType === ParkingECouponType.ParkNWork) {
      if (hasActiveParkNWorkECoupons) {
        screen = NavigationConstants.ParkingLandingBenefitActive
      } else if (totalParkNWorkECoupons > 0) {
        screen = NavigationConstants.ParkingLandingBenefitInActive
      }
      if (screen) {
        navigation.navigate(NavigationConstants.ParkingLandingBenefit, {
          screen,
        })
        return
      }
    }
    scrollRef?.current?.scrollTo({
      y: parkingECouponSectionOffsetY.value,
      animated: true,
    })
  }

  useEffect(() => {
    // Start the pulsating animation for dot icon
    opacityValue.value = withRepeat(withTiming(0.2, { duration: 500, easing: Easing.quad }), -1, true)
  }, [])

  if (parkingLandingCardFetching) {
    return <LoadingSkeleton />
  }

  if (shouldShowError) {
    return <ErrorOverlay />
  }

  return (
    <View style={styles.parkNXContainerStyle}>
      <MultimediaTouchableOpacity
        accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement__ParkNFlyECoupon`}
        androidRippleColor="transparent"
        onPress={() => handlePressParkNXECoupons(ParkingECouponType.ParkNFly)}
        style={styles.parkNFlyContainerStyle}
        testID={`${COMPONENT_NAME}__ParkingEntitlement__ParkNFlyECoupon`}
      >
        <BaseImage source={ParkNFlyECouponIcon} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <View style={styles.parkNFlyValueContainerStyle}>
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              style={styles.parkNFlyValueTextStyle}
              testID={`${COMPONENT_NAME}__ParkingEntitlement_ParkNFlyECoupon__TotalValue`}
              text={`${totalParkNFlyECoupons}`}
            />
            {hasActiveParkNFlyECoupons && (
              <View style={styles.parkNFlyActivateTagContainerStyle}>
                <Animated.View style={blingAnimatedStyle}>
                  <DotWoColor color={color.palette.basegreen} height={4} width={4} />
                </Animated.View>
                <Text style={styles.parkNFlyActivateTagTextStyle} tx="common.activated" />
              </View>
            )}
          </View>
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNFlyLabel"
          />
        </View>
      </MultimediaTouchableOpacity>
      <DashedLine
        axis="vertical"
        dashColor={color.palette.lightestPurple}
        dashGap={3}
        dashThickness={1}
        dashLength={2}
        style={styles.dividerStyle}
      />
      <MultimediaTouchableOpacity
        accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement__ParkNWorkECoupon`}
        androidRippleColor="transparent"
        onPress={() => handlePressParkNXECoupons(ParkingECouponType.ParkNWork)}
        style={[styles.parkNFlyContainerStyle, styles.parkNWorkContainerStyle]}
        testID={`${COMPONENT_NAME}__ParkingEntitlement__ParkNWorkECoupon`}
      >
        <BaseImage source={ParkNWorkECouponIcon} style={styles.parkNFlyIconStyle} />
        <View style={styles.parkNFlyRightSideContainerStyle}>
          <View style={styles.parkNFlyValueContainerStyle}>
            <Text
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement_ParkNWorkECoupon__TotalValue`}
              style={styles.parkNFlyValueTextStyle}
              testID={`${COMPONENT_NAME}__ParkingEntitlement_ParkNWorkECoupon__TotalValue`}
              text={`${totalParkNWorkECoupons}`}
            />
            {hasActiveParkNWorkECoupons && (
              <View style={styles.parkNFlyActivateTagContainerStyle}>
                <Animated.View style={blingAnimatedStyle}>
                  <DotWoColor color={color.palette.basegreen} height={4} width={4} />
                </Animated.View>
                <Text style={styles.parkNFlyActivateTagTextStyle} tx="common.activated" />
              </View>
            )}
          </View>
          <Text
            numberOfLines={2}
            style={styles.parkNFlyLabelTextStyle}
            tx="parkingLanding.parkNWorkLabel"
          />
        </View>
      </MultimediaTouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  parkNXContainerStyle: {
    alignItems: "stretch",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  parkNFlyContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    gap: 12,
    paddingRight: 20,
    width: "50%",
  },
  parkNWorkContainerStyle: {
    flex: 1,
    paddingLeft: 16,
    paddingRight: 0,
  },
  parkNFlyIconStyle: {
    height: 32,
    width: 32,
  },
  parkNFlyRightSideContainerStyle: {
    flex: 1,
    gap: 4,
  },
  parkNFlyValueContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
  },
  parkNFlyValueTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  parkNFlyActivateTagContainerStyle: {
    alignItems: "center",
    borderColor: "#BBE2C1",
    borderRadius: 6,
    borderWidth: 1,
    flexDirection: "row",
    gap: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  parkNFlyActivateTagTextStyle: {
    ...newPresets.XSmallBold,
    color: color.palette.basegreen,
    textTransform: "none",
  },
  parkNFlyLabelTextStyle: {
    ...newPresets.XSmallBold,
    color: color.palette.darkestGrey,
    textTransform: "none",
  },
  dividerStyle: {
    height: 50,
  },
  parkNFlyValueShimmerStyle: {
    width: 40,
    height: 12,
    borderRadius: 4,
  },
  refreshToReload: {
    ...newPresets.XSmallBold,
    textTransform: "none",
    color: color.palette.darkGrey999,
  },
})

export default ParkNXECoupons
