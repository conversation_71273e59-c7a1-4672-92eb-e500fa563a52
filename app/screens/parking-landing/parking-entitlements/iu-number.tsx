import React from "react"
import { View, TouchableOpacity } from "react-native"
import { useSelector } from "react-redux"

import { color } from "app/theme"
import { Edit } from "assets/icons"
import { Text } from "app/elements/text"
import { useRewardTier } from "app/hooks/useRewardTier"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NativeAuthSelectors } from "app/redux/nativeAuthRedux"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { trackAction, AdobeTagName } from "app/services/adobe"

import styles from "./styles"

interface ParkingEntitlementsProps {
  isLoading: boolean
  isInternetError: boolean
  isMaintenanceData: boolean
  onEditVehicleIU: () => void
  parkingLandingTrackValue?: string
}

const SECTION_NAME = "ParkingEntitlements__IUNumber"

const IUNumber: React.FC<ParkingEntitlementsProps> = ({
  isLoading,
  isInternetError,
  isMaintenanceData,
  onEditVehicleIU,
  parkingLandingTrackValue,
}) => {
  const isLoggedIn = useSelector(NativeAuthSelectors.isLoggedIn)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const profileError = useSelector(ProfileSelectors.profileError)
  const profileFetching = useSelector(ProfileSelectors.profileFetching)

  const isError = isInternetError || isMaintenanceData

  const shouldShowError = profileError || isError
  const vehicleIUSection = shouldShowError ? (
    <Text
      style={styles.profileErrorText}
      testID={`${SECTION_NAME}__IUNumberText`}
      tx="parkingLanding.errors.refreshToReload"
    />
  ) : (
    <Text
      style={styles.IUNumberText}
      text={`#${profilePayload?.vehicleIU}`}
      testID={`${SECTION_NAME}__IUNumberText`}
    />
  )

  const onOpenEditVehicleIU = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Top section | Edit IU`,
    })

    onEditVehicleIU?.()
  }

  if (!isLoggedIn && !isMaintenanceData && !isInternetError) {
    return null
  }

  if (profileFetching || isLoading) {
    return (
      <View style={styles.IUNumberElement}>
        <Text
          tx="parkingLanding.forIU"
          style={styles.IUNumberPrefix}
          testID={`${SECTION_NAME}__forIU`}
        />
        <ShimmerPlaceholder
          shimmerStyle={styles.IUNumberShimmer}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={color.shimmerPlacholderColor}
        />
      </View>
    )
  }

  if (!profilePayload?.vehicleIU && !shouldShowError) {
    return null
  }

  return (
    <View style={styles.IUNumberElement}>
      <Text
        tx="parkingLanding.forIU"
        style={styles.IUNumberPrefix}
        testID={`${SECTION_NAME}__forIU`}
      />
      {vehicleIUSection}
      {!shouldShowError && (
        <TouchableOpacity onPress={onOpenEditVehicleIU} testID={`${SECTION_NAME}__editButton`}>
          <Edit width={16} height={16} />
        </TouchableOpacity>
      )}
    </View>
  )
}

export default IUNumber
