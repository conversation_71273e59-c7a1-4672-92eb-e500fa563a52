import React from "react"
import { useSelector } from "react-redux"
import { useNavigation } from "@react-navigation/native"
import LinearGradient from "react-native-linear-gradient"
import { View, TouchableOpacity } from "react-native"

import { color } from "app/theme"
import { Text } from "app/elements/text"
import { useRewardTier } from "app/hooks/useRewardTier"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NavigationConstants, PARKING_BENEFITS_CTA_TYPES, SOURCE_SYSTEM } from "app/utils/constants"

import styles from "./styles"
import TotalDuration from "../components/total-duration"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { translate } from "app/i18n"
import { NativeAuthSelectors } from "app/redux/nativeAuthRedux"
import { trackAction, AdobeTagName } from "app/services/adobe"

const SECTION_NAME = "ParkingBenefits"

const LoadingSkeleton = () => {
  return (
    <View style={styles.parkingBenefitsContainer}>
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={color.shimmerPlacholderColorLighter}
        shimmerStyle={styles.parkingBenefitsTitleShimmer}
      />

      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={color.shimmerPlacholderColorLighter}
        shimmerStyle={styles.parkingBenefitsDurationShimmer}
      />
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={color.shimmerPlacholderColorLighter}
        shimmerStyle={styles.parkingBenefitsMessageShimmer}
      />
    </View>
  )
}

const MaintenanceErrorOverlay = ({maintenanceData}) => {
  const {header, subHeader} = maintenanceData || {}

  const title = header || translate("parkingLanding.errors.systemMaintenance")
  const message = subHeader || translate("parkingLanding.errors.maintenanceMessage")

  const parkingBenefitsErrorMessageStyle = [
    styles.parkingBenefitsMessage,
    { marginTop: 8, marginBottom: 60 }
  ]

  return (
    <View style={styles.parkingBenefitsContainer}>
      <Text text={title} style={styles.parkingBenefitsErrorTitle} />
      <Text text={message} style={parkingBenefitsErrorMessageStyle} />
    </View>
  )
}

const ErrorOverlay = ({ isInternetError }) => {
  const titleTx = isInternetError
    ? "parkingLanding.errors.noInternetConnection"
    : "parkingLanding.errors.unableToRetrieveBenefits"
  const messageTx = isInternetError
    ? "parkingLanding.errors.noInternetMessage"
    : "parkingLanding.errors.unableToRetrieveBenefitsMessage"

  const parkingBenefitsErrorMessageStyle = [
    styles.parkingBenefitsMessage,
    { marginTop: 8, marginBottom: 60 },
  ]

  return (
    <View style={styles.parkingBenefitsContainer}>
      <Text tx={titleTx} style={styles.parkingBenefitsErrorTitle} />
      <Text tx={messageTx} style={parkingBenefitsErrorMessageStyle} />
    </View>
  )
}

const ParkingBenefits = (props) => {
  const {
    scrollRef,
    refreshData,
    isInternetError,
    onEditVehicleIU,
    maintenanceData,
    setIsAllLoading,
    parkingLandingCardError,
    parkingLandingCardPayload,
    parkingLandingCardFetching,
    parkingPromotionsSectionOffsetY,
    parkingLandingTrackValue,
  } = props

  const navigation = useNavigation()
  const { memberIconInfo } = useRewardTier()
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const profileError = useSelector(ProfileSelectors.profileError)
  const isLoggedIn = useSelector(NativeAuthSelectors.isLoggedIn)
  const { ctaType, benefitTile, durationHour, durationMinute, copyBenefitText, ctaButtonTitle } = parkingLandingCardPayload || {}

  const shouldShowError =
    (profileError && isLoggedIn) ||
    !parkingLandingCardPayload ||
    parkingLandingCardError ||
    !benefitTile ||
    !copyBenefitText ||
    !ctaButtonTitle ||
    typeof durationHour !== "number" ||
    typeof durationMinute !== "number"

  const nonLoggedInTitle = isLoggedIn ? "" : translate("parkingLanding.nonLoggedInBenefits.title")
  const nonLoggedInMessage = isLoggedIn
    ? ""
    : translate("parkingLanding.nonLoggedInBenefits.message")
  const nonLoggedInCtaButton = isLoggedIn
    ? ""
    : translate("parkingLanding.nonLoggedInBenefits.button")

  const benefitTileText = benefitTile || nonLoggedInTitle
  const messageText = copyBenefitText || nonLoggedInMessage
  const ctaButtonTitleText = ctaButtonTitle || nonLoggedInCtaButton

  const isNoIU = ctaType === PARKING_BENEFITS_CTA_TYPES.NO_IU
  const isNonLogin = ctaType === PARKING_BENEFITS_CTA_TYPES.NON_LOGIN
  const isViewDetails = ctaType === PARKING_BENEFITS_CTA_TYPES.VIEW_DETAILS
  const isViewParkingPromos = ctaType === PARKING_BENEFITS_CTA_TYPES.VIEW_PARKING_PROMOS

  const handleNavigate = (...args) => {
    navigation.navigate(...(args as never))
  }

  const onPressParkingBenefits = () => {
    const dataToBeSent = `${parkingLandingTrackValue} | Top section | ${ctaButtonTitleText}`
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: dataToBeSent,
    })

    if (isNoIU) {
      onEditVehicleIU()
      return
    }
    if (isNonLogin || !isLoggedIn || !profilePayload) {
      setTimeout(() => {
        setIsAllLoading(true)
      }, 500);
      handleNavigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.OTHERS,
        callBackAfterLoginSuccess: refreshData,
        callBackAfterLoginCancel: () => setIsAllLoading(false),
        callBackAfterLoginWithProfileError: () => setIsAllLoading(false),
      })
      return
    }
    if (isViewParkingPromos) {
      const scrollToY = parkingPromotionsSectionOffsetY.value
      scrollRef?.current?.scrollTo({ y: scrollToY, animated: true })
      return
    }
    if (isViewDetails) {
      handleNavigate(NavigationConstants.ParkingLandingBenefit, {
        screen: NavigationConstants.ParkingLandingBenefitActive,
      })
      return
    }
  }

  if (parkingLandingCardFetching) {
    return <LoadingSkeleton />
  }

  if (isInternetError) {
    return <ErrorOverlay isInternetError />
  }

  if (maintenanceData) {
    return <MaintenanceErrorOverlay maintenanceData={maintenanceData} />
  }

  if (shouldShowError && isLoggedIn) {
    return <ErrorOverlay isInternetError={false} />
  }

  return (
    <View style={styles.parkingBenefitsContainer}>
      <Text style={styles.parkingBenefitsTitle} text={benefitTileText} />

      <TotalDuration
        durationHour={parkingLandingCardPayload?.durationHour}
        durationMinute={parkingLandingCardPayload?.durationMinute}
      />

      <Text text={messageText} style={styles.parkingBenefitsMessage} />

      <LinearGradient
        end={{ x: 1, y: 0 }}
        start={{ x: 0, y: 1 }}
        style={styles.parkingBenefitsGradient}
        colors={memberIconInfo?.benefitsButtonGradient}
      >
        <TouchableOpacity
          onPress={onPressParkingBenefits}
          style={styles.parkingBenefitsButton}
          testID={`${SECTION_NAME}__ButtonParkingBenefits`}
          accessibilityLabel={`${SECTION_NAME}__ButtonParkingBenefits`}
        >
          <Text text={ctaButtonTitleText} style={styles.parkingBenefitsButtonText} />
        </TouchableOpacity>
      </LinearGradient>
    </View>
  )
}

export default ParkingBenefits
