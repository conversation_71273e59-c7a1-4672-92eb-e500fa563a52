import React, { <PERSON><PERSON>RefObject, useEffect, useState, useMemo } from "react"
import { get } from "lodash"
import Toast from "react-native-root-toast"
import { View, Image } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import NetInfo from "@react-native-community/netinfo"

import { color } from "app/theme"
import { translate } from "app/i18n"
import { Text } from "app/elements/text"
import { useModal } from "app/hooks/useModal"
import { InfoRed, ParkedError } from "assets/icons"
import { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { CONTACT_EMAIL, sectionTagName } from "app/utils/constants"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import VehicleDetailsBottomSheet from "./vehicle-details-bottom-sheet"
import ProfileActions, { ProfileSelectors } from "app/redux/profileRedux"
import { PROFILE_UPDATE_ERROR_DETAILS } from "app/screens/profile/constants"
import { useNavigation, useIsFocused } from '@react-navigation/native';
import { handleModeCarPassCsmAPI } from "app/utils/navigation-helper"

import styles from "./styles"
import ParkNXECoupons from "./park-n-x-ecoupons"
import { SharedValue } from "react-native-reanimated"
import MonarchParkingEntitlementContent from "../components/monarch-parking-entitlement-content"
import { useRewardTier } from "app/hooks/useRewardTier"
import ParkingBenefits from "./parking-benefits"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import IUNumber from "./iu-number"
import LottieView from "lottie-react-native"
import { getUriImage } from "app/utils/screen-helper"
import { NativeAuthSelectors } from "app/redux/nativeAuthRedux"

interface ParkingEntitlementsProps {
  stateCode?: string
  isInternetError?: boolean
  callBackUpdateVehicleSuccess?: Function
  callBackUpdateVehicleError?: Function
  parkingECouponSectionOffsetY?: SharedValue<number>
  parkingLandingCardPayload?: any
  eCouponStateCodeRef: React.MutableRefObject<string>
  refreshData?: () => void
  scrollRef?: MutableRefObject<any>
  parkingPromotionsSectionOffsetY?: SharedValue<number>
  parkingEntitlementOffsetY?: SharedValue<number>
  parkingLandingCardError: boolean
  parkingLandingCardFetching: boolean
  setIsAllLoading: (value: boolean) => void
  parkingLandingTrackValue?: string
}

const SECTION_NAME = "ParkingEntitlements"

const parkedCarIcon = require("assets/icons/parking-landing/parked.json")
const notParkedCarIcon = require("assets/icons/parking-landing/not-parked.json")

const convertValueStateCode = (stateCode: string) => {
  return stateCode?.replace(/TIME-\d+/g, "");
}

const ParkedStatusIcon = ({ isParked }) => {
  const source = isParked ? parkedCarIcon : notParkedCarIcon

  return (
    <View style={styles.parkedCarStatusIconContainer}>
      <LottieView autoPlay loop={false} source={source} style={styles.parkedCarStatusIcon} />
    </View>
  )
}

const ParkingEntitlements: React.FC<ParkingEntitlementsProps> = (props) => {
  const dispatch = useDispatch()
  const isFocused = useIsFocused()
  const navigation = useNavigation()
  const {
    stateCode,
    isInternetError,
    eCouponStateCodeRef,
    callBackUpdateVehicleSuccess,
    callBackUpdateVehicleError,
    parkingECouponSectionOffsetY,
    parkingPromotionsSectionOffsetY,
    parkingLandingCardPayload,
    refreshData,
    scrollRef,
    parkingEntitlementOffsetY,
    parkingLandingCardError,
    parkingLandingCardFetching,
    setIsAllLoading,
    parkingLandingTrackValue,
  } = props
  const {
    isModalVisible,
    openModal: openEditVehicleIUModal,
    closeModal: closeEditVehicleIUModal,
  } = useModal("parkingEntitlements")

  const {
    isModalVisible: isErrorModalVisible,
    openModal: openUpdateVehicleErrorModal,
    closeModal: closeUpdateVehicleErrorModal,
  } = useModal("updateVehicleIUError")

  const [isHandleFDL, setIsHandleFDL] = useState(false)

  const errorCommon = useSelector(AemSelectors.getErrorsCommon)
  const messagesCommon = useSelector(AemSelectors.getMessagesCommon)
  const profileError = useSelector(ProfileSelectors.profileError)
  const isLoggedIn = useSelector(NativeAuthSelectors.isLoggedIn)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const profileUpdateFetching: boolean = useSelector(ProfileSelectors.profileUpdateFetching)
  const profileUpdateError: boolean = useSelector(ProfileSelectors.profileUpdateError)
  const profileUpdateErrors = useSelector(ProfileSelectors.profileUpdateErrors)
  const profileUpdateSuccess: boolean = useSelector(ProfileSelectors.profileUpdateSuccess)
  const { isMonarch } = useRewardTier()
  const dataErrorMaintenanceConfig = useSelector(
    AemSelectors.getAemConfig(AEM_PAGE_NAME.GET_ERROR_MAINTENANCE),
  )
  const listErrorMaintenance = get(dataErrorMaintenanceConfig, "data.list", [])
  const maintenanceData = useMemo(() => {
    const data = listErrorMaintenance?.find(
      (obj: any) => obj.sectionName === sectionTagName.parkingEntitlement,
    )
    return data?.enableMode ? data : undefined
  }, [listErrorMaintenance])

  const errorEHR15 = errorCommon?.find((item: any) => item.code === "EHR15")
  const errorEHR29 = errorCommon?.find((item: any) => item.code === "EHR29")
  const errorEHR37 = errorCommon?.find((item: any) => item.code === "EHR37")

  const isInvalidIU = profileUpdateErrors?.changi_rewards === PROFILE_UPDATE_ERROR_DETAILS.INVALID_IU
  const invalidIUErrorHeader = isInvalidIU ? get(errorEHR37, "header") : ""
  const invalidIUErrorSubHeader = isInvalidIU ? get(errorEHR37, "subHeader") : ""
  const invalidIUErrorButtonLabel = isInvalidIU ? get(errorEHR37, "buttonLabel") : ""

  const isExistingIU =
    profileUpdateErrors?.changi_rewards === PROFILE_UPDATE_ERROR_DETAILS.EXISTING_IU
  const existingIUErrorHeader = isExistingIU ? get(errorEHR29, "header") : ""
  const existingIUErrorSubHeader = isExistingIU ? get(errorEHR29, "subHeader") : ""
  const existingIUErrorButtonLabel = isExistingIU ? get(errorEHR29, "buttonLabel") : ""

  const bottomSheetErrorTitle =
    invalidIUErrorHeader || existingIUErrorHeader || get(errorEHR15, "header") || translate("popupError.somethingWrong")
  const bottomSheetErrorMessage =
    invalidIUErrorSubHeader || existingIUErrorSubHeader || get(errorEHR15, "subHeader") || translate("popupError.updateProfileMessage")
  const bottomSheetErrorButtonText =
    invalidIUErrorButtonLabel || existingIUErrorButtonLabel || get(errorEHR15, "buttonLabel") || translate("common.okay")

  const splitedInvalidIUErrorSubHeader = invalidIUErrorSubHeader?.split(CONTACT_EMAIL)
  const invalidIUErrorMessageChildren = isInvalidIU && invalidIUErrorSubHeader?.includes?.(CONTACT_EMAIL) ? (
    <Text style={styles.errorMessageRegular}>
      {splitedInvalidIUErrorSubHeader?.[0]}
      <Text style={styles.errorMessageBold}>{CONTACT_EMAIL}</Text>
      {splitedInvalidIUErrorSubHeader?.[1]}
    </Text>
  ) : undefined

  const splitedExistingIUErrorSubHeader = existingIUErrorSubHeader?.split(CONTACT_EMAIL)
  const existingIUErrorMessageChildren = isExistingIU && existingIUErrorSubHeader?.includes?.(CONTACT_EMAIL) ? (
    <Text style={styles.errorMessageRegular}>
      {splitedExistingIUErrorSubHeader?.[0]}
      <Text style={styles.errorMessageBold}>{CONTACT_EMAIL}</Text>
      {splitedExistingIUErrorSubHeader?.[1]}
    </Text>
  ) : undefined

  const bottomSheetErrorErrorMessageChildren = invalidIUErrorMessageChildren || existingIUErrorMessageChildren

  const isParkingOrProfileError = (parkingLandingCardError && isLoggedIn) || (profileError && isLoggedIn) || isInternetError || !!maintenanceData
  const isParkingCouponsError = (profileError && isLoggedIn) || (parkingLandingCardError&& isLoggedIn) || isInternetError || !!maintenanceData
  const parkedMaintenanceError = maintenanceData?.icon ? (
    <Image style={styles.parkedCarErrorIcon} source={{ uri: getUriImage(maintenanceData?.icon) }} />
  ) : (
    <ParkedError style={styles.parkedCarErrorIcon} />
  )
  const parkedCarStatusIcon = isParkingOrProfileError ? (
    parkedMaintenanceError
  ) : (
    <ParkedStatusIcon isParked={parkingLandingCardPayload?.isParked} />
  )
  const onEditVehicleIU = () => {
    openEditVehicleIUModal()
    eCouponStateCodeRef.current = ""
  }

  const getMessage = (mgsCode, defaultMsg) => {
    return (
      get(
        messagesCommon?.find((item) => item.code === mgsCode),
        "title",
      ) || defaultMsg
    )
  }

  const onPressCloseErrorSheet = () => {
    closeUpdateVehicleErrorModal()
    dispatch(ProfileActions.profileUpdateReset())
  }

  const callBackCloseModalUpdateIU = () => {
    callBackUpdateVehicleError()
    setIsHandleFDL(false)
  }

  const navigateToWebViewFDL = () => {
    if (isHandleFDL) {
      handleModeCarPassCsmAPI(navigation, dispatch, convertValueStateCode(stateCode))
    }
  }

  const handleFDLStateCode = () => {
    const isVehicleIU = profilePayload?.vehicleIU?.length > 0
    if (isVehicleIU) {
      handleModeCarPassCsmAPI(navigation, dispatch, convertValueStateCode(stateCode))
    } else {
      setIsHandleFDL(true)
      openEditVehicleIUModal()
    }
  }

  useEffect(() => {
    if (!isFocused) {
      setIsHandleFDL(false)
    }
  }, [isFocused])

  useEffect(() => {
    if (stateCode) {
      handleFDLStateCode()
    }
  }, [stateCode])

  useEffect(() => {
    if (profileUpdateSuccess) {
      dispatch(ProfileActions.profileUpdateReset())
      Toast.show(getMessage("MSG", translate("profile.msg80")), {
        duration: Toast.durations.SHORT,
        position: -40,
        shadow: false,
        animation: true,
        hideOnPress: true,
        opacity: 1,
        backgroundColor: color.palette.almostBlackGrey,
        textColor: color.palette.whiteGrey,
        textStyle: {
          paddingHorizontal: 3,
          paddingVertical: 4,
          fontFamily: "Lato",
          fontSize: 14,
          lineHeight: 18,
          fontStyle: "normal",
        },
      })
      navigateToWebViewFDL()
      callBackUpdateVehicleSuccess()
    }
  }, [profileUpdateSuccess])

  useEffect(() => {
    if (profileUpdateError) {
      setIsHandleFDL(false)
      openUpdateVehicleErrorModal()
      callBackUpdateVehicleError()
    }
  }, [profileUpdateError])

  useEffect(() => {
    if (profileUpdateFetching) {
      GlobalLoadingController.showLoading(true)
    } else {
      GlobalLoadingController.hideLoading()
    }
  }, [profileUpdateFetching])

  return (
    <View
      onLayout={(evt) => {
        if (parkingEntitlementOffsetY) {
          parkingEntitlementOffsetY.value = evt.nativeEvent.layout.y + evt.nativeEvent.layout.height
        }
      }}
      style={[styles.wrapper, isMonarch && styles.monarchWrapperStyle]}
    >
      {!parkingLandingCardFetching && parkedCarStatusIcon}
      <IUNumber
        onEditVehicleIU={onEditVehicleIU}
        isLoading={parkingLandingCardFetching}
        isInternetError={isInternetError}
        isMaintenanceData={!!maintenanceData}
        parkingLandingTrackValue={parkingLandingTrackValue}
      />
      {!isMonarch && (
        <>
          <ParkingBenefits
            scrollRef={scrollRef}
            refreshData={refreshData}
            onEditVehicleIU={onEditVehicleIU}
            isInternetError={isInternetError}
            maintenanceData={maintenanceData}
            setIsAllLoading={setIsAllLoading}
            parkingLandingCardError={parkingLandingCardError}
            parkingLandingCardPayload={parkingLandingCardPayload}
            parkingLandingCardFetching={parkingLandingCardFetching}
            parkingPromotionsSectionOffsetY={parkingPromotionsSectionOffsetY}
            parkingLandingTrackValue={parkingLandingTrackValue}
          />
          <ParkNXECoupons
            openEditVehicleIUModal={onEditVehicleIU}
            parkingECouponSectionOffsetY={parkingECouponSectionOffsetY}
            parkingLandingCardPayload={parkingLandingCardPayload}
            refreshData={refreshData}
            scrollRef={scrollRef}
            isError={isParkingCouponsError}
            parkingLandingCardFetching={parkingLandingCardFetching}
            parkingLandingTrackValue={parkingLandingTrackValue}
          />
        </>
      )}
      <MonarchParkingEntitlementContent
        isInternetError={isInternetError}
        maintenanceData={maintenanceData}
        openEditVehicleIUModal={onEditVehicleIU}
        parkingLandingCardFetching={parkingLandingCardFetching}
        parkingLandingTrackValue={parkingLandingTrackValue}
      />
      <VehicleDetailsBottomSheet
        screenName={SECTION_NAME}
        personalData={profilePayload}
        isModalVisible={isModalVisible}
        closeEditVehicleIUModal={closeEditVehicleIUModal}
        callBackCloseModalUpdateIU={callBackCloseModalUpdateIU}
      />

      <BottomSheetError
        openPendingModal
        icon={<InfoRed />}
        visible={isErrorModalVisible}
        shouldFitContentHeight
        animationInTiming={500}
        animationOutTiming={500}
        title={bottomSheetErrorTitle}
        errorMessage={bottomSheetErrorMessage}
        errorMessageChildren={bottomSheetErrorErrorMessageChildren}
        onClose={onPressCloseErrorSheet}
        buttonText={bottomSheetErrorButtonText}
        onButtonPressed={onPressCloseErrorSheet}
        iconUrl={get(errorEHR15, "icon")}
        testID={`${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
        accessibilityLabel={`${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
      />
    </View>
  )
}

export default ParkingEntitlements
