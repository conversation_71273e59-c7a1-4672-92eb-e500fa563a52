import React from "react"
import { View, Dimensions, TouchableOpacity } from "react-native"

import { Text } from "app/elements/text"
import BackgroundImage from "./background-image"
import { useRewardTier } from "app/hooks/useRewardTier"
import { REDEEM_CATALOGUE_COLORS } from "../parking-landing.styles"
import { NavigationConstants } from "app/utils/constants"
import { Tier } from "app/components/changi-rewards-member-card"

import styles from "./styles"
import { AdobeTagName, trackAction } from "app/services/adobe"

const screenWidth = Dimensions.get("window").width

const RedeemFromCRCatalogue = (props) => {
  const { navigation, parkingLandingTrackValue } = props

  const { currentTier } = useRewardTier()
  const backgroundProps = currentTier && REDEEM_CATALOGUE_COLORS[currentTier]

  const imageRatio = 343 / 68
  const imageWidth = screenWidth - 32
  const imageHeight = imageWidth / imageRatio
  const isMornach = currentTier === Tier.Monarch || currentTier === Tier.StaffMonarch

  const onPressRedeemButton = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Redeem Parking Rebates | Redeem`,
    })
    navigation?.navigate?.(NavigationConstants.redemptionCatalogueScreen as never)
  }

  if (!currentTier || isMornach) {
    return null
  }

  return (
    <View style={styles.wrapper}>
      <BackgroundImage
        width={imageWidth}
        height={imageHeight}
        {...backgroundProps}
        style={styles.backgroundImage}
      />

      <Text style={styles.guideText} tx="parkingLanding.crCatalogue.title" />
      <TouchableOpacity style={styles.button} onPress={onPressRedeemButton}>
        <Text tx="parkingLanding.crCatalogue.button" style={styles.buttonText} />
      </TouchableOpacity>
    </View>
  )
}

export default RedeemFromCRCatalogue
