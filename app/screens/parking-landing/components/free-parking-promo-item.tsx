import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import {
  COMPONENT_NAME,
  FreeParkingPromotionTag,
  FreeParkingPromotionType,
} from "../parking-landing.constants"
import { useHandleNavigation } from "app/utils/navigation-helper"
import LinearGradient from "react-native-linear-gradient"
import { Dimensions, StyleSheet, View } from "react-native"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"
import BaseImage from "app/elements/base-image/base-image"
import { mappingUrlAem } from "app/utils"
import RenderHTML from "react-native-render-html"
import { formatHtmlContent } from "app/utils/html/html-content"
import { systemFonts } from "app/utils/constants"
import { useMemo } from "react"
import { CheckV2 } from "assets/icons"
import { AdobeTagName, trackAction } from "app/services/adobe"

interface PropsType {
  index: number
  item: any
  handleOnPressJewel: Function
  parkingLandingTrackValue?: string
}

const FreeParkingPromoItem = (props: PropsType) => {
  const { index, item, handleOnPressJewel, parkingLandingTrackValue } = props
  const gradientColors = item?.bgColors?.map((color) => color?.tagName)
  const { handleNavigation } = useHandleNavigation("PARKING_LANDING")
  const isJewelMastercard = item?.promotionType === FreeParkingPromotionType.Jewel
  const isDisabled = isJewelMastercard &&
    [
      FreeParkingPromotionTag.NotAvailable,
      FreeParkingPromotionTag.FullyRedeemed,
      FreeParkingPromotionTag.Credited,
    ].some((val) => val === item?.promotionTag)

  const handlePressFreeParkingPromoItem = (item) => {
    if (isDisabled) return
    const dataToBeSent = `${parkingLandingTrackValue} | Parking Promotions | ${item?.title}`
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: dataToBeSent,
    })
    if (item?.promotionType === 'jewel') {
      handleOnPressJewel()
    } else {
      const navigationData = item?.callToAction?.navigation
      const redirectData = item?.callToAction?.redirect
      if (navigationData?.value) {
        handleNavigation(navigationData?.type, navigationData?.value, redirectData)
      }
    }
  }

  const renderPromoTag = useMemo(() => {
    if (!isJewelMastercard) return null
    switch (item?.promotionTag) {
      case FreeParkingPromotionTag.Credited:
        return (
          <View style={{ flexDirection: "row" }}>
            <View style={[styles.tagContainerStyle, styles.tagCreditedContainerStyle]}>
              <CheckV2 color={color.palette.basegreen} height={10} width={10} />
              <Text
                style={[styles.tagLabelTextStyle, styles.tagCreditedLabelTextStyle]}
                tx="parkingLanding.freeParkingPromo.tag.credited"
              />
            </View>
          </View>
        )
      case FreeParkingPromotionTag.LimitedSlots:
        return (
          <View style={{ flexDirection: "row" }}>
            <View style={[styles.tagContainerStyle, styles.tagLimitedSlotsContainerStyle]}>
              <Text
                style={[styles.tagLabelTextStyle, styles.tagLimitedSlotsLabelTextStyle]}
                tx="parkingLanding.freeParkingPromo.tag.limitedSlots"
                txOptions={{
                  count: item?.remainingSlots || 0,
                }}
              />
            </View>
          </View>
        )
      case FreeParkingPromotionTag.FullyRedeemed:
        return (
          <View style={{ flexDirection: "row" }}>
            <View style={[styles.tagContainerStyle, styles.tagFullyRedeemedContainerStyle]}>
              <Text
                style={[styles.tagLabelTextStyle, styles.tagFullyRedeemedLabelTextStyle]}
                tx="parkingLanding.freeParkingPromo.tag.fullyRedeemed"
              />
            </View>
          </View>
        )
      case FreeParkingPromotionTag.NotAvailable:
        return (
          <View style={{ flexDirection: "row" }}>
            <View style={[styles.tagContainerStyle, styles.tagNAContainerStyle]}>
              <Text
                style={[styles.tagLabelTextStyle, styles.tagNALabelTextStyle]}
                tx="parkingLanding.freeParkingPromo.tag.notAvailable"
              />
            </View>
          </View>
        )
      default:
        return null
    }
  }, [isJewelMastercard, item?.promotionTag])

  return (
    <MultimediaTouchableOpacity
      accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}`}
      key={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}`}
      onPress={() => handlePressFreeParkingPromoItem(item)}
      style={isDisabled && styles.unclickableStyle}
      testID={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}`}
    >
      <LinearGradient
        colors={gradientColors}
        end={{ x: 0, y: 1 }}
        start={{ x: 0, y: 0 }}
        style={[styles.containerStyle]}
      >
        <BaseImage
          accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Icon`}
          resizeMode="contain"
          source={{ uri: mappingUrlAem(item?.icon) }}
          style={styles.iconStyle}
          testID={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Icon`}
        />
        <View style={styles.contentContainerStyle}>
          {renderPromoTag}
          <Text
            accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Title`}
            style={[styles.titleTextStyle, isDisabled && styles.disableStyle]}
            testID={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Title`}
            text={item?.title}
          />
          <View
            accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Description`}
            style={isDisabled && styles.disableStyle}
            testID={`${COMPONENT_NAME}__FreeParkingPromo_Item__${index}__Description`}
          >
            <RenderHTML
              source={{ html: formatHtmlContent(item?.copy) }}
              tagsStyles={freeParkingPromoItemDescriptionTagStyles as any}
              systemFonts={systemFonts}
            />
          </View>
        </View>
      </LinearGradient>
    </MultimediaTouchableOpacity>
  )
}

const { width: screenWidth } = Dimensions.get("window")
const styles = StyleSheet.create({
  containerStyle: {
    alignItems: "center",
    borderRadius: 12,
    flexDirection: "row",
    gap: 8,
    marginBottom: 12,
    padding: 16,
  },
  lastItemContainerStyle: {
    marginBottom: 12,
  },
  iconStyle: {
    height: (screenWidth * 150) / 375,
    width: (screenWidth * 75) / 375,
  },
  contentContainerStyle: {
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 12,
    flex: 1,
    gap: 8,
    padding: 12,
  },
  titleTextStyle: {
    ...newPresets.bodyTextBlackBold,
    color: color.palette.almostBlackGrey,
  },
  tagContainerStyle: {
    alignItems: "center",
    borderRadius: 99,
    flexDirection: "row",
    gap: 4,
    minHeight: 22,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  tagLabelTextStyle: {
    ...newPresets.XSmallBold,
    textTransform: "none",
  },
  tagNAContainerStyle: {
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.midGrey,
    borderWidth: 1,
  },
  tagNALabelTextStyle: {
    color: color.palette.darkestGrey,
  },
  tagCreditedContainerStyle: {
    backgroundColor: "#DAEDD9",
  },
  tagCreditedLabelTextStyle: {
    color: color.palette.basegreen,
  },
  tagLimitedSlotsContainerStyle: {
    backgroundColor: color.palette.lightestRed,
  },
  tagLimitedSlotsLabelTextStyle: {
    color: color.palette.baseRed,
  },
  tagFullyRedeemedContainerStyle: {
    backgroundColor: color.palette.lightestRed,
  },
  tagFullyRedeemedLabelTextStyle: {
    color: color.palette.baseRed,
  },
  disableStyle: {
    opacity: 0.5,
  },
  unclickableStyle: {
    pointerEvents: "none",
  },
})
const freeParkingPromoItemDescriptionTagStyles = {
  ul: {
    ...newPresets.caption2Regular,
  },
  li: {
    ...newPresets.caption2Regular,
  },
  p: {
    ...newPresets.caption2Regular,
    margin: 0,
  },
  div: {
    width: screenWidth,
  },
  b: {
    ...newPresets.caption2Bold,
    color: color.palette.lightPurple,
  },
}

export default FreeParkingPromoItem
