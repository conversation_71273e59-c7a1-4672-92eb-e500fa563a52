import { newPresets, Text } from "app/elements/text"
import { StyleSheet, View } from "react-native"
import { COMPONENT_NAME } from "../parking-landing.constants"
import { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { useSelector } from "react-redux"
import { translate } from "app/i18n"
import FreeParkingPromoItem from "./free-parking-promo-item"
import { color } from "app/theme"
import FreeParkingPromoLoadingSkeleton from "./free-parking-promo-loading-skeleton"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import LinearGradient from "react-native-linear-gradient"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { SharedValue } from "react-native-reanimated"
import { PARKING_HEADER_HEIGHT } from "../constants"

interface PropsType {
  fetchFreeParkingPromos: Function
  freeParkingPromosError: boolean
  freeParkingPromosFetching: boolean
  freeParkingPromosPayload: any[]
  handleOnPressJewel: Function
  parkingPromotionsSectionOffsetY?: SharedValue
  parkingLandingTrackValue?: string
}

const FreeParkingPromoSection = (props: PropsType) => {
  const {
    fetchFreeParkingPromos,
    freeParkingPromosError,
    freeParkingPromosFetching,
    freeParkingPromosPayload,
    handleOnPressJewel,
    parkingPromotionsSectionOffsetY,
    parkingLandingTrackValue,
  } = props
  const dataCommonAEM = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.AEM_COMMON_DATA))
  const inf26 = dataCommonAEM?.data?.informatives?.find((e) => e?.code === "INF26")

  const handleRetryFreeParkingPromo = () => {
    GlobalLoadingController.showLoading(true)
    fetchFreeParkingPromos(GlobalLoadingController.hideLoading, true)
  }

  return (
    <View
      onLayout={(evt) => {
        parkingPromotionsSectionOffsetY.value = evt.nativeEvent.layout.y - PARKING_HEADER_HEIGHT
      }}
      style={styles.containerStyle}
    >
      <View style={styles.headerContainerStyle}>
        <Text
          accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Title`}
          style={styles.titleTextStyle}
          testID={`${COMPONENT_NAME}__FreeParkingPromo_Title`}
          tx="parkingLanding.freeParkingPromo.title"
          txOptions={{
            amount: freeParkingPromosPayload?.length
              ? ` (${freeParkingPromosPayload?.length})`
              : "",
          }}
        />
        {!freeParkingPromosError ? (
          <Text
            accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Description`}
            style={styles.descriptionTextStyle}
            testID={`${COMPONENT_NAME}__FreeParkingPromo_Description`}
            text={
              inf26?.informativeText || translate("parkingLanding.freeParkingPromo.description")
            }
          />
        ) : (
          <Text
            accessibilityLabel={`${COMPONENT_NAME}__FreeParkingPromo_Error_Msg`}
            style={styles.errorTextStyle}
            testID={`${COMPONENT_NAME}__FreeParkingPromo_Error_Msg`}
            tx="parkingLanding.freeParkingPromo.error"
          />
        )}
      </View>
      {freeParkingPromosFetching && <FreeParkingPromoLoadingSkeleton />}
      {freeParkingPromosPayload?.map?.((item, index, list) => {
        return (
          <FreeParkingPromoItem index={index} item={item} handleOnPressJewel={handleOnPressJewel} parkingLandingTrackValue={parkingLandingTrackValue} />
        )
      })}
      {freeParkingPromosError && (
        <View style={styles.errorBtnContainerStyle}>
          <MultimediaTouchableOpacity onPress={handleRetryFreeParkingPromo}>
            <LinearGradient
              colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
              end={{ x: 0, y: 1 }}
              start={{ x: 0, y: 0 }}
              style={styles.retryBtnContainerStyle}
            >
              <Text style={styles.retryBtnLabelTextStyle} tx="common.retry" />
            </LinearGradient>
          </MultimediaTouchableOpacity>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  containerStyle: {
    marginTop: 40,
    paddingHorizontal: 16,
  },
  headerContainerStyle: {
    gap: 4,
    marginBottom: 16,
  },
  titleTextStyle: {
    ...newPresets.bodyTextBlackBold,
    color: color.palette.almostBlackGrey,
  },
  descriptionTextStyle: {
    ...newPresets.caption1Regular,
    color: color.palette.darkestGrey,
  },
  errorTextStyle: {
    ...newPresets.caption1Regular,
    color: color.palette.darkestGrey,
  },
  errorBtnContainerStyle: {
    flexDirection: "row",
    marginBottom: 12,
  },
  retryBtnContainerStyle: {
    alignItems: "center",
    borderRadius: 99,
    justifyContent: "center",
    height: 28,
    paddingHorizontal: 10,
    paddingVertical: 5,
    minWidth: 60,
  },
  retryBtnLabelTextStyle: {
    ...newPresets.caption1Bold,
  },
})

export default FreeParkingPromoSection
