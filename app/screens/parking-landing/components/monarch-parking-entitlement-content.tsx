import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import { newPresets, Text } from "app/elements/text"
import { useRewardTier } from "app/hooks/useRewardTier"
import { ProfileSelectors } from "app/redux/profileRedux"
import { color } from "app/theme"
import { AYRFreeParkingIcon, SelectedReservedLotsIcon } from "assets/icons"
import { Dimensions, Platform, StyleSheet, View } from "react-native"
import { useSelector } from "react-redux"
import { COMPONENT_NAME } from "../parking-landing.constants"
import { useNavigation } from "app/utils/navigation-helper"
import { NavigationConstants } from "app/utils/constants"
import { translate } from "app/i18n"
import { AdobeTagName, trackAction } from "app/services/adobe"

interface PropsType {
  isInternetError: boolean
  maintenanceData?: {[key: string]: any}
  openEditVehicleIUModal?: Function
  parkingLandingCardFetching?: boolean
  parkingLandingTrackValue?: string
}

interface ErrorSectionProps {
  isInternetError: boolean
  maintenanceData?: { header?: string; subHeader?: string }
}

const getErrorContent = (
  isInternetError: boolean,
  maintenanceData?: { header?: string; subHeader?: string },
) => {
  if (isInternetError) {
    return {
      titleTx: "parkingLanding.errors.noInternetConnection",
      messageTx: "parkingLanding.errors.noInternetMessage",
      title: "",
      message: "",
    }
  }

  const { header, subHeader } = maintenanceData || {}
  return {
    titleTx: "",
    messageTx: "",
    title: header || translate("parkingLanding.errors.systemMaintenance"),
    message: subHeader || translate("parkingLanding.errors.maintenanceMessage"),
  }
}

const ErrorSection = ({ isInternetError, maintenanceData }: ErrorSectionProps) => {
const { titleTx, messageTx, title, message } = getErrorContent(isInternetError, maintenanceData)

  return (
    <>
      <Text text={title} tx={titleTx} style={styles.errorTitle} />
      <Text text={message} tx={messageTx} style={styles.errorMessage} />
    </>
  )
}

const MonarchParkingEntitlementContent = (props: PropsType) => {
  const { isInternetError, maintenanceData, openEditVehicleIUModal, parkingLandingCardFetching, parkingLandingTrackValue } = props
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const { isMonarch } = useRewardTier()
  const navigation = useNavigation()
  const vehicleIU = profilePayload?.vehicleIU
  const isIUNumberSectionVisible = parkingLandingCardFetching || vehicleIU

  const isError = isInternetError || !!maintenanceData

  const handlePressAddVehicleIU = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Top section | ${translate(
        "parkingLanding.monarchParkingEntitlement.ctaBtn.addVehicleIU",
      )}`,
    })
    openEditVehicleIUModal?.()
  }

  const handlePressViewDetails = () => {
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: `${parkingLandingTrackValue} | Top section | ${translate(
        "parkingLanding.monarchParkingEntitlement.ctaBtn.viewDetails",
      )}`,
    })
    navigation?.navigate(NavigationConstants.parkingBenefitsMonarch)
  }

  if (!isMonarch) {
    return null
  }
  return (
    <View style={[styles.containerStyle, !isIUNumberSectionVisible && styles.noIUContainerStyle]}>
      {isError && <ErrorSection isInternetError={isInternetError} maintenanceData={maintenanceData} />}

      <Text
        style={styles.tierTitleTextStyle}
        tx="parkingLanding.monarchParkingEntitlement.tierTitle"
      />
      <Text style={styles.titleTextStyle} tx="parkingLanding.monarchParkingEntitlement.title" />
      {!vehicleIU ? (
        <Text
          style={styles.nonIUDescriptionTextStyle}
          tx="parkingLanding.monarchParkingEntitlement.nonIUDescription"
        />
      ) : (
        <View style={{ marginBottom: 8 }} />
      )}
      <View style={styles.optionContainerStyle}>
        <AYRFreeParkingIcon height={32} width={32} />
        <Text
          style={styles.optionLabelTextStyle}
          tx="parkingLanding.monarchParkingEntitlement.option.ayrFreeParking"
        />
      </View>
      <View style={[styles.optionContainerStyle, isError && styles.optionContainerError]}>
        <SelectedReservedLotsIcon height={32} width={32} />
        <Text
          style={styles.optionLabelTextStyle}
          tx="parkingLanding.monarchParkingEntitlement.option.selectedReservedLots"
        />
      </View>
      {!isError && (
        <View style={{ flexDirection: "row" }}>
          {!vehicleIU ? (
            <MultimediaTouchableOpacity
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement__Monarch__CTA__AddVehicleIU`}
              onPress={handlePressAddVehicleIU}
              style={styles.ctaBtnStyle}
              testID={`${COMPONENT_NAME}__ParkingEntitlement__Monarch__CTA__AddVehicleIU`}
            >
              <Text
                style={styles.ctaBtnLabelTextStyle}
                tx="parkingLanding.monarchParkingEntitlement.ctaBtn.addVehicleIU"
              />
            </MultimediaTouchableOpacity>
          ) : (
            <MultimediaTouchableOpacity
              accessibilityLabel={`${COMPONENT_NAME}__ParkingEntitlement__Monarch__CTA__ViewDetails`}
              onPress={handlePressViewDetails}
              style={styles.ctaBtnStyle}
              testID={`${COMPONENT_NAME}__ParkingEntitlement__Monarch__CTA__ViewDetails`}
            >
              <Text
                style={styles.ctaBtnLabelTextStyle}
                tx="parkingLanding.monarchParkingEntitlement.ctaBtn.viewDetails"
              />
            </MultimediaTouchableOpacity>
          )}
        </View>
      )}
    </View>
  )
}

const { width: screenWidth } = Dimensions.get("window")
const styles = StyleSheet.create({
  containerStyle: {
    marginBottom: 8,
    marginTop: 8,
    width: (screenWidth / 375) * 240,
  },
  noIUContainerStyle: {
    marginTop: 0,
  },
  tierTitleTextStyle: {
    ...newPresets.caption2Bold,
    color: color.palette.darkestGrey,
    fontSize: 11,
    letterSpacing: 1.32,
    textTransform: "uppercase",
  },
  titleTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    marginBottom: 4,
  },
  nonIUDescriptionTextStyle: {
    ...newPresets.caption2Regular,
    marginBottom: 12,
  },
  optionContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
    marginBottom: 8,
  },
  optionContainerError: {
    marginBottom: 0,
  },
  optionLabelTextStyle: {
    ...newPresets.caption2Regular,
  },
  ctaBtnStyle: {
    backgroundColor: color.palette.almostBlackGrey,
    borderRadius: 60,
    minHeight: 28,
    marginTop: 4,
    paddingHorizontal: 11,
    paddingVertical: 5,
  },
  ctaBtnLabelTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.whiteGrey,
  },
  errorSection: {
    marginRight: 87,
  },
  errorTitle: {
    ...newPresets.bodyTextBlackBold,
    fontSize: 14,
    lineHeight: 18,
    color: color.palette.almostBlackGrey,
  },
  errorMessage: {
    ...newPresets.bodyTextRegular,
    fontSize: 12,
    lineHeight: 16,
    color: color.palette.darkestGrey,
    marginTop: 4,
    marginBottom: 20,
  },
})

export default MonarchParkingEntitlementContent
