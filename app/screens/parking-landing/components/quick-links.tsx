import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import BaseImage from "app/elements/base-image/base-image"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"
import { mappingUrlAem } from "app/utils"
import { Dimensions, Platform, StyleSheet, View } from "react-native"
import { COMPONENT_NAME } from "../parking-landing.constants"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import LinearGradient from "react-native-linear-gradient"
import { SharedValue } from "react-native-reanimated"
import { MutableRefObject, useMemo } from "react"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { AdobeTagName, trackAction } from "app/services/adobe"

interface PropsType {
  fetchParkingQuickLinks?: Function
  navigation?: any
  openEditVehicleIUModal?: Function
  parkingECouponSectionOffsetY?: SharedValue
  parkingMoreServicesSectionOffsetY?: SharedValue
  parkingPromotionsSectionOffsetY?: SharedValue
  quickLinksError?: boolean
  quickLinksFetching?: boolean
  quickLinksPayload?: any
  scrollRef?: MutableRefObject<any>
  parkingLandingTrackValue?: string
}
interface QuickLinkItemType {
  cta?: {
    navigation?: {
      type?: string
      value?: string
    }
    redirect: {
      utmParameters?: {
        campaign?: string
        content?: string
        medium?: string
        term?: string
      }
    }
  }
  highlightRibbon?: string
  icon: string
  title: string
  type: {
    tagName?: string
  }
}
interface ItemPropsType {
  handlePressQuickLinkItem?: Function
  item?: QuickLinkItemType
}
interface ErrorOverlayPropsType {
  fetchParkingQuickLinks?: Function
}

const QuickLinkItem = (props: ItemPropsType) => {
  const { handlePressQuickLinkItem, item } = props

  if (!item) {
    return <View style={styles.itemContainerStyle} />
  }
  return (
    <MultimediaTouchableOpacity
      accessibilityLabel={`${COMPONENT_NAME}__QuickLink__Item__${item?.title}`}
      onPress={() => handlePressQuickLinkItem?.(item)}
      style={styles.itemContainerStyle}
      testID={`${COMPONENT_NAME}__QuickLink__Item__${item?.title}`}
    >
      <View style={styles.itemIconContainerStyle}>
        {item?.icon ? (
          <BaseImage
            accessibilityLabel={`${COMPONENT_NAME}__QuickLink__Item__Icon`}
            source={{ uri: mappingUrlAem(item?.icon) }}
            style={styles.itemIconStyle}
            testID={`${COMPONENT_NAME}__QuickLink__Item__Icon`}
          />
        ) : (
          <View
            accessibilityLabel={`${COMPONENT_NAME}__QuickLink__Item__Icon__Default`}
            style={styles.itemDefaultIconStyle}
            testID={`${COMPONENT_NAME}__QuickLink__Item__Icon__Default`}
          />
        )}
        {item?.highlightRibbon && (
          <View
            accessibilityLabel={`${COMPONENT_NAME}__QuickLink__Item__Highlight`}
            style={styles.itemHighlightContainerStyle}
            testID={`${COMPONENT_NAME}__QuickLink__Item__Highlight`}
          >
            <Text
              numberOfLines={1}
              style={styles.itemHighlightLabelTextStyle}
              text={item?.highlightRibbon}
            />
          </View>
        )}
      </View>
      <Text
        accessibilityLabel={`${COMPONENT_NAME}__QuickLink__Item__Title`}
        numberOfLines={2}
        style={styles.itemTitleTextStyle}
        testID={`${COMPONENT_NAME}__QuickLink__Item__Title`}
        text={item.title}
      />
    </MultimediaTouchableOpacity>
  )
}

const LoadingSkeleton = () => {
  return (
    <View style={styles.containerStyle}>
      <View style={styles.rowContainerStyle}>
        {Array.from({ length: 4 }).map((_, index) => (
          <View style={styles.itemContainerStyle} key={`loading_${index}`}>
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={LOADING_COLORS}
              shimmerStyle={styles.loadingIconStyle}
            />
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={LOADING_COLORS}
              shimmerStyle={styles.loadingTitleStyle}
            />
          </View>
        ))}
      </View>
      <View style={styles.rowContainerStyle}>
        {Array.from({ length: 4 }).map((_, index) => (
          <View style={styles.itemContainerStyle} key={`loading_${index}`}>
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={LOADING_COLORS}
              shimmerStyle={styles.loadingIconStyle}
            />
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={LOADING_COLORS}
              shimmerStyle={styles.loadingTitleStyle}
            />
          </View>
        ))}
      </View>
    </View>
  )
}

const ErrorOverlay = (props: ErrorOverlayPropsType) => {
  const { fetchParkingQuickLinks } = props

  const handlePressRetry = () => {
    fetchParkingQuickLinks?.()
  }

  return (
    <View style={styles.errorContainerStyle}>
      <Text
        accessibilityLabel={`${COMPONENT_NAME}__QuickLink__ErrorOverlay__Description`}
        style={styles.errorDescriptionTextStyle}
        testID={`${COMPONENT_NAME}__QuickLink__ErrorOverlay__Description`}
        tx="parkingLanding.quickLinks.error.description"
      />
      <MultimediaTouchableOpacity
        accessibilityLabel={`${COMPONENT_NAME}__QuickLink__ErrorOverlay__Btn__Retry`}
        testID={`${COMPONENT_NAME}__QuickLink__ErrorOverlay__Btn__Retry`}
        onPress={handlePressRetry}
      >
        <LinearGradient
          colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
          end={{ x: 0, y: 1 }}
          start={{ x: 0, y: 0 }}
          style={styles.errorRetryBtnStyle}
        >
          <Text style={styles.errorRetryBtnLabelStyle} tx="common.retry" />
        </LinearGradient>
      </MultimediaTouchableOpacity>
    </View>
  )
}

// === Main Component ===
const QuickLinks = (props: PropsType) => {
  const {
    fetchParkingQuickLinks,
    openEditVehicleIUModal,
    parkingECouponSectionOffsetY,
    parkingMoreServicesSectionOffsetY,
    parkingPromotionsSectionOffsetY,
    quickLinksError,
    quickLinksFetching,
    quickLinksPayload,
    scrollRef,
    parkingLandingTrackValue,
  } = props
  const { handleNavigation } = useHandleNavigation("PARKING_QUICKLINKS")
  const upperLinks = useMemo(() => {
    return Array.from({ length: 4 }).map((_item, index) => {
      return quickLinksPayload?.[index]
    })
  }, [JSON.stringify(quickLinksPayload)])
  const lowerLinks = useMemo(() => {
    return Array.from({ length: 4 }).map((_item, index) => {
      return quickLinksPayload?.[index + 4]
    })
  }, [JSON.stringify(quickLinksPayload)])

  const handlePressQuickLinkItem = (item: QuickLinkItemType) => {
    const navigationData = item?.cta?.navigation
    const redirectData = item?.cta?.redirect
    if (!navigationData?.type || !navigationData?.value) {
      return
    }
    const dataToBeSent = `${parkingLandingTrackValue} | Quicklinks | ${item?.title}`
    trackAction(AdobeTagName.CAppParkingLanding, {
      [AdobeTagName.CAppParkingLanding]: dataToBeSent,
    })
    handleNavigation(navigationData?.type, navigationData?.value, redirectData, {
      handleParkingCoupons: () => {
        scrollRef?.current?.scrollTo({
          y: parkingECouponSectionOffsetY.value,
          animated: true,
        })
      },
      handleParkingMyIU: () => {
        openEditVehicleIUModal?.()
      },
      handleParkingPromotions: () => {
        scrollRef?.current?.scrollTo({
          y: parkingPromotionsSectionOffsetY.value,
          animated: true,
        })
      },
      handleParkingServices: () => {
        scrollRef?.current?.scrollTo({
          y: parkingMoreServicesSectionOffsetY.value,
          animated: true,
        })
      },
    })
  }

  if (quickLinksFetching) {
    return <LoadingSkeleton />
  }
  if (quickLinksError) {
    return <ErrorOverlay fetchParkingQuickLinks={fetchParkingQuickLinks} />
  }
  return (
    <View style={styles.containerStyle}>
      {!!upperLinks?.length && (
        <View style={styles.rowContainerStyle}>
          {upperLinks?.map((item, index) => (
            <QuickLinkItem
              handlePressQuickLinkItem={handlePressQuickLinkItem}
              item={item}
              key={`${index}_${item?.title}`}
            />
          ))}
        </View>
      )}
      {!!lowerLinks?.length && (
        <View style={styles.rowContainerStyle}>
          {lowerLinks?.map((item, index) => (
            <QuickLinkItem
              handlePressQuickLinkItem={handlePressQuickLinkItem}
              item={item}
              key={`${index}_${item?.title}`}
            />
          ))}
        </View>
      )}
    </View>
  )
}
// ======================

const { width: screenWidth } = Dimensions.get("window")
const LOADING_COLORS = [color.palette.lighterGrey, color.background, color.palette.lighterGrey]
const styles = StyleSheet.create({
  containerStyle: {
    gap: 24,
    marginTop: 12,
  },
  rowContainerStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
  },
  lowerRowContainerStyle: {
    marginTop: 24,
  },
  itemContainerStyle: {
    alignItems: "center",
    gap: 8,
    width: (screenWidth / 375) * 72,
  },
  itemIconContainerStyle: {
    width: 40,
  },
  itemIconStyle: {
    borderRadius: 16,
    height: 40,
    width: 40,
  },
  itemDefaultIconStyle: {
    backgroundColor: "#DEE6F6",
    borderRadius: 16,
    height: 40,
    width: 40,
  },
  itemHighlightContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.lighterOrange,
    borderColor: color.palette.orangeWarning300,
    borderRadius: 4,
    borderWidth: 1,
    height: 16,
    left: -3,
    paddingHorizontal: 3,
    position: "absolute",
    top: 30,
    width: 46,
  },
  itemHighlightLabelTextStyle: {
    ...newPresets.XSmallBold,
    color: "#F2721B",
    fontSize: 8,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
  },
  itemTitleTextStyle: {
    ...newPresets.XSmallBold,
    color: color.palette.almostBlackGrey,
    textAlign: "center",
    textTransform: "none",
  },
  loadingIconStyle: {
    borderRadius: 16,
    height: 40,
    width: 40,
  },
  loadingTitleStyle: {
    borderRadius: 4,
    height: 12,
    width: 48,
  },
  errorContainerStyle: {
    alignSelf: "center",
    gap: 9,
    marginTop: 24,
  },
  errorDescriptionTextStyle: {
    ...newPresets.caption1Regular,
    color: color.palette.darkestGrey,
    textAlign: "center",
  },
  errorRetryBtnStyle: {
    alignItems: "center",
    alignSelf: "center",
    borderRadius: 60,
    height: 28,
    justifyContent: "center",
    paddingHorizontal: 11,
  },
  errorRetryBtnLabelStyle: {
    ...newPresets.caption1Bold,
  },
})

export default QuickLinks
