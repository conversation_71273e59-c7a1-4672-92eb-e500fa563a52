// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PlayPassWebView> should render component 1`] = `
[
  <RCTSafeAreaView
    style={
      {
        "flex": 1,
      }
    }
  >
    <View
      style={
        [
          {
            "flexDirection": "row",
            "justifyContent": "space-between",
            "margin": 16,
          },
          {
            "marginTop": 0,
          },
        ]
      }
    >
      <View
        accessibilityLabel="BackButtonWebViewScreen"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        hitSlop={
          {
            "bottom": 10,
            "left": 10,
            "right": 10,
            "top": 10,
          }
        }
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "opacity": 1,
          }
        }
        testID="BackButtonWebViewScreen"
      >
        <View
          style={
            {
              "width": 32,
            }
          }
        />
      </View>
      <Text
        allowFontScaling={false}
        style={
          {
            "fontFamily": "Lato-Bold",
            "fontSize": 16,
            "fontStyle": "normal",
            "fontWeight": "700",
            "letterSpacing": 0,
            "lineHeight": 22,
            "textAlign": "left",
          }
        }
      />
      <View
        accessibilityLabel="CloseButtonWebViewScreen"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        hitSlop={
          {
            "bottom": 10,
            "left": 10,
            "right": 10,
            "top": 10,
          }
        }
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "opacity": 1,
          }
        }
        testID="CloseButtonWebViewScreen"
      >
        <View
          style={
            {
              "width": 32,
            }
          }
        />
      </View>
    </View>
  </RCTSafeAreaView>,
  <Modal
    animationType="none"
    deviceHeight={null}
    deviceWidth={null}
    hardwareAccelerated={false}
    hideModalContentWhileAnimating={true}
    onBackdropPress={[Function]}
    onModalHide={[Function]}
    onModalWillHide={[Function]}
    onModalWillShow={[Function]}
    onRequestClose={[Function]}
    onSwipeComplete={[Function]}
    panResponderThreshold={4}
    scrollHorizontal={false}
    scrollOffset={0}
    scrollOffsetMax={0}
    scrollTo={null}
    statusBarTranslucent={true}
    supportedOrientations={
      [
        "portrait",
        "landscape",
      ]
    }
    swipeDirection={null}
    swipeThreshold={100}
    transparent={true}
    visible={false}
  />,
  <Modal
    animationType="none"
    deviceHeight={null}
    deviceWidth={null}
    hardwareAccelerated={false}
    hideModalContentWhileAnimating={true}
    onBackdropPress={[Function]}
    onModalHide={[Function]}
    onModalWillHide={[Function]}
    onModalWillShow={[Function]}
    onRequestClose={[Function]}
    onSwipeComplete={[Function]}
    panResponderThreshold={4}
    scrollHorizontal={false}
    scrollOffset={0}
    scrollOffsetMax={0}
    scrollTo={null}
    statusBarTranslucent={true}
    supportedOrientations={
      [
        "portrait",
        "landscape",
      ]
    }
    swipeDirection={null}
    swipeThreshold={100}
    transparent={true}
    visible={false}
  />,
  <Modal
    animationType="none"
    deviceHeight={null}
    deviceWidth={null}
    hardwareAccelerated={false}
    hideModalContentWhileAnimating={true}
    onBackdropPress={[Function]}
    onModalHide={[Function]}
    onModalWillHide={[Function]}
    onModalWillShow={[Function]}
    onRequestClose={[Function]}
    onSwipeComplete={[Function]}
    panResponderThreshold={4}
    scrollHorizontal={false}
    scrollOffset={0}
    scrollOffsetMax={0}
    scrollTo={null}
    statusBarTranslucent={true}
    supportedOrientations={
      [
        "portrait",
        "landscape",
      ]
    }
    swipeDirection={null}
    swipeThreshold={100}
    transparent={true}
    visible={false}
  />,
]
`;

exports[`<UploadReceiptScreen> should render component 1`] = `
<View
  style={
    {
      "flex": 1,
    }
  }
>
  <RNCamera
    captureAudio={false}
    flashMode=""
    notAuthorizedView={<React.Fragment />}
    onCameraReady={[Function]}
    onResponderReject={[Function]}
    style={
      {
        "flex": 1,
      }
    }
    type=""
  />
</View>
`;
