import React, { ReactElement, useCallback, useEffect, useMemo, useRef, useState } from "react"
import { Keyboard, StatusBar, View, unstable_batchedUpdates, AppState, Dimensions, Platform } from "react-native"
import { styles } from "./changi-maps.styles"
import { LoadingOverlay } from "app/components/loading-modal"
import NetInfo from "@react-native-community/netinfo"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { BottomSheetUnableLoadLocation } from "app/components/bottom-sheet-unable-load-location/bottom-sheet-unable-load-location"
import ChangiMapHeader from "app/screens/changi-maps/components/changi-map-header/changi-map-header"
import { ChangiMapHeaderV2 } from './components/changi-map-header-V2';
import { isEmpty } from "lodash"
import { getTenantDetail } from "app/sagas/pageConfigSaga"
import { ChangiMapCallout } from "app/screens/changi-maps/components/changi-map-callout/changi-map-callout"
import { NavigationConstants, TrackingScreenName } from "app/utils/constants"
import {
  SelectItem,
  metaFeature,
  getAATagLevelSelector,
  uniqfyKeywordCollection,
  renderTitleHeaderStepMid,
} from "./map-action"
import { AdobeTagName, AdobeValueByTagName, trackAction } from "app/services/adobe"
import LevelSelector from "./components/level-selector/level-selector"
import { env } from "app/config/env-params"
import { load, remove, save, StorageKey } from "app/utils/storage"
import { useCurrentScreenActiveAndPreviousScreenHook } from "app/utils/screen-hook"
import { translate } from "app/i18n"
import { ButtonSearch } from './components/button-search';
import { BottomSheetSearch } from './components/bottom-sheet-search';
import { ViewDetailLocation } from './components/view-detail-location';
import { BottomSheetSearchMultiple } from './components/bottom-sheet-multiple';
import { ViewFilterLocation } from './components/view-filter-location';
import { ViewDirection } from './components/view-direction';
import { ViewEmptyDirection } from './components/view-empty-direction';
import { GestureHandlerRootView } from "react-native-gesture-handler";
import Animated, { interpolateColor, useSharedValue, useAnimatedStyle, interpolate } from 'react-native-reanimated';
import { color } from "app/theme"

const enum DefaultZoom {
  VALUE = 13.95,
}
const heightScreen = Dimensions.get('window').height;
const widthScreen = Dimensions.get('window').width;
const mapHeight = 0
const skipOverview = 2

const AtomMap = ({ navigation, route }) => {
  const MetaAtlasSDK = require("./sdk/meta-atlas-sdk-rn-v2.0.0.umd.min.js").MetaAtlasSDK
  const atoms = useRef(null)
  const [isLoading, setLoading] = useState(true)
  const [isNoConnection, setNoConnection] = useState(false)
  const unableToLoadLocationRef = useRef(null)
  const [currentPoint, setCurrentPoint] = useState<metaFeature>()
  const [isMapMoved, setMapMoved] = useState<boolean>(false)
  const [mapLoadedCount, setMapLoadedCount] = useState<number>(0)
  const [floors, setFloors] = useState<SelectItem[]>([])
  const [activeFloor, setActiveFloor] = useState<SelectItem>()
  const [activeBuildingName, setActiveBuildingName] = useState<string>("")
  const [levelSelector, setLevelSelector] = useState<ReactElement>(null)
  const [isMoveEnded, setMoveEnded] = useState<boolean>(false)
  const [isShouldHide, setHideLvlSelector] = useState<boolean>(false)
  const [showBottomSheetSearch, setShowBottomSheet] = useState<boolean>(false)
  const [showBottomSheetSearchMutiple, setShowBottomSheetMultiple] = useState<boolean>(false)
  const [showButtonSmall, setShowButtonSmall] = useState<boolean>(false)
  const [dataLocationTo, setDataLocationTo] = useState(null)
  const [dataLocationFrom, setDataLocationFrom] = useState(null)
  const [showViewDetailLocation, setShowViewDetailLocation] = useState({
    visible: false,
    data: null,
    tenantId: null
  })
  const [showViewFilterLocation, setShowViewFilterLocation] = useState(false)
  const [showViewDirection, setShowViewDirection] = useState(false)
  const [showViewEmptyDirection, setShowViewEmptyDirection] = useState(false)
  const [accessibleRoute, setAccessibleRoute] = useState<boolean>(false);
  const [dataDirection, setDataDirection] = useState(null);
  const [statusViewDirection, setStatusViewDirection] = useState<string>("OVERVIEW")
  const [highlightedSegment, setHighlightedSegment] = useState<number>(
    undefined as any,
  );
  const [titleHeader, setTitleHeader] = useState<string>(translate("changimap.header-v2"))
  const [firstShowViewDetailLocation, setFirstShowViewDetailLocation] = useState(false)
  const [readyViewDetailLocation, setReadyViewDetailLocation] = useState(false)

  const height = useSharedValue(0);
  const calloutRef = useRef<any>(null)
  const activeFloorRef = useRef<SelectItem>(null)
  const currentPointRef = useRef<metaFeature>(null)
  const searchKeywordCollection = useRef<Array<string>>([])
  const appState = useRef(AppState.currentState)
  const [isRoutingStatusUpdateSuccesss, saveRoutingStatusUpdate] = useState(false)
  const [delayOnMove, setDelayOnMove] = useState(Platform.OS === 'ios' ? true : false);

  const localRef = route?.params?.localRef;
  const isFocusToArea = route?.params?.isFocusToArea;

  useCurrentScreenActiveAndPreviousScreenHook(TrackingScreenName.ChangiMap)

  useEffect(() => {
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (appState.current.match(/inactive/) && nextAppState === "background") {
        save(StorageKey.keywordAtomSearchMissingByAppState, searchKeywordCollection.current)
      }
      appState.current = nextAppState
    })

    return () => {
      subscription.remove()
    }
  }, [])

  // call SDK function on highlightRoute segment state update
  useEffect(() => {
    if (highlightedSegment !== undefined) {
      ; (atoms.current as any).highlightRouteSegment(highlightedSegment);
    }
    if (highlightedSegment === dataDirection?.segments?.length - 1 || highlightedSegment === 0) {
      ; (atoms.current as any).dropPin(
        dataLocationTo,
        <ChangiMapCallout
          content={dataLocationTo}
          tenantId={null}
          handleNavigate={() => { }}
          ref={calloutRef}
        />,
      )
    } else {
      (atoms.current as any).hidePin();
      calloutRef?.current?.hideCallout()
    }
    changeTitleHeader(highlightedSegment)
  }, [highlightedSegment]);

  useEffect(() => {
    const getMissingKeywordSearch = async () => {
      const keywordCollection = await load(StorageKey.keywordAtomSearchMissingByAppState)
      if (!isEmpty(keywordCollection)) {
        uniqfyKeywordCollection(keywordCollection)
        remove(StorageKey.keywordAtomSearchMissingByAppState)
      }
    }
    getMissingKeywordSearch()
    return () => {
      remove(StorageKey.keywordAtomSearchMissingByAppState)
    }
  }, [])

  useEffect(() => {
    navigation?.setOptions({ headerShown: false })
  }, [])

  useEffect(() => {
    const checkInternet = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setNoConnection(true)
      }
    }
    checkInternet()
  }, [navigation])

  const defaultFloors = {
    T1: "where.changi.terminals.t1.l1",
    T2: "where.changi.terminals.t2.l1",
    T3: "where.changi.terminals.t3.l1",
    T4: "where.changi.terminals.t4.l1",
    Jewel: "where.changi.terminals.jewel.l1",
  }

  const getTerminal = (localRefStr?: string): string | undefined => {
    if (!localRefStr) return undefined;
    const localRefArr = localRefStr.split(".");
    return localRefArr.at(-2)?.toUpperCase();
  };
  const terminal = getTerminal(localRef);
  // If navigate from details flight, change chosen terminal's default floor to l2
  if (!!terminal && !!defaultFloors?.[terminal] && !!isFocusToArea) {
    defaultFloors[terminal] = localRef.split(".").slice(0, -1).join(".") + ".l2";
  }

  const changeFocus = async (level: SelectItem) => {
    if (level.taxonomyPath !== currentPoint?.whereDimension) {
      calloutRef?.current?.hideCallout()
    } else {
      calloutRef?.current?.showCallout()
    }
    changeFocusToTax(level.taxonomyPath)
    setActiveFloor(level)
    activeFloorRef.current = level
  }

  const changeFocusToTax = (taxonomyPath: string) => {
    ; (atoms.current as any).focusTo(taxonomyPath, false)
    trackAction(AdobeTagName.CAppATOMSLevelSelector, {
      [AdobeTagName.CAppATOMSLevelSelector]: `${getAATagLevelSelector(taxonomyPath)}`,
    })
  }

  const onReloadData = () => {
    const onReload = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (isConnected) {
        setNoConnection(false)
      }
    }
    onReload()
  }

  const onSearchStatusUpdate = (value) => {
    if (value) {
      setLoading(false)
      if (localRef === undefined) {
        Platform.OS === 'ios' && setTimeout(() => {
          setDelayOnMove(false)
        }, 3000)
      } else {
        if (isFocusToArea && localRef) {
          (atoms.current as any).focusTo(localRef, true);
        } else {
          findByLocalRef(localRef);
        }
        Platform.OS === 'ios' && setTimeout(() => {
          setDelayOnMove(false)
        }, 300)
      }
    }
  }

  const onRoutingStatusUpdate = (value) => {
    if (value) {
      saveRoutingStatusUpdate(true)
    }
  }

  const handleNavigate = (content: any, id: string) => {
    if (
      content?.whatDimension === "what.shop.retail" ||
      content?.whatDimension === "what.shop.retail_anchor"
    ) {
      navigation.navigate(NavigationConstants.shopDetailsScreen, {
        tenantId: id,
        name: content.name,
      })
    } else {
      navigation.navigate(NavigationConstants.restaurantDetailScreen, {
        tenantId: id,
        name: content.name,
      })
    }
  }

  const onPressMarker = (currentPointParams, tenantId) => {
    setShowViewDetailLocation({ data: currentPointParams, visible: true, tenantId: tenantId })
  }

  const flyNDrop = async (currentPointParams: metaFeature, iswithDefaultZoom: boolean) => {
    const input = {
      id: "",
      localRef: currentPointParams?.properties?.localRef,
    }
    const tenantPageID = await getTenantDetail({ input: input })
    const center = (atoms.current as any).getMidPointOfFeature(currentPointParams)
    calloutRef?.current?.showCallout()
    if (iswithDefaultZoom) {
      ; (atoms.current as any).flyTo({ center: center, zoomLevel: 17.25 })
        ; (atoms.current as any).dropPin(
        currentPointParams,
        <ChangiMapCallout
          content={currentPointParams}
          tenantId={tenantPageID}
          handleNavigate={() => onPressMarker(currentPointParams, tenantPageID)}
          ref={calloutRef}
        />,
      )
    } else {
      const zoom = await (atoms.current as any).getZoom()
        ; (atoms.current as any).flyTo({ center: center, zoomLevel: zoom })
        ; (atoms.current as any).dropPin(
        currentPointParams,
        <ChangiMapCallout
          content={currentPointParams}
          tenantId={tenantPageID}
          handleNavigate={() => onPressMarker(currentPointParams, tenantPageID)}
          ref={calloutRef}
        />,
      )
    }
    setShowViewDetailLocation({
      visible: true,
      data: currentPointParams,
      tenantId: tenantPageID
    })
  }

  const mapPressed = async (_data: any) => {
    if (!showViewDirection) {
      setFirstShowViewDetailLocation(false)
      setLoading(true)
      const lastClicked = (atoms.current as any).getLastClickedFeature()
      if (lastClicked.name !== undefined) {
        const activeFloorTemp: SelectItem = {
          name: lastClicked.whereDimension
            .slice(lastClicked.whereDimension.lastIndexOf(".") + 1, lastClicked.whereDimension.length)
            .toUpperCase(),
          taxonomyPath: lastClicked.whereDimension,
        }
        changeFocusToTax(activeFloorTemp.taxonomyPath)
        await flyNDrop(lastClicked, true)
        unstable_batchedUpdates(() => {
          setActiveFloor(activeFloorTemp)
          activeFloorRef.current = activeFloorTemp
          setCurrentPoint(lastClicked)
          currentPointRef.current = lastClicked
        })
        trackAction(AdobeTagName.CAppATOMSMapClicks, {
          [AdobeTagName.CAppATOMSMapClicks]: `${lastClicked?.name}`,
        })
        trackAction(AdobeTagName.CAppATOMsMap, {
          [AdobeTagName.CAppATOMsMap]: `${AdobeValueByTagName.CAppATOMSMapMapClicks}${lastClicked?.name}`,
        })
      }
      unstable_batchedUpdates(() => {
        setLoading(false)
      })

    }
  }

  const resetState = (atvFloor, targPoint) => {
    setActiveFloor(atvFloor)
    activeFloorRef.current = atvFloor
    setCurrentPoint(targPoint)
    currentPointRef.current = targPoint
    setLoading(false)
  }
  const findByLocalRef = (localRef?: string) => {
    if (!localRef) {
      unableToLoadLocationRef.current.show()
      setLoading(false)
    } else {
      try {
        (atoms.current as any).getMapObjectByLocalRef(localRef, async (data: metaFeature[]) => {
          const target = data?.[0]
          if (!target || !target?.properties?.localRef) {
            unableToLoadLocationRef.current.show()
            setLoading(false)
            return
          }
          const targetPoint: metaFeature = {
            mapObjectId: target?.mapObjectId,
            coordinates: target?.coordinates || target?.geometry?.coordinates,
            name: target?.name,
            whatDimension: target?.whatDimension,
            whereDimension: target?.whereDimension,
            geometry: target?.geometry,
            properties: target?.properties,
          }
          const activeFloorTemp: SelectItem = {
            name: targetPoint.whereDimension
              .slice(targetPoint.whereDimension.lastIndexOf(".") + 1, targetPoint.whereDimension.length)
              .toUpperCase(),
            taxonomyPath: targetPoint.whereDimension,
          }
          changeFocusToTax(activeFloorTemp.taxonomyPath)
          await flyNDrop(targetPoint, true)
          unstable_batchedUpdates(() => {
            resetState(activeFloorTemp, targetPoint)
          })
        })
      }
      catch (error) {
        unableToLoadLocationRef.current.show()
        setLoading(false)
      }
    }
  }

  const onMoveEnd = async () => {
    if (!showViewDirection) {
      const changeFocusPromise = (atoms.current as any).changeTerminalInFocusToUserViewpoint(
        defaultFloors,
      )
      await changeFocusPromise
      const building = (atoms.current as any).getCurrentFocusBuilding()
      const floor = (atoms.current as any).getCurrentFocusFloor()
      if (!floor) {
        return
      }
      unstable_batchedUpdates(() => {
        if (!isEmpty(floor)) {
          setActiveFloor(floor)
          activeFloorRef.current = floor
        }
        setActiveBuildingName(building.name)
        setFloors(building.floors)
        setMapLoadedCount(mapLoadedCount + 1)
        setMoveEnded(true)
      })
      if (showViewDetailLocation?.data) {
        setFirstShowViewDetailLocation(true)
      }
    }
  }

  useEffect(() => {
    const fetchMapProperties = async () => {
      const zoom = await (atoms.current as any).getZoom()
      if (zoom <= DefaultZoom.VALUE) {
        setHideLvlSelector(true)
      } else {
        setHideLvlSelector(false)
      }
    }
    fetchMapProperties()
    isMoveEnded &&
      setLevelSelector(() => (
        <LevelSelector
          building={activeBuildingName}
          levelData={floors}
          onSelectLevel={changeFocus}
          selectedFloor={activeFloor}
          isMoved={mapLoadedCount > 1 && isMapMoved}
          isShouldHide={isShouldHide}
          currentPoint={currentPoint}
        />
      ))
  }, [activeFloor, floors, isMapMoved, mapLoadedCount, isMoveEnded, isShouldHide])

  const navigateSearchResult = async (targetPoint: metaFeature) => {
    setFirstShowViewDetailLocation(false)
      ; (atoms.current as any).hidePin()
    setLoading(true)
    Keyboard.dismiss()
    const activeFloorTemp: SelectItem = {
      name: targetPoint.whereDimension
        .slice(targetPoint.whereDimension.lastIndexOf(".") + 1, targetPoint.whereDimension.length)
        .toUpperCase(),
      taxonomyPath: targetPoint.whereDimension,
    }
    changeFocusToTax(activeFloorTemp.taxonomyPath)
    await flyNDrop(targetPoint, true)
    trackAction(AdobeTagName.CAppATOMSSearchResults, {
      [AdobeTagName.CAppATOMSSearchResults]: `|${targetPoint.name}`,
    })
    unstable_batchedUpdates(() => {
      resetState(activeFloorTemp, targetPoint)
    })
  }

  const closeOrOpenBottomSheet = useCallback(() => {
    setShowBottomSheet(!showBottomSheetSearch);
  }, [showBottomSheetSearch])

  const closeOrOpenBottomSheetMultiple = useCallback(() => {
    setShowBottomSheetMultiple(!showBottomSheetSearchMutiple);
  }, [showBottomSheetSearchMutiple])

  const onDirection = (from, to) => {
    (atoms.current as any).hidePin();
    calloutRef?.current?.hideCallout()
    setShowViewDetailLocation({
      visible: false,
      data: null,
      tenantId: null
    })
    setFirstShowViewDetailLocation(false)
    setDataLocationFrom(from)
    setDataLocationTo(to)
    if (from && to) {
      closeOrOpenBottomSheetMultiple()
      getDirection(from, to, accessibleRoute)
    }
  }

  const reverseLocationBottomSheet = (from, to) => {
    if (from && to) {
      closeOrOpenBottomSheetMultiple()
      getDirection(from, to, accessibleRoute)
    }
  }

  const reverseLocation = useCallback(() => {
    setDataLocationFrom(dataLocationTo)
    setDataLocationTo(dataLocationFrom)
    if (dataLocationTo && dataLocationFrom) {
      getDirection(dataLocationTo, dataLocationFrom, accessibleRoute)
    }
  }, [dataLocationFrom, dataLocationTo, accessibleRoute])

  const getDirection = (from, to, accessible = false) => {
    setLoading(true)
    setTimeout(async () => {
      setDataDirection(null)
      setHighlightedSegment(undefined)
      ;(atoms.current as any).clearRoute()
      const mapObjectIdFrom = from?.mapObjectId
      const mapObjectIdTo = to?.mapObjectId
      const mapObjectIds = [mapObjectIdFrom, mapObjectIdTo]
      const directionData = await (atoms.current as any).computeRoutes(mapObjectIds, accessible)
      if (directionData?.segments?.length === 0) {
        setShowViewFilterLocation(true)
        setShowViewDirection(false)
        setShowViewEmptyDirection(true)
        setTitleHeader(dataLocationTo?.properties?.title)
      } else {
        setShowViewFilterLocation(true)
        setShowViewDirection(true)
        setShowViewEmptyDirection(false)
        setDataDirection(directionData)
        ;(atoms.current as any).setActiveRoute(directionData)
        setHighlightedSegment(0)
      }
      setLoading(false)
    }, 100)
  }

  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        height.value,
        [0, 244, heightScreen - 64],
        ['transparent', 'transparent', color.palette.overlayColor]
      ),
      width: interpolate(height.value, [240, 250, heightScreen - 64], [0, widthScreen, widthScreen]),
      height: interpolate(height.value, [240, 250, heightScreen - 64], [0, heightScreen, heightScreen])
    };
  });

  const onClearRoute = () => {
    ; (atoms.current as any).clearRoute();
    setShowViewDirection(false)
    setShowViewFilterLocation(false)
    setShowViewEmptyDirection(false)
    setAccessibleRoute(false)
    setDataDirection(null)
    setDataLocationFrom(null)
    setStatusViewDirection("OVERVIEW")
    setFirstShowViewDetailLocation(false)
  }

  const onCloseViewDirection = () => {
    setTitleHeader(translate("changimap.header-v2"))
    onClearRoute()
    flyNDrop(dataLocationTo, true)
  }

  const onBack = () => {
    if (showViewFilterLocation) {
      setTitleHeader(translate("changimap.header-v2"))
      onClearRoute()
      flyNDrop(dataLocationTo, true)
    } else if (!showViewFilterLocation && statusViewDirection === "STEP") {
      onExitDirection()
    }
    else {
      navigation.goBack()
    }
  }

  const changeTitleHeader = (step) => {
    if (dataDirection) {
      if (step === 0) {
        const distance = Math.ceil(Number(dataDirection?.segments[0]?.distance))
        const title = `${distance}m ${translate("changimap.to")} ${dataLocationTo?.properties?.title}`
        setTitleHeader(title)
      } else if (step === dataDirection?.segments?.length - 1) {
        const segment = dataDirection?.segments[step]
        const title = `${segment?.endEdge?.name}`
        setTitleHeader(title)
      } else if (step !== 0 || step !== dataDirection?.segments?.length - 1) {
        const segment = dataDirection?.segments[step]
        const title = renderTitleHeaderStepMid(segment)
        setTitleHeader(title)
      }
    }
    else {
      if (showViewFilterLocation) {
        setTitleHeader(dataLocationTo?.properties?.title)
      } else {
        setTitleHeader(translate("changimap.header-v2"))
      }
    }
  }

  const onStartDirection = (value: number) => {
    setShowViewFilterLocation(false);
    setHighlightedSegment(value);
    setStatusViewDirection("STEP");
  }

  const onExitDirection = () => {
    setShowViewFilterLocation(true);
    setHighlightedSegment(0);
    setStatusViewDirection("OVERVIEW");
  }

  const onNextStep = (value: number) => {
    setHighlightedSegment(value);
  }

  const onPreviousStep = (value: number) => {
    setHighlightedSegment(value);
  }

  const onMoveOrZoom = () => {
    if(!showButtonSmall && !delayOnMove){
      setShowButtonSmall(true)
    }
    if (firstShowViewDetailLocation && showViewDetailLocation?.visible && readyViewDetailLocation) {
      setShowViewDetailLocation({ ...showViewDetailLocation, visible: false })
    }
  }

  const initSDK = useMemo(() => {
    return (
      <MetaAtlasSDK
        ref={atoms}
        tileserverRoleName={env()?.CHANGI_MAP_ROLE_NAME}
        accessToken={env()?.CHANGI_MAP_ACCESS_TOKEN}
        onPress={mapPressed}
        onMove={() => {
          setMapMoved(true)
          setMoveEnded(false)
          onMoveOrZoom()
        }}
        onMoveEnd={onMoveEnd}
        maxMapHeight={mapHeight}
        isFullHeight={true}
        secretKey={env()?.CHANGI_MAP_SECRET_KEY}
        onRoutingStatusUpdate={onRoutingStatusUpdate}
        onSearchStatusUpdate={onSearchStatusUpdate}
      />
    )
  }, [mapPressed, onMoveEnd, onMoveOrZoom])

  if (!env()?.CHANGI_MAP_ROLE_NAME || !env()?.CHANGI_MAP_ACCESS_TOKEN) return null
  return (
    <GestureHandlerRootView>
      <View style={styles.container}>
        <StatusBar translucent barStyle={"dark-content"} />
        <View style={styles.container}>
          <ChangiMapHeaderV2 title={titleHeader}
            onBack={onBack}
          />
          {initSDK}
          {!showViewDirection && !showViewFilterLocation && !showViewDetailLocation?.visible && showButtonSmall && floors && levelSelector}
          {showViewEmptyDirection && <ViewEmptyDirection onPress={closeOrOpenBottomSheetMultiple} />}
          {showViewFilterLocation && <ViewFilterLocation
              dataLocationTo={dataLocationTo}
              dataLocationFrom={dataLocationFrom}
              reverseLocation={reverseLocation}
              onClick={closeOrOpenBottomSheetMultiple}
          />}
          {!showViewFilterLocation && <ButtonSearch
              isSmall={showButtonSmall}
              onPress={closeOrOpenBottomSheet}
              containerStyle={{ bottom: showViewDetailLocation?.visible ? 180 : 20 }}
          />}
          {showViewDetailLocation?.visible &&
            <ViewDetailLocation data={showViewDetailLocation?.data}
              onReady={(value) => setReadyViewDetailLocation(value)}
              closeView={() => {
                // (atoms.current as any).hidePin();
                // setDataLocationTo(null)
                // calloutRef?.current?.hideCallout()
                setShowViewDetailLocation({
                  ...showViewDetailLocation,
                  visible: false,
                })
                // setFirstShowViewDetailLocation(false)
              }}
              onPressRouting={(data) => {
                setDataLocationTo(data)
                closeOrOpenBottomSheetMultiple()
              }}
              tenantId={showViewDetailLocation?.tenantId}
              handleNavigate={handleNavigate}
            />
          }
          {showViewDirection && <Animated.View style={[styles.viewBlack, animatedStyle]} />}
          {showViewDirection && dataDirection && <ViewDirection
            onShowHideViewBackground={(value) => height.value = value}
              onCloseViewDirection={onCloseViewDirection}
              data={dataDirection}
              dataLocationTo={dataLocationTo}
              dataLocationFrom={dataLocationFrom}
              searchDirection={(value) => getDirection(dataLocationFrom, dataLocationTo, value)}
              accessibleRoute={accessibleRoute}
              setAccessibleRoute={setAccessibleRoute}
              status={statusViewDirection}
              skipOverview={skipOverview}
              step={highlightedSegment}
              onStartDirection={onStartDirection}
              onExitDirection={onExitDirection}
              onNextStep={onNextStep}
              onPreviousStep={onPreviousStep}
            />
          }
        </View>
        <LoadingOverlay visible={isLoading} customStyle={styles.loadingRoot}/>
        <ErrorOverlayNoConnection
          reload
          header={false}
          hideScreenHeader={true}
          headerBackgroundColor="transparent"
          visible={isNoConnection}
          onReload={onReloadData}
          onBack={() => {
            navigation.goBack()
          }}
          noInternetOverlayStyle={styles.overlayStyle}
        />
        <BottomSheetUnableLoadLocation ref={unableToLoadLocationRef} />
        {showBottomSheetSearch && <BottomSheetSearch visible={showBottomSheetSearch}
            onClosedSheet={closeOrOpenBottomSheet}
            mapRef={atoms}
            onSelectedSearchResult={(item) => {
              if (!showButtonSmall) {
                setShowButtonSmall(true)
              }
              navigateSearchResult(item)
            }}
            searchKeywordCollection={searchKeywordCollection}
        />}
        {showBottomSheetSearchMutiple && <BottomSheetSearchMultiple
            dataLocationTo={dataLocationTo}
            dataLocationFrom={dataLocationFrom}
            visible={showBottomSheetSearchMutiple}
            onClosedSheet={closeOrOpenBottomSheetMultiple}
            mapRef={atoms}
            setDataLocationTo={(dataTo) => {
              setDataLocationTo(dataTo)
            }}
            setDataLocationFrom={(dataFrom) => {
              setDataLocationFrom(dataFrom)
            }}
            onDirection={onDirection}
            reverseLocation={reverseLocationBottomSheet}
        />}
      </View>
    </GestureHandlerRootView>
  )
}

export default AtomMap
