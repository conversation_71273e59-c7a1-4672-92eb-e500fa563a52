import { SortBy } from "app/sections/deals-promos/filter-bottom-sheet/filter-bottom-sheet.constants"
import React, { useCallback, useRef, useState, memo, MutableRefObject, useEffect } from "react"
import { StyleSheet, View, RefreshControl } from "react-native"
import { useSelector } from "react-redux"
import {
  getPreviousScreen,
  useCurrentScreenActiveAndPreviousScreenHook,
} from "app/utils/screen-hook"
import { TrackingScreenName } from "app/utils/constants"
import { useFocusEffect } from "@react-navigation/native"
import { commonTrackingScreen } from "app/services/adobe"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import DealsPromosFilterBar from "app/sections/deals-promos/deals-promos-filter-bar/deals-promos-filter-bar"
import DealsPromosTile from "app/components/deals-promos-tile/deals-promos-tile"
import _isEqual from "lodash/isEqual"
import { useDealsPromosListingRequests } from "app/sections/deals-promos-category-listing/deals-promos-category-listing.hooks"
import { DealsPromosError } from "app/sections/deals-promos-category-listing/deals-promos-error"
import CollapsibleHeader from "app/components/collapsible-header"
import { DealsPromoListingBackground } from "assets/backgrounds/deals-promo-listing"
import { translate } from "app/i18n"
import { color } from "app/theme"

const PERK_ITEM_FIXED_MARGIN = 12

const ItemComponent = memo(
  ({
    dataLength,
    index,
    item,
    navigation,
    offsetRecalculationCount,
    perkItemOffsetListRef,
    setOffsetRecalculationCount,
  }: {
    dataLength?: number
    index: number
    item: any
    navigation?: any
    offsetRecalculationCount?: number
    perkItemOffsetListRef?: MutableRefObject<any>
    setOffsetRecalculationCount?: Function
  }) => {
    return (
      <DealsPromosTile
        dataLength={dataLength}
        index={index}
        item={item}
        navigation={navigation}
        onLayout={(event) => {
          const layoutHeight = event.nativeEvent.layout.height + PERK_ITEM_FIXED_MARGIN
          if (!perkItemOffsetListRef) return
          if (!perkItemOffsetListRef?.current) {
            perkItemOffsetListRef.current = {
              [index]: layoutHeight,
            }
          } else {
            perkItemOffsetListRef.current[index] = layoutHeight
          }
          if (Object.keys(perkItemOffsetListRef.current)?.length === dataLength) {
            setOffsetRecalculationCount(offsetRecalculationCount + 1)
          }
        }}
      />
    )
  },
  (prevProps, nextProps) => !_isEqual(prevProps, nextProps),
)

const DealsPromosListing = ({ navigation }) => {
  const rootItemOffsetRef = useRef([])
  const rootListRef = useRef(null)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const [areaFilters, setAreaFilters] = useState([])
  const [terminalFilters, setTerminalFilters] = useState([])
  const [categoryFilters, setCategoryFilters] = useState([])
  const [sortBy, setSortBy] = useState(SortBy.LatestAddedDate)

  const [offsetRecalculationCount, setOffsetRecalculationCount] = useState(0)
  const perkItemOffsetListRef = useRef([])

  const {
    listData,
    loadingDealsPromosList,
    hasError,
    handlePressReloadError,
    setListData,
    originalListData,
    onRefresh,
  } = useDealsPromosListingRequests()

  useEffect(() => {
    let filteredData = originalListData

    // Apply filters if any are active
    if (areaFilters?.length || terminalFilters?.length || categoryFilters?.length) {
      // Pre-compute filter sets for O(1) average time complexity lookups.
      const areaNamesSet =
        areaFilters?.length > 0 ? new Set(areaFilters.map((a) => a.tagName)) : null

      const terminalCodesSet =
        terminalFilters?.length > 0 ? new Set(terminalFilters.map((t) => t.tagCode)) : null

      const categoryFiltersSet = categoryFilters?.length > 0 ? new Set(categoryFilters) : null

      // Pre-compute special terminal conditions to avoid repeated lookups inside the loop.
      const hasJewelTerminal = terminalCodesSet?.has("jewel") || terminalCodesSet?.has("tj")
      const hasJ1Terminal = terminalCodesSet?.has("j1")
      const hasAllTerminal = terminalCodesSet?.has("all")

      // A single pass over the data combines all filtering logic.
      filteredData = originalListData.filter((item) => {
        // Category filter check (AND logic) - must be satisfied if categoryFilters exist
        if (categoryFiltersSet) {
          // Parse categories string to array if it's a string
          let itemCategories = item.categories
          if (typeof itemCategories === "string") {
            // Parse string like "[SHOPPING_PERKS, NEW_STAFF_PERKS]" to array
            itemCategories = itemCategories
              .replace(/[\[\]]/g, "") // Remove square brackets
              .split(",")
              .map((cat) => cat.trim())
              .filter((cat) => cat.length > 0)
          }

          if (!itemCategories?.some((category) => categoryFiltersSet.has(category))) {
            return false
          }
        }

        // Location filter check (OR logic between area and terminal)
        const hasAreaFilters = areaNamesSet && areaNamesSet.size > 0
        const hasTerminalFilters = terminalCodesSet && terminalCodesSet.size > 0 && !hasAllTerminal

        if (hasAreaFilters || hasTerminalFilters) {
          let hasMatchingLocation = false

          // Check area filters
          if (hasAreaFilters && item.area?.some((area) => areaNamesSet.has(area))) {
            hasMatchingLocation = true
          }

          // Check terminal filters (if area check didn't pass or no area filters)
          if (hasTerminalFilters && !hasMatchingLocation) {
            const hasMatchingTerminal = item.terminal?.some((terminal) => {
              if (terminalCodesSet.has(terminal)) return true
              if (hasJewelTerminal && terminal === "j1") return true
              if (hasJ1Terminal && (terminal === "jewel" || terminal === "tj")) return true
              return false
            })
            if (hasMatchingTerminal) {
              hasMatchingLocation = true
            }
          }

          // If we have location filters but no match found, exclude the item
          if (!hasMatchingLocation) {
            return false
          }
        }

        return true
      })
    }

    // Apply sorting based on sortBy
    let sortedData = [...filteredData]

    if (sortBy === SortBy.AZ) {
      // Sort by tenantName in ascending order
      sortedData.sort((a, b) => {
        const tenantNameA = (a.tenantName || "").toLowerCase()
        const tenantNameB = (b.tenantName || "").toLowerCase()
        return tenantNameA.localeCompare(tenantNameB)
      })
    }
    // For SortBy.LatestAddedDate, keep original order (no sorting needed)

    setListData(sortedData)
  }, [originalListData, areaFilters, terminalFilters, categoryFilters, sortBy])

  // Reset perkItemOffsetListRef when sortBy changes to force recalculation
  useEffect(() => {
    perkItemOffsetListRef.current = []
    setOffsetRecalculationCount(0)
  }, [sortBy])

  useCurrentScreenActiveAndPreviousScreenHook(TrackingScreenName.DealsPromosListing)

  useFocusEffect(
    useCallback(() => {
      commonTrackingScreen(TrackingScreenName.DealsPromosListing, getPreviousScreen(), isLoggedIn)
    }, [navigation, isLoggedIn]),
  )

  const renderFilterBar = () => (
    <DealsPromosFilterBar
      isShow={!loadingDealsPromosList && !hasError}
      areaFilters={areaFilters}
      setAreaFilters={setAreaFilters}
      terminalFilters={terminalFilters}
      setTerminalFilters={setTerminalFilters}
      categoryFilters={categoryFilters}
      setCategoryFilters={setCategoryFilters}
      sortBy={sortBy}
      setSortBy={setSortBy}
    />
  )

  return (
    <View style={styles.container}>
      <CollapsibleHeader
        navigation={navigation}
        headerImageSource={DealsPromoListingBackground}
        headerTitle={translate("dealsPromosScreen.title")}
        renderFilter={renderFilterBar}
        hasError={!!hasError}
        renderError={() => (
          <DealsPromosError
            typeError={hasError}
            handlePressReload={handlePressReloadError}
            rootItemOffsetRef={rootItemOffsetRef}
          />
        )}
        isLoading={loadingDealsPromosList}
        sortBy={sortBy}
        listData={listData}
        perkItemOffsetListRef={perkItemOffsetListRef}
        rootItemOffsetRef={rootItemOffsetRef}
        rootListRef={rootListRef}
        data={listData}
        renderItem={({ index, item }) => {
          return (
            <ItemComponent
              dataLength={listData?.length}
              index={index}
              item={item}
              navigation={navigation}
              offsetRecalculationCount={offsetRecalculationCount}
              perkItemOffsetListRef={perkItemOffsetListRef}
              setOffsetRecalculationCount={setOffsetRecalculationCount}
            />
          )
        }}
        keyExtractor={(item, index) => `${item?.id}-${sortBy}-${index}`}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
        ItemSeparatorComponent={
          !loadingDealsPromosList && hasError === null
            ? () => <View style={styles.itemSeparator} />
            : null
        }
        initialNumToRender={1000}
        maxToRenderPerBatch={50}
        windowSize={50}
        refreshControl={
          <RefreshControl refreshing={false} onRefresh={onRefresh} progressViewOffset={-300} />
        }
        scrollEnabled={listData.length > 0}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
  },
  contentContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    paddingBottom: 24,
  },
  itemSeparator: {
    height: 12,
  },
})
export default DealsPromosListing
