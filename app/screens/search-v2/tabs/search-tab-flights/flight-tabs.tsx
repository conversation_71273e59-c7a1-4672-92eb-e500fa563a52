import React, { memo, useCallback, useEffect, useMemo } from "react"
import { View } from "react-native"
import { FlightArrivalIcon, FlightDepartureIcon } from "ichangi-fe/assets/icons"
import { Text } from "app/elements/text"
import { TAB_WIDTH, TAB_WIDTH_WITH_BORDER, tabSectionStyles } from "./search-flights.styles"
import { color } from "app/theme"
import { FlightDirection } from "app/utils/constants"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  useDerivedValue,
  runOnJS,
} from "react-native-reanimated"
import { ArrivalTab } from "./components/arrival-tab"
import { DepartureTab } from "./components/departure-tab"

const FlightTabs = (props: {
  selectedTab: FlightDirection
  content: React.ReactElement
  onSelectTab: (type: FlightDirection) => void
}) => {
  const { selectedTab, onSelectTab } = props

  const arrivalX = useSharedValue(
    selectedTab === FlightDirection.Arrival ? 0 : TAB_WIDTH_WITH_BORDER,
  )

  const departureX = useSharedValue(
    selectedTab === FlightDirection.Arrival ? -TAB_WIDTH_WITH_BORDER : 0,
  )
  const contextX = useSharedValue(0)
  const contentFollowX = useDerivedValue(() => contextX.value + TAB_WIDTH_WITH_BORDER)

  useEffect(() => {
    if (selectedTab === FlightDirection.Arrival) {
      arrivalX.value = withTiming(0, { duration: 0 })
      departureX.value = withTiming(-TAB_WIDTH_WITH_BORDER, { duration: 0 })
      contextX.value = withTiming(0, { duration: 100 })
    } else {
      arrivalX.value = withTiming(TAB_WIDTH_WITH_BORDER, { duration: 0 })
      departureX.value = withTiming(0, { duration: 0 })
      contextX.value = withTiming(-TAB_WIDTH_WITH_BORDER, { duration: 100 })
    }
  }, [selectedTab])

  const arrivalStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: arrivalX.value }],
    position: "absolute",
    width: TAB_WIDTH,
  }))

  const departureStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: departureX.value }],
    position: "absolute",
    width: TAB_WIDTH,
  }))

  const contextStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: contextX.value }],
  }))

  const contentFollowStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: contentFollowX.value }],
  }))

  const handleSelectedTab = useCallback(
    (direction: FlightDirection.Arrival) => {
      const animationCallback = (isFinished) => {
        if (isFinished) {
          if (isFinished) {
            runOnJS(onSelectTab)(direction)
          }
        }
      }

      if (direction === FlightDirection.Arrival) {
        arrivalX.value = withSequence(
          withTiming(TAB_WIDTH - TAB_WIDTH / 2, { duration: 0 }),
          withTiming(0, { duration: 300 }),
        )
        departureX.value = withTiming(-TAB_WIDTH_WITH_BORDER, { duration: 0 })
        contextX.value = withTiming(0, { duration: 300 }, animationCallback)
      } else {
        arrivalX.value = withTiming(TAB_WIDTH_WITH_BORDER, { duration: 0 })
        departureX.value = withSequence(
          withTiming(-(TAB_WIDTH - TAB_WIDTH / 2), { duration: 0 }),
          withTiming(0, { duration: 300 }),
        )
        contextX.value = withTiming(-TAB_WIDTH_WITH_BORDER, { duration: 300 }, animationCallback)
      }
    },
    [arrivalX, departureX, onSelectTab, selectedTab],
  )

  const clonedContent = useMemo(() => React.cloneElement(props.content), [props.content])

  return (
    <View style={tabSectionStyles.flightTabContainer}>
      <View style={tabSectionStyles.container}>
        <View style={tabSectionStyles.tabHeaderWrapperInActive}>
          <View style={tabSectionStyles.tabItemHeaderContainer}>
            <FlightArrivalIcon width={24} height={24} color={color.palette.midGrey} />
            <Text tx={"flightLanding.arrivalTabTitle"} style={tabSectionStyles.tabTitleInactive} />
          </View>
          <View style={tabSectionStyles.tabItemHeaderContainer}>
            <FlightDepartureIcon width={24} height={24} color={color.palette.midGrey} />
            <Text
              tx={"flightLanding.departureTabTitle"}
              style={tabSectionStyles.tabTitleInactive}
            />
          </View>
        </View>

        <Animated.View style={arrivalStyle}>
          <ArrivalTab onSelectTab={handleSelectedTab} />
        </Animated.View>
        <Animated.View style={departureStyle}>
          <DepartureTab onSelectTab={handleSelectedTab} />
        </Animated.View>
        <Animated.View style={[tabSectionStyles.tabContentParentContainer, contextStyle]}>
          <View style={tabSectionStyles.tabContentContainer}>{props.content}</View>
        </Animated.View>
        <Animated.View style={[tabSectionStyles.tabContentParentContainer, contentFollowStyle]}>
          <View style={tabSectionStyles.tabContentContainer}>{clonedContent}</View>
        </Animated.View>
      </View>
    </View>
  )
}

export default memo(FlightTabs)
