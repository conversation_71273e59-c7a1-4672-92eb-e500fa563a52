import React, { useState, useCallback, useEffect, useRef } from "react"
import { Alert, Keyboard, Platform, Pressable, SafeAreaView, View } from "react-native"
import NetInfo from "@react-native-community/netinfo"
import { useDispatch, useSelector } from "react-redux"
import { debounce } from "lodash"
import moment from "moment"

import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import TabContent from "./components/tab-content"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { Text } from "app/elements/text"
import { Cross } from "assets/icons"
import { bottomSheetStyles, rootStyles, tabContentStyles } from "./search-flights.styles"
import AutocompleteSearch from "./components/autocomplete-search"
import SearchActions, { SearchSelectors } from "app/redux/searchRedux"
import SearchBar from "app/sections/search-bar/search-bar"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import { translate } from "app/i18n"
import Calendar from "./components/calendar"
import { BottomSheetType, SeachType } from "./consts"
import { useNavigation } from "@react-navigation/native"
import { FlightDirection, NavigationConstants } from "app/utils/constants"
import { REMOTE_FLAG_VALUE } from "app/services/firebase/remote-config"
import { AemSelectors } from "app/redux/aemRedux"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { openSettings, PERMISSIONS, request, RESULTS } from "react-native-permissions"
import SwitchToast from "./components/switch-toast"
import FlightTabs from "./flight-tabs"
import { useModal } from "app/hooks/useModal"

const SCREEN_NAME = "SearchFlightsV2__"

const SearchFlightsTab = (props) => {
  const dispatch = useDispatch()
  const switchToastRef = useRef(null)
  const navigation = useNavigation<any>()
  const autoCompleteKeywork = useSelector(SearchSelectors.searchKeyword)
  const isFirstTimeEnterFlightSearch = useSelector(SearchSelectors.isFirstTimeEnterFlightSearch)
  const [keySearch, setKeySearch] = useState(props.keyword ?? autoCompleteKeywork)
  const [isNoConnection, setNoConnection] = useState(false)
  const [selectedTab, setSelectedTab] = useState<FlightDirection>(
    props.direction ?? FlightDirection.Arrival,
  )
  const {isModalVisible, openModal, closeModal} = useModal("searchTabFlightFilter")
  const [bottomSheetType, setBottomSheetType] = useState(BottomSheetType.AutoComplete)
  const [selectedDate, setSelectedDate] = useState(props.date ?? moment())
  const messageCommon = useSelector(AemSelectors.getMessagesCommon)
  const msg61 = messageCommon?.find((e) => e?.code === "MSG61")
  const msg62 = messageCommon?.find((e) => e?.code === "MSG62")

  const Rationale = {
    title: msg61?.title || translate("requestPermission.camera.title"),
    message: msg61?.message || translate("requestPermission.camera.message"),
    buttonPositive: msg61?.secondButton || translate("requestPermission.camera.buttonPositive"),
    buttonNegative: msg61?.firstButton || translate("requestPermission.camera.buttonNegative"),
  }

  const handleScan = () => {
    trackAction(AdobeTagName.CAppSearchResultFlyScanBoardingPass, {
      [AdobeTagName.CAppSearchResultFlyScanBoardingPass]: "1",
    })
    request(
      Platform.OS === "ios" ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA,
      Rationale,
    ).then((result) => {
      if (result === RESULTS.BLOCKED) {
        Alert.alert(
          msg62?.title || translate("scanCode.needAccessPermission.title"),
          msg62?.message || translate("scanCode.needAccessPermission.description"),
          [
            {
              text: msg62?.firstButton || translate("scanCode.needAccessPermission.firstButton"),
              style: "cancel",
              onPress: () => {
                openSettings()
              },
            },
            {
              text: msg62?.secondButton || translate("scanCode.needAccessPermission.secondButton"),
              onPress: () => null,
            },
          ],
        )
      } else if (result === RESULTS.GRANTED) {
        navigation.navigate("scanCode", { shouldTrackDetectedFlightNumber: true })
      }
    })
  }

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setNoConnection(false)
    } else {
      setNoConnection(true)
    }
  }

  const checkEnterSearchFlightFirstTime = () => {
    if (!isFirstTimeEnterFlightSearch) {
      switchToastRef?.current?.show?.()
      dispatch(SearchActions.setFirstTimeEnterFlightSearch())
    }
  }

  useEffect(() => {
    checkInternet()
    checkEnterSearchFlightFirstTime()
  }, [])

  const onGetAutoCompleteKeyword = useCallback(
    (newKeyword: string) => {
      const param = { text: newKeyword.trim(), dataType: Object.values(SeachType).join(",") }
      dispatch(SearchActions.getAutoCompleteKeywordRequest(param))
      dispatch(SearchActions.setSearchKeyword(newKeyword))
    },
    [dispatch],
  )

  const onDebounceKeySearch = useCallback(debounce(onGetAutoCompleteKeyword, 200), [
    onGetAutoCompleteKeyword,
  ])

  const handleSearchKeywordChange = useCallback(
    (newKeyword: string) => {
      if (newKeyword.trim().length > 1) return onDebounceKeySearch(newKeyword)
      dispatch(SearchActions.resetListAutoCompleteKeyword())
      dispatch(SearchActions.setSearchKeyword(newKeyword))
    },
    [onDebounceKeySearch, autoCompleteKeywork],
  )

  const updateKeySearch = useCallback(
    (value: string) => {
      setKeySearch(value)
      closeModal()
      dispatch(SearchActions.setSearchKeyword(value))
      handleSearchKeywordChange(value)
    },
    [handleSearchKeywordChange, dispatch],
  )

  const onPressAutoCompleteItem = (item, index) => {
    const itemPosition = `${index + 1}`
    const tabLabel = translate("search.tabTitles.flights")
    const flightDirectionLabel = selectedTab === FlightDirection.Arrival ? translate("flightLanding.arrivalTabTitle") : translate("flightLanding.departureTabTitle")
    trackAction(AdobeTagName.CAppSearchAutoComplete, {
      [AdobeTagName.CAppSearchAutoComplete]: `${tabLabel}(${flightDirectionLabel}) | ${autoCompleteKeywork} | ${item?.name} | ${itemPosition}`
    })

    updateKeySearch(item.name)
  }

  const renderTabContent = useCallback(
    () => (
      <TabContent
        searchTerm={keySearch}
        date={selectedDate}
        onSearchTermPress={() => {
          openModal()
          handleSearchKeywordChange(keySearch)
          setBottomSheetType(BottomSheetType.AutoComplete)
        }}
        onDatePress={() => {
          openModal()
          setBottomSheetType(BottomSheetType.DatePicker)
        }}
        onSearch={() => {
          if (props.onSearch) {
            return props.onSearch({
              keyword: keySearch,
              date: selectedDate,
              direction: selectedTab,
            })
          }
          const params = {
            keyword: keySearch,
            date: selectedDate,
            direction: selectedTab,
            onGoBack: (params) => {
              if (!params) return
              params.direction && setSelectedTab(params.direction)
              params.date && setSelectedDate(params.date)
              params.keyword && setKeySearch(params.keyword)
            },
          }

          if (props.isPushNavigation) {
            navigation.push(NavigationConstants.searchFlightsV2Result, params)
          } else {
            navigation.navigate(NavigationConstants.searchFlightsV2Result, params)
          }
        }}
        onScanBoardingPass={handleScan}
      />
    ),
    [keySearch, selectedDate, selectedTab],
  )
  return isNoConnection ? (
    <ErrorOverlayNoConnection
      reload
      visible
      header={false}
      hideScreenHeader={false}
      onReload={checkInternet}
      headerBackgroundColor="transparent"
      testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
      accessibilityLabel={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
      noInternetOverlayStyle={rootStyles.overlayStyle}
    />
  ) : (
    <SafeAreaView style={rootStyles.containerStyle}>
      <FlightTabs
        selectedTab={selectedTab}
        onSelectTab={setSelectedTab}
        content={renderTabContent()}
      />
      <SwitchToast ref={switchToastRef} />
      <BottomSheet
        isModalVisible={isModalVisible}
        onClosedSheet={closeModal}
        stopDragCollapse
        onBackPressHandle={closeModal}
        containerStyle={bottomSheetStyles.container}
        animationInTiming={300}
        animationOutTiming={300}
        openPendingModal
      >
        <View style={bottomSheetStyles.headerContainer}>
          <Text
            style={bottomSheetStyles.headerTitle}
            tx={
              bottomSheetType === BottomSheetType.DatePicker
                ? "searchV2.flightsTab.datePickerTitle"
                : `searchV2.flightsTab.${selectedTab}.title`
            }
          />
          <View style={bottomSheetStyles.headerIcon}>
            <Cross width={24} height={24} onPress={closeModal}/>
          </View>
        </View>

        {bottomSheetType === BottomSheetType.DatePicker ? (
          <Calendar
            selectedDate={selectedDate}
            onSelectDate={(date) => {
              setSelectedDate(date)
              closeModal()
            }}
          />
        ) : (
          <Pressable style={bottomSheetStyles.contentContainer} onPress={Keyboard.dismiss}>
            <SearchBar
              useInputFieldV2
              searchAutoCompleteFlag={REMOTE_FLAG_VALUE.ON}
              containerStyle={bottomSheetStyles.searchBar}
              inputContainerStyle={bottomSheetStyles.searchInput}
              inputProps={{
                placeholder: translate("searchV2.placeHolder.flights1"),
                placeholderList: [
                  translate("searchV2.placeHolder.flights1"),
                  translate("searchV2.placeHolder.flights2"),
                ],
              }}
              isShowBack={false}
              tab={SearchIndex.flights}
              keyword={keySearch}
              onChangeKeyword={handleSearchKeywordChange}
              onSearchClear={() => {
                dispatch(SearchActions.setSearchKeyword(""))
                handleSearchKeywordChange("")
              }}
              onSubmitLocal={() => {
                updateKeySearch(autoCompleteKeywork)
                Keyboard.dismiss()
              }}
              testID={`${SCREEN_NAME}SearchBar`}
              accessibilityLabel={`${SCREEN_NAME}SearchBar`}
              returnKeyType="previous"
            />
            <View style={bottomSheetStyles.textContainer}>
              <Text
                tx={`searchV2.flightsTab.${selectedTab}.information`}
                style={bottomSheetStyles.informationText}
              />
              <Text
                style={bottomSheetStyles.askText}
                tx={`searchV2.flightsTab.${selectedTab}.ask`}
                onPress={() =>
                  setSelectedTab(
                    selectedTab === FlightDirection.Arrival
                      ? FlightDirection.Departure
                      : FlightDirection.Arrival,
                  )
                }
              />
            </View>
            <AutocompleteSearch
              containerStyle={tabContentStyles.autocompleteContainerStyle}
              keySearch={autoCompleteKeywork.trim()}
              flightType={selectedTab}
              handleItemOnPress={onPressAutoCompleteItem}
            />
          </Pressable>
        )}
      </BottomSheet>
    </SafeAreaView>
  )
}

export default SearchFlightsTab
