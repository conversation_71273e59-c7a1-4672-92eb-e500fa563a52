import React, { use<PERSON><PERSON>back, useMemo } from "react"
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  ViewStyle,
  ListRenderItem,
} from "react-native"
import { useSelector } from "react-redux"
import { isEmpty } from "lodash"
import { Text } from "app/elements/text"
import { color, spacing } from "app/theme"
import { palette } from "app/theme/palette"
import { SearchSelectors } from "app/redux/searchRedux"
import { getUriImage } from "app/utils/screen-helper"
import { SeachType as SearchType } from "../consts"
import Responsive from "app/utils/responsive"
import BaseImage from "app/elements/base-image/base-image"
import { Plane } from "assets/icons"
import { FlightDirection } from "app/utils/constants"

interface AutocompleteSearchProps {
  keySearch?: string
  flightType: FlightDirection
  handleItemOnPress?: (item: IItemAutocomplete, index: number) => void
  containerStyle?: ViewStyle
  selector?: (state: any) => IItemAutocomplete[];
}

export interface IItemAutocomplete {
  name: string
  code: string
  dataType: string
  navigation: any
  image: string
  text: string
  direction: string
}

const SEPARATE_CHAR = "·"

const AutocompleteSearch = ({
  keySearch = "",
  flightType,
  handleItemOnPress,
  containerStyle,
  selector = SearchSelectors.autoCompleteKeywordList,
}: AutocompleteSearchProps) => {
  const autoCompleteKeywordList: IItemAutocomplete[] = useSelector(selector)

  const filteredList = useMemo(
    () =>
      autoCompleteKeywordList.filter(
        (item) =>
          item.dataType?.toLowerCase() !== SearchType.Flights || item.direction === flightType,
      ),
    [autoCompleteKeywordList, flightType],
  )

  const rootStyles = useMemo(() => [styles.container, containerStyle], [containerStyle])

  const escapeRegExp = useCallback((str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), [])

  const renderTextAutoComplete = useCallback(
    (item: IItemAutocomplete) => {
      if (!keySearch) return <Text style={styles.textItemStyles}>{item.name}</Text>

      const regex = new RegExp(`(${escapeRegExp(keySearch)})`, "gi")
      const parts = item.name.split(regex)

      let keywordCount = 0
      const highlightedText = parts.map((part, index) => {
        const isMatch = part.toLowerCase() === keySearch.toLowerCase()
        if (isMatch && keywordCount === 0) {
          keywordCount++
          return (
            <Text key={index} style={styles.textItemHighlight} preset="bodyTextBold">
              {part}
            </Text>
          )
        }
        return <Text key={index}>{part}</Text>
      })

      return (
        <View style={styles.root}>
          <Text style={styles.textItemStyles} numberOfLines={1}>
            {highlightedText}
          </Text>
          {item.dataType?.toLowerCase() === SearchType.Flights && (
            <>
              <Text style={styles.resultName}>{SEPARATE_CHAR}</Text>
              <Text preset="caption2Regular">{item.text}</Text>
            </>
          )}
        </View>
      )
    },
    [keySearch, escapeRegExp],
  )

  const renderItem: ListRenderItem<IItemAutocomplete> = useCallback(
    ({ item, index }) => {
      const renderLogo = () => {
        if (item.dataType !== SearchType.Airlines) return

        if (isEmpty(item?.image))
          return (
            <View style={styles.defaultLogoBox}>
              <Plane width={16} height={16} color={color.palette.darkGrey999} />
            </View>
          )
        return (
          <View style={styles.iconContainer}>
            <BaseImage source={{ uri: getUriImage(item.image) }} style={styles.iconStyles} />
          </View>
        )
      }
      return (
        <TouchableOpacity
          style={styles.itemViewContainer}
          onPress={() => handleItemOnPress?.(item, index)}
        >
          {renderLogo()}
          {renderTextAutoComplete(item)}
        </TouchableOpacity>
      )
    },
    [handleItemOnPress, renderTextAutoComplete],
  )

  return (
    <View style={rootStyles}>
      <FlatList
        data={filteredList}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        initialNumToRender={10}
        maxToRenderPerBatch={8}
        windowSize={10}
        getItemLayout={(_, index) => ({
          length: 56,
          offset: 56 * index,
          index,
        })}
      />
    </View>
  )
}

export default React.memo(AutocompleteSearch)

const styles = StyleSheet.create({
  root: {
    flexDirection: "row",
    alignItems: "center",
  },
  container: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
    marginTop: spacing[3],
  },
  iconContainer: {
    borderRadius: 5,
    height: 24,
    marginRight: 10,
    overflow: "hidden",
    width: 24,
  },
  defaultLogoBox: {
    height: 24,
    width: 24,
    justifyContent: "center",
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    alignItems: "center",
    marginRight: 12,
  },
  iconStyles: {
    height: 24,
    width: 24,
  },
  resultName: {
    color: palette.almostBlackGrey,
    marginHorizontal: 4,
  },
  itemViewContainer: {
    alignContent: "center",
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
    flexDirection: "row",
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  textItemHighlight: {
    color: color.palette.almostBlackGrey,
  },
  textItemStyles: {
    color: color.palette.almostBlackGrey,
    fontSize: Responsive.getFontSize(16),
  },
})
