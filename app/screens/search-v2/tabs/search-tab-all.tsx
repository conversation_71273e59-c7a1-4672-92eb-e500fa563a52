import React, { useEffect, useState, useContext, useCallback } from "react"
import { StyleSheet, Keyboard, ScrollView, Pressable } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import { useIsFocused } from "@react-navigation/native"
import NetInfo from "@react-native-community/netinfo"
import { SearchRecentSectionType } from "app/sections/search-recent-v2/search-recent-section.props"
import SearchRecentSection from "app/sections/search-recent-v2/search-recent-section"
import SearchPopularSection from "app/sections/search-popular-v2/search-popular-section"
import SearchActions, { SearchSelectors } from "app/redux/searchRedux"
import { presets } from "app/elements/text"
import { color } from "app/theme"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { AdobeTagName, commonTrackingScreen, trackAction } from "app/services/adobe"
import { trackActionNewFormat } from "app/utils/screen-helper"
import SearchScreenContext, { getSearchTabName } from "app/screens/search/search-screen-context"
import {
  getPreviousScreen,
  useCurrentScreenActiveAndPreviousScreenHook,
} from "app/utils/screen-hook"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { useRecentSearch } from "app/screens/search/recent-search-hook"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import SearchBar from "app/sections/search-bar/search-bar"
import { debounce, isEmpty } from "lodash"
import AutocompleteSearch, { IItemAutocomplete } from "../components/autocomplete-search"
import { translate } from "app/i18n"
import { NavigationConstants } from "app/utils/constants"
import { REMOTE_FLAG_VALUE } from "app/services/firebase/remote-config"

const SCREEN_NAME = "SearchAll__"

const SearchTabAll = ({ navigation, autoFocusTextInput: autoFocusTextInputProp = false }) => {
  const dispatch = useDispatch()
  const isFocused = useIsFocused()
  const { isPressSearchKey, setIsPressSearchKey } = useContext(SearchScreenContext)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const { addNewRecentSearchKeyword } = useRecentSearch()
  const [isNoConnection, setNoConnection] = useState(false)
  const keyword = useSelector(SearchSelectors.searchKeyword)
  const popularSearchKeywordList = useSelector(SearchSelectors.popularSearchKeywordList)
  const [keySearch, setKeySearch] = useState("")
  const keySearchLength = !isEmpty(keySearch) && keySearch?.trim()?.length
  const onGetAutoCompleteKeyword = (newKeyword: string) => {
    const param = {
      text: newKeyword?.trim(),
      dataType: "",
    }
    dispatch(SearchActions.setViewAllFlight(false))
    dispatch(SearchActions.getAutoCompleteKeywordRequest(param))
  }
  const [autoFocusTextInput, setAutoFocusTextInput] = useState(false)

  const onDebounceKeySearch = useCallback(debounce(onGetAutoCompleteKeyword, 200), [])

  const { handleNavigation } = useHandleNavigation("SEARCH_TAB_ALL")

  useCurrentScreenActiveAndPreviousScreenHook("Search_All")
  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      commonTrackingScreen("Search_All", getPreviousScreen(), isLoggedIn)
    })
    return unsubscribeFocus
  }, [navigation])

  useEffect(() => {
    const checkInternet = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setNoConnection(true)
      }
    }
    checkInternet()
    return () => {
      dispatch(SearchActions.searchAllV2Reset())
    }
  }, [])

  useEffect(() => {
    return () => {
      if (isFocused) {
        dispatch(SearchActions.searchAllV2Reset())
      }
    }
  }, [keyword, isFocused])

  useEffect(() => {
    if (isFocused && autoFocusTextInputProp) {
      // Manually trigger TextInput focus when tab becomes active.
      // Delay avoids Android focus glitches during tab transition.
      setTimeout(() => {
        setAutoFocusTextInput(true)
      }, 100)
    }

    if (!isFocused) {
      dispatch(SearchActions.resetListYouMayAlsoLikeData())
    }
  }, [isFocused])

  useEffect(() => {
    if (isPressSearchKey) {
      searchAll(keyword)
      setIsPressSearchKey(false)
    }
  }, [isPressSearchKey])

  const searchAll = async (newKeyword: string) => {
    const textSearch = newKeyword?.trim()
    if (!textSearch || textSearch?.length < 2) {
      return
    }
    dispatch(SearchActions.setSearchKeyword(keyword))
    navigation.navigate(NavigationConstants.searchResult)
  }

  const onReloadData = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setNoConnection(false)
      searchAll(keyword)
    } else {
      setNoConnection(true)
    }
  }

  if (isNoConnection) {
    return (
      <ErrorOverlayNoConnection
        reload
        header={false}
        headerBackgroundColor="transparent"
        hideScreenHeader={false}
        visible
        testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
        accessibilityLabel={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
        onReload={onReloadData}
        noInternetOverlayStyle={styles.overlayStyle}
      />
    )
  }

  const handleSearchKeywordChange = (newKeyword: string) => {
    if (!isEmpty(keyword) && keyword !== newKeyword) {
      dispatch(SearchActions.setSearchKeyword(""))
    }
    setKeySearch(newKeyword)
    if (newKeyword?.trim()?.length > 1) {
      onDebounceKeySearch(newKeyword)
    } else {
      dispatch(SearchActions.resetListAutoCompleteKeyword())
    }
  }

  const handleAutoCompleteOnPress = (item: IItemAutocomplete, index: number) => {
    const searchTabName = getSearchTabName(SearchIndex.all)
    const itemPosition = `${index + 1}`
    trackAction(AdobeTagName.CAppSearchAutoComplete, {
      [AdobeTagName.CAppSearchAutoComplete]: `${searchTabName} | ${keySearch} | ${item?.name} | ${itemPosition}`
    })
    if (isEmpty(item.navigation)) {
      setKeySearch("")
      addNewRecentSearchKeyword(item.name)
      if (item.locationFilter) {
        dispatch(SearchActions.setSearchKeyword(item.originalKeyword))
        navigation.navigate(NavigationConstants.searchResult, {
          locationFilter: item.locationFilter,
        })
      } else {
        dispatch(SearchActions.setSearchKeyword(item.name))
        navigation.navigate(NavigationConstants.searchResult)
      }
    } else {
      const { navigation } = item
      handleNavigation(navigation?.type, navigation?.value, navigation?.redirect || {})
    }
  }

  return (
    <>
      <Pressable style={styles.container} onPress={Keyboard.dismiss}>
        <SearchBar
          useInputFieldV2
          containerStyle={styles.wrapSearchBar}
          inputContainerStyle={styles.inputContainerStyle}
          inputProps={{
            placeholder: translate("searchV2.placeHolder.all1"),
            inputFieldStyle: {
              inputGroupStyle: styles.inputGroupStyle,
            },
            placeholderList: [
              translate("searchV2.placeHolder.all1"),
              translate("searchV2.placeHolder.all2"),
              translate("searchV2.placeHolder.all3"),
            ],
          }}
          isShowBack={false}
          tab={SearchIndex.all}
          keyword={keyword}
          onChangeKeyword={(s) => {
            handleSearchKeywordChange(s)
          }}
          onSearchClear={() => {
            dispatch(SearchActions.setSearchKeyword(""))
            handleSearchKeywordChange("")
          }}
          onSubmitEditing={() => {
            setKeySearch("")
          }}
          searchAutoCompleteFlag={REMOTE_FLAG_VALUE.ON}
          testID={`${SCREEN_NAME}SearchBar`}
          accessibilityLabel={`${SCREEN_NAME}SearchBar`}
          autoFocusTextInput={autoFocusTextInput}
        />
        {keySearchLength > 1 ? (
          <AutocompleteSearch
            containerStyle={styles.autocompleteContainerStyle}
            keySearch={keySearch?.trim()}
            handleItemOnPess={handleAutoCompleteOnPress}
          />
        ) : (
          <ScrollView
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            keyboardDismissMode="on-drag"
            keyboardShouldPersistTaps="always"
          >
            <SearchRecentSection type={SearchRecentSectionType.default} />
            <SearchPopularSection
              data={
                popularSearchKeywordList?.find((item) => item?.searchCategoryTab === "all")
                  ?.popularKeywords
              }
              searchIndex={SearchIndex.all}
        />
          </ScrollView>
        )}
      </Pressable>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
  },
  autocompleteContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  containerEvent: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  containerFacilities: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  dividerStyle: {
    backgroundColor: color.palette.lighterGrey,
    height: 1,
    marginTop: 16,
  },
  emptySectionListStyle: {
    alignItems: "center",
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  flatListItemStyle: {
    marginBottom: 12,
  },
  footerEventSectionStyles: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24,
  },
  footerFacilityAndServiceSectionStyles: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24,
  },
  footerSectionStyles: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10,
    marginTop: 12,
  },
  headerEventSectionStyle: {
    marginBottom: 7,
  },
  headerSectionStyle: {
    ...presets.h4,
    color: color.palette.almostBlackGrey,
    letterSpacing: 0.96,
    lineHeight: 22,
    marginBottom: 16,
    marginTop: 40,
  },
  itemResultStyle: {
    marginBottom: 12,
  },
  moreTextStyles: {
    ...presets.bodyTextBold,
    color: color.palette.lightPurple,
  },
  overlayStyle: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
    marginBottom: 80,
  },
  sectionContainerStyles: {
    paddingHorizontal: 20,
  },
  wrapSearchBar: {
    backgroundColor: color.palette.almostWhiteGrey,
    marginTop: 20,
    paddingLeft: 16,
    paddingRight: 16,
  },
  inputContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  iconLeft: {
    marginTop: 11,
    marginLeft: 5,
    marginRight: 10,
  },
  inputGroupStyle: {
    borderColor: color.palette.lightGrey,
    height: 44,
  },
})

export { SearchTabAll }
