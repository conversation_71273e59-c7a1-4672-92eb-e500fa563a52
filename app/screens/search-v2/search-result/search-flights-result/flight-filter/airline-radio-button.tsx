import * as React from "react"
import { Pressable, StyleSheet, View, ViewStyle } from "react-native"
import { color } from "app/theme"
import { Text } from "app/elements/text"
import { isEmpty } from "lodash"
import { presets } from "app/elements/text/text.presets"
import BaseImage from "app/elements/base-image/base-image"
import { Plane } from "assets/icons"
import { FILTER_ALL } from "../consts"

type Props = {
  text: string
  state?: "airline" | "airport"
  onPress: () => void
  tx?: string
  imageUrl?: string
  disabled?: boolean
  testID?: string
  accessibilityLabel?: string
  innerContainer?: ViewStyle
  outerContainer?: ViewStyle
  textContainer?: ViewStyle
  customStyle?: ViewStyle
}

export function RadioButton(props: Props) {
  const {
    text,
    state = "airline",
    tx,
    imageUrl,
    onPress,
    disabled,
    outerContainer,
    testID = "AirlineRadioButton",
    accessibilityLabel = "AirlineRadioButton",
  } = props
  const outerCircle = outerContainer ? outerContainer : styles.outerRadioRingStyle
  const textContainer = props.textContainer ? props.textContainer : styles.textContainerStyle
  const [logoError, setLogoError] = React.useState(false)

  const renderLogo = () => {
    if (state === "airport") return

    if (isEmpty(imageUrl))
      return (
        text !== FILTER_ALL && (
          <View style={styles.defaultLogoBox}>
            <Plane width={16} height={16} color={color.palette.darkGrey999} />
          </View>
        )
      )
    return (
      <View style={styles.logoContainer}>
        {logoError && (
          <View style={styles.fallBackLogoContainer}>
            <Plane width={16} height={16} color={color.palette.darkGrey999} />
          </View>
        )}
        <BaseImage
          source={{ uri: imageUrl }}
          resizeMode="cover"
          style={styles.logoStyles}
          onError={() => {
            setLogoError(true)
          }}
        />
      </View>
    )
  }

  return (
    <Pressable
      style={[styles.radioOptionsInnerContainerStyle, props?.customStyle]}
      onPress={onPress}
      disabled={disabled}
      testID={`${testID}__Pressable`}
      accessibilityLabel={`${accessibilityLabel}__Pressable`}
    >
      <View style={outerCircle}>
        <View style={props.innerContainer ? props.innerContainer : styles.innerRadioCircleStyle} />
      </View>
      {renderLogo()}

      <View style={textContainer}>
        <Text tx={tx} text={text || tx} style={styles.textStyles} numberOfLines={1} />
      </View>
    </Pressable>
  )
}

const styles = StyleSheet.create({
  outerRadioRingStyle: {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center",
  },
  innerRadioCircleStyle: {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: color.palette.lightPurple,
  },
  textStyles: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
  },
  textContainerStyle: {
    marginLeft: 12,
    flex: 1,
  },
  radioOptionsInnerContainerStyle: {
    flexDirection: "row",
    alignItems: "center",
  },
  logoStyles: {
    height: 24,
    width: 24,
  },
  logoContainer: {
    height: 24,
    width: 24,
    borderRadius: 8,
    overflow: "hidden",
    marginLeft: 12,
  },
  fallBackLogoContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    height: 24,
    width: 24,
    justifyContent: "center",
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    alignItems: "center",
  },
  defaultLogoBox: {
    height: 24,
    width: 24,
    justifyContent: "center",
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    alignItems: "center",
    marginLeft: 12,
  },
})
