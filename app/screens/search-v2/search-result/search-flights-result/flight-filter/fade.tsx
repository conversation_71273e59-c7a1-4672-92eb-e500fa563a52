import { palette } from "app/theme/palette"
import { StyleSheet, View } from "react-native"
import LinearGradient from "react-native-linear-gradient"

export const Fade = () => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["rgba(252, 252, 252, 0)", palette.whiteGrey]}
        locations={[0, 0.65]}
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 1 }}
        style={styles.gradient}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    bottom: -1,
    width: "100%",
    height: 40,
  },
  gradient: {
    flex: 1,
  },
})
