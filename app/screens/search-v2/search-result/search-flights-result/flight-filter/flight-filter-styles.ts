import { color } from "app/theme"
import { StyleSheet, Dimensions, Platform } from "react-native"
import { presets } from "app/elements/text/text.presets"

const modalSheetHeight = 749
const headerSheetHeight = 64
const toggleViewHeight = 52
const bottomSpace = 10
const bottomSheetHeight = 97
const contentCollapsibleHeight =
  modalSheetHeight - toggleViewHeight * 3 - headerSheetHeight - bottomSpace - bottomSheetHeight

export const styles = StyleSheet.create({
  airlineCollapsibleStyles: {
    height: contentCollapsibleHeight,
  },
  airlineItemView: {
    borderBottomColor: color.palette.lighterGrey,
    height: 48,
    justifyContent: "center",
    marginHorizontal: 20,
    marginRight: 28
  },
  applyTextStyles: {
    color: color.palette.almostWhiteGrey,
  },
  bottomFilterSheet: {
    backgroundColor: color.palette.almostWhiteGrey,
    borderTopColor: color.palette.lighterGrey,
    borderTopWidth: 1,
    paddingHorizontal: 24,
    width: "100%",
    height: 96,
  },
  bottomSheetContainer: {
    flexDirection: "row",
    marginBottom: 37,
    marginTop: 16,
  },
  bottomSheetStyle: {
    margin: 0,
    height: modalSheetHeight,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  btnCloseModalStyles: {
    position: "absolute",
    right: 16,
  },
  buttonLinearStyles: {
    borderRadius: 60,
    width: "100%",
  },
  buttonReloadOfErrorCloudStyles: {
    marginBottom: 63,
    marginTop: 24,
    width: "auto",
  },
  cityAirportCollapsibleStyles: {
    height: contentCollapsibleHeight,
  },
  cityAirportItemView: {
    borderBottomColor: color.palette.lighterGrey,
    height: 56,
    justifyContent: "center",
    marginHorizontal: 20,
    marginRight: 28,
  },
  clearButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: "100%",
  },
  clearButtonStyle: {
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 60,
    height: 44,
    marginHorizontal: 0,
  },
  clearFilterButtonStyle: {
    color: color.palette.lightPurple,
  },
  collapsibleHeaderStyles: {
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
  },
  container: {
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: modalSheetHeight,
    width: "100%",
    flex: 1,
  },
  contentTextOfErrorCloudStyles: {
    marginHorizontal: 62,
    paddingHorizontal: 0,
    width: "auto",
  },
  directionTextActiveStyles: {
    color: color.palette.lightPurple,
  },
  directionTextInActiveStyles: {
    color: color.palette.greyCCCCCC,
  },
  directionTextStyles: {
    ...presets.bodyTextBold,
    marginLeft: 4,
  },
  errorCloudComponentStyle: {
    backgroundColor: color.palette.whiteGrey,
    height: "auto",
  },
  headerFilterSheet: {
    alignItems: "center",
    flexDirection: "row",
    height: 64,
    justifyContent: "center",
    width: "100%",
  },
  iconActiveStyles: {
    color: color.palette.lightPurple,
  },
  iconInActiveStyles: {
    color: color.palette.greyCCCCCC,
  },
  innerRadioCircle: {
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 6,
    height: 8,
    width: 8,
  },
  loadingButtonStyles: {
    alignItems: "center",
    backgroundColor: color.palette.lightGrey,
    borderRadius: 60,
    height: 44,
    justifyContent: "center",
    width: "100%",
  },
  lottieView: {
    height: 24,
  },
  noInternetViewStyles: {
    height: Dimensions.get("window").height * 0.6,
  },
  noResultTextStyles: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
    marginHorizontal: 24,
  },
  outerRadioRing: {
    alignItems: "center",
    borderColor: color.palette.darkGrey,
    borderRadius: 12,
    borderWidth: 1,
    height: 20,
    justifyContent: "center",
    width: 20,
  },
  outerRadioRingSelected: {
    alignItems: "center",
    borderColor: color.palette.lightPurple,
    backgroundColor: color.palette.lightPurple,
    borderRadius: 12,
    borderWidth: 1,
    height: 20,
    justifyContent: "center",
    width: 20,
  },
  overlayStyle: {
    height: "100%",
    paddingBottom: headerSheetHeight,
    width: "100%",
  },
  parentContainer: {
    width: 176,
    alignSelf: "flex-end",
  },
  spaceViewStyles: {
    height: 12,
    width: "100%",
  },
  switchActiveButton: {
    backgroundColor: color.palette.whiteGrey,
    ...Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: { width: 0, height: 3 },
      },
      android: {
        elevation: 3,
      },
    }),
  },
  switchButton: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  switchButtonLeft: {
    margin: 4,
    marginRight: 0,
    borderRadius: 63,
  },
  switchButtonRight: {
    margin: 4,
    marginLeft: 0,
    borderRadius: 63,
  },
  switchFlightContainer: {
    backgroundColor: color.palette.lightestGrey,
    borderRadius: 52,
    flexDirection: "row",
    height: 44,
    marginHorizontal: 48.5,
  },
  terminalCollapsibleStyles: {
    paddingBottom: 24,
  },
  terminalItemView: {
    borderBottomColor: color.palette.lighterGrey,
    marginHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  terminalTextItemStyles: {
    ...presets.bodyTextBold,
    color: color.palette.almostBlackGrey,
    fontWeight: "400",
  },
  titleFilterSheet: {
    color: color.palette.almostBlackGrey,
    ...presets.subTitleBold,
    flexGrow: 2,
    textAlign: "center",
  },
  titleTextOfErrorCloudStyles: {
    marginTop: 20,
  },
  fullSize: {
    flex: 1,
  },
  listItemContainer: {
    paddingBottom: 40,
  },
})
