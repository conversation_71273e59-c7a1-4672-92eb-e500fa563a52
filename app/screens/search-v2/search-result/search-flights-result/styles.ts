import Responsive from "app/utils/responsive"
import { color, spacing, typography } from "app/theme"
import { Platform, StyleSheet } from "react-native"
import { newPresets, presets } from "app/elements/text"

export const rootStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
  },
  wrapSearchBar: {
    backgroundColor: color.palette.almostWhiteGrey,
    paddingLeft: 6,
    paddingRight: spacing[4],
  },
  inputContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  overlayStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
    paddingBottom: 80,
  },
  caption: {
    marginTop: spacing[5],
    alignSelf: "center",
    marginHorizontal: 20
  },
  errorContainerStyle: {
    justifyContent: "flex-start",
    marginTop: 50,
  },
  errorTextContainerStyle: {
    marginTop: 24,
  },
  feedBackToastStyle: {
    width: "100%",
    paddingHorizontal: 16,
    bottom: 8,
  },
  toastButtonStyle: {
    ...presets.textLink,
    fontWeight: "normal",
    color: color.palette.lightBlue,
    alignItems: "flex-end",
  },
  toastTextStyle: {
    ...presets.bodyTextRegular,
    color: color.palette.whiteGrey,
    width: "80%",
  },
})

export const flightItemStyle = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignSelf: "center",
    position: "relative",
    marginHorizontal: 20,
  },
  flightStatus: {
    position: "absolute",
    top: 0,
    right: 0,
    zIndex: 10,
  },
  scheduledTime: {
    width: 48,
    marginRight: spacing[4],
    alignItems: "center",
  },
  flightDetailContainer: {
    flex: 1,
    marginBottom: spacing[0],
    borderBottomWidth: 1,
    borderColor: color.palette.lighterGrey,
    paddingBottom: 20,
    flexDirection: "row",
  },
  flexSize: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing[1]
  },
  logo: {
    height: 16,
    width: 16,
  },
  iconContainer: {
    borderRadius: 8,
    height: 16,
    marginRight: spacing[1],
    overflow: "hidden",
    width: 16,
  },
  spaces: {
    marginBottom: spacing[1],
  },
  flightNumber: {
    color: color.palette.almostBlackGrey,
  },
  journey: {
    fontSize: Responsive.getFontSize(14),
    lineHeight: Responsive.getFontSize(18),
    color: color.palette.almostBlackGrey,
  },
  slaveFlights: {
    maxWidth: 240,
    marginBottom: spacing[1],
  },
  footer: {
    flexDirection: "row",
    paddingTop: spacing[2],
  },
  rightSection: {
    height: "100%",
    width: 32,
    marginLeft: spacing[3],
    flexDirection: "row",
    alignItems: "flex-end",
  },
  seperateChar: {
    ...presets.caption2Bold,
    color: color.palette.darkestGrey,
    marginHorizontal: spacing[1],
    textAlign: "center"
  },
  textBase: {
    textAlign: "center",
    color: color.palette.almostBlackGrey,
  },
  strikeLine: {
    position: "absolute",
    top: "50%",
    height: 2,
    width: 40,
    backgroundColor: color.palette.darkestGrey,
  },
  unavailable: {
    color: color.palette.darkGrey999,
  },
  buttonStyleSaved: {
    width: 32,
    height: 32,
    borderRadius: 20,
    justifyContent: "center",
    backgroundColor: color.palette.lightPurple,
    alignItems: "center",
    borderWidth: 1,
    borderColor: color.palette.lightPurple,
  },
  buttonStyleAdd: {
    width: 32,
    height: 32,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.purpleD5BBEA,
    borderWidth: 1,
  },
  buttonDisabledStyleAdd: {
    width: 32,
    height: 32,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.midGrey,
    borderWidth: 1,
  },
  disabledText: {
    color: color.palette.midGrey,
  },
  disabledStrikeLine: {
    backgroundColor: color.palette.darkGrey999,
  },
  disabledScheduledTime: {
    color: color.palette.darkGrey999,
  },
  disabledSavedButton: {
    borderColor: color.palette.lighterGrey,
    backgroundColor: color.palette.greyCCCCCC,
  },
  textDarkestGrey: {
    color: color.palette.darkestGrey,
  },
  itemMarginTop: {
    marginTop: 20,
  },
  tooltipText: {
    fontSize: 14,
    lineHeight: 18,
    textAlign: "left",
    textAlignVertical: "top",
    fontWeight: "400",
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
    fontFamily: typography.medium,
  },
  arrowStyle: {
    width: 18,
    height: 18,
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 4,
    position: "relative",
    marginLeft: -10,
    top: 12,
    transform: [{ rotate: "45deg" }],
    zIndex: 100,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
  },
  wrapButtonToolTip: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 30,
    height: 60,
    justifyContent: "center",
    width: 60,
    position: "relative",
    right: 14,
    top: 14,
  },
  buttonStyleAddTooltip: {
    width: 32,
    height: 32,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.purpleD5BBEA,
  },
  contentStyle: {
    borderRadius: 8,
    padding: 12,
    top: 2,
    ...(Platform.OS === "android" ? { minHeight: 78 } : {}),
  },
})

export const noContentStyles = StyleSheet.create({
  container: {
    padding: spacing[5],
    alignItems: "center",
  },
  noFlightResults: {
    paddingVertical: spacing[2],
    color: color.palette.almostBlackGrey,
  },
  textCenter: {
    textAlign: "center",
  },
  suggestionTryAgain: {
    paddingTop: spacing[5],
  },
})

export const filterStyles = StyleSheet.create({
  container: {
    backgroundColor: color.transparent,
    flex: 1,
  },
  contentContainer: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  scrollContainer: {},
  firstStyle: {
    flexDirection: "row",
  },
  viewContent: {
    paddingHorizontal: 20,
    marginBottom: 50,
  },
  wrapCheckboxList: {
    paddingBottom: 16,
  },
  wrapChipList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    paddingBottom: 16,
  },
  wrapHeaderSection: {
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  titleStyle: {
    color: color.palette.almostBlackGrey,
  },
  subTitle: {
    ...presets.caption2Bold,
    color: color.palette.darkestGrey,
    marginBottom: 8,
  },
  subTitleCheckbox: {
    ...presets.caption2Bold,
    color: color.palette.darkestGrey,
  },
  wrapL1Category: {
    marginTop: 12,
  },
  checkboxContainer: {
    marginRight: 8,
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
    width: "100%",
  },
  wrapCheckbox: {
    marginRight: 8,
    paddingVertical: 12,
    width: "100%",
  },
  allCheckContainer: {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center",
  },
  checkboxTitle: {
    color: color.palette.almostBlackGrey,
  },
  textContainer: {
    marginLeft: 16,
  },
  locationCheckboxContainer: {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center",
    alignSelf: "flex-start",
  },
  areaCheckboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start",
  },
  marginForArea: {
    marginLeft: 20,
  },
  lineGrey: {
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
    marginTop: 16,
  },
  chipsContainer: {},
  unableToDisplayStyle: {
    paddingHorizontal: 20,
    color: color.palette.almostBlackGrey,
  },
  bottomSheetContainer: {
    height: "90%",
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomShetFilterContainer: {
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "100%",
    width: "100%",
  },
  headerFilter: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 16,
    marginVertical: 20,
    alignItems: "center",
  },
  filterTitle: {
    ...presets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  btnCloseStyles: {
    position: "absolute",
    right: 0,
  },
  buttonContainer: {},
  wrapActionBtn: {
    flexDirection: "row",
    gap: 24,
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 12,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: color.palette.lighterGrey,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  wrapActionBtnDisabled: {
    justifyContent: "flex-end",
  },
  clearAllButton: {},
  txtClearAll: {
    ...presets.caption1Bold,
    color: color.palette.lightPurple,
  },
  btnApplyFilters: {
    backgroundColor: color.palette.lightGrey,
  },
  btnApplyFiltersActive: {
    backgroundColor: undefined,
  },
  txtApplyFilters: {
    color: color.palette.darkGrey,
  },
  txtApplyFiltersActive: {
    color: color.palette.whiteGrey,
  },
  buttonGradient: {
    backgroundColor: color.palette.basePurple,
    borderRadius: 60,
    width: 176,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderRadius: 4,
  },
  checkboxOutline: {
    borderColor: color.palette.midGrey,
    borderWidth: 1,
  },
  clearAll: {
    ...newPresets.caption1Bold,
    color: color.palette.lightPurple,
  },
  marginBottom0: {
    marginBottom: 0,
  },
})
