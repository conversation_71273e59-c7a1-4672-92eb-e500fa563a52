import { Dimensions, InteractionManager, Platform, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import React, { useEffect, useMemo, useState } from "react"
import BaseImage from "app/elements/base-image/base-image"
import moment from "moment"
import { flightItemStyle } from "./styles"
import { Text } from "app/elements/text"
import { getUriImage } from "app/utils/screen-helper"
import { translate } from "app/i18n"
import { FlightType } from "../../tabs/search-tab-flights/consts"
import { DateFormats } from "app/utils/date-time/date-time"
import { FlightDirection } from "app/utils/constants"
import { Plane } from "assets/icons"
import { useSelector } from "react-redux"
import { MytravelSelectors } from "app/redux/mytravelRedux"
import Tooltip from "react-native-walkthrough-tooltip"
import { handleCondition, ifAllTrue, ifOneTrue, simpleCondition } from "app/utils"
import PlusDisabled from "app/components/flight-listing-card/plus-disabled.svg"
import CheckSaved from "../../components/Checkbox.svg"
import PlusV2 from "../../components/Plus-Minus.svg"
import { color } from "app/theme"
import { isEmpty } from "validate.js"
import { FlightListingProps } from "app/components/flight-listing-card/flight-listing-card.props"
import { useIsFocused } from "@react-navigation/native"
import { StorageKey } from "app/utils/storage/storage-key"
import { load, save } from "app/utils/storage"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import Plus from "app/components/flight-listing-card/plus.svg"

const SEPARATE_CHAR = "·"
const { width } = Dimensions.get("window")

export interface FlighItem {
  scheduled_date: string
  scheduled_time: string
  flight_number: string
  actual_timestamp: string
  estimated_timestamp: string
  display_timestamp: string
  direction: string
  airport: string
  slave_flights: string[]
  check_in_row: string
  display_gate: string
  via_airport_details: {
    name: string
  }
  terminal: string
  display_belt: string
  technical_flight_status1: string
  technical_flight_status2: string
  airport_details: {
    name: string
    country: string
  }
  airline_details: {
    name: string
    logo_url: string
  }
  flight_status: string
  status_mapping: {
    belt_status_en: string
    details_status_en: string
    details_status_zh: string
    listing_status_en: string
    listing_status_zh: string
    status_text_color: string
    show_gate: string
  }
}

type Props = {
  flight: FlightListingProps
  isLoggedIn: boolean
  onSaveFlight: (value: {
    flight: FlightListingProps
    isSaved: boolean
    canSaveFlight: boolean
  }) => void
  onPressed: (flight: FlightListingProps) => void
  containerStyle?: ViewStyle
  isFirstFlight?: boolean
}

export const FlightItem = React.memo((props: Props) => {
  const { flight, onSaveFlight, isLoggedIn, onPressed, containerStyle, isFirstFlight } = props
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const isFocused = useIsFocused()
  const inset = useSafeAreaInsets()
  const [needShowToolTip, setShowToolTip] = useState(false)

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (isFirstFlight) {
        const checkToolTip = async () => {
          const openFlyScreenBefore = await load(StorageKey.needDisableToolTipFlyScreen)
          if (!openFlyScreenBefore) {
            setShowToolTip(true)
            save(StorageKey.needDisableToolTipFlyScreen, true)
            const timeOutCloseToolTip = setTimeout(() => {
              setShowToolTip(false)
            }, 5000)
            return () => {
              clearTimeout(timeOutCloseToolTip)
            }
          }
        }
        checkToolTip()
      }
    })
  }, [])

  let isSaved = myTravelFlightsPayload?.getMyTravelFlightDetails?.some((savedFlight) => {
    return (
      savedFlight.flightNumber === flight.flightNumber &&
      savedFlight.scheduledDate === flight.scheduledDate &&
      (savedFlight.flightDirection || savedFlight.direction) === flight.direction
    )
  })

  const isDisabledStatus = useMemo(() => {
    const statusMapping = simpleCondition({
      condition: isEmpty(flight?.beltStatusMapping),
      ifValue: flight?.flightStatusMapping,
      elseValue: flight?.beltStatusMapping,
    })

    return (
      (flight.direction.toLowerCase() === FlightType.ARRIVAL &&
        statusMapping === "Last Bag on Belt") ||
      (flight.direction.toLowerCase() === FlightType.DEPARTURE && statusMapping === "Departed")
    )
  }, [flight.direction, flight?.flightStatusMapping, flight?.beltStatusMapping])

  const canSaveFlight = useMemo(() => {
    const status = flight?.flightStatus?.toLowerCase()
    const priorityTime =
      flight?.actualTimestamp ||
      flight?.estimatedTimestamp ||
      `${flight?.scheduledDate} ${flight?.timeOfFlight}`
    const currentTimeToUTC = moment().tz("Asia/Singapore")
    const flightTime = moment(priorityTime, DateFormats.YearMonthDayTime, "Asia/Singapore")
    if (FlightDirection.Departure === flight?.direction) {
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
          return false
        default:
          return true
      }
    }
    switch (true) {
      case /cancelled/gim.test(status):
      case /landed/gim.test(status) &&
        moment(flightTime).add(1, "hours").format(DateFormats.YearMonthDayTime) <
          currentTimeToUTC.format(DateFormats.YearMonthDayTime):
        return false
      default:
        return true
    }
  }, [
    flight?.flightStatus,
    flight?.actualTimestamp,
    flight?.estimatedTimestamp,
    flight?.scheduledDate,
    flight?.timeOfFlight,
    flight?.direction,
  ])

  //style
  const disabledScheduleTimeStyle = useMemo<TextStyle>(
    () => isDisabledStatus && flightItemStyle.disabledScheduledTime,
    [isDisabledStatus],
  )

  const disabledTextStyle = useMemo<TextStyle>(
    () => isDisabledStatus && flightItemStyle.disabledText,
    [isDisabledStatus],
  )

  const disabledSavedButtonStyle = useMemo<ViewStyle>(
    () => (isDisabledStatus || !canSaveFlight) && flightItemStyle.disabledSavedButton,
    [isDisabledStatus, canSaveFlight],
  )

  const terminalStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.textDarkestGrey, !flight.terminal && flightItemStyle.unavailable, disabledTextStyle],
    [flight.terminal, disabledTextStyle],
  )

  const displayBeltStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.textDarkestGrey, !flight.displayBelt && flightItemStyle.unavailable, disabledTextStyle],
    [flight.displayBelt, disabledTextStyle],
  )

  const displayGateStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.textDarkestGrey, !flight.displayGate && flightItemStyle.unavailable, disabledTextStyle],
    [flight.displayGate, disabledTextStyle],
  )

  const scheduleTimeTextStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.textBase, disabledScheduleTimeStyle],
    [disabledScheduleTimeStyle],
  )

  const separateCharStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.seperateChar, disabledTextStyle],
    [disabledTextStyle],
  )

  const strikeLineStyle = useMemo<TextStyle[]>(
    () => [flightItemStyle.strikeLine, isDisabledStatus && flightItemStyle.disabledStrikeLine],
    [isDisabledStatus],
  )

  const flightStatusStyle = useMemo<TextStyle>(
    () => ({
      textTransform: "none",
      color: flight.statusColor?.toLowerCase(),
    }),
    [flight.statusColor],
  )

  const addIconStyle = useMemo<ViewStyle[]>(
    () => [
      flightItemStyle.buttonDisabledStyleAdd,
      isDisabledStatus && { borderColor: color.palette.lighterGrey },
    ],
    [isDisabledStatus],
  )

  const darkestGreyTextStyle = useMemo<TextStyle[]>(
    () => [
      flightItemStyle.textDarkestGrey,
      disabledTextStyle
    ],
    [disabledTextStyle],
  )

  const shouldShowToolTip = ifAllTrue([
    needShowToolTip,
    isFirstFlight,
    isFocused,
  ])

  const handleTopAdjustment = () => {
    if (Platform.OS === "android") {
      if (inset?.top && !isNaN(inset?.top)) {
        return -inset?.top
      }
      return 0
    }
    return 0
  }
  
  // func
  const renderScheduleTime = (item) => {
    let mainTime = item.timeOfFlight
    let displayTimestamp = item.displayTimestamp
    let scheduledDate = item.scheduledDate
    let reTimeFlag = false
    let numberDaysDiff = 0
    if (displayTimestamp) {
      mainTime = displayTimestamp?.split(" ")[1]
      const mainDate = displayTimestamp?.split(" ")[0]
      if (ifOneTrue([scheduledDate !== mainDate, item.timeOfFlight !== mainTime])) {
        reTimeFlag = true
        numberDaysDiff = moment(mainDate).diff(moment(scheduledDate), "days")
      }
    }
    return (
      <>
        {simpleCondition({
          condition: reTimeFlag,
          ifValue: (
            <View>
              <Text preset="bodyTextBlack" style={scheduleTimeTextStyle}>
                {item.timeOfFlight}
              </Text>
              <View style={strikeLineStyle} />
            </View>
          ),
          elseValue: <></>,
        })}
        <Text preset="bodyTextBlack" style={scheduleTimeTextStyle} text={mainTime} />
        {simpleCondition({
          condition: ifAllTrue([reTimeFlag, !!numberDaysDiff]),
          ifValue: (
            <>
              {!isNaN(numberDaysDiff) && (
                <Text preset="bodyTextBlack" style={scheduleTimeTextStyle}>
                  {simpleCondition({
                    condition: numberDaysDiff > 0,
                    ifValue: `(+${numberDaysDiff})`,
                    elseValue: `(${numberDaysDiff})`,
                  })}
                </Text>
              )}
            </>
          ),
          elseValue: <></>,
        })}
      </>
    )
  }

  return (
    <TouchableOpacity onPress={() => onPressed(flight)} style={[flightItemStyle.container, containerStyle]}>
      {flight?.statusColor && (
        <View style={flightItemStyle.flightStatus}>
          <Text
            preset="XSmallBold"
            style={flightStatusStyle}
            text={simpleCondition({
              condition: isEmpty(flight?.beltStatusMapping),
              ifValue: flight?.flightStatusMapping,
              elseValue: flight?.beltStatusMapping,
            })}
          />
        </View>
      )}
      <View style={flightItemStyle.scheduledTime}>{renderScheduleTime(flight)}</View>
      <View style={flightItemStyle.flightDetailContainer}>
        <View style={flightItemStyle.flexSize}>
          <View style={flightItemStyle.header}>
            <View style={flightItemStyle.iconContainer}>
              {!flight?.logo ? (
                <Plane width={16} height={16} color={color.palette.darkGrey999} />
              ) : (
                <BaseImage
                  source={{ uri: getUriImage(flight.logo) }}
                  style={flightItemStyle.logo}
                />
              )}
            </View>
            <Text preset="bodyTextBlack" style={flightItemStyle.flightNumber}>
              {flight.flightNumber}
            </Text>
          </View>
          <View style={flightItemStyle.spaces}>
            <Text preset="bodyTextBold" style={flightItemStyle.journey}>
              {translate(`searchV2.flightsResult.journey.${flight.direction.toLowerCase()}`, {
                airport: flight?.airportDetails?.name || "-",
              })}
            </Text>
          </View>
          {flight.viaAirportDetails?.name && (
            <View style={flightItemStyle.spaces}>
              <Text preset="caption2Bold">
                {translate("searchV2.flightsResult.via", {
                  airport: flight?.viaAirportDetails?.name || "-",
                })}
              </Text>
            </View>
          )}
          {!!flight.codeShare?.length && (
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              preset="caption2Regular"
              style={flightItemStyle.slaveFlights}
            >
              {flight.codeShare?.join(", ")}
            </Text>
          )}
          <View style={flightItemStyle.footer}>
            <Text
              tx="searchV2.flightsResult.terminal.prefix"
              preset="caption2Regular"
              style={darkestGreyTextStyle}
            />
            <Text preset="caption2Bold" style={terminalStyle}>
              {!!flight.terminal
                ? translate("searchV2.flightsResult.terminal.value", { terminal: flight.terminal })
                : translate("searchV2.flightsResult.notAvailable")}
            </Text>
            <Text style={separateCharStyle}>{SEPARATE_CHAR}</Text>
            {flight.direction.toLowerCase() === FlightType.ARRIVAL && (
              <Text preset="caption2Bold" style={displayBeltStyle}>
                {translate("searchV2.flightsResult.baggageBelt", {
                  baggageBelt:
                    flight.displayBelt || translate("searchV2.flightsResult.notAvailable"),
                })}
              </Text>
            )}
            {flight.direction.toLowerCase() === FlightType.DEPARTURE && (
              <>
                <Text preset="caption2Bold" style={darkestGreyTextStyle}>
                  {translate("searchV2.flightsResult.checkinRow", {
                    checkinRow: flight.checkInRow || translate("searchV2.flightsResult.notAvailable"),
                  })}
                </Text>
                <Text style={separateCharStyle}>{SEPARATE_CHAR}</Text>
                <Text preset="caption2Bold" style={displayGateStyle}>
                  {translate("searchV2.flightsResult.gate", {
                    gate: flight.displayGate || "N/A",
                  })}
                </Text>
              </>
            )}
          </View>
        </View>

        <View style={flightItemStyle.rightSection}>
          <Tooltip
            isVisible={shouldShowToolTip}
            content={
              <Text
                text={translate("flyLanding.toolTipFlyV2")}
                style={flightItemStyle.tooltipText}
              />
            }
            displayInsets={{ top: 24, bottom: 24, left: width * 0.44, right: 16 }}
            placement="bottom"
            onClose={() => {
              setShowToolTip(false)
            }}
            disableShadow={true}
            topAdjustment={handleTopAdjustment()}
            contentStyle={flightItemStyle.contentStyle}
            arrowStyle={flightItemStyle.arrowStyle}
            backgroundColor="#454545CC"
          >
            {shouldShowToolTip ? (
              <View style={flightItemStyle.wrapButtonToolTip}>
                <View style={flightItemStyle.buttonStyleAddTooltip}>
                  <Plus />
                </View>
              </View>
            ) : (
              <TouchableOpacity
                disabled={isSaved && (isDisabledStatus || !canSaveFlight)}
                onPress={() =>
                  onSaveFlight({
                    flight,
                    isSaved,
                    canSaveFlight: canSaveFlight && !isDisabledStatus,
                  })
                }
                style={handleCondition(
                  isLoggedIn && isSaved,
                  [flightItemStyle.buttonStyleSaved, disabledSavedButtonStyle],
                  handleCondition(
                    canSaveFlight && !isDisabledStatus,
                    flightItemStyle.buttonStyleAdd,
                    addIconStyle,
                  ),
                )}
              >
                {handleCondition(
                  isLoggedIn && isSaved,
                  <CheckSaved width={20} height={20} />,
                  handleCondition(
                    canSaveFlight && !isDisabledStatus,
                    <PlusV2 width={20} height={20} />,
                    <PlusDisabled
                      width={20}
                      height={20}
                      color={isDisabledStatus ? color.palette.greyCCCCCC : color.palette.midGrey}
                    />,
                  ),
                )}
              </TouchableOpacity>
            )}
          </Tooltip>
        </View>
      </View>
    </TouchableOpacity>
  )
})
