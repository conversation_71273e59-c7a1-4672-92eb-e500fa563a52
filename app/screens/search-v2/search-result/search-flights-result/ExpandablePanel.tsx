import { color } from "app/theme"
import { simpleCondition } from "app/utils"
import React, { useEffect, useRef, useState } from "react"
import {
  Animated,
  StyleSheet,
  Dimensions,
  LayoutRectangle,
  ViewStyle,
  View,
  Pressable,
} from "react-native"

const SCREEN_HEIGHT = Dimensions.get("window").height

interface ExpandablePanelProps {
  anchor: React.RefObject<any>
  isExpanded: boolean
  children: React.ReactNode
  onDismiss: () => void
  containerStyle?: ViewStyle
  isPaneAbsoluteToRoot?: boolean
}

export const ExpandablePanel: React.FC<ExpandablePanelProps> = ({
  anchor,
  isExpanded,
  children,
  containerStyle,
  onDismiss,
  isPaneAbsoluteToRoot,
}) => {
  const animatedHeight = useRef(new Animated.Value(0)).current
  const [buttonLayout, setButtonLayout] = useState<LayoutRectangle | null>(null)
  const [panelMaxHeight, setPanelMaxHeight] = useState<number>(0)

  useEffect(() => {
    if (!anchor.current) return
    anchor.current.measure((x = 0, y = 0, width = 0, height = 0, pageX = 0, pageY = 0) => {
      const panelHeight = SCREEN_HEIGHT - (y + height) // space below the button
      setButtonLayout({ x: pageX, y: pageY, width, height })
      setPanelMaxHeight(panelHeight)
    })
  }, [anchor.current])

  useEffect(() => {
    if (!anchor.current) return

    Animated.timing(animatedHeight, {
      toValue: isExpanded ? panelMaxHeight : 0,
      duration: 250,
      useNativeDriver: false,
    }).start()
  }, [isExpanded, panelMaxHeight, anchor.current])

  if (!buttonLayout) return null

  return (
    <Animated.View
      style={[
        styles.panel,
        {
          top: simpleCondition({
            condition: isPaneAbsoluteToRoot,
            ifValue: buttonLayout.y + buttonLayout.height,
            elseValue: buttonLayout.height,
          }),
          left: buttonLayout.x,
          width: buttonLayout.width,
          height: animatedHeight,
        },
        containerStyle,
      ]}
    >
      <Pressable style={styles.backdrop} onPress={onDismiss} />
      <View style={styles.contentStyle}>{children}</View>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  panel: {
    position: "absolute",
    backgroundColor: `${color.palette.darkestGrey}99`,
    overflow: "hidden",
    zIndex: 999,
    bottom: 0,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: `${color.palette.black}66`,
  },
  contentStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
})
