import * as React from "react"
import { Platform, StyleSheet, TouchableOpacity } from "react-native"
import { Text } from "app/elements/text"
import { color, typography } from "app/theme"
import { cloneElement } from "react"

const COMPONENT_NAME = "Chip"

const iconStyle = {
  width: 16,
  height: 16,
  color: color.palette.darkestGrey,
}

const iconActiveStyle = {
  color: color.palette.lightPurple,
}

export type ChipState = "applied" | "active"

export type ChipProps = {
  title: string
  onPress: () => void
  state?: ChipState
  iconLeft?: React.ReactElement
  iconRight?: React.ReactElement
  testID?: string
  accessibilityLabel?: string
  disabled?: boolean
}

export const Chip = (props: ChipProps) => {
  const { title, onPress, iconLeft, iconRight, state, testID, accessibilityLabel, disabled } = props
  const iconProps = { ...iconStyle, ...(state ? iconActiveStyle : {}) }
  const stateMapping = {
    applied: {
      container: styles.itemActive,
      text: styles.textActive,
    },
    active: {
      container: styles.itemApplied,
      text: styles.textActive,
    },
  }
  return (
    <TouchableOpacity
      accessibilityLabel={`${accessibilityLabel || ""}${COMPONENT_NAME}__Btn__${title}`}
      testID={`${testID || ""}${COMPONENT_NAME}__Btn__${title}`}
      style={[styles.item, stateMapping[state]?.container]}
      onPress={onPress}
      disabled={disabled}
    >
      {iconLeft && cloneElement(iconLeft, iconProps)}
      <Text
        accessibilityLabel={`${accessibilityLabel || ""}${COMPONENT_NAME}__Name__${title}`}
        testID={`${testID || ""}${COMPONENT_NAME}__Name__${title}`}
        // preset="caption1Bold"
        style={[styles.text, stateMapping[state]?.text]}
      >
        {title}
      </Text>
      {iconRight && cloneElement(iconRight, iconProps)}
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  item: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 99,
    borderWidth: 1,
    height: 30,
    borderColor: color.palette.lighterGrey,
  },
  itemActive: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
    paddingRight: 6,
  },
  text: {
    color: color.palette.darkestGrey,
    marginTop: -2,
    marginHorizontal: 2,
    fontFamily: typography.bold,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    textAlign: "center",
  },
  textActive: {
    color: color.palette.lightPurple,
  },
  itemApplied: {
    borderColor: color.palette.lightPurple,
    backgroundColor: color.palette.whiteGrey,
  },
})
