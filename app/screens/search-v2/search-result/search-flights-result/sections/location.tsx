import { Button } from "app/elements/button/button"
import { Checkbox } from "app/elements/checkbox/checkbox"
import { Text } from "app/elements/text"
import { color } from "app/theme"
import { Check, CrossBlack } from "assets/icons"
import { useState } from "react"
import { FlatList, TouchableOpacity, View } from "react-native"
import { styles } from "./styles"
import LinearGradient from "react-native-linear-gradient"
import { AdobeTagName, trackAction } from "app/services/adobe"

const COMPONENT = "Location_Filter"

type Location = {
  tagName: string
  tagTitle: string
  tagCode: string
  selected: boolean
}

type Props = {
  items: Location[]
  onClosedSheet: () => void
  onApplyFilter: (items: Location[]) => void
}

export const Location = (props: Props) => {
  const { items, onClosedSheet, onApplyFilter } = props
  const [data, setData] = useState(items)

  const handleOnCheckedAll = (value: boolean) => {
    setData(data.map((item) => ({ ...item, selected: value })))
  }

  const handleOnCheckboxChange = (selectedIndex, value) => {
    if (selectedIndex === 0) {
      return setData(data.map((item) => ({ ...item, selected: value })))
    }

    const updatedData = data.map((item, index) => {
      if (index === 0) return item
      if (index === selectedIndex) return { ...item, selected: value }
      return item
    })

    const allSelected = updatedData.slice(1).every((item) => item.selected)
    updatedData[0] = { ...updatedData[0], selected: allSelected }

    setData(updatedData)
  }

  const onPressApplyFilters = () => {
    onApplyFilter(data)

    const dataToApply = data?.filter((item) => item?.selected)
    let appliedValue = dataToApply?.map((item) => item?.tagTitle).join(", ")
    if (dataToApply?.length === items?.length) {
      appliedValue = "All"
    }
    trackAction(AdobeTagName.CAppSearchResultFilter, {
      [AdobeTagName.CAppSearchResultFilter]: `Flights | Quick Filters | Location: ${appliedValue}`,
    })
  }

  const handleClearAllFilter = () => handleOnCheckedAll(true)

  const renderLocationItem = (item: Location, index: number) => {
    return (
      <View style={styles.checkboxContainer}>
        <Checkbox
          value={item.selected}
          onToggle={(value) => {
            if (index === 0) {
              handleOnCheckedAll(value)
              return
            }
            handleOnCheckboxChange(index, value)
          }}
          testID={`${COMPONENT}__CheckBoxFilterLocations__${index}`}
          accessibilityLabel={`${COMPONENT}__CheckBoxFilterLocations__${index}`}
          text={item.tagTitle}
          textStyle={styles.checkboxLabel}
          outlineStyle={styles.outlineStyle}
          fillStyle={[styles.outlineStyle, styles.fillStyle]}
          icon={<Check fill={color.palette.whiteGrey} width={18} height={18} />}
        />
      </View>
    )
  }

  return (
    <View style={styles.bottomSheetContainer}>
      <View style={styles.headerFilter}>
        <View style={styles.rightHeader}>
          <Text
            style={styles.locationLabel}
            preset="bodyTextBlack"
            tx="searchV2.flightsResult.filter.tabs.terminal"
          />
        </View>
        <TouchableOpacity
          onPress={onClosedSheet}
          testID={`${COMPONENT}__testID__CloseFilter`}
          accessibilityLabel={`${COMPONENT}__accessibilityLabel__CloseFilter`}
        >
          <CrossBlack width={20} height={20} />
        </TouchableOpacity>
      </View>
      <View style={styles.content}>
        <FlatList
          data={data}
          renderItem={({ item, index }) => renderLocationItem(item, index)}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) => `locationData-${item?.tagTitle}-${index.toString()}`}
        />
        <View style={styles.locationFooter}>
          <View style={styles.clearButtonVerticalAlign}>
            <TouchableOpacity onPress={handleClearAllFilter}>
              <Text
                preset="bodyTextBold"
                tx="searchV2.flightsResult.filter.clearAll"
                style={styles.clearFilterButtonStyle}
              ></Text>
            </TouchableOpacity>
          </View>
          <View>
            <LinearGradient
              style={styles.containerApplyButtonStyle}
              start={{ x: 0, y: 1 }}
              end={{ x: 1, y: 0 }}
              colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
            >
              <Button
                tx="searchV2.flightsResult.filter.applyFilters"
                typePreset="secondary"
                onPress={onPressApplyFilters}
                statePreset="default"
                backgroundPreset="dark"
                textPreset="buttonLarge"
                textStyle={styles.applyFilterButtonStyle}
                style={styles.applyButtonStyle}
                testID={`${COMPONENT}__TouchableApplyFiltersLocations`}
                accessibilityLabel={`${COMPONENT}__TouchableAppFiltersLocations`}
              />
            </LinearGradient>
          </View>
        </View>
      </View>
    </View>
  )
}
