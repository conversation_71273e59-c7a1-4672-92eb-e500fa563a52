import { presets } from "app/elements/text"
import { color } from "app/theme"
import { Dimensions, Platform, StyleSheet } from "react-native"
const { width } = Dimensions.get("window")

export const styles = StyleSheet.create({
  bottomSheetContainer: {
    paddingTop: 24,
    paddingBottom: 40,
  },
  headerFilter: {
    display: "flex",
    flexDirection: "row",
    width: "100%",
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  content: {
    paddingHorizontal: 20,
  },
  rightHeader: {
    flex: 1,
  },
  locationLabel: {
    textAlign: "left",
    color: color.palette.almostBlackGrey,
  },
  locationFooter: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 50,
  },
  clearButtonVerticalAlign: {
    justifyContent: "center",
  },
  applyFilterButtonStyle: {
    color: color.palette.almostWhiteGrey,
    fontSize: 16,
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    textAlign: "center",
  },
  containerApplyButtonStyle: {
    borderRadius: 60,
    width: 176,
  },
  applyButtonStyle: {
    borderWidth: 0,
  },
  clearFilterButtonStyle: {
    ...presets.caption1Bold,
    textAlign: "center",
    textAlignVertical: "center",
    color: color.palette.lightPurple,
  },
  clearButtonStyle: {
    marginRight: 13,
    width: width / 2 - 31,
  },
  checkboxContainer: {
    borderBottomColor: color.palette.lightGrey,
    borderBottomWidth: 1,
    marginBottom: 12,
    paddingBottom: 12,
    marginRight: 8
  },
  outlineStyle: {
    borderColor: color.palette.midGrey,
    width: 16,
    height: 16,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
  },
  radioTextStyle: {
    color: color.palette.almostBlackGrey,
  },
  innerRadioCircleStyle: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: color.palette.whiteGrey,
  },
  outerRadioRingStyle: {
    height: 20,
    width: 20,
    borderRadius: 12,
    backgroundColor: color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center",
  },
  outerRadioGrayStyle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: color.palette.midGrey,
    alignItems: "center",
    justifyContent: "center",
  },
  devider: {
    marginTop: 16,
    height: 1,
    backgroundColor: color.palette.lighterGrey,
  },
  radioGroup: {
    paddingTop: 12,
  },
  radio: {
    width: "100%",
    paddingTop: 12,
  },
  checkboxLabel: {
    ...presets.bodyTextRegular,
    color: color.palette.almostBlackGrey,
  },
  fillStyle: {
    borderColor: color.palette.lightPurple,
  },
})
