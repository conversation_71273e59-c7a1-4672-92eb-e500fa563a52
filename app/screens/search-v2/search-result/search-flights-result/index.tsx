import NetInfo from "@react-native-community/netinfo"
import SearchActions, { SearchSelectors } from "app/redux/searchRedux"
import { getSearchTabName, SearchScreenProvider } from "app/screens/search/search-screen-context"
import { <PERSON>ert, FlatList, Keyboard, SafeAreaView, StatusBar, View } from "react-native"
import { flightItemStyle, rootStyles } from "./styles"
import { translate } from "app/i18n"
import { useDispatch, useSelector } from "react-redux"
import { useCallback, useContext, useEffect, useRef, useState } from "react"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
import { FlightItem } from "./flight-item"
import { Text } from "app/elements/text"
import { Empty } from "./empty"
import { DateFormats } from "app/utils/date-time/date-time"
import moment, { Moment } from "moment"
import { LoadingView } from "./loading"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { FlightDirection, NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import {
  FlyProfileEnum,
  TravelOption,
} from "app/screens/fly/flights/flight-details/flight-detail.props"
import { AdobeTagName, trackAction, AdobeValueByTagName } from "app/services/adobe"
import { save, StorageKey } from "app/utils/storage"
import SavedFlightTravelOptionsModal from "app/screens/fly/flights/save-flight-travel-option/save-flight-trave-option-wrap"
import { ProfileSelectors } from "app/redux/profileRedux"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { dtManualActionEvent, FE_LOG_PREFIX } from "app/services/firebase"
import { AlertApp } from "app/components/alert-app/alert-app"
import { AlertTypes } from "app/components/alert-app/alert-app.props"
import { AemSelectors } from "app/redux/aemRedux"
import { getLastSavedFlightTime, setIsShowModalCheckRatingPopup, setLastSavedFlightTime } from "app/utils/storage/mmkv-storage"
import { FlightListingProps } from "app/components/flight-listing-card/flight-listing-card.props"
import { getViewerUID } from "app/utils/screen-helper"
import Category from "./category"
import { FlyCreators, FlySelectors } from "app/redux/flyRedux"
import { DURATION, FeedBackToast, FeedBackToastType } from "app/components/feedback-toast"
import { useFlightSaveErrorHandling } from "app/hooks/useFlightSaveErrorHandling"
import ConfirmSaveFlight from "app/components/flight-details-card/confirm-popup-save-flight"
import { ifAllTrue, ifOneTrue } from "app/utils"
import { env } from "app/config/env-params"
import { get, size } from "lodash"
import { handleImageUrl } from "app/utils/media-helper"
import AddReturnCalendar from "app/screens/fly/flights/add-return-calendar"
import SearchBox from "./search-box"
import { defaultListLocation } from "./consts"
import { useModal } from "app/hooks/useModal"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import { FLIGHT_TYPE } from "app/screens/fly/flights/flight-details/useFlightDetailV2"
import ModalManagerActions from "app/redux/modalManagerRedux"
import SearchDepartureFlight from "app/components/search-departure-flight"
import { useFocusEffect, useIsFocused } from "@react-navigation/native"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"

const SCREEN_NAME = "SearchFlightV2ResultScreen__"
export type SearchFlightsOptions = {
  keyword?: string
  date?: Moment
  direction: FlightDirection
  terminal?: string[]
  airport?: string
  airline?: string
}

const SearchFlightsResult = ({ navigation, route }) => {
  const dispatch = useDispatch()
  const isFocused = useIsFocused()
  const isRequestedSearchRef = useRef(false)
  const shouldRestoreRef = useRef(false)
  const initialSearchFlightsV2ResultRef = useRef(null)
  const { keyword, date = moment(), direction = FlightDirection.Arrival, onGoBack, terminal, airport, airline } = route.params
  const [options, setOptions] = useState<SearchFlightsOptions>({
    keyword,
    date,
    direction: direction?.toUpperCase(),
    terminal,
    airport,
    airline,
  })
  const alertApp = useRef(null)
  const { flyLandingFeatureFlag } = useContext(AccountContext)
  const dataCommonAEM = useSelector(AemSelectors.getMessagesCommon)
  const msg58 = dataCommonAEM?.find((e) => e?.code === "MSG58")
  const msg48 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG48")
  const msg47 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG47")
  const msg50 = dataCommonAEM?.find((e) => e?.code === "MSG50")

  const searchFlightsV2Result = useSelector(SearchSelectors.searchFlightsV2Result)
  const searchAllLoading = useSelector(SearchSelectors.searchAllLoading)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const insertFlightPayload = useSelector(MytravelSelectors.insertFlightPayload)
  const [isNoConnection, setNoConnection] = useState(false)
  const {isModalVisible: isModalVisibleOption, openModal: openModalOption, closeModal: closeModalOption} = useModal("saveFlightTravelOptionSearchFlight")
  const [selectedFlight, setSelectedFlight] = useState<FlightListingProps>(null)
  const [loadingSaveFlight, setLoadingSaveFlight] = useState(false)
  const [selectedTravelOption, setSelectedTravelOption] = useState(
    options.direction === FlightDirection.Arrival
      ? TravelOption.iAmPicking
      : TravelOption.iAmTravelling,
  )
  const removeFlightPayload = useSelector(MytravelSelectors.removeFlightPayload)

  const toastForSavedFlight = useRef(null)
  const toastForRemoveFlight = useRef(null)
  useFlightSaveErrorHandling()
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const {isModalVisible, openModal, closeModal} = useModal("saveConnectingFlightSearch")
  const [showCalendarModal, setShowCalendarModal] = useState(false)
  const [selectedFlightType, setSelectedFlightType] = useState(null)

  const connectingFlightPayload = useSelector(FlySelectors.connectingFlightPayload)
 const isFlyLandingEnabled = getFeatureFlagInit(
    REMOTE_CONFIG_FLAGS.FLY_LANDING,
    flyLandingFeatureFlag,
  )

  const handleMessage48 = (message, number, place) => {
    if (message) {
      return message.replace("<Flight No.>", number).replace("<country>", place)
    }
    return message
  }

  const handleMessage58 = (flight: FlightListingProps, message) => {
    if (message) {
      let status = flight?.flightStatus?.toLowerCase()
      if (status?.includes("cancelled")) {
        status = `been ${status}`
      }
      return message
        .replace("<Flight No.>", flight?.flightNumber)
        .replace("<departed/landed/been cancelled>", status)
    }
    return message
  }

  const notAbleToSaveAlert = (flight: FlightListingProps) => {
    const temp = flight.flightStatus?.split(" ")
    const status = temp?.length > 0 ? temp[0] : ""
    const message =
      handleMessage58(flight, msg58?.message) ||
      `${translate("flightLanding.flight")} ${flight?.flightNumber} ${translate(
        "flightLanding.has",
      )} ${status} ${translate("flightLanding.notSaveMessage")}`
    alertApp?.current?.show({
      title: msg58?.title || translate("flightLanding.alert"),
      description: message,
      labelAccept: msg58?.firstButton || translate("flightLanding.okay"),
      onAccept: () => null,
      type: AlertTypes.ALERT,
    })
  }

  const formatDTAction = (): string => {
    return options.direction === FlightDirection.Arrival ? "arrival" : "departure"
  }

  const removeFlight = async (flight: FlightListingProps) => {
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: flight?.flightNumber,
      flightScheduledDate: flight?.scheduledDate,
      flightDirection: flight?.direction,
    }
    Alert.alert(
      msg48?.title || translate("flightLanding.areYouSure"),
      msg48?.message
        ? handleMessage48(msg48?.message, flight?.flightNumber, flight?.airportDetails?.name)
        : `${translate("flightLanding.removeMessage1")} ${flight?.flightNumber} ${translate(
            "flightLanding.to",
          )} ${flight?.airportDetails?.name} ${translate("flightLanding.removeMessage2")}`,
      [
        {
          text: msg48?.firstButton || translate("flightLanding.cancel"),
        },
        {
          text: msg48?.secondButton || translate("flightLanding.remove"),
          style: "cancel",
          onPress: () => {
            const dtAction = dtManualActionEvent(
              `${FE_LOG_PREFIX}App__flight-${formatDTAction()}-landing-unsave`,
            )
            dtAction.reportStringValue(
              `flight-${formatDTAction()}-landing-unsave-press-flightNumber`,
              `${flight?.flightNumber}`,
            )
            dtAction.reportStringValue(
              `flight-${formatDTAction()}-landing-unsave-press-scheduledDate`,
              `${flight?.scheduledDate}`,
            )
            dtAction.reportStringValue(
              `flight-${formatDTAction()}-landing-unsave-press-direction`,
              `${flight?.direction}`,
            )
            dispatch(
              MytravelCreators.flyMyTravelRemoveFlightRequest(data, {
                item: flight,
              }),
            )
            setIsShowModalCheckRatingPopup(false)
            dtAction.leaveAction()
          },
        },
      ],
    )
    setIsShowModalCheckRatingPopup(true)
  }

  const handleOnPressSave = async ({ flight, isSaved, canSaveFlight }) => {
    const viewerUID = await getViewerUID({shouldReturnNull: true})
    const actionText = isLoggedIn && isSaved ? AdobeValueByTagName.CAppSearchResultFlyRemoveFlight : AdobeValueByTagName.CAppSearchResultFlySaveFlight
    const flightDirection = flight?.direction === FlightDirection.Arrival ? translate("flightLanding.arrivalTabTitle") : translate("flightLanding.departureTabTitle")
    const dataToBeSent = `${flight?.flightNumber} | ${flightDirection} | ${flight?.scheduledDate} | ${actionText} | ${viewerUID}`
    trackAction(AdobeTagName.CAppSearchResultFlySaveFlight, {
      [AdobeTagName.CAppSearchResultFlySaveFlight]: dataToBeSent,
    })

    if (isLoggedIn && isSaved) {
      return removeFlight(flight)
    }
    if (!canSaveFlight) return notAbleToSaveAlert(flight)
    if (!isLoggedIn)
      return navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.FLIGHTS,
        callBackAfterLoginSuccess: () => {
          setSelectedFlight(flight)
          setIsShowModalCheckRatingPopup(true)
          openModalOption()
        },
        callBackAfterLoginCancel: () => null,
      })
    setSelectedFlight(flight)
    setSelectedTravelOption(
      options.direction === FlightDirection.Arrival
        ? TravelOption.iAmPicking
        : TravelOption.iAmTravelling,
    )
    openModalOption()
  }

  const onReloadData = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setNoConnection(false)
      isRequestedSearchRef.current = true
      dispatch(
        SearchActions.searchFlightsV2Request(
          options.keyword?.trim(),
          moment(options.date).format(DateFormats.YearMonthDay),
          options.direction,
          options.terminal?.length === defaultListLocation.length - 1
            ? ""
            : options.terminal?.map((item) => item?.split("-")[1]).join(","),
          options.airport,
          options.airline,
        ),
      )
    } else {
      setNoConnection(true)
    }
  }

  useEffect(() => {
    onReloadData()
  }, [options])

  useFocusEffect(
    useCallback(() => {
      if (shouldRestoreRef.current && initialSearchFlightsV2ResultRef.current) {
        dispatch(SearchActions.setSearchFlightsV2(initialSearchFlightsV2ResultRef.current))
        shouldRestoreRef.current = false 
        initialSearchFlightsV2ResultRef.current = null
      }
    }, [dispatch])
  )
 
  const setRestoreSearchFlightsResult = () => {
    shouldRestoreRef.current = true
    initialSearchFlightsV2ResultRef.current = searchFlightsV2Result
  }

  const showToastForSaveFlightSuccess = () => {
    toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
  }
  
   const openSearchDepartureFlightModal = () => {
    dispatch(ModalManagerActions.openModal("searchDepartureFlight"))
  }

  const onFlightPress = useCallback((flight) => {
    const flightDate = get(flight, "flightDate", "")
    const flightNumber = get(flight, "flightNumber", "")
    const direction = get(flight, "direction", "")
    const searchTabName = getSearchTabName(SearchIndex.flights)
    const flightDirectionLabel = direction === FlightDirection.Arrival ? translate("flightLanding.arrivalTabTitle") : translate("flightLanding.departureTabTitle")

    trackAction(AdobeTagName.CAppFlyFlightListFlightCardClicked, {
      [AdobeTagName.CAppFlyFlightListFlightCardClicked]: "1",
    })
    trackAction(AdobeTagName.CAppFlyFlightListViewFlightDetails, {
      [AdobeTagName.CAppFlyFlightListViewFlightDetails]: `${direction}|${flightDate}|${flightNumber}`,
    })
    trackAction(AdobeTagName.CAppSearchResultClicked, {
      [AdobeTagName.CAppSearchResultClicked]: `${searchTabName} (${flightDirectionLabel}) | ${options?.keyword} | ${flightNumber}`,
    })
    navigation.navigate("flightDetails", {
      payload: { item: flight },
      direction,
    })
  }, [])

  const renderFlight = ({ item }) => (
    <FlightItem
      flight={item}
      onPressed={onFlightPress}
      isLoggedIn={isLoggedIn}
      onSaveFlight={handleOnPressSave}
      containerStyle={flightItemStyle.itemMarginTop}
    />
  )

  const onClosedSheet = () => {
    if (!loadingSaveFlight) {
      closeModalOption()
    }
  }

  const savedFlightTravelOptionsOnModalHide = () => {
    setIsShowModalCheckRatingPopup(false)
  }

  useEffect(() => {
    if (removeFlightPayload?.isRemovedSuccessFully) {
      toastForSavedFlight?.current?.closeNow()
      toastForRemoveFlight?.current?.show(DURATION.LENGTH_LONG)
      dispatch(MytravelCreators.flyClearInsertFlightPayload())
    }
  }, [removeFlightPayload])

  useEffect(() => {
    const timeStamp = new Date().getTime()
    const insertFlightSuccessCondition: boolean = ifOneTrue([
      insertFlightPayload?.insertFlightData?.success,
      insertFlightPayload?.recordExist,
    ])
    const addReturnPopupCondition: boolean = ifOneTrue([
      getLastSavedFlightTime() + env()?.FLIGHT_SHOW_POPUP_ADD_RETURN < timeStamp,
      size(myTravelFlightsPayload?.getMyTravelFlightDetails) === 1,
    ])
    if (insertFlightSuccessCondition) {
      setLoadingSaveFlight(false)
      if (ifAllTrue([insertFlightPayload?.isInsertSuccessfully, isFocused])) {
        if (addReturnPopupCondition && insertFlightPayload?.flightData?.isPassenger) {
          openModal()
          dispatch(SearchActions.resetAutoCompleteFlight())
          setIsShowModalCheckRatingPopup(true)
          setLastSavedFlightTime(0)
          save(StorageKey.isSaveFlightTriggered, true)
        } else {
          closeModalOption()
          // handleFadeShow()
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          toastForRemoveFlight?.current?.closeNow()
          toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          save(StorageKey.isSaveFlightTriggered, true)
        }
      }
    }

    if (insertFlightPayload?.errorFlag) {
      setLoadingSaveFlight(false)
      closeModalOption()
    }
  }, [insertFlightPayload])

  const savedFlightOnPress = async () => {
    setLoadingSaveFlight(true)
    const data = {
      enterpriseUserId: profilePayload?.email,
      countryOfResidence: profilePayload?.residentialCountry,
      flightNumber: selectedFlight?.flightNumber,
      flightScheduledDate: selectedFlight.scheduledDate,
      flightDirection: options.direction,
      flightPax: selectedTravelOption === TravelOption.iAmTravelling,
    }
    if (isLoggedIn) {
      const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-arrival-landing-save`)
      dtAction.reportStringValue(
        "flight-arrival-landing-save-press-flightNumber",
        `${selectedFlight.flightNumber}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-landing-save-press-scheduledDate",
        `${selectedFlight.scheduledDate}`,
      )
      dtAction.reportStringValue(
        "flight-arrival-landing-save-press-travelOption",
        `${selectedTravelOption}`,
      )
      dispatch(
        MytravelCreators.flyMyTravelInsertFlightRequest(data, {
          item: selectedFlight,
        }),
      )
      dtAction.leaveAction()
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.FLIGHTS,
      })
    }
  }

  const travelOptionTapped = (option) => {
    const flyProfile =
      option === TravelOption.iAmTravelling ? FlyProfileEnum.flying : FlyProfileEnum.nonFlying
    const flightNumber = selectedFlight?.flightNumber
    const flightDate = selectedFlight?.scheduledDate
    trackAction(AdobeTagName.CAppFlyFlightDetailFlyProfile, {
      [AdobeTagName.CAppFlyFlightDetailFlyProfile]: `${flyProfile}|${flightNumber}|${flightDate}`,
    })
    setSelectedTravelOption(option)
  }

  const renderContent = () => {
    if (isNoConnection)
      return (
        <ErrorOverlayNoConnection
          reload
          visible
          header={false}
          hideScreenHeader={false}
          onReload={onReloadData}
          headerBackgroundColor="transparent"
          testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          accessibilityLabel={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          noInternetOverlayStyle={rootStyles.overlayStyle}
        />
      )
    const isError = !!searchFlightsV2Result?.searchError
    if (isError) {
      return (
        <ErrorOverlayNoConnection
          reload
          header={false}
          headerBackgroundColor="transparent"
          hideScreenHeader={false}
          visible
          titleTx={isError ? "searchV2.error.unavailable" : undefined}
          messageTx={isError ? "searchV2.error.unavailableMessage" : undefined}
          testID={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          accessibilityLabel={`${SCREEN_NAME}__ErrorOverlayNoConnection`}
          onReload={onReloadData}
          noInternetOverlayStyle={rootStyles.overlayStyle}
          containerStyle={rootStyles.errorContainerStyle}
          textContainerStyle={rootStyles.errorTextContainerStyle}
        />
      )
    }
    return (
      <FlatList
        initialNumToRender={10}
        windowSize={7}
        ListHeaderComponent={
          <View style={rootStyles.caption}>
            <Text preset="caption2Regular" tx="searchV2.flightsResult.caption" />
          </View>
        }
        maxToRenderPerBatch={10}
        onScroll={Keyboard.dismiss}
        showsVerticalScrollIndicator={false}
        data={searchFlightsV2Result?.searchData}
        renderItem={renderFlight}
        keyExtractor={(_, index) => `${SCREEN_NAME}-FlatList-${index}`}
        ListEmptyComponent={<Empty date={options.date} />}
      />
    )
  }
  const showToastForRemoveFlight = () => {
    return (
      <FeedBackToast
        ref={toastForRemoveFlight}
        style={rootStyles.feedBackToastStyle}
        textButtonStyle={rootStyles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 8 }}
        textStyle={rootStyles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={msg50?.message || translate("flyLanding.removeFlightNew")}
      />
    )
  }

  const handleConnectingFlightOnPress = () => {
    const connectingFlightData = {
      isConnecting: true,
      flightConnecting: {
        ...selectedFlight,
        isPassenger: true,
      },
    }
    setSelectedFlightType(FLIGHT_TYPE.CONNECTING)
    closeModal()
    dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightData))
  }

  const renderConfirmSaveFlight = () => {
    return (
      <ConfirmSaveFlight
        imageUrl={handleImageUrl(msg47?.icon)}
        visible={isModalVisible}
        title={translate("flightDetails.newPopupConfirmSaveFlight.title")}
        messageText={translate("flightDetails.newPopupConfirmSaveFlight.arrivalMessage")}
        onClose={() => {
          closeModal()
          save(StorageKey.isSaveFlightTriggered, true)
        }}
        onButtonPressed={() => {
          setSelectedFlightType(FLIGHT_TYPE.RETURN)
          closeModal()
        }}
        textButtonConfirm={translate(
          "flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton",
        )}
        textButtonCancel={translate("flightDetails.newPopupConfirmSaveFlight.cancelButton")}
        onModalHide={() => {
          const timeStamp = new Date().getTime()
          setIsShowModalCheckRatingPopup(false)
          // setStateIsModalCheckShowRatingPopup(false)
          setLastSavedFlightTime(timeStamp)
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          if (selectedFlightType) {
            if (
              selectedFlight.direction === FlightDirection.Arrival &&
              selectedFlightType === FLIGHT_TYPE.CONNECTING && isFlyLandingEnabled
            ) {
              openSearchDepartureFlightModal()
            } else {
              setShowCalendarModal(true)
            }
            setSelectedFlightType(null)
          } else {
            toastForRemoveFlight?.current?.closeNow()
            toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
            dispatch(MytravelCreators.flyClearInsertFlightPayload())
          }
        }}
        isShowButtonConnection={options.direction === FlightDirection.Arrival}
        onButtonConnectionPressed={handleConnectingFlightOnPress}
        textButtonConnection={translate(
          "flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton",
        )}
        disableCloseButton={true}
        openPendingModal
      />
    )
  }

  const renderReturnCalendarView = () => {
    return (
      <AddReturnCalendar
        isVisible={showCalendarModal}
        filterDate={
          moment(selectedFlight ? selectedFlight.displayTimestamp : "").format(
            DateFormats.YearMonthDay,
          ) || moment().format(DateFormats.YearMonthDay)
        }
        initialMinDate={
          moment(selectedFlight ? selectedFlight.displayTimestamp : "").format(
            DateFormats.YearMonthDay,
          ) || moment().format(DateFormats.YearMonthDay)
        }
        onClosedCalendarModal={() => {
          setShowCalendarModal(false)
          toastForSavedFlight?.current?.show(DURATION.LENGTH_LONG)
          const connectingFlightPayloadToClear = {
            isConnecting: false,
            flightConnecting: null,
          }
          dispatch(FlyCreators.setConnectingFlightPayload(connectingFlightPayloadToClear))
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          save(StorageKey.isSaveFlightTriggered, true)
        }}
        onDateSelected={(dateString) => {
          setShowCalendarModal(false)
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
          save(StorageKey.isSaveFlightTriggered, true)
          if (isFlyLandingEnabled) {
            const country = selectedFlight.airportDetails?.country || ""
            const date = moment(dateString)
            setOptions({
              ...options,
              date: date,
              direction:
                direction === FlightDirection.Departure
                  ? FlightDirection.Arrival
                  : FlightDirection.Departure,
              keyword: country,
            })
            return
          }
          const country = connectingFlightPayload.isConnecting
            ? "Singapore"
            : selectedFlight.airportDetails?.country
          navigation.navigate("flightResultLandingScreen", {
            screen:
              options.direction === FlightDirection.Arrival
                ? FlightDirection.Departure
                : FlightDirection.Arrival,
            country,
            selectedDate: moment(dateString).format(DateFormats.YearMonthDay),
          })
          
        }}
        testID={`${SCREEN_NAME}__AddReturnCalendar`}
        accessibilityLabel={`${SCREEN_NAME}__AddReturnCalendar`}
      />
    )
  }

  const renderFlightAddedFeedBackToastMessage = () => {
    return (
      <FeedBackToast
        ref={toastForSavedFlight}
        style={rootStyles.feedBackToastStyle}
        textButtonStyle={rootStyles.toastButtonStyle}
        position={"bottom"}
        textStyle={rootStyles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={translate("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved")}
        onCallback={() => dispatch(MytravelCreators.flyClearInsertFlightPayload())}
      />
    )
  }

  useEffect(() => {
      if (
        options?.keyword &&
        !searchAllLoading &&
        isRequestedSearchRef.current &&
        searchFlightsV2Result?.searchData
      ) {
        isRequestedSearchRef.current = false
        const flightDirectionLabel = options.direction === FlightDirection.Arrival ? translate("flightLanding.arrivalTabTitle") : translate("flightLanding.departureTabTitle")
        const resultQuantity = searchFlightsV2Result?.searchData?.length === 0 ? "No Result" : searchFlightsV2Result?.searchData?.length
        trackAction(AdobeTagName.CAppSearchEvent, {
          [AdobeTagName.CAppSearchEvent]: `Flights (${flightDirectionLabel}) | ${options?.keyword} | ${resultQuantity}`,
        })
      }
    }, [searchAllLoading, searchFlightsV2Result?.searchData, options?.keyword])

  return (
    <SearchScreenProvider>
      <SafeAreaView
        style={rootStyles.containerStyle}
        testID={SCREEN_NAME}
        accessibilityLabel={SCREEN_NAME}
      >
        <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
        <SearchBox
          direction={options.direction}
          keyword={options.keyword}
          date={options.date}
          onPressBack={() => {
            dispatch(SearchActions.setSearchKeyword(""))
            onGoBack &&
              onGoBack({
                keyword: options.keyword,
                direction: options.direction,
                date: options.date,
              })
            navigation.goBack()
          }}
          onPress={() =>
            navigation.navigate(NavigationConstants.searchFlightsV2Modal, {
              date: options.date,
              direction: options.direction,
              keyword: options.keyword,
              onGoBack: (data) =>
                setOptions({
                  ...options,
                  date: data.date,
                  direction: data.direction,
                  keyword: data.keyword,
                }),
            })
          }
        />
        <Category
          filter={{
            terminal: options.terminal,
            direction: options.direction,
            airline: options.airline,
            cityAirport: options.airport,
          }}
          onFilterFlight={(options) =>
            setOptions((data) => ({
              ...data,
              direction: options.direction,
              terminal: options.terminal,
              airline: options.airline,
              airport: options.cityAirport,
            }))
          }
        />
        {!searchAllLoading && renderContent()}
        {!!searchAllLoading && [...Array(6)].map((_, i) => <LoadingView key={i} />)}
      </SafeAreaView>
      <AlertApp ref={alertApp} />
      <SearchDepartureFlight
        handleOnClose={showToastForSaveFlightSuccess}
        displayTimestamp={selectedFlight?.displayTimestamp}
        isPushNavigation
        setRestoreSearchFlightsResult={setRestoreSearchFlightsResult}
      />
      <SavedFlightTravelOptionsModal
        onModalHide={savedFlightTravelOptionsOnModalHide}
        visible={isModalVisibleOption}
        onClosed={onClosedSheet}
        loadingSaveFlight={loadingSaveFlight}
        onBackPressed={onClosedSheet}
        selectedOption={selectedTravelOption}
        savedFlightOnPress={savedFlightOnPress}
        onPress={(option) => travelOptionTapped(option)}
        flightDirection={options.direction}
      />
      {showToastForRemoveFlight()}
      {renderFlightAddedFeedBackToastMessage()}
      {renderConfirmSaveFlight()}
      {renderReturnCalendarView()}
    </SearchScreenProvider>
  )
}

export default SearchFlightsResult
