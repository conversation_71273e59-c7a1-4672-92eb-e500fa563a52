import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { color, spacing } from "app/theme"
import { View, ViewStyle } from "react-native"
import { flightItemStyle } from "./styles"

const lightGreyLoadingColors = [color.palette.lightGrey, color.background, color.palette.lightGrey]
const lighterGreyLoadingColors = [
  color.palette.lightGrey,
  color.background,
  color.palette.lightGrey,
]
const skeletonLayout: ViewStyle[] = [
  {
    width: 48,
    height: 48,
    borderRadius: 8,
  },
  {
    width: 271,
    height: 12,
    marginLeft: spacing[4],
    marginBottom: spacing[3],
    borderRadius: 4,
  },
  {
    width: 148,
    height: 12,
    marginLeft: spacing[4],
    marginBottom: spacing[3],
    borderRadius: 4,
  },
  {
    width: 80,
    height: 12,
    marginLeft: spacing[4],
    borderRadius: 4,
  },
]

export const LoadingView = () => {
  return (
    <View style={[flightItemStyle.container, flightItemStyle.itemMarginTop]}>
      <View>
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lightGreyLoadingColors}
          shimmerStyle={skeletonLayout[0]}
        />
      </View>
      <View style={flightItemStyle.flightDetailContainer}>
        <View>
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerColors={lighterGreyLoadingColors}
            shimmerStyle={skeletonLayout[1]}
          />
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerColors={lighterGreyLoadingColors}
            shimmerStyle={skeletonLayout[2]}
          />
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerColors={lighterGreyLoadingColors}
            shimmerStyle={skeletonLayout[3]}
          />
        </View>
      </View>
    </View>
  )
}
