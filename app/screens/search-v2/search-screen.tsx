import React, { use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, SafeAreaView, StatusBar } from "react-native"
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs"
import { useDispatch } from "react-redux"
import SearchActions from "app/redux/searchRedux"
import { styles } from "./search-screen.styles"
import { translate } from "app/i18n"
import { SearchIndex } from "app/screens/search//tabs/searchIndex"
import SearchFlightsTab from "./tabs/search-tab-flights"
import { trackAction, AdobeTagName } from "app/services/adobe"
import { SearchScreenProvider } from "app/screens/search//search-screen-context"
import { FlyCreators } from "app/redux/flyRedux"
import { TabNavBarWithTopIcon } from "app/navigators/navigation-utilities"
import { useFocusEffect } from "@react-navigation/native"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { SearchTabAll } from "./tabs/search-tab-all"
import { PlaneIcon, SearchIconV2 } from "assets/icons"

const SearchTab = createMaterialTopTabNavigator()
const SCREEN_NAME = "SearchScreenV2__"

const SearchScreen = ({ navigation, route }) => {
  const dispatch = useDispatch()
  const isModuleFlights = SearchIndex.flights === route.params?.screen
  const routeModule = isModuleFlights ? SearchIndex.flights : SearchIndex.all
  const autoFocusTextInput = route.params?.focusTextInput || false

  useEffect(() => {
    dispatch(SearchActions.resetSearchKeywordCollection())
    dispatch(SearchActions.popularSearchKeywordRequest())
    return () => {
      dispatch(SearchActions.setSearchKeyword(""))
    }
  }, [])

  useEffect(() => {
    dispatch(SearchActions.resetSearchKeywordCollection())
    dispatch(FlyCreators.setFilterDateSearchArrival(null))
    dispatch(FlyCreators.setFilterDateSearchDeparture(null))
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    dispatch(SearchActions.popularSearchKeywordRequest())
    return () => {
      dispatch(SearchActions.setSearchKeyword(""))
    }
  }, [])

  const handlePressBack = () => {
    dispatch(SearchActions.setSearchKeyword(""))
    navigation.goBack()
  }

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handlePressBack()
        return true
      }

      const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress)
      return () => subscription.remove()
    }, []),
  )

  return (
    <>
      <SafeAreaView
        style={styles.headerStyle}
        testID={SCREEN_NAME}
        accessibilityLabel={SCREEN_NAME}
      />
      <SafeAreaView style={styles.containerStyle}>
        <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
        <SearchScreenProvider>
          <SearchTab.Navigator
            initialRouteName={routeModule}
            keyboardDismissMode="none"
            tabBar={(props) => {
              return (
                <TabNavBarWithTopIcon
                  props={{ ...props }}
                  isCenter
                  topTabParentStyle={styles.topTabParentStyle}
                  topIconActiveStyle={styles.topIconActiveStyle}
                  topIconInActiveStyle={styles.topIconInActiveStyle}
                  topTabTouchableOpacityStyle={styles.topTabTouchableOpacityStyle}
                  topTabActiveIndicatorStyle={styles.topTabActiveIndicatorStyle}
                  topTabActiveLabelStyle={styles.topTabActiveStyle}
                  topTabInActiveLabelStyle={styles.topTabInActiveStyle}
                  topTabInActiveIndicatorStyle={styles.topTabInActiveIndicatorStyle}
                />
              )
            }}
          >
            <SearchTab.Screen
              name={SearchIndex.all}
              options={{
                tabBarIcon: SearchIconV2,
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: translate("search.tabTitles.all"),
                tabBarTestID: `${SCREEN_NAME}TabAll`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabAll`,
              }}
              listeners={{
                tabPress: () => {
                  navigation.setParams({
                    screen: SearchIndex.all,
                  })
                  trackAction(AdobeTagName.CAppSearchResultCategoryToggle, {
                    [AdobeTagName.CAppSearchResultCategoryToggle]:
                      translate("search.tabTitles.all"),
                  })
                },
              }}
            >
              {(props) => <SearchTabAll autoFocusTextInput={autoFocusTextInput} {...props} />}
            </SearchTab.Screen>
            <SearchTab.Screen
              name={SearchIndex.flights}
              options={{
                tabBarIcon: PlaneIcon,
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: translate("search.tabTitles.flights"),
                tabBarTestID: `${SCREEN_NAME}TabFlights`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabFlights`,
              }}
              listeners={{
                tabPress: () => {
                  navigation.setParams({
                    screen: SearchIndex.flights,
                  })
                  trackAction(AdobeTagName.CAppSearchResultCategoryToggle, {
                    [AdobeTagName.CAppSearchResultCategoryToggle]: translate(
                      "search.tabTitles.flights",
                    ),
                  })
                },
              }}
            >
              {() => (
                <SearchFlightsTab
                  keyword={route?.params?.keyword}
                  date={route?.params?.date}
                  direction={route?.params?.direction}
                  isPushNavigation={!!route?.params}
                />
              )}
            </SearchTab.Screen>
          </SearchTab.Navigator>
        </SearchScreenProvider>
      </SafeAreaView>
    </>
  )
}

export default SearchScreen
