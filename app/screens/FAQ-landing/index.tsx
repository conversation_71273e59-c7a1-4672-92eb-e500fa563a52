import { color } from 'app/theme';
import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { LoadingFAQ } from './Loading';
import { ErrorFAQ } from './Error';
import NetInfo from "@react-native-community/netinfo";
import { WebView } from "react-native-webview";
import { styles as webviewStyle } from "app/screens/terms-of-use-policy/terms-of-use-policy.styles"
import { parseJsonWebviewMessage } from 'app/utils';
import { trackAction, AdobeTagName } from 'app/services/adobe';

const FAQLanding = (props) => {
  const { route } = props
  const { url } = route?.params || {}
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  const onMessageHandle = (message) => {
    const messageData = parseJsonWebviewMessage(message)
    if (messageData?.adobeAnalytics) {
      const {aaTag, aaValue} = messageData?.adobeAnalytics || {}
      const questionContent = aaValue?.split?.(' | ')?.[0]
      if (questionContent && aaTag === AdobeTagName.CAppL3FAQPage) {
        trackAction(AdobeTagName.CAppL3FAQPage, {
          [AdobeTagName.CAppL3FAQPage]: `Parking | ${questionContent} | Link`
        })
      }
    }
  }

  useEffect(() => {
    const checkInternet = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setError(true)
        setLoading(false)
      }
    }
    checkInternet()
  }, [])

  if (error || !url) {
    return (
      <View style={[styles.container, styles.paddingT24]}>
        <ErrorFAQ />
      </View>
    )
  }

  const checkEncodeURI = (url: string) => {
    return /%/i.test(url)
  }

  const onError = () => {
    setLoading(false)
    setError(true)
  }

  return (
    <View style={styles.container}>
      {loading && <View style={styles.paddingT24}><LoadingFAQ /></View>}
      <View style={styles.content}>
        {url && <WebView
          containerStyle={[webviewStyle.containerStyleWebview, styles.background]}
          source={{
            uri: Platform.OS === "ios" && !checkEncodeURI(url) ? encodeURI(url) : url,
          }}
          originWhitelist={["*"]}
          style={[webviewStyle.containerWebview, styles.background]}
          automaticallyAdjustContentInsets={false}
          showsVerticalScrollIndicator={false}
          scrollEnabled={true}
          showsHorizontalScrollIndicator={false}
          javaScriptEnabled
          onLoadEnd={() => { setLoading(false) }}
          onError={onError}
          onHttpError={onError}
          onMessage={onMessageHandle}
        />}
      </View>
    </View>
  )
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
    borderTopWidth: 1,
    borderColor: color.palette.lighterGrey
  },
  background: {
    backgroundColor: color.palette.almostWhiteGrey
  },
  paddingT24: {
    paddingTop: 24
  },
  viewError: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  content: {
    flex: 1,
  }
})

export default FAQLanding