import { Keyboard, View } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import NativeAuthActions, { NativeAuthSelectors } from "app/redux/nativeAuthRedux"
import styles from "../native-login-style"
import ModalTopBar from "../components/modal-top-bar"
import { Text } from "app/elements/text"
import { color } from "app/theme"
import OTPInputView from "@twotalltotems/react-native-otp-input"
import React, { useContext, useEffect, useRef, useState } from "react"
import { translate } from "app/i18n"
import LottieView from "lottie-react-native"
import LoadingSpinner from "../components/loading_spinner.json"
import LoadingSuccess from "../components/loading_success.json"
import { ErrorOutlined } from "ichangi-fe/assets/icons"
import { RES_CODE } from "app/sagas/nativeAuthSaga"
import BottomErrorCommon from "../components/bottom-error-common"
import { NativeLoginContext } from "../native-login-screen"
import { LoadingOverlay } from "app/components/loading-modal"
import { STEP, USER_FLOW } from "../types"
import { getAuthTokenPayload } from "app/utils/storage/mmkv-encryption-storage"
import { AdobeTagName, AdobeValueByTagName, trackAction } from "app/services/adobe"
import ErrorConnection from "../components/error-connection"
import { DT_SECURITY_EVENT_NAME } from "app/services/firebase/analytics"
import { maskEmail } from "../login-helper"
import { getCurrentScreenActive } from "app/utils/screen-hook"
import { triggerSecurityEvent } from "app/services/firebase/security-events-helper"

/**
 * @description Component for verifying the user's email address using an OTP (One-Time Password).
 * Handles OTP input, resending OTP, error handling, and navigation to the next step.
 *
 * @returns {JSX.Element} The VerifyEmail component.
 */

export default function VerifyEmail() {
  const {
    oauthData,
    setCurrentStep,
    setLoading,
    loading,
    sourceSystem,
    setEmailProfile: setEmail,
    currentFlow,
    setPrevStep,
    checkNetwork,
    setNetworkConnected,
    networkConnected,
    setShowSavedEmail,
  } = useContext(NativeLoginContext)
  const dispatch = useDispatch()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const fromLinkAccount = currentFlow === USER_FLOW.SOCIAL_LINKING
  const isFromSignUpNative = currentFlow === USER_FLOW.SIGNUP_NATIVE
  const [numberOTP, setNumberOTP] = useState("")
  const [seconds, setSeconds] = useState(0)
  const [showIndicator, setShowIndicator] = useState({ spinner: false, success: false })
  const [otpMsg, setOtpMsg] = useState("")
  const [isShowError, setIsShowError] = useState(false)
  const emailRegister = useSelector(NativeAuthSelectors.emailRegister) || ""
  const socialResponse = useSelector(NativeAuthSelectors.socialResponse) || {}
  const email = fromLinkAccount ? socialResponse?.email || "" : emailRegister
  const verifyOTPCodeStatus = useSelector(NativeAuthSelectors.verifyOTPCodeStatus)
  const sendOTPCodeStatus = useSelector(NativeAuthSelectors.sendOTPCodeStatus)
  const getFieldsAccountStatus = useSelector(NativeAuthSelectors.getFieldsAccountStatus)
  const missingInfoUser = useSelector(NativeAuthSelectors.missingInfoUser)
  const linkAccountStatus = useSelector(NativeAuthSelectors.linkAccountStatus)
  const isMissingField = useSelector(NativeAuthSelectors.isMissingField)
  const connectionStatus = useSelector(NativeAuthSelectors.connectionStatus)
  const vtoken = useSelector(NativeAuthSelectors.vtoken)
  const [showResend, setShowResend] = useState(false)
  const txtBtnOtp =
    seconds > 0
      ? translate("nativeLoginScreen.verifyEmail.resendOtpWaiting", { time: seconds })
      : translate("nativeLoginScreen.verifyEmail.resendOtp")

  /**
   * @description Handles the back button press. Navigates to the appropriate previous step.
   * setEmail here is used to set the email for UI display.
   */
  const onPressBack = () => {
    if (fromLinkAccount) {
      setCurrentStep(STEP.LINK_ACCOUNT)
    } else {
      setEmail(email)
      setShowSavedEmail(true)
      setCurrentStep(STEP.LOGIN)
    }
  }

  const trackingVerifyEmailFailed = () => {
    if (isFromSignUpNative) {
      trackAction(AdobeTagName.CAppSignupFlow, {
        [AdobeTagName.CAppSignupFlow]: `${AdobeValueByTagName.CAppVerifyEmailPage}Fail`,
      })
    } else {
      trackAction(AdobeTagName.CAppLoginFlow, {
        [AdobeTagName.CAppLoginFlow]: `${AdobeValueByTagName.CAppVerifyEmailPage}Fail`,
      })
    }
  }

  /**
   * @description useEffect hook to manage the countdown timer for resending OTP.
   */
  useEffect(() => {
    if (seconds > 0) {
      intervalRef.current = setInterval(() => {
        setSeconds((prev) => prev - 1)
      }, 1000)
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [seconds])

  /**
   * @description Handles the "Resend OTP" button press. Resets the countdown and dispatches
   * the action to resend the OTP.
   *
   * @async
   */
  const resendOTP = async () => {
    const connected = await checkNetwork()
    if (!connected) return
    setSeconds(60)
    dispatch(NativeAuthActions.sendOtpCode(email, sourceSystem))
  }

  const onCloseErrorModal = () => {
    setIsShowError(false)
  }

  const linkAccount = () => {
    dispatch(
      NativeAuthActions.linkAccount(
        socialResponse?.email,
        socialResponse?.password,
        oauthData?.access_token,
      ),
    )
  }

  const getFields = async () => {
    const authTokenPayload = getAuthTokenPayload()
    dispatch(
      NativeAuthActions.getFieldsRequest(
        authTokenPayload?.postMethod?.access_token,
        missingInfoUser?.uid || "",
      ),
    )
  }
  /**
   * @description useEffect hook to handle the send OTP code status.
   * if successful, it shows the resend button and starts the countdown timer.
   * else it shows an error message.
   */
  useEffect(() => {
    if (sendOTPCodeStatus) {
      if (sendOTPCodeStatus === RES_CODE.LIMIT_REACHED) {
        setOtpMsg("nativeLoginScreen.verifyEmail.error.tooManyRequest")
      } else if (sendOTPCodeStatus === RES_CODE.COMMON_SUCCESS) {
        setShowResend(true)
        setSeconds(60)
      } else {
        setTimeout(() => {
          setIsShowError(true)
          Keyboard.dismiss()
        }, 1000)
      }
      dispatch(NativeAuthActions.clearSendOtpCodeStatus())
    }
  }, [sendOTPCodeStatus])

  /**
   * @description useEffect hook to check if the OTP input is complete (6 digits).
   */
  useEffect(() => {
    if (numberOTP.length === 6) {
      checkOTP()
    }
  }, [numberOTP])

  /**
   * @description useEffect hook to handle the link account status.
   * if successful, it triggers the getFields action.
   * else it shows an error message.
   */
  useEffect(() => {
    if (linkAccountStatus) {
      if (linkAccountStatus === RES_CODE.COMMON_SUCCESS) {
        getFields()
      } else {
        setLoading(false)
        setIsShowError(true)
      }
      dispatch(NativeAuthActions.clearLinkAccountStatus())
    }
  }, [linkAccountStatus])

  /**
   * @description useEffect hook to handle the verify OTP code status.
   * if error, it shows an error message and set the indicator to error.
   * if successful and fromLinkAccount, we call linkAccount. if not fromLinkAccount, we navigate to the next step.
   */
  useEffect(() => {
    if (verifyOTPCodeStatus) {
      const otpCodeStatusTemp = verifyOTPCodeStatus
      dispatch(NativeAuthActions.clearVerifyOtpCodeStatus())
      setShowResend(true)
      if (otpCodeStatusTemp === RES_CODE.VERIFY_INVALID_PARAMS) {
        setOtpMsg("nativeLoginScreen.verifyEmail.error.otpInvalid")
        setShowIndicator({ success: false, spinner: false })
        triggerSecurityEvent("Signup-"+getCurrentScreenActive(),DT_SECURITY_EVENT_NAME.DT_EMULATOR_DETECTED_FOR_EMAIL_SIGNUP_EMAIL_VERIFICATION,"An invalid OTP entry was attempted on an emulator",null)
        trackingVerifyEmailFailed()
      } else if (otpCodeStatusTemp === RES_CODE.COMMON_SUCCESS) {
        setShowIndicator({ success: true, spinner: false })
        triggerSecurityEvent("Signup-"+getCurrentScreenActive(),DT_SECURITY_EVENT_NAME.DT_EMULATOR_DETECTED_FOR_EMAIL_SIGNUP_EMAIL_VERIFICATION,"OTP verfication attempt was successful on an emulator",null)
        if (isFromSignUpNative) {
          trackAction(AdobeTagName.CAppSignupFlow, {
            [AdobeTagName.CAppSignupFlow]: `${AdobeValueByTagName.CAppVerifyEmailPage}Success`,
          })
        } else {
          trackAction(AdobeTagName.CAppLoginFlow, {
            [AdobeTagName.CAppLoginFlow]: `${AdobeValueByTagName.CAppVerifyEmailPage}Success`,
          })
        }

        if (fromLinkAccount) {
          linkAccount()
          setTimeout(() => {
            setLoading(true)
          }, 1200)
        } else {
          setTimeout(() => {
            setPrevStep(STEP.VERIFY_EMAIL)
            setCurrentStep(STEP.SUPPLEMENT_DATA)
          }, 1200)
        }
      } else {
        setIsShowError(true)
        setShowIndicator({ success: false, spinner: false })
        triggerSecurityEvent("Signup-"+getCurrentScreenActive(),DT_SECURITY_EVENT_NAME.DT_EMULATOR_DETECTED_FOR_EMAIL_SIGNUP_EMAIL_VERIFICATION,"Email verification failed on an emulator for " + maskEmail(email),null)
        trackingVerifyEmailFailed()
      }
    }
  }, [verifyOTPCodeStatus])

  /**
   * @description useEffect hook to handle the get fields status.
   * if successful and isMissingField, it navigates to the next step.
   */
  useEffect(() => {
    if (getFieldsAccountStatus) {
      if (getFieldsAccountStatus === RES_CODE.COMMON_SUCCESS) {
        if (isMissingField) setCurrentStep(STEP.SUPPLEMENT_DATA)
      } else {
        setIsShowError(true)
      }
      setLoading(false)
      dispatch(NativeAuthActions.getFieldsReset())
    }
  }, [getFieldsAccountStatus])

  /**
   * @description checks the network connection
   * and dispatches the action to verify the OTP code when the input is complete.
   */
  const checkOTP = async () => {
    const connected = await checkNetwork()
    if (!connected) return
    setOtpMsg("")
    if (seconds === 0) setShowResend(false)
    setShowIndicator({ success: false, spinner: true })
    dispatch(NativeAuthActions.verifyOtpCode(email, sourceSystem, numberOTP, vtoken))
  }

  const retryNetwork = async () => {
    if (connectionStatus === false) {
      const status = await checkNetwork()
      if (status) {
        dispatch(NativeAuthActions.sendOtpCode(email, sourceSystem))
      }
      return
    }
    if (!networkConnected) {
      const status = await checkNetwork()
      if (status) {
        checkOTP()
      }
      return
    }
  }

  const closeNetworkError = () => {
    setNetworkConnected(true)
  }

  return (
    <View style={styles.containerPage}>
      {!networkConnected || connectionStatus === false ? (
        <ErrorConnection retryNetwork={retryNetwork} onClose={closeNetworkError} />
      ) : (
        <>
          <ModalTopBar onPressBack={onPressBack} />
          <View style={styles.content}>
            <Text
              style={{ ...styles.textContent, color: color.palette.almostBlackGrey }}
              tx={"nativeLoginScreen.verifyEmail.title"}
            />
            <Text style={styles.paddingPrimary}>
              <Text style={styles.textResetLink} tx={"nativeLoginScreen.verifyEmail.description"} />
              <Text style={styles.textEnterMail} text={` ${email}`} />
            </Text>
            <View style={{ ...styles.otpContainer, alignItems: "center" }}>
              <OTPInputView
                style={styles.wrapInputNumber}
                pinCount={6}
                code={numberOTP}
                onCodeChanged={setNumberOTP}
                autoFocusOnLoad
                codeInputHighlightStyle={{
                  ...styles.highlightFocusedInput,
                  borderColor: color.palette.gradientColor1Start,
                }}
                codeInputFieldStyle={styles.inputNumber}
                selectionColor={color.palette.overlayColor}
              />
              {showIndicator.spinner && (
                <LottieView style={styles.indicatorSpinner} source={LoadingSpinner} autoPlay loop />
              )}
              {showIndicator.success && (
                <LottieView
                  style={styles.indicatorSuccess}
                  source={LoadingSuccess}
                  autoPlay
                  loop={false}
                />
              )}
            </View>
            {!!otpMsg && (
              <View style={styles.errorAreaVerifyEmail}>
                <ErrorOutlined style={styles.errorIconVerifyEmail} />
                <Text tx={otpMsg} style={styles.txErrorLogin} />
              </View>
            )}
            {showResend && (
              <Text
                disabled={seconds > 0}
                onPress={resendOTP}
                style={seconds > 0 ? styles.txResendOtpWaiting : styles.txResendOtp}
                text={txtBtnOtp}
              />
            )}
          </View>
          <LoadingOverlay visible={loading} />
          <BottomErrorCommon isVisible={isShowError} close={onCloseErrorModal} />
        </>
      )}
    </View>
  )
}
