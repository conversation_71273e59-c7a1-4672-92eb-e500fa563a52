/**
 * This is the bottom tab navigator you will modify to display the main screens of the app.
 * As per the design bottom tab will hold only 4 tabs.
 */
import {
  NativeModules,
  Platform,
  View,
  ViewStyle,
  NativeEventEmitter,
  PixelRatio,
  Linking,
  StyleProp,
  TextStyle,
  TouchableOpacity,
  Text,
  LayoutChangeEvent,
  InteractionManager,
  StyleSheet,
} from "react-native"
import { createBottomTabNavigator, BottomTabNavigationOptions } from "@react-navigation/bottom-tabs"
import { useDispatch, useSelector } from "react-redux"
import { getFocusedRouteNameFromRoute } from "@react-navigation/core"
import { presets } from "app/elements/text/text.presets"
import { translate } from "app/i18n"
import { color } from "app/theme"
import {
  AccountTabScreen,
  AppConfigPermissionTypes,
  BottomTabScreens,
  ChangiPayStatusCode,
  NavigationConstants,
  SOURCE_SYSTEM,
  StateCode,
  TrackingScreenName,
  WIDGET_QUERY_STRING,
  APP_DEEPLINKS,
  FlightDirection,
  screenTagName,
} from "app/utils/constants"
import {
  DineShopFilled,
  DineShopOutline,
  ExploreFilled,
  ExploreOutline,
  FlyFilled,
  FlyOutline,
  ForYouFilled,
  ForYouOutline,
  NaviIconFilled,
  NaviIconOutline,
} from "ichangi-fe/assets/icons/bottom-navigation"
import React, { createContext, useContext, memo, useEffect, useState, useMemo, useRef } from "react"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { FlyLanding, FlyLandingV2 } from "../screens/index"
import ExploreScreenWrapper from '../screens/explore-screen/explore-screen-wrapper'
import { ForYouNavigator } from "./foryou-navigator"
import { ProfileSelectors } from "app/redux/profileRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { useNavigation } from "@react-navigation/native"
import NotificationActions, { NotificationSelectors } from "app/redux/notificationRedux"
import { LoadingOverlay } from "app/components/loading-modal"
import { DT_SECURITY_EVENT_NAME, updateBadgeCount } from "app/services/firebase"
import { closeChangiPay, useCPay } from "app/helpers/changipay"
import { AdobeTagName, getExperienceCloudId, trackAction } from "app/services/adobe"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { useGetConfigurationPermissionHelper } from "app/utils/get-configuration-permission"
import { useHandleNotification } from './handlers/notification'
import { BAGGAGE_TRACKER_CONTEXT } from "app/services/context/baggage-tracker"
import Braze from "@braze/react-native-sdk"
import { getAuthTokenPayload } from "app/utils/storage/mmkv-encryption-storage"
import Animated, { SharedValue, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from "react-native-reanimated"
import { getActiveRouteName } from "./navigation-utilities"
import ChangiEcardController from "app/components/changi-ecard/changi-ecard-controler"
import {  countUnreadAnnouncements, getWidgetTrackingValue, resetInactivityTimeout, trackingShowRatingPopup, trackingShowRatingPopupExploreScreen } from "app/utils/screen-helper"
import { ENUM_STORAGE_MMKV, ENUM_STORAGE_TYPE, getMMKVdata, setIsShowModalCheckRatingPopup, setMMKVdata } from "app/utils/storage/mmkv-storage"
import { PanResponderContext } from "app/services/context/pan-responder"
import { getCurrentScreenActive } from "app/utils/screen-hook"
import { store } from "app/redux/store"
import isEmpty from "lodash/isEmpty"
import AuthActions from "app/redux/nativeAuthRedux";
import { checkSurvey } from "app/services/survey/qualtrics"
import { env } from "app/config/env-params"
import L2AnnouncementBanner from "app/components/l2-announcement/l2-announcement"
import { getFeatureFlagInit, isFlagOnCondition, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import { NotificationOutline } from "ichangi-fe/assets/icons"
import parse from "url-parse"
import get from "lodash/get"
import InAppBrowser from "react-native-inappbrowser-reborn"
import DineShopWrapper from "app/screens/dine-shop-v2"
import { AlertApp } from "app/components/alert-app/alert-app"
import { triggerAdobeCampaignTrackingCodeUTM } from "app/helpers/deeplink/deeplink-parameter"
import {triggerSecurityEvent} from "app/services/firebase/security-events-helper"
// To be refractored once all the screens are developed
const EmptyView = () => {
  return <></>
}

const bg = presets.XSmallBold
delete bg.color

const BottomTab = createBottomTabNavigator()
const rnEventEmitter =
  Platform.OS === "ios"
    ? new NativeEventEmitter(NativeModules.RNEventEmitter)
    : new NativeEventEmitter(NativeModules.ChangiPayModule)

const InAppBrowserManager = NativeModules.InAppBrowserManager

const handleBrazeUrl = (url) => {
  if (url) {
    if (url.startsWith('cagichangi://')) {
      Linking.openURL(url)
    } else if (url.startsWith('https://cagapp3.page.link') || url.startsWith('https://ichangi3.page.link')) {
      if (Platform.OS === 'ios') {
        InAppBrowserManager.openUniversalLinkInApp(url)
      }
    }
  }
}

const useHandleDeepLink = ({ navigation, onChangiPayWidget }) => {
  const handleNavigateToStaffPerkListing = (isUserLoggedIn) => {
    if (isUserLoggedIn) {
      const userProfile = store.getState().profileReducer?.profilePayload
      if (userProfile?.airportStaff) {
        navigation.navigate(NavigationConstants.staffPerkListing)
      }
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => {
          const updatedUserProfile = store.getState().profileReducer?.profilePayload
          if (updatedUserProfile?.airportStaff) {
            navigation.navigate(NavigationConstants.staffPerkListing)
          }
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const handleNavigateToVPR = (isUserLoggedIn) => {
    if (isUserLoggedIn) {
      navigation.navigate(NavigationConstants.vouchersPrizesRedemptionsScreen)
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => {
          navigation.navigate(NavigationConstants.vouchersPrizesRedemptionsScreen)
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const handleNavigateToRedemptionCatalogue = (isUserLoggedIn) => {
    if (isUserLoggedIn) {
      navigation.navigate(NavigationConstants.redemptionCatalogueScreen)
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => {
          navigation.navigate(NavigationConstants.redemptionCatalogueScreen)
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }
  const handleDeepLink = async ({ url }) => {
    let isLoggedIn = false
    const authTokenPayload = getAuthTokenPayload()
    if (authTokenPayload?.postMethod?.access_token) {
      isLoggedIn = true
    }
    const widgetTypeQueryString = `${WIDGET_QUERY_STRING.TYPES.TYPE}=${WIDGET_QUERY_STRING.VALUES.WIDGET}`
    if (url?.startsWith("cagichangi://pay")) {
      closeChangiPay()
      ChangiEcardController.hideModal()
      await new Promise((resolve) => setTimeout(resolve, 500))
      const isFromWidget = url?.includes(widgetTypeQueryString)
      if (isFromWidget) {
        const trackingStringValue = getWidgetTrackingValue(url)
        if (trackingStringValue) {
          trackAction(AdobeTagName.CAppWidget, {
            [AdobeTagName.CAppWidget]: trackingStringValue,
          })
        }
      }

      onChangiPayWidget()
    } else if (url?.startsWith("cagichangi://cr_ecard")) {
      closeChangiPay()
      ChangiEcardController.hideModal()
      await new Promise((resolve) => setTimeout(resolve, 500))
      const isFromWidget = url?.includes(widgetTypeQueryString)
      if (isFromWidget) {
        const trackingStringValue = getWidgetTrackingValue(url)
        if (trackingStringValue) {
          trackAction(AdobeTagName.CAppWidget, {
            [AdobeTagName.CAppWidget]: trackingStringValue,
          })
        }
      }

      if (isLoggedIn) {
        ChangiEcardController.showModal(navigation)
        setIsShowModalCheckRatingPopup(true)
      } else {
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP,
          callBackAfterLoginSuccess: () => {
            ChangiEcardController.showModal(navigation)
            setIsShowModalCheckRatingPopup(true)
          },
          callBackAfterLoginCancel: () => null,
        })
      }
    } else if (url?.startsWith("cagichangi://login")) {
      const isFromWidget = url?.includes(widgetTypeQueryString)
      if (isFromWidget) {
        const trackingStringValue = getWidgetTrackingValue(url)
        if (trackingStringValue) {
          trackAction(AdobeTagName.CAppWidget, {
            [AdobeTagName.CAppWidget]: trackingStringValue,
          })
        }
      }
      navigation.navigate(NavigationConstants.authScreen, { sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP })
    } else if (url?.startsWith(APP_DEEPLINKS.EXPLORE_CHANGI)) {
      const isScrollToExploreChangiSection = url.includes("section=explorechangi")
      navigation.navigate(NavigationConstants.explore, {
        params: {
          isOpenApp: false,
          isScrollToExploreChangiSection,
        },
      })
    } else if (url?.startsWith(APP_DEEPLINKS.FLY_DEPARTURE)) {
      navigation.navigate(NavigationConstants.fly, {
        screen: screenTagName.flights,
        params: { initFlightListTabKey: FlightDirection.Departure },
      })
    } else if (url?.startsWith(APP_DEEPLINKS.DINESHOP_DINE_PAGE)) {
      const parsedUrl = parse(url, true)
      const query = parsedUrl.query
      const timestamp = new Date().getTime()
      navigation.navigate(NavigationConstants.dineShop, { screen: query?.page, section: query?.section, timestamp })
    } else if (url?.startsWith(APP_DEEPLINKS.STAFF_PERK_LISTING)) {
      handleNavigateToStaffPerkListing(isLoggedIn)      
    } else if (url?.startsWith(APP_DEEPLINKS.REDEEMABLE_PRIZES)) {
      handleNavigateToVPR(isLoggedIn)
    } else if (url?.startsWith(APP_DEEPLINKS.REDEEM_POINTS)) {
      handleNavigateToRedemptionCatalogue(isLoggedIn)
    } else if (url?.startsWith(APP_DEEPLINKS.RETRO_CLAIM_DETAILS)) {
      const parsedUrl = parse(url, true)
      const query = get(parsedUrl, "query")
      navigation.navigate(NavigationConstants.retroClaimsNotifications, {
        data: query,
      })
    } else if (url?.startsWith(APP_DEEPLINKS.EXTERNAL_LINK)) {
      const parsedUrl = parse(url, true)
      const query = get(parsedUrl, "query")
      const link = get(query, "link", "")
      if (link) {
        if (Platform.OS === 'ios') {
          InAppBrowserManager.openInAppBrowser(link)
        } else {
          InAppBrowser.open(link)
        }
      }
    } else if (url?.startsWith(APP_DEEPLINKS.WEBVIEW)) {
      const parsedUrl = parse(url, true)
      const query = get(parsedUrl, "query")
      const link = get(query, "link", "")
      if (link) {
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: link,
          needCloseButton: true,
          needBackButton: true,
        })
      }
    } else if (url?.startsWith(APP_DEEPLINKS.EPICPAGE_PROD)) {
      if (isLoggedIn) {
        navigation.navigate("dineShop")
      } else {
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP,
          callBackAfterLoginSuccess: () => {
            navigation.navigate("dineShop")
          },
          callBackAfterLoginCancel: () => null,
        })
      }
    } else if (url?.startsWith(APP_DEEPLINKS.ACCOUNT)) {
      const parsedUrl = parse(url, true)
      const query = get(parsedUrl, "query")
      triggerAdobeCampaignTrackingCodeUTM(query)
      navigation.navigate(NavigationConstants.account, {
        screen: NavigationConstants.forYouScreen,
      })
    } else if (url?.startsWith(APP_DEEPLINKS.MY_ACCOUNT)) {
      const parsedUrl = parse(url, true)
      const query = get(parsedUrl, "query")
      triggerAdobeCampaignTrackingCodeUTM(query)
      const redirectToAccount = () => {
        navigation.navigate(NavigationConstants.account, {
          screen: NavigationConstants.forYouScreen,
        })
      }
      if (isLoggedIn) {
        redirectToAccount()
      } else {
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP,
          callBackAfterLoginSuccess: () => {
            redirectToAccount()
          },
          callBackAfterLoginCancel: () => redirectToAccount(),
        })
      }
    }
  }

  useEffect(() => {
    const linkingEvent: any = Linking.addEventListener("url", handleDeepLink)
    Linking.getInitialURL().then((url) => {
      handleDeepLink({ url })
    })
    return () => {
      linkingEvent?.remove()
    }
  }, [])
}

export interface IBottomNavContext {
  hideBottombar: () => void,
  showBottombar: () => void,
  bottomTabsOpacity?: SharedValue<number>,
  bottomTabsPosition?: SharedValue<number>,
  bottomTabActualHeight?: SharedValue<number>,
  l2AnnouncementPosition?: SharedValue<number>,
  isL2AnnouncementDisplay?: SharedValue<boolean>,
}

export const BottomNavContext = createContext<IBottomNavContext>({
  hideBottombar: () => {},
  showBottombar: () => {},
})

const BottomTabNavigator = memo(() => {
  const dispatch = useDispatch()
  const insets = useSafeAreaInsets()
  const userProfile = useSelector(ProfileSelectors.profilePayload)
  const isLoggedIn: boolean = useSelector(AuthSelectors.isLoggedIn)
  const navigation = useNavigation()
  const alertAppRef = useRef(null)
  const notificationsCountPayload = useSelector(NotificationSelectors.notificationsCountPayload)
  const eventAndPromotionNotificationCount = useSelector(
    NotificationSelectors.eventAndPromotionNotificationCount,
  )
  const allL1AdvisoriesPayload = useSelector(NotificationSelectors.allL1AdvisoriesPayload)
  const allAnnouncementsPayload = useSelector(NotificationSelectors.allAnnouncementsPayload)
  const isDismissAllAnnouncements = useSelector(NotificationSelectors.isDismissAllAnnouncements)
  const unReadAnnouncementCount = countUnreadAnnouncements([...(allL1AdvisoriesPayload || []), ...(allAnnouncementsPayload || [])])
  const countNotification = notificationsCountPayload + eventAndPromotionNotificationCount + unReadAnnouncementCount
  const BAGGAGE_HANDLERS = React.useContext(BAGGAGE_TRACKER_CONTEXT).Handlers
  const { conditionTimeRef, idleTimeRef, isPageLoadingRef } = useContext(PanResponderContext)
  const { l2AnnouncementFeatureFlag, flyLandingFeatureFlag } = useContext(AccountContext)
  const showL2Announcement = isFlagOnCondition(l2AnnouncementFeatureFlag)

  const { handleNavigation } = useHandleNavigation("STATE_CODE_CHANGI_PAY")
  const {
    loadingGetConfig,
    getConfigApp,
    notifyDisableChangiPay,
  } = useGetConfigurationPermissionHelper()
  const [isSetChangiPay, setChangiPay] = useState(null)
  const bellIconDegree = useSharedValue(0)
  const bellIconOpacity = useSharedValue(0)
  const accountIconOpacity = useSharedValue(1)

  const {openChangiPay} = useCPay()

  const bellIconStyle = useAnimatedStyle(() => {
    return {
      left: 0,
      opacity: bellIconOpacity.value,
      position: "absolute",
      top: 0,
      transform: [{ rotate: `${bellIconDegree.value}deg` }]
    }
  }, [bellIconDegree.value, bellIconOpacity.value])

  const accountIconStyle = useAnimatedStyle(() => {
    return {
      opacity: accountIconOpacity.value,
    }
  }, [accountIconOpacity.value])

  const tabBarLabelStyle: StyleProp<TextStyle> = useMemo(() => {
    return {
      ...bg,
      fontSize: Math.min(PixelRatio.getFontScale() * 11, 11),
      textTransform: "none",
      textAlignVertical: "center",
      marginTop: 12,
      marginBottom: insets.bottom > 0 ? 0 : 5,
    }
  }, [insets.bottom])
  const screenTabBarOption: BottomTabNavigationOptions = useMemo(() => {
    return {
      tabBarLabelStyle,
      tabBarAllowFontScaling: false,
      tabBarInactiveTintColor: color.palette.midGrey,
      tabBarActiveTintColor: color.palette.lightPurple,
      tabBarStyle: styles.tabBarStyle,
      tabBarItemStyle: styles.tabBarItemStyle,
    }
  }, [tabBarLabelStyle?.marginBottom])

  useHandleNotification({ BAGGAGE_HANDLERS, navigation, alertAppRef })

  const onChangiPay = () => {
    if (isLoggedIn && userProfile) {
      InteractionManager.runAfterInteractions(() => {
        setChangiPay(true)
      })
    } else {
      //@ts-ignore
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.CPAY,
        callBackAfterLoginSuccess: () => {
          InteractionManager.runAfterInteractions(() => {
            setChangiPay(true)
          })
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const onChangiPayWidget = () => {
    const persistAuthReducer : any = "persist:authReducer"
    const persistAuthReducerNative: any = "persist:nativeAuthReducer"
    const persistProfileReducer : any = "persist:profileReducer"
    const localAuthData = getMMKVdata(persistAuthReducer, ENUM_STORAGE_TYPE.string).toString() || getMMKVdata(persistAuthReducerNative, ENUM_STORAGE_TYPE.string).toString()
    const localProfileData = getMMKVdata(persistProfileReducer, ENUM_STORAGE_TYPE.string).toString()
    const isLoggedInData = JSON.parse(localAuthData).isLoggedIn
    const profileData = JSON.parse(localProfileData)
    
    if (isLoggedInData && profileData) {
      setChangiPay(true)
    } else {
      //@ts-ignore
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.CPAY,
        callBackAfterLoginSuccess: () => {
          setChangiPay(true)
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  useHandleDeepLink({ navigation, onChangiPayWidget })

  const handleNavigateByCode = (code) => {
    switch (code) {
      case ChangiPayStatusCode.PROFILE_UPDATE_REQUIRED:
        //@ts-ignore
        navigation.navigate(NavigationConstants.profileScreen)
        break
      case ChangiPayStatusCode.REDIRECT_ISHOPCHANGI:
        handleNavigation(NavigationTypeEnum.deepLink, StateCode.ISHOPCHANGI)
        break
      case ChangiPayStatusCode.REDIRECT_CHANGIMILLIONAIRE_402:
      case ChangiPayStatusCode.REDIRECT_CHANGIMILLIONAIRE_403:
        handleNavigation(NavigationTypeEnum.deepLink, StateCode.CHANGI_MILLIONAIRE)
    }
  }
  useEffect(() => {
    const brazeGetInitialUrl = async () => {
      Braze.getInitialPushPayload(pushPayload => {
        handleBrazeUrl(pushPayload?.url)
      })
    }
    brazeGetInitialUrl()
    rnEventEmitter.addListener("onChange", (result) => {
      if (result?.code) {
        handleNavigateByCode(result?.code)
      }
    })
    rnEventEmitter.addListener("onCloseChangiPay", () => {
      setIsShowModalCheckRatingPopup(false)
      resetInactivityTimeout({
        conditionTimeRef,
        idleTimeRef,
        callback: () =>
          getCurrentScreenActive() === TrackingScreenName.Explore
            ? trackingShowRatingPopupExploreScreen({ isPageLoadingRef })
            : trackingShowRatingPopup({ isPageLoadingRef }),
      })
      setTimeout(() => {
        checkSurvey(env())
      }, 500)
    })
    return () => {
      rnEventEmitter?.removeAllListeners("onChange")
    }
  }, [])

  useEffect(() => {
    updateBadgeCount(countNotification)
  }, [countNotification])

  useEffect(() => {
    if (isSetChangiPay && userProfile) {
      getConfigApp({
        configKey: AppConfigPermissionTypes.changiappWalletEnabled,
        callbackSuccess: () => {
          openChangiPay()
        },
        callbackFailure: () => notifyDisableChangiPay(),
      })
      setChangiPay(false)
    }
  }, [isSetChangiPay, userProfile])

  useEffect(() => {
    //don't proceed if null or undefined
    if (!userProfile) return;

    const isBotSignup = getMMKVdata(ENUM_STORAGE_MMKV.IS_BOT_SIGNUP, ENUM_STORAGE_TYPE.boolean)
    if(userProfile && isBotSignup) {
      triggerSecurityEvent("Sign-up-"+getCurrentScreenActive(),DT_SECURITY_EVENT_NAME.DT_SIGN_UP_ACCOUNT_AND_PROFILE_CREATED,"Native Account Sign up was completed on an emulator",userProfile)
      setMMKVdata(ENUM_STORAGE_MMKV.IS_BOT_SIGNUP, false)
    }
  }, [userProfile])

  const trackingTab = (tagName) => {
    trackAction(tagName, {
      [tagName]: "1",
    })
  }

  const bottomTabsOpacity = useSharedValue(1)
  const bottomTabsPosition = useSharedValue(0)
  const isShow = useSharedValue(1)
  const bottomTabHeight = useSharedValue(0)
  const l2AnnouncementPosition = useSharedValue(0)
  const isL2AnnouncementDisplay = useSharedValue(false)

  const onBottomTabLayout = (e: LayoutChangeEvent) => {
    bottomTabHeight.value = e.nativeEvent.layout.height
  }

  const hideTabs = () => {
    bottomTabsOpacity.value = withTiming(0, {duration: 200})
    bottomTabsPosition.value = withTiming(-bottomTabHeight.value, {duration: 200}, () => {
      isShow.value = 0
    })
  }

  const showTabs = () => {
    bottomTabsOpacity.value = withTiming(1, {duration: 200})
    bottomTabsPosition.value = withTiming(0, {duration: 200}, () => {
      isShow.value = 1
    })
  }

  // Trigger account icon animation when dismissing all banners
  useEffect(() => {
    if (!isDismissAllAnnouncements) return
    showTabs()
    bellIconDegree.value = -20
    bellIconOpacity.value = withTiming(1, { duration: 100 }, () => {
      bellIconDegree.value = withRepeat(withTiming(20, { duration: 175 }), 4, true, () => {
        bellIconDegree.value = withTiming(0, { duration: 175 }, () => {
          bellIconOpacity.value = withTiming(0)
          accountIconOpacity.value = withTiming(1)
        })
      })
    })
    accountIconOpacity.value = withTiming(0, { duration: 100 })
    dispatch(NotificationActions.setDismissAllTrigger(false))
  }, [isDismissAllAnnouncements])

  return (
    <>
      <BottomNavContext.Provider value={{
        hideBottombar: hideTabs,
        showBottombar: showTabs,
        bottomTabsOpacity,
        bottomTabsPosition,
        bottomTabActualHeight: bottomTabHeight,
        l2AnnouncementPosition,
        isL2AnnouncementDisplay,
      }}>
      <BottomTab.Navigator screenOptions={{ headerShown: false }}  tabBar={props => <MyBottomTabBar {...props} onBottomTabLayout={onBottomTabLayout} />}>
        <BottomTab.Screen
          name="explore"
          component={ExploreScreenWrapper}
          initialParams={{ isOpenApp: true }}
          options={{
            ...screenTabBarOption,
            tabBarLabel: translate("bottomNavigation.explore"),
            tabBarIcon: function renderFocused({ focused }) {
              return focused ? <ExploreFilled /> : <ExploreOutline />
            },
          }}
          listeners={{
            tabPress: (_e) => {
              // Prevent default action
              trackingTab(AdobeTagName.CAppNavigationBarHome)
            },
          }}
        />
        <BottomTab.Screen
          name="fly"
          component={getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag) ? FlyLandingV2 : FlyLanding}
          options={{
            ...screenTabBarOption,
            tabBarLabel: translate("bottomNavigation.fly"),
            tabBarIcon: function renderFocused({ focused }) {
              return focused ? <FlyFilled /> : <FlyOutline />
            },
          }}
          listeners={{
            tabPress: (_e) => {
              // Prevent default action
              trackingTab(AdobeTagName.CAppNavigationBarFly)
            },
          }}
        />
        <BottomTab.Screen
          name="pay"
          component={EmptyView}
          options={{
            ...screenTabBarOption,
            tabBarLabel: translate("bottomNavigation.pay"),
            tabBarIcon: function renderFocused({ focused }) {
              return focused ? <NaviIconFilled /> : <NaviIconOutline />
            },
          }}
          listeners={{
            tabPress: (e) => {
              // Prevent default action
              e.preventDefault()
              onChangiPay()
              trackingTab(AdobeTagName.CAppNavigationBarCPay)
            },
          }}
        />
        <BottomTab.Screen
          name="dineShop"
          component={DineShopWrapper}
          options={{
            ...screenTabBarOption,
            tabBarLabel: translate("bottomNavigation.dineShop"),
            tabBarIcon: function renderFocused({ focused }) {
              return focused ? <DineShopFilled /> : <DineShopOutline />
            },
          }}
          listeners={{
            tabPress: (_e) => {
              // Prevent default action
              trackingTab(AdobeTagName.CAppNavigationBarDineShop)
            },
          }}
        />
        <BottomTab.Screen
          name={NavigationConstants.account}
          component={ForYouNavigator}
          options={({ route }) => {
            const routeName = getFocusedRouteNameFromRoute(route)
            return {
              ...screenTabBarOption,
              tabBarLabel: translate("bottomNavigation.account"),
              tabBarVisible: routeName
                ? [NavigationConstants.forYouScreen].includes(routeName)
                : true,
              tabBarIcon({ focused }) {
                return (
                  <View>
                    <Animated.View style={bellIconStyle}>
                      <NotificationOutline
                        color={focused ? color.palette.lightPurple : color.palette.midGrey}
                        height={24}
                        width={24}
                      />
                    </Animated.View>
                    <Animated.View style={accountIconStyle}>
                      {focused ? <ForYouFilled /> : <ForYouOutline />}
                      {countNotification > 0 && <View style={styles.dotStyles} />}
                    </Animated.View>
                  </View>
                )
              },
            }
          }}
          listeners={{
            tabPress: (_e) => {
              // Prevent default action
              trackingTab(AdobeTagName.CAppNavigationBarAccount)
            },
          }}
        />
      </BottomTab.Navigator>
      <AlertApp ref={alertAppRef} />
      {showL2Announcement && <L2AnnouncementBanner />}
      </BottomNavContext.Provider>
      <LoadingOverlay visible={loadingGetConfig} />
    </>
  )
})

function MyBottomTabBar(props) {
  const { state, descriptors, navigation, onBottomTabLayout } = props
  const dispatch = useDispatch()
  const insets = useSafeAreaInsets()
  const {bottomTabsPosition, bottomTabsOpacity, showBottombar, hideBottombar} = useContext(BottomNavContext)

  useEffect(() => {
    const currentName = getActiveRouteName(state)
    const neverShowTabs = !BottomTabScreens.includes(currentName)
    if(currentName && neverShowTabs){
      hideBottombar()
    } else {
      showBottombar()
    }
    const isAccountScreen = AccountTabScreen.includes(currentName)
    const isJustLoggedOut = isEmpty(store.getState().nativeAuthReducer) || !store.getState().nativeAuthReducer.hasOwnProperty("loadingLogin")
    if(!isAccountScreen && isJustLoggedOut){
      dispatch(AuthActions.nativeAuthLoginReset())
    }
  }, [state])

  const tabBarStyle: StyleProp<ViewStyle> = {
    backgroundColor: color.palette.whiteGrey,
    elevation: 5,
    shadowColor: color.palette.almostBlackGrey,
    shadowOpacity: 0.16,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0,
    },
    flexDirection: 'row'
  }
  const tabBarLabelStyle: StyleProp<TextStyle> = {
    ...bg,
    fontSize: Math.min(PixelRatio.getFontScale() * 11, 11),
    textTransform: "none",
    textAlignVertical: "center",
    paddingTop: 3,
    paddingBottom: insets.bottom > 4 ? insets.bottom - 4 : 20,
    textAlign: 'center',
  }
  const tabBarStyleItemContainer: StyleProp<ViewStyle> = { flex: 1, paddingTop: 10 }
  const tabBarStyleItemIcon: StyleProp<ViewStyle> = { alignSelf: 'center' }
  
  const animatedStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      bottom: bottomTabsPosition.value,
      opacity: bottomTabsOpacity.value,
    }
  })

  return (
    <>
        <Animated.View 
          style={[tabBarStyle, animatedStyle]}
          onLayout={onBottomTabLayout}
          >
          {state.routes.map((route, index) => {
            const { options } = descriptors[route.key]
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                  ? options.title
                  : route.name
            const isFocused = state.index === index
            const TabBarIcon = options.tabBarIcon || (() => null)

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              })

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name, route.params)
              }
            }

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              })
            }

            return (
              <TouchableOpacity
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={tabBarStyleItemContainer}
                key={`MyBottomTabBar-${index}`}
                activeOpacity={1}
              >
                <View style={tabBarStyleItemIcon}>
                  <TabBarIcon focused={isFocused} />
                </View>
                <Text style={{
                  ...tabBarLabelStyle,
                  color: isFocused ? color.palette.lightPurple : color.palette.midGrey,
                }}>
                  {label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </Animated.View>
    </>
  )
}

const styles = StyleSheet.create({
  tabBarItemStyle: {
    paddingTop: 16,
  },
  tabBarStyle: {
    backgroundColor: color.palette.whiteGrey,
    elevation: 5,
    shadowColor: color.palette.almostBlackGrey,
    shadowOpacity: 0.16,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0,
    },
  },
  dotStyles: {
    backgroundColor: color.palette.lightRed,
    borderColor: color.palette.whiteGrey,
    borderRadius: 7,
    borderWidth: 1,
    height: 9,
    right: 2,
    overflow: "hidden",
    position: "absolute",
    top: 0,
    width: 9,
  },
  forYouContainerStyle: {

  }
})

export default BottomTabNavigator
