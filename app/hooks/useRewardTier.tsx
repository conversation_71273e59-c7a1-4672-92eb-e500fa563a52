import { Tier } from "app/components/changi-rewards-member-card"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { color } from "app/theme/color"
import { useMemo } from "react"
import { useSelector } from "react-redux"
import {
  CRGoldCardQr,
  CRMemberCardQr,
  CRMonarchCardQr,
  CRPlatinumCardQr,
  CRGoldSmallIcon,
  CRMemberSmallIcon,
  CRMonarchSmallIcon,
  CRPlatinumSmallIcon,
  CRMemberCardQrV2,
  CRGoldCardQrV2,
  CRPlatinumCardQrV2,
  CRMonarchCardQrV2,
  CRMemberBFIcon,
  CRGoldBFIcon,
  CRPlatinumBFIcon,
  CRMonarchBFIcon,
} from "ichangi-fe/assets/icons"
import { SvgProps } from "react-native-svg"
export interface IRewardTierInfo {
  title: Tier,
  isStaff: boolean,
  color: string,
  backgroundColor: string,
  cardGradientColor: string[],
  smallIcon: React.FC<SvgProps>,
  bfIcon: React.FC<SvgProps>,
  iconBgColor: string
  iconColor: string
  redeemBtnColors: string[]
  dashedLineColor: string
  crCardIcon: React.FC<SvgProps>
  crCardIconV2: React.FC<SvgProps>
  unitLabelColor: string
  benefitsButtonGradient: string[]
  backgroundMemberInfoExploreV2: string[]
  colorTextRewardCard: string
}


export const MAPPING_COLOR_BY_TIER: {[x: string]: IRewardTierInfo} = {
    [Tier.Member]: {
      title: Tier.Member,
      isStaff: false,
      color: color.palette.memberTier,
      backgroundColor: color.palette.memberTierBackground,
      cardGradientColor: color.palette.memberTierCardGradient,
      smallIcon: CRMemberSmallIcon,
      bfIcon: CRMemberBFIcon,
      iconBgColor: color.palette.memberTierIconBackground,
      iconColor: color.palette.memberTierIcon,
      redeemBtnColors: color.palette.memberTierRedeemBtnGradient,
      dashedLineColor: color.palette.memberTierDashedLine,
      crCardIcon: CRMemberCardQr,
      crCardIconV2: CRMemberCardQrV2,
      unitLabelColor: color.palette.memberTierUnitLabelColor,
      benefitsButtonGradient: color.palette.memberTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundMemberInfo,
      colorTextRewardCard: color.palette.lightPurple
    },
    [Tier.StaffMember]: {
      title: Tier.Member,
      isStaff: true,
      color: color.palette.memberTier,
      backgroundColor: color.palette.memberTierBackground,
      cardGradientColor: color.palette.memberTierCardGradient,
      smallIcon: CRMemberSmallIcon,
      bfIcon: CRMemberBFIcon,
      iconBgColor: color.palette.memberTierIconBackground,
      iconColor: color.palette.memberTierIcon,
      redeemBtnColors: color.palette.memberTierRedeemBtnGradient,
      dashedLineColor: color.palette.memberTierDashedLine,
      crCardIcon: CRMemberCardQr,
      crCardIconV2: CRMemberCardQrV2,
      unitLabelColor: color.palette.memberTierUnitLabelColor,
      benefitsButtonGradient: color.palette.memberTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundMemberInfo,
      colorTextRewardCard: color.palette.lightPurple
    },
    [Tier.Gold]: {
      title: Tier.Gold,
      isStaff: false,
      color: color.palette.goldTier,
      backgroundColor: color.palette.goldTierBackground,
      cardGradientColor: color.palette.goldTierCardGradient,
      smallIcon: CRGoldSmallIcon,
      bfIcon: CRGoldBFIcon,
      iconBgColor: color.palette.goldTierIconBackground,
      iconColor: color.palette.goldTierIcon,
      redeemBtnColors: color.palette.goldTierRedeemBtnGradient,
      dashedLineColor: color.palette.goldTierDashedLine,
      crCardIcon: CRGoldCardQr,
      crCardIconV2: CRGoldCardQrV2,
      unitLabelColor: color.palette.goldTierUnitLabelColor,
      benefitsButtonGradient: color.palette.goldTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundGoldInfo,
      colorTextRewardCard: color.palette.goldTierUnitLabelColor
    },
    [Tier.StaffGold]: {
      title: Tier.Gold,
      isStaff: true,
      color: color.palette.goldTier,
      backgroundColor: color.palette.goldTierBackground,
      cardGradientColor: color.palette.goldTierCardGradient,
      smallIcon: CRGoldSmallIcon,
      bfIcon: CRGoldBFIcon,
      iconBgColor: color.palette.goldTierIconBackground,
      iconColor: color.palette.goldTierIcon,
      redeemBtnColors: color.palette.goldTierRedeemBtnGradient,
      dashedLineColor: color.palette.goldTierDashedLine,
      crCardIcon: CRGoldCardQr,
      crCardIconV2: CRGoldCardQrV2,
      unitLabelColor: color.palette.goldTierUnitLabelColor,
      benefitsButtonGradient: color.palette.goldTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundGoldInfo,
      colorTextRewardCard: color.palette.goldTierUnitLabelColor
    },
    [Tier.Platinum]: {
      title: Tier.Platinum,
      isStaff: false,
      color: color.palette.platinumTier,
      backgroundColor: color.palette.platinumTierBackground,
      cardGradientColor: color.palette.platinumTierCardGradient,
      smallIcon: CRPlatinumSmallIcon,
      bfIcon: CRPlatinumBFIcon,
      iconBgColor: color.palette.platinumTierIconBackground,
      iconColor: color.palette.platinumTierIcon,
      redeemBtnColors: color.palette.platinumTierRedeemBtnGradient,
      dashedLineColor: color.palette.platinumTierDashedLine,
      crCardIcon: CRPlatinumCardQr,
      crCardIconV2: CRPlatinumCardQrV2,
      unitLabelColor: color.palette.platinumTierUnitLabelColor,
      benefitsButtonGradient: color.palette.platinumTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundPlantiumInfo,
      colorTextRewardCard: color.palette.darkestGrey
    },
    [Tier.StaffPlatinum]: {
      title: Tier.Platinum,
      isStaff: true,
      color: color.palette.platinumTier,
      backgroundColor: color.palette.platinumTierBackground,
      cardGradientColor: color.palette.platinumTierCardGradient,
      smallIcon: CRPlatinumSmallIcon,
      bfIcon: CRPlatinumBFIcon,
      iconBgColor: color.palette.platinumTierIconBackground,
      iconColor: color.palette.platinumTierIcon,
      redeemBtnColors: color.palette.platinumTierRedeemBtnGradient,
      dashedLineColor: color.palette.platinumTierDashedLine,
      crCardIcon: CRPlatinumCardQr,
      crCardIconV2: CRPlatinumCardQrV2,
      unitLabelColor: color.palette.platinumTierUnitLabelColor,
      benefitsButtonGradient: color.palette.platinumTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundPlantiumInfo,
      colorTextRewardCard: color.palette.darkestGrey
    },
    [Tier.Monarch]: {
      title: Tier.Monarch,
      isStaff: false,
      color: color.palette.monarchTier,
      backgroundColor: color.palette.monarchTierBackground,
      cardGradientColor: color.palette.monarchTierCardGradient,
      smallIcon: CRMonarchSmallIcon,
      bfIcon: CRMonarchBFIcon,
      iconBgColor: color.palette.monarchTierIconBackground,
      iconColor: color.palette.monarchTierIcon,
      redeemBtnColors: color.palette.monarchTierRedeemBtnGradient,
      dashedLineColor: color.palette.monarchTierDashedLine,
      crCardIcon: CRMonarchCardQr,
      crCardIconV2: CRMonarchCardQrV2,
      unitLabelColor: color.palette.monarchTierUnitLabelColor,
      benefitsButtonGradient: color.palette.monarchTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundMonarchInfo,
      colorTextRewardCard: color.palette.darkestGrey
    },
    [Tier.StaffMonarch]: {
      title: Tier.Monarch,
      isStaff: true,
      color: color.palette.monarchTier,
      backgroundColor: color.palette.monarchTierBackground,
      cardGradientColor: color.palette.monarchTierCardGradient,
      smallIcon: CRMonarchSmallIcon,
      bfIcon: CRMonarchBFIcon,
      iconBgColor: color.palette.monarchTierIconBackground,
      iconColor: color.palette.monarchTierIcon,
      redeemBtnColors: color.palette.monarchTierRedeemBtnGradient,
      dashedLineColor: color.palette.monarchTierDashedLine,
      crCardIcon: CRMonarchCardQr,
      crCardIconV2: CRMonarchCardQrV2,
      unitLabelColor: color.palette.monarchTierUnitLabelColor,
      benefitsButtonGradient: color.palette.monarchTierBenefitsButtonGradient,
      backgroundMemberInfoExploreV2: color.palette.backgroundMonarchInfo,
      colorTextRewardCard: color.palette.almostBlackGrey
    },
  }

export const useRewardTier = () => {
    const rewardsFetching = useSelector(ForYouSelectors.rewardsFetching)
    const rewardsData = useSelector(ForYouSelectors.rewardsData)

    const currentTier = useMemo(() => {
      return rewardsData?.reward?.currentTierInfo?.replace(" ", "")
    }, [rewardsData?.reward?.currentTierInfo])

    const memberIconInfo = useMemo(() => {
      if (!currentTier) {
        return MAPPING_COLOR_BY_TIER[Tier.Member]
      }
      return MAPPING_COLOR_BY_TIER[currentTier]
    }, [currentTier])

    const isMonarch = useMemo(() => {
      return currentTier === Tier.Monarch || currentTier === Tier.StaffMonarch
    }, [currentTier])

    return {
      currentTier,
      isMonarch,
      memberIconInfo,
      rewardsFetching,
    }
}