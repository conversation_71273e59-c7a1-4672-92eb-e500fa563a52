import ModalManagerActions, { IModalKey, ModalManagerSelectors } from "app/redux/modalManagerRedux";
import { useDispatch, useSelector } from "react-redux";

export const useModal = (modalKey: keyof IModalKey) => {
    const dispatch = useDispatch()
    const isModalVisible: boolean = useSelector(ModalManagerSelectors[modalKey])

    const openModal = () => {
        dispatch(ModalManagerActions.openModal(modalKey))
    }

    const closeModal = () => {
        dispatch(ModalManagerActions.closeModal(modalKey))
    }

    //flight landing exclusively
    const closeLandingOptionModal = () => {
        dispatch(ModalManagerActions.closeModal("saveFlightTravelOptionLandingdep"))
        dispatch(ModalManagerActions.closeModal("saveFlightTravelOptionLandingArr"))
    }

    return {
        isModalVisible,
        openModal,
        closeModal,
        closeLandingOptionModal
    }
}