import { useIsFocused } from "@react-navigation/native"
import { translate } from "app/i18n/translate"
import { AemSelectors } from "app/redux/aemRedux"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { useEffect } from "react"
import { Alert } from "react-native"
import { useDispatch, useSelector } from "react-redux"

export const useFlightSaveErrorHandling = (shouldOverride?: boolean, skipAlert: boolean = false) => {
  const dispatch = useDispatch()

  const insertFlightPayload = useSelector(MytravelSelectors.insertFlightPayload)
  const removeFlightPayload = useSelector(MytravelSelectors.removeFlightPayload)

  const dataCommonAEM = useSelector(AemSelectors.getAemConfig("AEM_COMMON_DATA"))
  const msg57 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG57")
  const msg59 = dataCommonAEM?.data?.messages?.find((e) => e?.code === "MSG59")

  const isFocused = useIsFocused()

  useEffect(() => {
    if (shouldOverride || skipAlert) {
      return;
    }
    if (insertFlightPayload?.errorFlag || removeFlightPayload?.errorFlag) {
      if (!isFocused) {
        return
      }
      Alert.alert(
        (insertFlightPayload?.errorFlag ? msg57?.title : msg59?.title) ||
        translate("flightLanding.currentlyUnavailable"),
        insertFlightPayload?.errorFlag
          ? msg57?.message || translate("flightLanding.saveFlightError")
          : msg59?.message || translate("flightLanding.removeFlightError"),
        [
          {
            text:
              (insertFlightPayload?.errorFlag ? msg57?.firstButton : msg59?.firstButton) ||
              translate("flightLanding.okay"),
            style: "cancel",
            onPress: () => dispatch(MytravelCreators.flyClearInsertFlightPayload()),
          },
        ],
      )
    }
  }, [insertFlightPayload?.errorFlag, removeFlightPayload?.errorFlag, isFocused, shouldOverride])
}