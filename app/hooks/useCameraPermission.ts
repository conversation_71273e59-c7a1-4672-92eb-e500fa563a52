import { Alert, Platform } from 'react-native'
import { openSettings, PERMISSIONS, request, RESULTS } from 'react-native-permissions'
import { translate } from 'app/i18n'
import { useSelector } from 'react-redux'
import { AemSelectors } from 'app/redux/aemRedux'

const CAMERA_PERMISSION = Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA

/**
 * Hook for camera permission handling with user feedback.
 * 
 * @returns {Object} Object containing handleCameraPermission function
 * 
 * @example
 * ```tsx
 * const { handleCameraPermission } = useCameraPermission()
 * 
 * const onScanPress = () => {
 *   handleCameraPermission(() => navigation.navigate('scanCode'))
 * }
 * ```
 */
export const useCameraPermission = () => {
  const messageCommon = useSelector(AemSelectors.getMessagesCommon)

  const msg61 = messageCommon?.find((e) => e?.code === 'MSG61')
  const msg62 = messageCommon?.find((e) => e?.code === 'MSG62')

  const rationaleMessage = {
    title: msg61?.title || translate('requestPermission.camera.title'),
    message: msg61?.message || translate('requestPermission.camera.message'),
    buttonPositive: msg61?.secondButton || translate('requestPermission.camera.buttonPositive'),
    buttonNegative: msg61?.firstButton || translate('requestPermission.camera.buttonNegative'),
  };
  
  const blockedMessage = {
    title: msg62?.title || translate('scanCode.needAccessPermission.title'),
    message: msg62?.message || translate('scanCode.needAccessPermission.description'),
    firstButton: msg62?.firstButton || translate('scanCode.needAccessPermission.firstButton'),
    secondButton: msg62?.secondButton || translate('scanCode.needAccessPermission.secondButton'),
  }

  const showBlockedPermissionAlert = () => {
    Alert.alert(
      blockedMessage.title,
      blockedMessage.message,
      [
        {
          text: blockedMessage.firstButton,
          style: 'cancel',
          onPress: openSettings,
        },
        {
          text: blockedMessage.secondButton,
          onPress: () => null,
        },
      ],
    )
  }

  const requestCameraPermission = async (rationale) => {
    return await request(CAMERA_PERMISSION, rationale)
  }

  /**
   * Request camera permission and handle result.
   * 
   * @param {Function} onSuccess - Called when permission granted
   */
  const handleCameraPermission = (onSuccess: () => void) => {
    requestCameraPermission(rationaleMessage)
      .then((result) => {
        if (result === RESULTS.BLOCKED || result === RESULTS.DENIED) {
          showBlockedPermissionAlert()
        } else if (result === RESULTS.GRANTED) {
          onSuccess()
        }
      })
  }

  return {
    handleCameraPermission,
  }
} 