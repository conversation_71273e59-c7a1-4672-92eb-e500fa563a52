import { all, call, put, takeLatest, select } from "redux-saga/effects"
import { graphqlOperation } from "aws-amplify"
import { get, isEmpty, uniq, forEach } from 'lodash';
import {
  searchDineShopQuery,
  searchAllQuery,
  searchAllV2Query,
  searchFacilitiesAndServices,
  searchAttractionsQuery,
  searchEventsQuery,
  searchFlightsV2Query,
} from "../models/queries"
import search, { SearchSelectors, SearchTypes, mapTenantFields } from "../redux/searchRedux"
import restApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { convertDataSearchResultV2, convertSearchAllData, convertSearchAllV2Data, getExcludeIdToGetYMAL } from "app/utils/search-data-helper"
import path from "app/services/api/apis.json"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { load, remove, StorageKey } from "app/utils/storage"
import { SearchIndex } from "app/screens/search/tabs/searchIndex"
import { translate } from "app/i18n"
import { simpleCondition } from "app/utils"
import { store } from "app/redux/store"
import { getUriImage } from "app/utils/screen-helper"
import { REMOTE_CONFIG_FLAGS, isFlagON } from "app/services/firebase/remote-config"
import { MAX_AUTOCOMPLETE_ITEMS_V1, MAX_AUTOCOMPLETE_ITEMS_V2, SEARCH_SOURCES } from "app/screens/search-v2/constants"
import { SeachType } from "app/screens/search-v2/tabs/search-tab-flights/consts"

export function* getDineSearchResults(action) {
  const listSearchYouMayAlsoLike = store.getState().searchReducer?.listSearchYouMayAlsoLike
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { pageNumber, pageSize, keyword } = action?.params
  const query = {
    filter: {
      type: "dine",
    },
    page_number: pageNumber,
    page_size: pageSize,
    text: keyword,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchDineShopQuery, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const data = response?.data?.data?.search
    const dineSearchExistingData = store.getState().searchReducer?.dineSearchExistingData
    if (data?.items) {
      const pageNumber = data?.page_number || 0
      const pageSize = data?.page_size || 0
      if ((pageNumber * pageSize >= data?.total || data?.total === 0)) {
        const dineResults =
          data?.page_number === 1
            ? mapTenantFields(data?.items)
            : [...dineSearchExistingData, ...mapTenantFields(data?.items)]
        const params = {
          category: "dine",
          exclude: getExcludeIdToGetYMAL(dineResults),
          text: keyword,
        }
        yield put(search.getYouMayAlsoLikeRequest(params))
      }
      yield put(search.dineSearchSuccess(data))
    } else throw response
  } catch (error) {
    yield put(search.dineSearchFailure(error))
  }
}

export function* getShopSearchResults(action) {
  const listSearchYouMayAlsoLike = store.getState().searchReducer?.listSearchYouMayAlsoLike
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { keyword } = action?.params
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchDineShopQuery, getQueryFields(action)),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response.success || response.errors || response?.data?.data?.search?.items === null) {
      throw response
    }
    const data = response?.data?.data?.search
    const shopSearchExistingData = store.getState().searchReducer?.shopSearchExistingData
    const pageNumber = data?.page_number || 0
    const pageSize = data?.page_size || 0
    if ((pageNumber * pageSize >= data?.total || data?.total === 0)) {
      const shopResults =
        data?.page_number === 1
          ? mapTenantFields(data?.items)
          : [...shopSearchExistingData, ...mapTenantFields(data?.items)]
      const params = {
        category: "shop",
        exclude: getExcludeIdToGetYMAL(shopResults),
        text: keyword,
      }
      yield put(search.getYouMayAlsoLikeRequest(params))
    }
    yield put(search.shopSearchSuccess(data))
  } catch (errors) {
    console.log("errors", errors)
    yield put(search.shopSearchFailure(errors))
  }
}

const getQueryFields = (action) => {
  const { params } = action
  const { keyword, pageNumber } = params
  return {
    text: keyword.trim(),
    page_number: pageNumber,
    filter: {
      type: "shop",
    },
    page_size: 15,
  }
}

export function* handleSearchAllRequest(action) {
  const listSearchYouMayAlsoLike = store.getState().searchReducer?.listSearchYouMayAlsoLike
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { textSearch, savedFlight } = action
  const query = {
    flight_filter: {},
    page_number: 1,
    page_size: 3,
    text: textSearch,
    facilities_page_size: 4,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchAllQuery, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    // CAPP-1122 fix
    // const dataSearch = response?.data?.data
    const dataSearch = response?.data?.data?.search?.groups
    const shopResults = dataSearch?.shops?.items || []
    if (dataSearch) {
      const params = {
        category: "shop",
        exclude: getExcludeIdToGetYMAL(shopResults),
        text: textSearch,
      }
      yield put(search.getYouMayAlsoLikeRequest(params))
      yield put(search.searchAllSuccess(convertSearchAllData(dataSearch, savedFlight, textSearch)))
    } else {
      yield put(search.searchAllFailure())
    }
  } catch (error) {
    yield put(search.searchAllFailure())
  }
}

export function* handleSearchAllV2Request(action) {
  const listSearchYouMayAlsoLike = yield select(SearchSelectors.listSearchYouMayAlsoLike)
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { textSearch } = action
  const query = {
    page_number: 1, // no paging from api
    page_size: 1, // no paging from api
    text: textSearch,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchAllV2Query, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const dataSearch = response?.data?.data?.search_v2
    if (dataSearch) {
      const dataFilter = dataSearch?.items?.filter?.(
        (item) =>
          item.source === SEARCH_SOURCES.TENANTS || item.source === SEARCH_SOURCES.ATTRACTIONS,
      )
      const params = {
        category: "all",
        exclude: getExcludeIdToGetYMAL(dataFilter || []),
        text: textSearch,
      }
      yield put(search.getYouMayAlsoLikeRequest(params))
      yield put(search.searchAllV2Success(convertSearchAllV2Data(dataSearch)))
    } else {
      yield put(search.searchAllV2Failure())
    }
  } catch (error) {
    yield put(search.searchAllV2Failure())
  }
}

export function* airportSearchRequest(action) {
  const listSearchYouMayAlsoLike = store.getState().searchReducer?.listSearchYouMayAlsoLike
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { text, pagingRequest } = action
  const pageSize = 15
  const airportSearchQuery = yield select(SearchSelectors.airportSearchQuery)
  const pageNumber = pagingRequest ? get(airportSearchQuery, "pageNumber", 0) : 0

  const query = {
    pageNumber: pageNumber + 1,
    pageSize,
    text,
    pagingRequest,
  }

  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchFacilitiesAndServices, {
        page_number: pageNumber + 1,
        page_size: pageSize,
        text,
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const airportSearchExistingData = store.getState().searchReducer?.airportSearchPayload?.data
    const data = get(response, "data.data.search")
    const errorsMsg = get(response, "data.errors.0.message")
    if (data && !errorsMsg) {
      const pageNumber = data?.page_number || 0
      const pageSize = data?.page_size || 0
      if ((pageNumber * pageSize >= data?.total || data?.total === 0)) {
        const airportSearchResults =
          pageNumber === 1 ? data?.items : [...airportSearchExistingData, ...data?.items]
        const convertAirportSearchResults = airportSearchResults?.map((item) => ({
          ...item,
          id: item.contentId,
        }))
        const params = {
          category: "facilities",
          exclude: getExcludeIdToGetYMAL(convertAirportSearchResults),
          text,
        }
        yield put(search.getYouMayAlsoLikeRequest(params))
      }
      yield put(search.airportSearchSuccess({ query, data }))
      return
    }
    yield put(search.airportSearchFailure({ query: { ...query, pageNumber }, error: errorsMsg }))
  } catch (error) {
    yield put(search.airportSearchFailure({ query: { ...query, pageNumber }, error }))
  }
}

export function* getAttractionsSearchResults(action) {
  const listSearchYouMayAlsoLike = store.getState().searchReducer?.listSearchYouMayAlsoLike
  if (!isEmpty(listSearchYouMayAlsoLike)) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
  const { pageNumber, pageSize, keyword } = action?.params
  const query = {
    page_number: pageNumber,
    page_size: pageSize,
    text: keyword,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchAttractionsQuery, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const attractionSearchExistingData = store.getState().searchReducer?.attractionsSearchExistingData
    const data = response?.data?.data?.search
    if (data) {
      const pageNumber = data?.page_number || 0
      const pageSize = data?.page_size || 0
      if ((pageNumber * pageSize >= data?.total || data?.total === 0)) {
        const attractionSearchResults =
          pageNumber === 1 ? data?.items : [...attractionSearchExistingData, ...data?.items]
        const params = {
          category: "attractions",
          exclude: getExcludeIdToGetYMAL(attractionSearchResults),
          text: keyword,
        }
        yield put(search.getYouMayAlsoLikeRequest(params))
      }
      yield put(search.attractionsSearchSuccess(data))
    } else throw response
  } catch (error) {
    yield put(search.attractionsSearchFailure(error))
  }
}

export function* getEventsSearchResults(action) {
  const { pageNumber, pageSize, keyword } = action?.params
  const query = {
    page_number: pageNumber,
    page_size: pageSize,
    text: keyword,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchEventsQuery, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const data = response?.data?.data?.search
    if (data) {
      yield put(search.eventsSearchSuccess(data))
    } else throw response
  } catch (error) {
    yield put(search.eventsSearchFailure(error))
  }
}

export function* getPopularSearchKeywords() {
  try {
    const paramsArray = path.getPopularKeySearch.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.AEM_URL + paramsArray[1]
    const response = yield call(restApi, { url, method, parameters: {} })
    if (!response.success || response.errors) {
      throw response
    }
    yield put(search.popularSearchKeywordSuccess(response?.data))
  } catch (error) {
    yield put(search.popularSearchKeywordFailure())
  }
}

const getSearchTabName = (searchIndex: string) => {
  switch (searchIndex) {
    case SearchIndex.all:
      return translate("search.tabTitles.all")
    case SearchIndex.dine:
      return translate("search.tabTitles.dine")
    case SearchIndex.shop:
      return translate("search.tabTitles.shop")
    case SearchIndex.flights:
      return translate("search.tabTitles.flights")
    case SearchIndex.airport:
      return translate("search.tabTitles.airport")
    case SearchIndex.attractions:
      return translate("search.tabTitles.attractions")
    case SearchIndex.events:
      return translate("search.tabTitles.events")
    default:
      return ""
  }
}

const getValueResultSend = (numberResult: number = 0) => {
  const valueResultSend = simpleCondition({
    condition: numberResult === 0,
    ifValue: "No Result",
    elseValue: numberResult,
  })
  return valueResultSend
}

export const standardizeKeywordCollection = (sourceArr, pageType, sourceType) => {
  const UID = store.getState().profileReducer?.profilePayload?.id
  const preHandleSourceArr = uniq(sourceArr.map(item => item.keySearch)).sort(function (a: any, b: any) {
    return a.length - b.length
  })
  function isIncluded(tar, arr) {
    const isExist = arr.find((e) => e.includes(tar))
    if (isExist) return true
    return false
  }
  const finalArr = []
  forEach(preHandleSourceArr, (e, i) => {
    const newArr = [...preHandleSourceArr]
    newArr.splice(i, 1)
    const isExist = isIncluded(e, newArr)
    if (!isExist) {
      finalArr.push(e)
      const getSearchCollectionToSend = sourceArr.findLast(collectionItem => collectionItem.keySearch === e)
      trackAction(AdobeTagName.CAppSearchResult, {
        [AdobeTagName.CAppSearchByCategory]: `${pageType}|${e}`,
      })
      trackAction(AdobeTagName.CAppSearchEvent, {
        [AdobeTagName.CAppSearchEvent]: `${sourceType} | ${getSearchTabName(pageType)} | ${e} | ${getValueResultSend(getSearchCollectionToSend?.numberResult)} | ${UID ? UID : "null"}`,
      })
    }
  })
}

const checkStorage = async () => {
  const keywordCollectionFromStorage = await load(StorageKey.keywordSearchMissingByAppState)
  if (!isEmpty(keywordCollectionFromStorage)) {
    remove(StorageKey.keywordSearchMissingByAppState)
  }
}

export function* sendSearchKeywordCollection(action) {
  checkStorage()
  try {
    const { screenType, sourceType } = action
    const keywordCollection = yield select(SearchSelectors.searchKeywordCollection)
    if (!isEmpty(keywordCollection)) {
      standardizeKeywordCollection(keywordCollection, screenType, sourceType)
    }
    yield put(search.sendSearchKeywordCollectionSuccess())
  } catch (error) {
    yield put(search.sendSearchKeywordCollectionSuccess())
  }
}

export function* handleGetAutoCompleteKeywordRequest(action) {
  const { text, dataType } = action?.params
  const params = {
    text: text,
    data_type: dataType,
  }
  const isSearchV2FlagOn = isFlagON(REMOTE_CONFIG_FLAGS.SEARCHV2_EPIC)
  const MAX_ITEMS = isSearchV2FlagOn ? MAX_AUTOCOMPLETE_ITEMS_V2 : MAX_AUTOCOMPLETE_ITEMS_V1
  try {
    const paramsArray = path.getAutoCompleteKeyword.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.SEARCH_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, { url, method, parameters: params })
    if (!response.success || response.errors) {
      throw response
    }
    const listAutoComplete = response?.data?.list || []
    if (listAutoComplete.length > MAX_ITEMS) {
      yield put(search.getAutoCompleteKeywordSuccess(listAutoComplete.slice(0, MAX_ITEMS)))
    } else {
      yield put(search.getAutoCompleteKeywordSuccess(listAutoComplete))
    }
  } catch (error) {
    yield put(search.getAutoCompleteKeywordFailure())
  }
}

export function* handleGetAutoCompleteFlightRequest(action) {
  const params = {
    text: action?.keyword,
    data_type: Object.values(SeachType).join(","),
  }

  const isSearchV2FlagOn = isFlagON(REMOTE_CONFIG_FLAGS.SEARCHV2_EPIC)
  const MAX_ITEMS = isSearchV2FlagOn ? MAX_AUTOCOMPLETE_ITEMS_V2 : MAX_AUTOCOMPLETE_ITEMS_V1

  try {
    const paramsArray = path.getAutoCompleteKeyword.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.SEARCH_GATEWAY_URL + paramsArray[1]

    const response = yield call(restApi, { url, method, parameters: params })

    if (!response.success || response.errors) {
      throw response
    }
    const listAutoComplete = response?.data?.list || []
    if (listAutoComplete.length > MAX_ITEMS) {
      yield put(search.getAutoCompleteFlightSuccess(listAutoComplete.slice(0, MAX_ITEMS)))
    } else {
      yield put(search.getAutoCompleteFlightSuccess(listAutoComplete))
    }
  } catch (error) {
    yield put(search.getAutoCompleteFlightFailure())
  }
}

export function* handleGetYouMayAlsoYouLikeRequest(action) {
  const { category, exclude, text } = action?.params

  const params = {
    category: category,
    exclude: exclude,
    text: text,
  }

  try {
    const paramsArray = path.getYouMayAlsoLike.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.SEARCH_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, {
      url,
      method,
      parameters: params,
    })
    if (!response.success || response.errors) {
      throw response
    }
    const listSearchYouMayAlsoLike = response?.data?.list || []
    const convertData = listSearchYouMayAlsoLike?.map((item) => {
      return {
        ...item,
        logoImage: getUriImage(item?.logoImage || item?.image),
      }
    })
    yield put(search.getYouMayAlsoLikeSuccess(convertData))
  } catch (error) {
    yield put(search.resetListYouMayAlsoLikeData())
  }
}

export function* handleSearchFlightsV2Request(action) {
  const query = {
    filter: {
      scheduled_date: action.date,
      direction: action.direction,
      terminal: action.terminal,
      airport: action.airport,
      airline: action.airline,
    },
    page_number: 1, // no paging from api
    page_size: 100, // no paging from api
    text: action.keyword,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchFlightsV2Query, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })

    const dataSearch = response?.data?.data?.search_v2
    const errors = response?.data?.data?.errors
    if (dataSearch && !errors) {
      yield put(search.searchFlightsV2Success(convertDataSearchResultV2(dataSearch.items)))
    } else {
      yield put(search.searchFlightsV2Failure())
    }
  } catch (error) {
    yield put(search.searchFlightsV2Failure())
  }
}

export function* searchSaga() {
  yield all([
    takeLatest(SearchTypes.SEARCH_ALL_REQUEST, handleSearchAllRequest),
    takeLatest(SearchTypes.SEARCH_ALL_V2_REQUEST, handleSearchAllV2Request),
    takeLatest(SearchTypes.DINE_SEARCH_REQUEST, getDineSearchResults),
    takeLatest(SearchTypes.DINE_SEARCH_PAGINATE, getDineSearchResults),
    takeLatest(SearchTypes.SHOP_SEARCH_REQUEST, getShopSearchResults),
    takeLatest(SearchTypes.SHOP_SEARCH_PAGINATE, getShopSearchResults),
    takeLatest(SearchTypes.AIRPORT_SEARCH_REQUEST, airportSearchRequest),
    takeLatest(SearchTypes.ATTRACTIONS_SEARCH_REQUEST, getAttractionsSearchResults),
    takeLatest(SearchTypes.ATTRACTIONS_SEARCH_PAGINATE, getAttractionsSearchResults),
    takeLatest(SearchTypes.EVENTS_SEARCH_REQUEST, getEventsSearchResults),
    takeLatest(SearchTypes.EVENTS_SEARCH_PAGINATE, getEventsSearchResults),
    takeLatest(SearchTypes.POPULAR_SEARCH_KEYWORD_REQUEST, getPopularSearchKeywords),
    takeLatest(SearchTypes.SEND_SEARCH_KEYWORD_COLLECTION, sendSearchKeywordCollection),
    takeLatest(SearchTypes.GET_AUTO_COMPLETE_KEYWORD_REQUEST, handleGetAutoCompleteKeywordRequest),
    takeLatest(SearchTypes.GET_AUTO_COMPLETE_FLIGHT_REQUEST, handleGetAutoCompleteFlightRequest),
    takeLatest(SearchTypes.GET_YOU_MAY_ALSO_LIKE_REQUEST, handleGetYouMayAlsoYouLikeRequest),
    takeLatest(SearchTypes.SEARCH_FLIGHTS_V2_REQUEST, handleSearchFlightsV2Request),
  ])
}
