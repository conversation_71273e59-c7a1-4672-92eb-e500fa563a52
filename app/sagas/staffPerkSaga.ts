import { all, call, put, takeLatest } from "redux-saga/effects"
import { graphqlOperation } from "aws-amplify"
import { StaffPerkActions } from "../redux/staffPerkRedux"
import {
  getStaffPerkListingQuery,
  getStaffPerkPromotionDetailQuery,
  getAvailableDiscountStaffPerkQuery,
  getAvailableDiscountDealsPromosQuery,
  getGroupBuyBannersQuery
} from "app/models/queries"
import restApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { isArray } from "validate.js"
import NetInfo from "@react-native-community/netinfo"

export function* getStaffPerkListingRequest(action) {
  const { params } = action
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getStaffPerkListingQuery, { input: params }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response.success || response.errors) {
      throw response
    }
    if (response.statusCode === 200 && response.success) {
      yield put(
        StaffPerkActions.Creators.getStaffPerkListingSuccess(
          response?.data?.data?.getStaffPerksListing,
        ),
      )
    }
  } catch (error) {
    yield put(StaffPerkActions.Creators.getStaffPerkListingFailure())
  }
}

export async function getStaffPerkListingReq(params) {
  try {
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getStaffPerkListingQuery, params),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (response.statusCode === 200 && response.success) {
      if (response?.data?.errors) {
        return {
          errors: response?.data?.errors
        }
      } else {
        return response?.data?.data?.getStaffPerksListing
      }
    }
  } catch (err) {
    console.log('err', err)
  }
}

export function* getStaffPerkListingPaginationRequest(action) {
  const { params } = action
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getStaffPerkListingQuery, { input: params }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response.success || response.errors) {
      throw response
    }
    if (response.statusCode === 200 && response.success) {
      yield put(
        StaffPerkActions.Creators.getStaffPerkListingPaginationSuccess(
          response?.data?.data?.getStaffPerksListing,
        ),
      )
    }
  } catch (error) {
    yield put(StaffPerkActions.Creators.getStaffPerkListingPaginationFailure())
  }
}

export function* getStaffPerkPromotionDetailRequest(action) {
  const { params } = action
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getStaffPerkPromotionDetailQuery, params),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response.success || response.errors) {
      throw response
    }
    if (response.statusCode === 200 && response.success) {
      yield put(
        StaffPerkActions.Creators.getStaffPerkPromotionDetailSuccess(
          response?.data?.data?.getOfferDetails?.promo,
        ),
      )
    }
  } catch (error) {
    yield put(StaffPerkActions.Creators.getStaffPerkPromotionDetailFailure())
  }
}

export async function getStaffPerkAvailableDiscount(params) {
  const { tenantId } = params

  try {
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getAvailableDiscountStaffPerkQuery, { tenant_id: tenantId }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    return response?.data?.data?.getStaffPerksTenantDetails?.promos
  } catch (error) {
    return false
  }
}

export async function getDealsPromosAvailableDiscount(params) {
  const { tenantId } = params
  try {
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getAvailableDiscountDealsPromosQuery, { tenant_id: tenantId }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    return response?.data?.data?.getDealsPromosTenantDetails?.promos
  } catch (error) {
    return false
  }
}

export function* getGroupbuyBanner() {
  const { isConnected } = yield NetInfo.fetch()
  if (isConnected) {
    try {
      const response = yield call(restApi, {
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getGroupBuyBannersQuery),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      if(response?.data?.data?.getGroupBuyBanners === null){
        yield put(StaffPerkActions.Creators.getGroupbuyBannerFailure())
      } else {
        if ((isArray(response?.data?.errors) && response?.data?.errors?.length > 0) || response?.data?.data?.getGroupBuyBanners[0]?.isError) {
          yield put(StaffPerkActions.Creators.getGroupbuyBannerFailure())
        }
        else yield put(StaffPerkActions.Creators.getGroupbuyBannerSuccess(response?.data?.data?.getGroupBuyBanners))
      }
    } catch (error) { }
  } else {
    yield put(StaffPerkActions.Creators.getGroupbuyBannerFailure())
  }
}

export function* staffPerkSaga() {
  yield all([
    takeLatest(StaffPerkActions.Types.GET_STAFF_PERK_LISTING_REQUEST, getStaffPerkListingRequest),
    takeLatest(
      StaffPerkActions.Types.GET_STAFF_PERK_LISTING_PAGINATION_REQUEST,
      getStaffPerkListingPaginationRequest,
    ),
    takeLatest(
      StaffPerkActions.Types.GET_STAFF_PERK_PROMOTION_DETAIL_REQUEST,
      getStaffPerkPromotionDetailRequest,
    ),
    takeLatest(
      StaffPerkActions.Types.GET_GROUPBUY_BANNER_REQUEST,
      getGroupbuyBanner,
    ),
  ])
}
