/**
 * Welcome to the main entry point of the app. In this file, we'll
 * be kicking off our app.
 *
 * Most of this file is boilerplate and you shouldn't need to modify
 * it very often. But take some time to look through and understand
 * what is going on here.
 *
 * The app navigation resides in ./app/navigators, so head over there
 * if you're interested in adding screens and navigators.
 */
import { NavigationContainerRef } from "@react-navigation/native"
import { Amplify } from "aws-amplify"
import moment from "moment"
import React, { useContext, useEffect, useLayoutEffect, useRef, useState } from "react"
import { ActivityIndicator, Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native"
import { initialWindowMetrics, SafeAreaProvider } from "react-native-safe-area-context"
import { RootSiblingParent } from "react-native-root-siblings"
// This puts screens in a native ViewController or Activity. If you want fully native
// stack navigation, use `createNativeStackNavigator` in place of `createStackNavigator`:
// https://github.com/kmagiera/react-native-screens#using-native-stack-navigator
import { enableScreens } from "react-native-screens"
import { Provider } from "react-redux"
import { PersistGate } from "redux-persist/integration/react"
import debugConfig from "./config/debugConfig"
import "./i18n"
import {
  canExit,
  RootNavigator,
  setRootNavigation,
  useBackButtonHandler,
  useNavigationPersistence,
} from "./navigators"
import { persistor, store } from "./redux/store"
import "./utils/ignore-warnings"
import * as storage from "./utils/storage"
import JailMonkey from "jail-monkey"
import RootedDetectionAlert from "app/screens/rooted-detection-alert"
import { processCacheImage } from "./utils/get-configuration-permission"
import path from "app/services/api/apis.json"
import { getEnvSetting } from "app/utils/env-settings"
import restApi from "app/services/api/request"
import DeviceInfo from "react-native-device-info"
import NetInfo from "@react-native-community/netinfo"
import ProfileActions from "app/redux/profileRedux"
import {
  remoteConfigGetAllValue,
  REMOTE_CONFIG_FLAGS,
  rmFetchAndActive,
} from "app/services/firebase/remote-config"
import { initQualtric, USER_ACTION_ENUM } from "./services/survey/qualtrics"
import Reactotron from "app/config/reactotronConfig"
import { FLY_CONTEXT } from "app/services/context/fly"
import SearchScreenContext from "app/screens/search/search-screen-context"
import { AccountContext } from "app/services/context/account"
import { fetchWithTimeout } from "app/utils/fetch"
import { ModalCommonComponent } from "./components/modal-common"
import { translate } from "./i18n"
import Responsive from "./utils/responsive"
import { color, typography } from "./theme"
import { presets } from "./elements/text/text.presets"
import NativeAuthReducer from "app/redux/nativeAuthRedux"
import { dtACtionLogEvent, convertStringValue, dtBizEvent, convertBooleanValue, dtManualActionEvent, FE_LOG_PREFIX, DT_SECURITY_EVENT, DT_SECURITY_EVENT_NAME } from "./services/firebase"
import axios from 'axios';
import { ExploreContext } from "./services/context/explore"
import {
  getAppSettingVersion,
  getAppSettingNeedToRefreshStatus,
  getFirstFetchRemoteConfig,
  setFirstFetchRemoteConfig,
  setFirstSetDeviceIDForBraze,
  getFirstSetDeviceIDForBraze,
} from "app/utils/storage/mmkv-storage"
import { getAppSettingsData, getAuthTokenPayload, removeAuthTokenPayload, setAppSettingsData } from "app/utils/storage/mmkv-encryption-storage"
import { hideSplashScreen } from "./utils/screen-hook"
import { DineShopContext } from "./services/context/dine-shop"
import { DriveContext } from "./services/context/drive"
import { API_FAILURE_KIND } from "./utils/constants"
import { pageConfigurationRender } from "./services/api/page"
import { useAppsFlyer } from "./services/appsflyers"
import Braze from "@braze/react-native-sdk"

const deviceId = DeviceInfo.getUniqueIdSync()
const resetAxiosConfig = () => {
  axios.defaults.headers.common['Cache-Control'] = 'no-cache';
  
  axios.defaults.headers.common['Expires'] = '0';
};
const Settings = getEnvSetting()

const timestamp = moment().unix()

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"
/**
 * This is the root component of our app.
 */
function App(props): React.JSX.Element {
  const navigationRef = useRef<NavigationContainerRef<any>>()
  setRootNavigation(navigationRef)
  useBackButtonHandler(navigationRef, canExit)
  const { initialNavigationState, onNavigationStateChange } = useNavigationPersistence(
    storage,
    NAVIGATION_PERSISTENCE_KEY,
  )
  const [settingData, setSettingData] = useState(null)
  const [isNoConnection, setNoConnection] = useState(false)
  const FLY_CONTEXT_HANDLERS = useContext(FLY_CONTEXT).Handlers
  const SEARCH_CONTEXT_HANDLERS = useContext(SearchScreenContext)
  const AccountContextHandler = useContext(AccountContext)
  const ExploreContextHandler = useContext(ExploreContext)
  const DineShopContextHandler = useContext(DineShopContext)
  const DriveContextHandler = useContext(DriveContext)

  const registerDevicesCountRef = useRef(0)
  const appSettingsCountRef = useRef(0)
  const [isErrorState, setErrorState] = useState(false)
  const [registerDevicesResponse, updateRegisterDevicesResponse] = useState(null)
  const [isHiddenRetry, setHiddenRetry] = useState(false)

  const deviceName = DeviceInfo.getDeviceNameSync()

  if (!__DEV__) {
    const isRootedDevice =
      Platform.OS === "android"
        ? JailMonkey.hookDetected() ||
          JailMonkey.androidRootedDetectionMethods.rootBeer.detectRootManagementApps ||
          JailMonkey.androidRootedDetectionMethods.rootBeer.checkSuExists ||
          JailMonkey.androidRootedDetectionMethods.rootBeer.checkForRootNative ||
          JailMonkey.androidRootedDetectionMethods.rootBeer.checkForMagiskBinary ||
          JailMonkey.androidRootedDetectionMethods.rootBeer.checkForDangerousProps
        : JailMonkey.trustFall()
    if (isRootedDevice) {
      dtBizEvent("AppLaunch",DT_SECURITY_EVENT_NAME.DT_ROOTED_DEVICE_DETECTED,DT_SECURITY_EVENT,{})
      return <RootedDetectionAlert />
    }
  }

  const getRemoteConfigAll = (flags = null) => {
    const getFlags = flags || remoteConfigGetAllValue()
    FLY_CONTEXT_HANDLERS.fly_appscapade_flight_landing =
      getFlags?.[REMOTE_CONFIG_FLAGS.APPSCAPADE_FLIGHTLANDING]?.value
    FLY_CONTEXT_HANDLERS.fly_eci_dynamic_display = getFlags?.[REMOTE_CONFIG_FLAGS.ECI_DYNAMIC_DISPLAY]?.value
    FLY_CONTEXT_HANDLERS.flyDetailP1Flag = getFlags?.[REMOTE_CONFIG_FLAGS.FLIGHT_DETAILS_P1]?.value
    FLY_CONTEXT_HANDLERS.flyDetailsFirstFlag = getFlags?.[REMOTE_CONFIG_FLAGS.PROJECTFIRST_FLIGHTDETAILS]?.value
    FLY_CONTEXT_HANDLERS.projectFirstFlightJourney = getFlags?.[REMOTE_CONFIG_FLAGS.PROJECTFIRST_FLIGHTJOURNEY]?.value
    FLY_CONTEXT_HANDLERS.flySavePrompt = getFlags?.[REMOTE_CONFIG_FLAGS.FLIGHT_SAVEPROMPT]?.value
    SEARCH_CONTEXT_HANDLERS.searchCollectClickStreamFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.COLLECT_CLICKSTREAM]?.value
    AccountContextHandler.accountCM24FeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.ACCOUNT_V2_CM24]?.value
    AccountContextHandler.pointsTxnRetroClaimsFeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.TXNHISTORY_RETROCLAIMS]?.value
    AccountContextHandler.pendingPointsRetroClaims =
      getFlags?.[REMOTE_CONFIG_FLAGS.TXNHISTORY_PENDINGCLAIMS]?.value
    ExploreContextHandler.baggagePredictionFeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.BAGGAGE_PREDICTION_TIMING]?.value
    ExploreContextHandler.exploreJustForYouFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.EXPLORE_JUSTFORYOU]?.value
    ExploreContextHandler.exploreScrollBuddyFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.EXPLORE_SCROLLBUDDY]?.value
    ExploreContextHandler.exploreStaffPerksFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.EXPLORE_STAFFPERKS]?.value
    ExploreContextHandler.staffPerksTilesV2Flag = getFlags?.[REMOTE_CONFIG_FLAGS.STAFFPERKS_TILESV2]?.value
    ExploreContextHandler.exploreScreenV2 = getFlags?.[REMOTE_CONFIG_FLAGS.EXPLORE_V2]?.value
    AccountContextHandler.cm24Flag = getFlags?.[REMOTE_CONFIG_FLAGS.CSM_CM24]?.value
    AccountContextHandler.ciamUnlinkSocial = 
      getFlags?.[REMOTE_CONFIG_FLAGS.CIAM_UNLINKSOCIAL]?.value
    AccountContextHandler.groupBuy = getFlags?.[REMOTE_CONFIG_FLAGS.CSM_GROUPBUY]?.value
    AccountContextHandler.l2AnnouncementFeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.L2_ANNOUNCEMENT]?.value
    AccountContextHandler.notificationInboxV2 = 
      getFlags?.[REMOTE_CONFIG_FLAGS.NOTIFICATION_INBOX_V2]?.value
    AccountContextHandler.ciamSocialLinking = 
      getFlags?.[REMOTE_CONFIG_FLAGS.CIAM_SOCIALLINKING]?.value
    AccountContextHandler.accountContactUsFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.ACCOUNT_CONTACTUS]?.value
    AccountContextHandler.vceaLiveChatFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.VCEA_LIVECHAT]?.value
    AccountContextHandler.notificationPreferencesV2FeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.NOTIFICATION_PREFERENCES_V2]?.value
    AccountContextHandler.ciamBiometrics = getFlags?.[REMOTE_CONFIG_FLAGS.CIAM_BIOMETRICS]?.value
    AccountContextHandler.l1AnnouncementFeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.L1_ANNOUNCEMENT]?.value
    DineShopContextHandler.shopDineEpicFFlag = getFlags?.[REMOTE_CONFIG_FLAGS.SHOPDINE_EPIC]?.value
    DineShopContextHandler.shopDineV2 = getFlags?.[REMOTE_CONFIG_FLAGS.SHOPDINE_V2]?.value
    DineShopContextHandler.shopDineV2JustForYou = getFlags?.[REMOTE_CONFIG_FLAGS.SHOPDINE_V2_JUSTFORYOU]?.value
    AccountContextHandler.enableRoutingMap = getFlags?.[REMOTE_CONFIG_FLAGS.ATOMS_ROUTING]?.value
    AccountContextHandler.enableSearchV2 = getFlags?.[REMOTE_CONFIG_FLAGS.SEARCHV2_EPIC]?.value
    AccountContextHandler.miffyGameFeatureFlag = getFlags?.[REMOTE_CONFIG_FLAGS.MIFFYGAME_ACCOUNT]?.value
    AccountContextHandler.miffygameVPR = getFlags?.[REMOTE_CONFIG_FLAGS.MIFFYGAME_VPR]?.value
    AccountContextHandler.nativeOnboardingScreen = getFlags?.[REMOTE_CONFIG_FLAGS.NATIVE_ONBOARDING_SCREEN]?.value
    AccountContextHandler.accountPromoCodesFF = getFlags?.[REMOTE_CONFIG_FLAGS.ACCOUNT_PROMOCODES]?.value
    AccountContextHandler.flyLandingFeatureFlag = getFlags?.[REMOTE_CONFIG_FLAGS.FLY_LANDING].value
    DriveContextHandler.driveParkingFeatureFlag =
      getFlags?.[REMOTE_CONFIG_FLAGS.DRIVE_PARKING]?.value
    DriveContextHandler.csmCarPassFeatureFlag = getFlags?.[REMOTE_CONFIG_FLAGS.CSM_CARPARSS]?.value
    DriveContextHandler.driveCSAT = getFlags?.[REMOTE_CONFIG_FLAGS.DRIVE_CSAT]?.value
  }

  useLayoutEffect(() => {
    if (!getFirstSetDeviceIDForBraze()) {
      Braze.setCustomUserAttribute('capp_device_id', `${deviceId}`)
      Braze.requestImmediateDataFlush()
      setTimeout(() => {
        setFirstSetDeviceIDForBraze(true)
      }, 0)
    }
  }, [])

  const runRemoteConfig = async () => {
    const fetchRC = await rmFetchAndActive()
    if (fetchRC?.status) {
      getRemoteConfigAll(fetchRC?.remoteData)
    } else {
      getRemoteConfigAll()
    }
  }

  const loadProfile = async () => {
    const { isLoggedIn, isJustSignUp } = store.getState().nativeAuthReducer

    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__loadProfile`)
    dtAction.reportStringValue('isLoggedIn', `${isLoggedIn}`)
    dtAction.reportStringValue('isJustSignUp', `${isJustSignUp}`)
    

    if (isJustSignUp) {
      store.dispatch(NativeAuthReducer.setLogin())
      store.dispatch(ProfileActions.profileRequest())
      dtAction.leaveAction()
      return true
    }

    const authTokenPayload = getAuthTokenPayload()
    dtAction.reportStringValue('log__isAccess', `${!!authTokenPayload?.postMethod?.access_token}`)
    if (isLoggedIn && authTokenPayload?.postMethod?.access_token) {
      dtAction.reportStringValue('log__loadProfile', `isLoggedIn`)
      store.dispatch(ProfileActions.profileRequest())
    } else if (!isLoggedIn && authTokenPayload?.postMethod?.access_token) {
      dtAction.reportStringValue('log__loadProfile', `nonIsLoggedIn`)
      removeAuthTokenPayload()
    } else if (isLoggedIn && !authTokenPayload?.postMethod?.access_token) {
      dtAction.reportStringValue('log__loadProfile', `unauthorized`)
      store.dispatch({
        type: "NATIVE_AUTH_TOKEN_VERIFY_FAILURE",
        kind: API_FAILURE_KIND.UNAUTHORIZED,
      })
    }
    dtAction.leaveAction()
  }

  const checkSurveysStorage = async () => {
    const checkSurveysStorage = await storage.load(storage.StorageKey.checkSurveys)
    if (checkSurveysStorage.currentAction && checkSurveysStorage.list && checkSurveysStorage.list.length) {
      const mainSurvey = checkSurveysStorage.list.find(el => el.action === USER_ACTION_ENUM.E_CARD)
      if (mainSurvey) {
        await storage.save(storage.StorageKey.lasTimeOpenedECard, true)
      }
    }
  }

  const setUpSettings = (appSettingData) => {
    const currentUtcDate = moment().utc().format()
    initQualtric(appSettingData)
    setAppSettingsData({
      settings: appSettingData,
      date: currentUtcDate,
    })
    store.dispatch({
      type: "APP_SETTINGS_SAVING",
      payload: appSettingData,
    })
    // console.log("appSettingsData", appSettingData)
    Amplify.configure({
      aws_appsync_graphqlEndpoint: appSettingData?.APPSYNC_GRAPHQL_URL,
      aws_appsync_region: "ap-southeast-1",
      aws_appsync_authenticationType: "API_KEY",
      aws_appsync_apiKey: appSettingData?.APPSYNC_GRAPHQL_API_KEY,
    })
    setTimeout(() => {
      setSettingData(appSettingData)
      loadProfile()
      checkSurveysStorage()
      pageConfigurationRender(appSettingData)
    }, 0)
  }

  const registerDevices = async () => {
    const registerDevicesParamsArray = path.postRegisterDevices.split(" ")
    const registerDevicesMethod = registerDevicesParamsArray[0] || "GET"
    const registerDevicesUrl = Settings.API_GATEWAY_URL + registerDevicesParamsArray[1]
    try {
      const response = await restApi({
        url: registerDevicesUrl,
        method: registerDevicesMethod,
        headers: {
          "x-api-key": Settings.X_API_KEY,
        },
        data: {
          deviceId,
          platform: Platform.OS.toUpperCase(),
          timestamp,
        },
        timeout: 3000,
      })
      if (response?.success) {
        updateRegisterDevicesResponse(response?.data)
      } else {
        if (registerDevicesCountRef.current >= 2) {
          setErrorState(true)
          setHiddenRetry(false)
          dtACtionLogEvent("RegisterDevices_Failed", {
            device_name: convertStringValue(deviceName),
            device_id: deviceId,
            status_code: convertStringValue(response?.statusCode),
            message: convertStringValue(response?.message)
          })
          dtBizEvent("AppLaunch","DeviceRegistration","App-Event",{            
            status_code: convertStringValue(response?.statusCode),
            message: convertStringValue(response?.message)})
        } else {
          registerDevicesCountRef.current += 1
          registerDevices()
        }
      }
    } catch (error) {
      dtACtionLogEvent(`RegisterDevices_${error?.message}`, {
        device_name: convertStringValue(deviceName),
        device_id: deviceId,
        status_code: convertStringValue(error?.statusCode),
        message: convertStringValue(error?.message),
        error_code: convertStringValue(error?.errorCode),
        retry_count: convertStringValue(registerDevicesCountRef?.current)
      })
      dtBizEvent("AppLaunch","DeviceRegistration","App-Event",{
        status_code: convertStringValue(error?.statusCode),
        message: convertStringValue(error?.message),
        error_code: convertStringValue(error?.errorCode),
        retry_count: convertStringValue(registerDevicesCountRef?.current)})
      if (registerDevicesCountRef.current >= 2) {
        resetAxiosConfig()
        setHiddenRetry(false)
        setErrorState(true)
      } else {
        registerDevicesCountRef.current += 1
        registerDevices()
      }
    }
  }

  const callAppSettings = async (device_signature) => {
    const appSettingsArray = path.postAppSetting.split(" ")
    const appSettingsMethod = appSettingsArray[0] || "GET"
    const appSettingsUrl = Settings.API_GATEWAY_URL + appSettingsArray[1]

    try {
      const response = await restApi({
        url: appSettingsUrl,
        method: appSettingsMethod,
        headers: {
          "x-api-key": Settings.X_API_KEY,
        },
        data: {
          deviceId,
          deviceSignature: device_signature,
          timestamp,
        },
        timeout: 3000,
      })
      if (response?.success) {
        setHiddenRetry(false)
        setErrorState(false)
        setUpSettings(response?.data)
      } else {
        if (appSettingsCountRef.current >= 2) {
          setHiddenRetry(false)
          setErrorState(true)
          dtACtionLogEvent("AppSettings_Failed", {
            device_name: convertStringValue(deviceName),
            device_id: deviceId,
            status_code: convertStringValue(response?.statusCode),
            message: convertStringValue(response?.message)
          })
          dtBizEvent("AppLaunch","AppSettings","App-Event",{
            status_code: convertStringValue(response?.statusCode),
            message: convertStringValue(response?.message)})
        } else {
          appSettingsCountRef.current += 1
          callAppSettings(device_signature)
        }
      }
    } catch (error) {
      dtACtionLogEvent(`AppSettings_${error?.message}`, {
        device_name: convertStringValue(deviceName),
        device_id: deviceId,
        status_code: convertStringValue(error?.statusCode),
        message: convertStringValue(error?.message),
        error_code: convertStringValue(error?.errorCode),
        retry_count: convertStringValue(appSettingsCountRef?.current)
      })
      dtBizEvent("AppLaunch","AppSettings","App-Event",{
        status_code: convertStringValue(error?.statusCode),
        message: convertStringValue(error?.message),
        error_code: convertStringValue(error?.errorCode),
        retry_count: convertStringValue(appSettingsCountRef?.current)
    })
      if (appSettingsCountRef.current >= 2) {
        resetAxiosConfig()
        setHiddenRetry(false)
        setErrorState(true)
      } else {
        appSettingsCountRef.current += 1
        callAppSettings(device_signature)
      }
    }
  }

  const loadConfig = () => {
    const currentAppSettingsVersion = getAppSettingVersion()
    const currentNeedToRefreshStatus = getAppSettingNeedToRefreshStatus()
    const appSettingsData = getAppSettingsData()
    if (
      typeof currentNeedToRefreshStatus !== "boolean" ||
      !!currentNeedToRefreshStatus ||
      !currentAppSettingsVersion ||
      !appSettingsData ||
      !appSettingsData?.settings
    ) {
      // calling API
      dtACtionLogEvent(`LoadingConfig_NeedToCallAPI`, {
        device_name: convertStringValue(deviceName),
        device_id: DeviceInfo.getUniqueIdSync(),
        currentNeedToRefreshStatus: convertBooleanValue(currentNeedToRefreshStatus),
        appSettingsDataStatus: convertBooleanValue(!!appSettingsData?.settings),
        currentAppSettingsVersion: convertStringValue(currentAppSettingsVersion),
      })
      registerDevices()
    } else {
      // use cache instead of calling API
      dtACtionLogEvent(`LoadingConfig_UseCacheData`, {
        device_name: convertStringValue(deviceName),
        device_id: DeviceInfo.getUniqueIdSync(),
        currentAppSettingsVersion: convertStringValue(currentAppSettingsVersion),
      })
      setUpSettings(appSettingsData.settings)
    }
  }
  
  useAppsFlyer()

  const loadAppSettingStorage = () => {
    const appSettingsData = getAppSettingsData()
    // console.log("Lost Connected - App Setting in Storage", appSettingStorage)
    if (appSettingsData) {
      setUpSettings(appSettingsData?.settings)
      loadProfile()
    } else {
      setNoConnection(true)
      dtACtionLogEvent(`NoNetwork_FirstOpenApp_NoAppSettingData`, {
        device_name: convertStringValue(deviceName),
        device_id: DeviceInfo.getUniqueIdSync(),
      })
      dtBizEvent("AppLaunch","NoNetwork_FirstOpenApp_NoAppSettingData","App-Event",{})
      resetAxiosConfig()
    }
  }

  const getAppSetting = async () => {
    if (Platform.OS === "ios") {
      enableScreens(false)
    }
    try {
      const { isConnected }: any = await fetchWithTimeout(NetInfo.fetch(), 5000);
      if (!isConnected) {
        loadAppSettingStorage()
      } else {
        loadConfig()
      }
    } catch (error) {
      console.log("ERROR__getAppSetting", error?.message)
      loadAppSettingStorage()
    }
  }
  
  const awaitFirstFetchRemoteConfig = async () => {
    await runRemoteConfig()
    setFirstFetchRemoteConfig(true)
    getAppSetting()
  }

  useEffect(() => {
    if(!getFirstFetchRemoteConfig()){
      awaitFirstFetchRemoteConfig()
    } else {
      runRemoteConfig()
      getAppSetting()
    }
    processCacheImage()
  }, [])

  useEffect(() => {
    if (registerDevicesResponse?.device_signature) {
      callAppSettings(registerDevicesResponse?.device_signature)
    }
  }, [registerDevicesResponse?.device_signature])

  const hiddenSplashScreen = () => {
    hideSplashScreen()
    return <></>
  }

  const retry = async () => {
    if(!getFirstFetchRemoteConfig()){
      await runRemoteConfig()
    } else {
      runRemoteConfig()
    }
    setHiddenRetry(true)
    loadConfig()
  }

  if ((isNoConnection && !settingData) || isErrorState) {
    return (
      <View style={styles.screenContainer}>
        {hiddenSplashScreen()}
        <ModalCommonComponent
          visible={(isNoConnection && !settingData) || (isErrorState && !isHiddenRetry)}
        >
          <View style={styles.parentContainer}>
            <View style={styles.descriptionContainer}>
              <Text style={styles.titleStyles} numberOfLines={2}>
                {translate("appLoadingError.title")}
              </Text>
              <Text style={styles.contentTextStyles} numberOfLines={5}>
                {translate("appLoadingError.description")}
              </Text>
            </View>
            <View style={styles.lineStyles} />
            <View style={styles.bottomContainer}>
              <TouchableOpacity style={styles.buttonStyle} onPress={retry}>
                <Text style={styles.textBluStyles}>{translate("common.retry")}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ModalCommonComponent>
      </View>
    )
  }

  // Before we show the app, we have to wait for our state to be ready.
  // In the meantime, don't render anything. This will be the background
  // color set in native by rootView's background color. You can replace
  // with your own loading component if you wish.
  // if (!rootStore) return null

  // otherwise, we're ready to render the app
  return (
    <RootSiblingParent>
      <Provider store={store}>
        <PersistGate loading={<ActivityIndicator />} persistor={persistor}>
          <SafeAreaProvider initialMetrics={initialWindowMetrics}>
            {settingData && (
              <RootNavigator
                ref={navigationRef}
                initialState={initialNavigationState}
                onStateChange={onNavigationStateChange}
                onReady={() => {
                  hideSplashScreen()
                }}
              />
            )}
          </SafeAreaProvider>
        </PersistGate>
      </Provider>
    </RootSiblingParent>
  )
}

export default debugConfig.useReactotron ? Reactotron.overlay(App) : App

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: color.palette.lightestGrey, flex: 1
  },
  parentContainer: {
    backgroundColor: color.palette.backgroundCommonPopup,
    borderRadius: 14,
    height: Responsive.getFontSize(160),
    width: Responsive.getFontSize(270),
  },
  descriptionContainer: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 16,
  },
  titleStyles: {
    ...presets.bold,
    textAlignVertical: "top",
    fontStyle: "normal",
    letterSpacing: -0.41,
    color: color.palette.black,
    fontSize: Responsive.getFontSize(17),
    lineHeight: Responsive.getFontSize(22),
    marginBottom: 5,
  },
  contentTextStyles: {
    ...presets.caption1Regular,
    fontStyle: "normal",
    textAlignVertical: "top",
    color: color.palette.almostBlackGrey,
    textAlign: "center",
  },
  lineStyles: {
    backgroundColor: color.palette.lineColorCommonPopup,
    height: 0.5,
    width: "100%",
  },
  bottomContainer: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-evenly",
  },
  buttonStyle: {
    alignItems: "center",
    height: 44,
    justifyContent: "center",
    margin: "auto",
    width: "100%"
  },
  textBluStyles: {
    color: "#007AFF",
    fontSize: Responsive.getFontSize(17),
    letterSpacing: -0.41,
    lineHeight: Responsive.getFontSize(22),
    fontFamily: typography.regular,
  },
})
