import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme"
import { useMemo } from "react"
import { StyleSheet, TextStyle, View, ViewStyle } from "react-native"
import { MultimediaTouchableOpacity } from "../multimedia-touchable-opacity/multimedia-touchable-opacity"
import { Cross } from "ichangi-fe/assets/icons"
import { handleCondition, ifAllTrue } from "app/utils"

interface PropsType {
  accessibilityLabel?: string
  active?: boolean
  containerStyle?: ViewStyle
  disabled?: boolean
  IconComponent?: any
  iconSize?: number
  label: string
  labelColor?: string
  labelStyle?: TextStyle
  onClearSelection?: () => void
  onPress?: () => void
  outlineColor?: string
  rightIconComponent?: any
  size?: "sm" | "md" | "lg"
  testID?: string
  variant?: "solid" | "outline"
  showDot?: boolean
}

const FilterPill = (props: PropsType) => {
  const {
    accessibilityLabel,
    active,
    containerStyle,
    disabled,
    IconComponent,
    label,
    labelColor,
    labelStyle,
    onClearSelection,
    onPress,
    outlineColor,
    rightIconComponent,
    size = "md",
    testID,
    variant = "solid",
    showDot,
  } = props
  const iconSize = props?.iconSize || 12

  const styleConfig = useMemo(() => {
    const container: any[] = [styles.commonContainerStyle]
    const label: any[] = []
    if (variant === "outline") {
      container.push(styles.outlineContainerStyle)
    }
    switch (size) {
      case "lg":
        container.push(styles.lgContainerStyle)
        label.push(styles.labelLgTextStyle)
        break
      case "sm":
        container.push(styles.smContainerStyle)
        label.push(styles.labelSmTextStyle)
        break
      case "md":
      default:
        container.push(styles.mdContainerStyle)
        label.push(styles.labelMdTextStyle)
        break
    }
    if (active) {
      label.push(styles.activeLabelTextStyle)
      if (variant === "outline") {
        container.push(styles.activeOutlineContainerStyle)
      } else {
        container.push(styles.activeContainerStyle)
      }
    }
    if (containerStyle) {
      container.push(containerStyle)
    }
    if (ifAllTrue([variant === "outline", !active, outlineColor])) {
      container.push({ borderColor: outlineColor })
    }
    if (ifAllTrue([!active, labelColor])) {
      label.push({ color: labelColor })
    }
    if (labelStyle) {
      label.push(labelStyle)
    }

    return {
      container,
      label,
    }
  }, [active, containerStyle, size, variant])

  const shouldShowCloseOrDot = ifAllTrue([active, !rightIconComponent])
  const showCloseButton = ifAllTrue([shouldShowCloseOrDot, !showDot])

  const handlePressCloseBtn = () => {
    if (onClearSelection) {
      onClearSelection()
    } else {
      onPress?.()
    }
  }

  return (
    <MultimediaTouchableOpacity
      accessibilityLabel={accessibilityLabel}
      disabled={disabled}
      onPress={onPress}
      style={[styleConfig.container, showCloseButton && styles.containerWithPaddingStyle]}
      testID={testID}
    >
      {IconComponent && (
        <IconComponent
          color={handleCondition(active, color.palette.lightPurple, color.palette.darkestGrey)}
          height={iconSize}
          width={iconSize}
        />
      )}
      <Text style={styleConfig.label} text={label} />
      {showCloseButton && (
        <MultimediaTouchableOpacity onPress={handlePressCloseBtn} style={styles.closeBtnStyle}>
          <Cross
            color={color.palette.lighterPurple}
            height={20}
            style={{ marginRight: -6 }}
            width={20}
          />
        </MultimediaTouchableOpacity>
      )}
      {ifAllTrue([shouldShowCloseOrDot, showDot]) && <View style={styles.dotStyle} />}
      {handleCondition(rightIconComponent, rightIconComponent, null)}
    </MultimediaTouchableOpacity>
  )
}

const styles = StyleSheet.create({
  commonContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.halfLighterGrey,
    flexDirection: "row",
    gap: 2,
  },
  smContainerStyle: {
    borderRadius: 14,
    height: 28,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  mdContainerStyle: {
    borderRadius: 16,
    height: 32,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  lgContainerStyle: {
    borderRadius: 21,
    height: 42,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  activeContainerStyle: {
    backgroundColor: color.palette.lightestPurple,
  },
  outlineContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    borderColor: color.palette.greyCCCCCC,
    borderWidth: 1,
  },
  activeOutlineContainerStyle: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
    borderWidth: 1,
  },
  labelSmTextStyle: {
    ...newPresets.XSmallBold,
    color: color.palette.darkestGrey,
    textTransform: "none",
  },
  labelMdTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
  },
  labelLgTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
  },
  activeLabelTextStyle: {
    color: color.palette.lightPurple,
  },
  containerWithPaddingStyle: {
    paddingRight: 25,
  },
  closeBtnStyle: {
    position: "absolute",
    right: 15,
    width: 10,
  },
  dotStyle: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: color.palette.lightPurple,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    position: "absolute",
    right: 0,
    top: 0,
  },
})

export default FilterPill
