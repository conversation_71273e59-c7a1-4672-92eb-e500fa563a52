import React, { useEffect, useRef, useState } from "react"
import { Text } from "app/elements/text"
import { View, StyleSheet, TouchableOpacity, Animated, Easing, TextStyle } from "react-native"
import Collapsible from "react-native-collapsible"
import { ArrowDown } from "ichangi-fe/assets/icons"
import { color } from "app/theme"

export interface CollapsibleComponentProps {
  title?: string
  content?: any
  defaultCollapse?: boolean
  customStyleTitle?: TextStyle
}
const CollapsibleComponent = ({
  title,
  content,
  defaultCollapse = true,
  customStyleTitle
}: CollapsibleComponentProps) => {
  const [collapsed, setCollapse] = useState(defaultCollapse)
  const animatedController = useRef(new Animated.Value(0)).current
  const arrowAngle = animatedController.interpolate({
    inputRange: [0, 1],
    outputRange: ["0rad", `${Math.PI}rad`],
  })
  useEffect(() => {
    if (collapsed) {
      Animated.timing(animatedController, {
        duration: 300,
        toValue: 0,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start()
    } else {
      Animated.timing(animatedController, {
        duration: 300,
        toValue: 1,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start()
    }
  }, [collapsed])
  const toogleCollapse = () => {
    setCollapse(!collapsed)
  }

  return (
    <View style={styles.wrapItemCollapse}>
      <TouchableOpacity onPress={() => toogleCollapse()} activeOpacity={0.5}>
        <View style={styles.titleContainer}>
          <Text preset="h4" text={title} style={[styles.title, customStyleTitle]} />
          <Animated.View style={{ transform: [{ rotateZ: arrowAngle }] }}>
            <ArrowDown color={color.palette.lightPurple} />
          </Animated.View>
        </View>
      </TouchableOpacity>
      <Collapsible collapsed={collapsed}>
        <View style={styles.contentCollapse}>{content}</View>
      </Collapsible>
    </View>
  )
}

const styles = StyleSheet.create({
  contentCollapse: {},
  title: {
    width: "90%",
  },
  titleContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  wrapItemCollapse: {
    paddingVertical: 5,
  },
})
export default CollapsibleComponent
