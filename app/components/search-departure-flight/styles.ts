import { StyleSheet } from "react-native"
import { newPresets } from "app/elements/text"
import { color } from "app/theme"
import { palette } from "app/theme/palette"

const styles = StyleSheet.create({
  container: {
    height: 749,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
    backgroundColor: color.palette.whiteGrey,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    height: 64,
    position: "relative",
    width: "100%",
    backgroundColor: color.palette.transparent,
  },
  headerTitle: {
    ...newPresets.bodyTextBold,
    color: palette.almostBlackGrey,
    height: 22,
  },
  headerIcon: {
    position: "absolute",
    right: 20,
  },
  contentContainer: {
    backgroundColor: color.palette.almostWhiteGrey,
    flex: 1,
    width: "100%",
  },
  searchBar: {
    backgroundColor: color.palette.almostWhiteGrey,
    marginTop: 0,
    paddingLeft: 20,
    paddingRight: 20,
  },
  searchInput: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  autocompleteContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
})

export default styles
