import { StatusBar, ViewStyle } from "react-native"
import { BasicAuthCredential } from "react-native-webview/lib/WebViewTypes"

type MediaCapturePermissionGrantType =
  | 'grantIfSameHostElsePrompt'
  | 'grantIfSameHostElseDeny'
  | 'deny'
  | 'grant'
  | 'prompt'

export interface WebViewComponentProps {
  route: {
    params: {
      uri: string
      basicAuthCredential: BasicAuthCredential
      screen: string
      fromFAQ?: boolean
      showFooter?: boolean
      isAdvisoriesRightHeader?: boolean
      useForward?: boolean
      useClose?: boolean
      advisoriesNoticationId?: string
      noNeedDecode?: boolean
      useAATracking?: boolean
      onGoBack?: void
      headerContainerStyle?: ViewStyle
      statusBarTranslucent?: boolean
      originWhitelist?: string[]
      allowsInlineMediaPlayback?: boolean
      mediaPlaybackRequiresUserAction?: boolean
      mediaCapturePermissionGrantType?: MediaCapturePermissionGrantType
      refreshFlightDetails?: (refreshAllData?: boolean) => void
      showHeaderLeftButton?: boolean
    }
  }
  navigation: { addListener: any }
}

export const headerHeightAndroid = 100 - StatusBar.currentHeight
export const headerHeightIOS = 100 - 44
export const footerHeightIOS = 83
export const footerHeightAndroid = 48
export const marginTopIOS = 44
