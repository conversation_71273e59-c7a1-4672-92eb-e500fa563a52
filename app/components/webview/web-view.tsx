import React, { Fragment, useCallback, useEffect, useRef, useState } from "react"
import { View, Animated, StatusBar, SafeAreaView, ViewStyle, Platform, TouchableOpacity, BackHandler, Linking } from "react-native"
import { WebView } from "react-native-webview"
import { StackActions, useFocusEffect, useNavigation } from "@react-navigation/native"
import { useSelector, useDispatch } from "react-redux"
import NetInfo from "@react-native-community/netinfo"
import { color } from "app/theme/color"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { ProfileSelectors } from "app/redux/profileRedux"
import {
  ArrowLeft,
  ArrowRight,
  ArrowRightDisabled,
  CrossBlue,
  ShareBlue,
  Delete,
} from "ichangi-fe/assets/icons"
import { Text } from "app/elements/text"
import * as Progress from "react-native-progress"
import { WebViewComponentProps } from "./web-view-props"
import * as styles from "./web-view-styles"
import sha256 from "crypto-js/sha256"
import { ErrorOverlayNoConnection } from "../error-overlay/error-overlay-no-connection"
import AirportLandingCreator, { AirportLandingSelectors } from "app/redux/airportLandingRedux"
import { FlyCreators } from "app/redux/flyRedux"
import { isEmpty, get } from "lodash"
import { NavigationConstants, TrackingScreenName, SOURCE_SYSTEM, GAMI_DEEPLINKS } from "app/utils/constants"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import { ErrorOverlay } from "../error-overlay"
import {
  getCurrentScreenActive,
  getPreviousScreen,
  setPreviousScreen,
  useCurrentScreenActiveAndPreviousScreenHook,
  useGeneratePlayPassUrl,
} from "app/utils/screen-hook"
import { commonTrackingScreen, trackAction, AdobeTagName } from "app/services/adobe"
import { env } from "app/config/env-params"
import notificationAction from "app/redux/notificationRedux"
import { handleCondition, parseJsonWebviewMessage } from "app/utils"
import { useHandleNavigation } from "app/utils/navigation-helper"
import { BottomSheetMapUnavailable } from "../bottom-sheet-map-unavailable/bottom-sheet-map-unavailable"
import { REMOTE_CONFIG_FLAGS, isFlagON } from "app/services/firebase/remote-config"
import { PlayPassEntryPoint } from "app/redux/types/explore/explore-item-type"
import { sendAATrackingNotification } from "app/screens/notifications"
import urlParse from "url-parse"
import { MODE_CODE } from "app/helpers/deeplink/deeplink-parameter"
import useL3BaggagePrediction from "app/hooks/useL3BaggagePrediction"
import { getEnvSetting } from "app/utils/env-settings"

const SCREEN_NAME = "Webview__"
const AnimatedWebView = Animated.createAnimatedComponent(WebView)

enum L3NavigationType {
  inApp = "in-app",
  external = "external",
  deepLink = "deep-link",
}

const WebViewComponent = (props: WebViewComponentProps) => {
  const {
    uri,
    basicAuthCredential,
    screen,
    fromFAQ = false,
    isAdvisoriesRightHeader,
    advisoriesNoticationId,
    useForward = true,
    useClose = true,
    useAATracking,
    onGoBack = undefined,
    headerContainerStyle,
    statusBarTranslucent = Platform.OS === 'android' ? true : false,
    originWhitelist,
    mediaPlaybackRequiresUserAction,
    mediaCapturePermissionGrantType,
    allowsInlineMediaPlayback = false,
    refreshFlightDetails,
    showHeaderLeftButton = true,
  } = props?.route?.params || {}
  const navigation = useNavigation()
  const dispatch = useDispatch()
  const webViewRef = useRef(null)
  const isL3Page = useRef(false)
  const mapUnavailable = useRef(null)
  const [progress, setProgress] = useState(0)
  const [isLoaded, setLoaded] = useState(false)
  const [canGoBack, setCanGoBack] = useState(false)
  const [canGoForward, setCanGoForward] = useState(false)
  const [isLoggedIn, setLoggedIn] = useState(false)
  const [title, setTitle] = useState("")
  const [isConnection, setConnection] = useState(true)
  const FAQparams = useRef(null)
  const { getCommonLoginModule } = useGeneratePlayPassUrl("FAQupload")
  const isTokenInvalid = useSelector(AuthSelectors.isTokenInvalid)
  const isLogin = useSelector(AuthSelectors.isLoggedIn)
  const changePasswordUrl = useSelector(ProfileSelectors.changePasswordUrl)
  const errorCallAPI = useSelector(AirportLandingSelectors.getCarParkUrlError)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  navigation && navigation?.setOptions({ headerShown: false })
  const { handleNavigation } = useHandleNavigation("WEB_VIEW")
  const [mapRMFlag, setMapRMFlag] = useState(false)
  const refRetryAction = useRef(null)
  const playPassUrlPayload = useSelector(AirportLandingSelectors.getPlayPassUrlPayload("FAQupload"))
  const retryOpenFQAData = useRef(null)
  const { handleOpenL3BaggagePrediction } = useL3BaggagePrediction()

  useEffect(() => {
    dispatch(AirportLandingCreator.clearChangiGameUrl())
    dispatch(notificationAction.forceClosePopupUserNotifications(true))
  }, [])

  useEffect(() => {
    return () => {
      if (isL3Page.current) {
        setPreviousScreen(getCurrentScreenActive())
      }
      isL3Page.current = false
    }
  }, [])

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      trackAction(AdobeTagName.common, {
        [AdobeTagName.cagPagename]: "AppWide_WebView",
      })
    })
    return unsubscribeFocus
  }, [navigation])

  useEffect(() => {
    // Token is invalid & in webview change password -> go back
    if (isTokenInvalid && uri === changePasswordUrl) {
      navigation.canGoBack() && navigation.goBack()
    }
  }, [isTokenInvalid, uri, changePasswordUrl])

  useEffect(() => {
    const checkConnection = async () => {
      const { isConnected } = await NetInfo.fetch()
      setConnection(isConnected)
    }
    checkConnection()
  }, [navigation])

  useEffect(() => {
    const type = get(FAQparams.current, "type", "")
    const tokenType = get(FAQparams.current, "tokenType", "")
    const stateCode = get(FAQparams.current, "stateCode", "")
    const navigationValue = get(FAQparams.current, "link", "")
    const redirect = get(FAQparams.current, "redirect", "")
    
    if (fromFAQ && isLoggedIn && !isEmpty(profilePayload)) {
      if (type?.toLowerCase() === NavigationTypeEnum.inApp) {
        handleActionNavigate(type, navigationValue, redirect)
        FAQparams.current = null
        return
      }
      if (tokenType && stateCode) {
        const hashEmail = sha256(profilePayload?.email?.toLocaleLowerCase())?.toString()
        const params = `__${tokenType}__${hashEmail}`
        const cookiesData = {
          entryPoint: PlayPassEntryPoint.FAQ_UPDATE_RECEIPT,
          tokenType: tokenType,
        }
        getCommonLoginModule(stateCode, params, cookiesData)
        FAQparams.current = null
      }
    }
  }, [fromFAQ, isLoggedIn, profilePayload, getCommonLoginModule])

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const isInitWebviewUrl = (url) => {
    if (!isEmpty(url) && !isEmpty(uri)) {
      const newUrl =
        url?.lastIndexOf("/") + 1 === url?.length ? url?.slice(0, url?.lastIndexOf("/")) : url
      const newUri =
        uri?.lastIndexOf("/") + 1 === uri?.length ? uri?.slice(0, uri?.lastIndexOf("/")) : uri
      if (newUri === newUrl) {
        return true
      }
      return false
    }
    return false
  }

  const onNavigationStateChange = (navState) => {
    if (navState?.title && useAATracking && screen === "notificationScreen") {
      sendAATrackingNotification(useAATracking, navState?.title)
      navigation.setParams({ useAATracking: null })
    }
    setTitle(navState?.title)
    if (isInitWebviewUrl(navState?.url)) {
      setCanGoBack(false)
    }
    setCanGoForward(navState?.canGoForward)
    setCanGoBack(navState?.canGoBack)
  }

  const onBack = () => {
    webViewRef.current?.goBack()
  }

  const onBackExitWebview = () => {
    if (onGoBack) {
      onGoBack(navigation)
    } else {
      if (refreshFlightDetails) {
        refreshFlightDetails(true);
      }
      navigation.goBack()
    }
  }

  const onForward = () => {
    webViewRef.current?.goForward()
  }

  const handleActionNavigate = (navigationType, navigationValue, redirect) => {
    if (!navigationType || !navigationValue) return
    const navType = navigationType?.toLowerCase()
    handleNavigation(navType, navigationValue, redirect)
  }

  const handleMessageFromFAQ = (dataTrigger) => {
    const dataTriggerType = dataTrigger?.type?.toLowerCase()
    const dataTriggerLink = dataTrigger?.link
    const redirect = dataTrigger?.redirect
    const sourceSystem =
      dataTriggerType === NavigationTypeEnum.inApp && dataTriggerLink
        ? SOURCE_SYSTEM.ANYTHING_IN_APP
        : SOURCE_SYSTEM.PLAY_PASS
    if (isEmpty(profilePayload) || !isLogin) {
      FAQparams.current = dataTrigger
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem,
        callBackAfterLoginSuccess: () => setLoggedIn(true),
        callBackAfterLoginCancel: () => {
          FAQparams.current = null
        },
      })
      return
    }
    if (dataTriggerType && dataTriggerType === NavigationTypeEnum.inApp) {
      handleActionNavigate(dataTriggerType, dataTriggerLink, redirect)
      return
    }

    if (dataTrigger?.stateCode && dataTrigger?.tokenType) {
      const hashEmail = sha256(profilePayload?.email?.toLocaleLowerCase())?.toString()
      const params = `__${dataTrigger?.tokenType}__${hashEmail}`
      const cookiesData = {
        entryPoint: "FAQ_UploadReceipt",
        tokenType: dataTrigger.tokenType,
      }
      getCommonLoginModule(dataTrigger?.stateCode, params, cookiesData)
      return
    }
    if (dataTriggerType && dataTriggerLink) {
      handleNavigation(dataTriggerType, dataTriggerLink, redirect)
    }
  }

  const handleMessageNotificationScreen = (jsonData) => {
    if (isEmpty(jsonData)) {
      return
    }
    if (jsonData?.type && jsonData?.link) {
      const redirect = jsonData?.redirect
      handleL3NavigationType(jsonData?.type, jsonData?.link, redirect)
      return null
    }
  }

  const goToNewUri = (newUri) => {
    const script = `window.location.href = "${newUri}";`
    webViewRef.current.injectJavaScript(script)
  }

  const navigationHandler = (newRequest) => {
    const { url = "" } = newRequest || {}
    if (newRequest?.url.startsWith("http://")) {
      const newUri = newRequest?.url.replace(/^http:\/\//i, "https://")
      const redirectTo = 'window.location = "' + newUri + '"'
      webViewRef.current?.injectJavaScript(redirectTo)
      return false
    }
    setCanGoForward(newRequest?.canGoForward)
    setCanGoBack(newRequest?.canGoBack)

    const isDeepLinkItem = GAMI_DEEPLINKS.find(item => url?.startsWith(item))
    if (!!isDeepLinkItem) {
      webViewRef?.current?.stopLoading?.()
      Linking.openURL(url)
      return false
    }

    // Handle dynamic link
    const decodedUrl = decodeURIComponent(url)
    const dynamicLinkPrefix = getEnvSetting().DYNAMIC_LINK_PREFIX
    const isDynamicLink = decodedUrl?.startsWith(dynamicLinkPrefix)
    if (isDynamicLink) {
      const parsedUrl = urlParse(decodedUrl, true)
      const modeValue = urlParse(parsedUrl?.query?.link, true)?.query?.mode

      if (modeValue === MODE_CODE.baggage_prediction) {
        const { terminal } = parsedUrl?.query || {}
        handleOpenL3BaggagePrediction({ queryData: { terminal }, goToExternalLink: goToNewUri })
        return false
      }
    }

    return true
  }

  const handleMessage = (message) => {
    const jsonData = parseJsonWebviewMessage(message)
    if (isEmpty(jsonData)) {
      return
    }

    const adobeAnalytics = get(jsonData, "adobeAnalytics")
    if (!isEmpty(adobeAnalytics)) {
      if (adobeAnalytics.aaTag === AdobeTagName.CAppL3FAQPage) {
        const { aaValue } = adobeAnalytics || {}
        const questionContent = aaValue?.split?.(" | ")?.[0]
        if (questionContent) {
          trackAction(AdobeTagName.CAppL3FAQPage, {
            [AdobeTagName.CAppL3FAQPage]: `Parking | ${questionContent} | Link`,
          })
        }
        return
      }
      trackAction(adobeAnalytics.aaTag, {
        [adobeAnalytics.aaTag]: adobeAnalytics.aaValue,
      })
    }

    const pageL3Info = get(jsonData, "pageL3Info")
    if (!isEmpty(pageL3Info)) {
      isL3Page.current = true
      const pageName = `${pageL3Info.parentPageName}_${pageL3Info.pageName}`
      const pagePath = env()?.AEM_URL + pageL3Info.pagePath
      trackAction(AdobeTagName.common, {
        [AdobeTagName.cagPagePath]: pagePath,
      })
      trackAction(AdobeTagName.cagPagePath, {
        [pageName]: pagePath,
      })
      trackAction(AdobeTagName.cagPagePath, {
        [AdobeTagName.cagPagePath]: {
          [pageName]: pagePath,
        },
      })
      commonTrackingScreen(pageName, getPreviousScreen(), isLogin)
      return
    }
    const dataTriggerType = jsonData?.type?.toLowerCase()
    if (dataTriggerType === NavigationTypeEnum.external) {
      return true
    }

    if (fromFAQ) {
      handleMessageFromFAQ(jsonData)
      retryOpenFQAData.current = jsonData
      return
    }

    if (screen === "notificationScreen") {
      handleMessageNotificationScreen(jsonData)
      return
    }

    const type = get(jsonData, "type", "")
    const link = get(jsonData, "link", "")
    if (type === "directionMap") {
      const value = get(jsonData, "value", {})
      openWebView(value?.localref)
    } else {
      const redirect = get(jsonData, "redirect", "")
      handleL3NavigationType(type, link, redirect)
    }
  }

  const openWebView = async (localRef) => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      refRetryAction.current = null
      if (!mapRMFlag) {
        return mapUnavailable?.current?.show()
      }
      trackAction(AdobeTagName.CAppATOMSEntryClick, {
        [AdobeTagName.CAppATOMSEntryClick]: `L3/CF Details Page|Get Directions|${localRef}`,
      })
      navigation.navigate(NavigationConstants.changiMap, { localRef: localRef })
    } else {
      refRetryAction.current = localRef
      setConnection(false)
    }
  }

  const mappingL3NavigationToAppNavigation = (type) => {
    switch (type) {
      case L3NavigationType.inApp:
        return NavigationTypeEnum.inApp
      case L3NavigationType.external:
        return NavigationTypeEnum.external
      case L3NavigationType.deepLink:
        return NavigationTypeEnum.deepLink
    }
  }

  const handleL3NavigationType = (type, link, redirect) => {
    if (!type || !link) return null
    const l3Type = type?.toLowerCase()
    if (
      [L3NavigationType.inApp, L3NavigationType.external, L3NavigationType.deepLink].includes(
        l3Type,
      )
    ) {
      const navigationType = mappingL3NavigationToAppNavigation(type)
      const navigationValue = link
      if (navigationType === NavigationTypeEnum.external) {
        webViewRef?.current?.injectJavaScript(`window.location="${navigationValue}"`)
        return
      }
      handleNavigation(navigationType, navigationValue, redirect)
    }
  }
  const StatusBarComponent = ({ backgroundColor }) => (
    <View style={[styles.statusBarStyle, { backgroundColor }]}>
      <SafeAreaView>
        <StatusBar translucent={statusBarTranslucent} backgroundColor={backgroundColor} barStyle="dark-content" />
      </SafeAreaView>
    </View>
  )

  const handleReloadWebView = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      setConnection(isConnected)
      if (refRetryAction.current) {
        openWebView(refRetryAction.current)
        return
      }
      webViewRef?.current?.reload()
    }
  }

  const onDeleteNotification = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected && !isEmpty(advisoriesNoticationId)) {
      navigation.goBack()
      dispatch(notificationAction.deleteSingleNotificationRequest(advisoriesNoticationId))
    }
  }

  const handleForwardButton = (forward) => {
    return forward ? <ArrowRight /> : <ArrowRightDisabled />
  }

  const webViewStyle: ViewStyle = {
    opacity: 0.99,
    overflow: "hidden",
  }
  const scriptToInject = `
  window.ReactNativeWebView.postMessage(document.title)
  `

  const checkEncodeURI = (url: string) => {
    return /%/i.test(url)
  }

  const onLoadEnd = (event) => {
    setLoaded(false)
    setCanGoBack(event?.nativeEvent?.canGoBack)
  }

  useEffect(() => {
    if(playPassUrlPayload?.error){
      // navigation.navigate(NavigationConstants.explore)
      navigation.dispatch(StackActions.popToTop())
    }
  }, [playPassUrlPayload?.error])

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        onBackExitWebview()
        return true
      }

      if (onGoBack) {
        navigation.setOptions({gestureEnabled: false})
      }

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress
      )
      return () => {
        if(Platform.OS === 'android'){
          StatusBar.setBackgroundColor('transparent')
        }
        subscription.remove()
      }
    }, [])
  )

  useCurrentScreenActiveAndPreviousScreenHook(TrackingScreenName.WebView)

  return (
    <Fragment>
      <View style={styles.container}>
        <StatusBarComponent backgroundColor={color.palette.whiteGrey} />
        <Animated.View
          style={{
            ...styles.animatedHeaderContainerStyle,
          }}
        >
          <View style={[styles.headerContainerStyle, headerContainerStyle]}>
            <View style={styles.leftHeaderWebview}>
              {!!showHeaderLeftButton && (
                <TouchableOpacity
                  style={styles.headerBackButton}
                  onPressIn={canGoBack ? onBack : onBackExitWebview}
                  hitSlop={{ top: 10, left: 10, bottom: 10, right: 10 }}
                  testID={"BackButtonWebViewScreen"}
                  accessibilityLabel={"BackButtonWebViewScreen"}
                >
                  <ArrowLeft />
                </TouchableOpacity>
              )}
            </View>
            <View style={styles.middleHeaderWebview}>
              <Text
                preset={"subTitleBold"}
                text={title || ""}
                style={styles.headerTitleTextStyle}
                numberOfLines={1}
                testID={"HeaderWebViewScreen"}
                accessibilityLabel={"HeaderWebViewScreen"}
              />
            </View>
            {handleCondition(
              isAdvisoriesRightHeader,
              <View style={styles.rightHeaderWebview}>
                <TouchableOpacity
                  style={styles.headerForwardButton}
                  onPress={() => {
                    // TODO: Handle share notifications
                  }}
                  hitSlop={{ top: 5, left: 5, bottom: 5, right: 5 }}
                  testID={"ShareButtonWebViewScreen"}
                  accessibilityLabel={"ShareButtonWebViewScreen"}
                >
                  <ShareBlue />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.headerCloseButton}
                  onPress={onDeleteNotification}
                  hitSlop={{ top: 5, left: 5, bottom: 5, right: 5 }}
                  testID={"DeleteButtonWebViewScreen"}
                  accessibilityLabel={"DeleteButtonWebViewScreen"}
                >
                  <Delete width={24} height={24} />
                </TouchableOpacity>
              </View>,
              <View style={styles.rightHeaderWebview}>
                {useForward ? (
                  <TouchableOpacity
                    style={styles.headerForwardButton}
                    onPress={onForward}
                    disabled={!canGoForward}
                    hitSlop={{ top: 5, left: 5, bottom: 5, right: 5 }}
                    testID={"ForwardButtonWebViewScreen"}
                    accessibilityLabel={"ForwardButtonWebViewScreen"}
                  >
                    {handleForwardButton(canGoForward)}
                  </TouchableOpacity>
                ) : (
                  <View style={styles.headerForwardButton} />
                )}
                {useClose ? (
                  <TouchableOpacity
                    style={styles.headerCloseButton}
                    onPress={onBackExitWebview}
                    hitSlop={{ top: 5, left: 5, bottom: 5, right: 5 }}
                    testID={"CloseButtonWebViewScreen"}
                    accessibilityLabel={"CloseButtonWebViewScreen"}
                  >
                    <CrossBlue width={24} height={24} />
                  </TouchableOpacity>
                ) : (
                  <View style={styles.headerCloseButton} />
                )}
              </View>,
            )}
          </View>
          {isLoaded && (
            <Progress.Bar
              progress={progress}
              width={null}
              height={4}
              borderWidth={0}
              borderRadius={0}
              color={color.palette.lightPurple}
              style={styles.progressBarStyle}
            />
          )}
        </Animated.View>
        <AnimatedWebView
          ref={webViewRef}
          source={{ uri: Platform.OS === "ios" && !checkEncodeURI(uri) ? encodeURI(uri) : uri }}
          basicAuthCredential={basicAuthCredential}
          onLoadProgress={(event) => setProgress(event.nativeEvent.progress)}
          onLoadStart={() => setLoaded(true)}
          onLoadEnd={onLoadEnd}
          onNavigationStateChange={onNavigationStateChange}
          javaScriptEnabled={true}
          showsHorizontalScrollIndicator={false}
          onShouldStartLoadWithRequest={navigationHandler}
          allowsInlineMediaPlayback={allowsInlineMediaPlayback}
          domStorageEnabled={true}
          bounces={false}
          mediaCapturePermissionGrantType={mediaCapturePermissionGrantType}
          mediaPlaybackRequiresUserAction={mediaPlaybackRequiresUserAction}
          originWhitelist={originWhitelist || ["https://*", "cagichangi://*"]}
          showsVerticalScrollIndicator={false}
          injectedJavaScript={scriptToInject}
          onMessage={handleMessage}
          nestedScrollEnabled={true}
          overScrollMode={"always"}
          onError={() => {
            if (screen === "flightDetails") {
              dispatch(FlyCreators.flyCheckInOnlineLoadFailed(true))
              onBackExitWebview()
            }
          }}
          style={webViewStyle}
        />
        <ErrorOverlayNoConnection
          reload={true}
          header
          headerBackgroundColor="transparent"
          visible={!isConnection}
          onBack={() => {
            navigation.goBack()
          }}
          onReload={() => handleReloadWebView()}
        />
        <BottomSheetMapUnavailable ref={mapUnavailable} />
      </View>
      <ErrorOverlay
        reload
        header
        headerBackgroundColor="transparent"
        visible={errorCallAPI}
        onReload={() => {
          // TO DO
        }}
        onBack={() => {
          navigation.goBack()
          dispatch(AirportLandingCreator.getCarParkUrlReset())
        }}
        testID={`${SCREEN_NAME}__ErrorOverlay`}
        accessibilityLabel={`${SCREEN_NAME}__ErrorOverlay`}
      />
    </Fragment>
  )
}

export { WebViewComponent }
