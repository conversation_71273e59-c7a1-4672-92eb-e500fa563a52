import React, { useEffect, useState } from "react"
import { WebView } from "react-native-webview"
import base64 from "react-native-base64"
import { get, isEmpty } from "lodash"
import { View, Platform, ViewStyle, ActivityIndicator } from "react-native"
import { StorageKey, loadFromEncryptedStorage } from "app/utils/storage"
import { removeAllCookies } from "app/utils/cookies"
import CookieManager from "@react-native-cookies/cookies"
import moment from "moment"

import { styles } from "./extended-webview.styles"
import { parseJsonWebviewMessage } from "app/utils"

export interface WebViewComponentProps {
  isPlayPassView?: boolean
  uri: string
  username: string
  password: string
  navigationHandler: any
  webviewRef: any
  needCredentialDecoding: boolean
  onTitleChange?: (title: string) => void
  onError?: (event: any) => void
  onNavigationStateChange?: (navState: any) => void
  headers?: any
  loading?: boolean
  basicAuthCredential?: any
  playpassOnLoadEnd?: any
  incognito?: boolean
  onLoadStart?: (event: any) => void
  onLoad?: (event: any) => void
  shouldShowLoadingCookies?: boolean
  loadingCookiesStyles?: ViewStyle
  loadingCookiesSection?: JSX.Element
  handleForAA?: (data) => void
}

const webViewStyle: ViewStyle = {
  opacity: 0.99,
  overflow: "hidden",
}

const scriptToInject = `
window.ReactNativeWebView.postMessage(document.title)
`

const LoadingCookies = ({loadingCookiesStyles}) => {

  return (
    <View style={[styles.loadingWrapper, loadingCookiesStyles]}>
      <ActivityIndicator size={50} />
    </View>
  )
}

const ExtendedWebView = (props: WebViewComponentProps) => {
  const {
    navigationHandler,
    uri,
    username,
    password,
    needCredentialDecoding,
    webviewRef,
    onTitleChange,
    onNavigationStateChange,
    headers = {},
    basicAuthCredential = {
      username: needCredentialDecoding ? base64.decode(username) : username,
      password: needCredentialDecoding ? base64.decode(password) : password,
    },
    isPlayPassView = false,
    playpassOnLoadEnd,
    incognito = false,
    onError,
    onLoadStart,
    onLoad,
    loadingCookiesSection,
    loadingCookiesStyles,
    shouldShowLoadingCookies,
    handleForAA,
  } = props

  const [webViewInterval, setWebViewInterval] = useState<any>()
  const [isHaveCookies, setHaveCookies] = useState(false)

  useEffect(() => {
    const setCookies = async () => {
      const authCookies = await loadFromEncryptedStorage(StorageKey.authCookies)
      if (authCookies) {
        for (const [parentKey, childObject] of Object.entries(authCookies)) {
          const dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ"
          const epxireDate = moment().add(1, "year").format(dateFormat).toString()
          await Promise.all(
            Object.entries(childObject).map((el) => 
              CookieManager.set(parentKey, {
                name: el[0],
                value: el[1].value,
                version: "1",
                expires: epxireDate,
              })
            )
          )
        }
      }
      setTimeout(() => {
        setHaveCookies(true)
      }, 500)
    }
    setCookies()
    return () => {
      if (isPlayPassView) {
        removeAllCookies()
      }
    } 
  }, [])

  useEffect(() => {
    return () => clearInterval(webViewInterval)
  }, [webViewInterval])

  const handleMessage = (message: { nativeEvent: { data: string } }) => {
    if (handleForAA) {
      const jsonData = parseJsonWebviewMessage(message)
      const pageL3Info = get(jsonData, "pageL3Info")
      if (!isEmpty(pageL3Info)) return handleForAA(jsonData)
    }

    const title: string = message.nativeEvent.data
    const truncatedTitle = title.substring(0, 20)
    onTitleChange(truncatedTitle)
  }
  const handleOnLoadEnd = (syntheticEvent: any) => {
    const interval = setInterval(() => {
      webviewRef?.current?.injectJavaScript(scriptToInject)
    }, 1000)
    setWebViewInterval(interval)
    if (isPlayPassView && !isEmpty(syntheticEvent)) {
      playpassOnLoadEnd(syntheticEvent)
    }
  }

  const checkEncodeURI = (url: string) => {
    return /%/i.test(url)
  }

  return (
    <>
      {loadingCookiesSection && loadingCookiesSection}
      {!isHaveCookies && shouldShowLoadingCookies && !loadingCookiesSection && (
        <LoadingCookies loadingCookiesStyles={loadingCookiesStyles} />
      )}
      {isHaveCookies && 
        <WebView
          source={{
            uri: Platform.OS === "ios" && !checkEncodeURI(uri) ? encodeURI(uri) : uri,
            headers,
          }}
          ref={webviewRef}
          incognito={incognito}
          sharedCookiesEnabled
          javaScriptEnabled
          onLoadEnd={(syntheticEvent: any) => handleOnLoadEnd(syntheticEvent)}
          onShouldStartLoadWithRequest={navigationHandler}
          onNavigationStateChange={onNavigationStateChange}
          originWhitelist={["https://*", "cagichangi://*"]}
          basicAuthCredential={basicAuthCredential}
          allowUniversalAccessFromFileURLs={true}
          domStorageEnabled
          onMessage={handleMessage}
          onError={onError}
          setSupportMultipleWindows={false}
          allowsInlineMediaPlayback={true}
          style={webViewStyle}
          cacheEnabled={true}
          onLoadStart={onLoadStart}
          onLoad={onLoad}
        />
      }
    </>
  )
}
export { ExtendedWebView }
