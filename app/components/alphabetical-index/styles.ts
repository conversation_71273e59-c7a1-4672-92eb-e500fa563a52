import { StyleSheet } from "react-native"
import { newPresets } from "app/elements/text"
import { color } from "app/theme"

export const styles = StyleSheet.create({
  alphabeticalIndexSearchContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.almostWhiteGrey,
    position: "absolute",
    right: 0,
    top: "50%",
    transform: [{ translateY: -150 }], // Center vertically
    width: 15,
    zIndex: 10,
  },
  indexContainerStyle: {
    alignItems: "center",
    height: 14,
    justifyContent: "center",
    width: 14,
  },
  indexTextStyle: {
    ...newPresets.bold,
    fontSize: 10,
    lineHeight: 12,
  },
})
