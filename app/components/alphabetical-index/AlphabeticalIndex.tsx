import React, { use<PERSON>allback, useMemo } from "react"
import { View, Text, InteractionManager } from "react-native"
import { Gesture, GestureDetector } from "react-native-gesture-handler"
import { runOnJS } from "react-native-reanimated"
import _isNumber from "lodash/isNumber"
import { styles } from "./styles"

const INDEX_ITEM_HEIGHT = 14

export interface AlphabeticalIndexItem {
  label: string
  value: string
}

export interface AlphabeticalIndexProps {
  /**
   * List of items to index (must have a field to get name from)
   */
  data: any[]

  /**
   * Field name to get name from each item (default: 'tenantName')
   */
  nameField?: string

  /**
   * Alphabet index list (default: '#ABCDEFGHIJKLMNOPQRSTUVWXYZ')
   */
  alphabetList?: AlphabeticalIndexItem[]

  /**
   * Callback when pressing a letter in the index
   */
  onIndexPress: (letter: string, index: number) => void

  /**
   * Calculate offset for scroll position
   * Array containing accumulated height totals for each item
   */
  itemAccumulateHeights?: number[]

  /**
   * Whether to show the component
   */
  visible?: boolean

  /**
   * Custom tracking function
   */
  onTrackingAction?: (letter: string) => void

  /**
   * Custom styles
   */
  containerStyle?: any
  indexItemStyle?: any
  indexTextStyle?: any
}

const DEFAULT_ALPHABET_LIST: AlphabeticalIndexItem[] = "#ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  .split("")
  .map((letter) => ({
    label: letter,
    value: letter.toLowerCase(),
  }))

export const AlphabeticalIndex: React.FC<AlphabeticalIndexProps> = ({
  data,
  nameField = "tenantName",
  alphabetList = DEFAULT_ALPHABET_LIST,
  onIndexPress,
  itemAccumulateHeights = [],
  visible = true,
  onTrackingAction,
  containerStyle,
  indexItemStyle,
  indexTextStyle,
}) => {
  // Calculate index data from items list
  const indexData = useMemo(() => {
    if (!data?.length) return {}

    const result = {}
    for (let i = 0; i < data.length; i++) {
      const itemName = data[i]?.[nameField]
      let firstLetter = itemName?.[0]?.toLowerCase?.()
      if (!firstLetter) continue
      if (!/[a-z]/.test(firstLetter)) firstLetter = "#"
      if (result[firstLetter] || result[firstLetter] === 0) continue
      result[firstLetter] = i
    }

    // Handle letters without data
    const alphabetValues = alphabetList.map((item) => item.value)
    let missingIndexes = []
    for (let i = 0; i < alphabetValues.length; i++) {
      const letter = alphabetValues[i]
      if (!_isNumber(result[letter])) {
        missingIndexes.push(letter)
      } else {
        // Assign previous missing indexes with current index
        for (let j = 0; j < missingIndexes.length; j++) {
          result[missingIndexes[j]] = result[letter]
        }
        missingIndexes = []
      }
    }

    // Assign final missing indexes with last index
    if (missingIndexes.length) {
      for (let i = 0; i < missingIndexes.length; i++) {
        result[missingIndexes[i]] = data.length - 1
      }
    }

    return result
  }, [data, nameField, alphabetList])

  const handlePressIndexSearch = useCallback(
    (letter: string) => {
      InteractionManager.runAfterInteractions(() => {
        // Custom tracking
        if (onTrackingAction) {
          onTrackingAction(letter)
        }

        const indexToScroll = indexData[letter]
        if (!indexToScroll && indexToScroll !== 0) return

        onIndexPress(letter, indexToScroll)
      })
    },
    [indexData, onIndexPress, onTrackingAction],
  )

  const handleIndexGesture = useCallback(
    (e) => {
      const index = Math.floor(e.y / INDEX_ITEM_HEIGHT)
      if (index >= 0 && index < alphabetList.length) {
        runOnJS(handlePressIndexSearch)(alphabetList[index]?.value)
      }
    },
    [handlePressIndexSearch, alphabetList],
  )

  const gesture = Gesture.Pan()
    .onBegin(handleIndexGesture)
    .onUpdate(handleIndexGesture)
    .runOnJS(true)

  if (!visible || !data?.length) {
    return null
  }

  return (
    <GestureDetector gesture={Gesture.Simultaneous(gesture, Gesture.Native())}>
      <View style={[styles.alphabeticalIndexSearchContainerStyle, containerStyle]}>
        {alphabetList.map((item, index) => (
          <View key={index} style={[styles.indexContainerStyle, indexItemStyle]}>
            <Text style={[styles.indexTextStyle, indexTextStyle]}>{item.label}</Text>
          </View>
        ))}
      </View>
    </GestureDetector>
  )
}

export default AlphabeticalIndex
