import React from "react"
import { <PERSON><PERSON><PERSON>, TextStyle, View, ViewStyle } from "react-native"
import LinearGradient from "react-native-linear-gradient"
import { Text } from "app/elements/text"
import { Button } from "app/elements/button/button"
import { ErrorCloudV2 } from "assets/icons"
import { styles, reloadButtonColors } from "./error-section-v2.styles"
import { translate } from "app/i18n"
import { useSelector } from "react-redux"
import { AemSelectors } from "app/redux/aemRedux"
import { mappingUrlAem } from "app/utils"
import BaseImage from "app/elements/base-image/base-image"

type ErrorSectionV2Props = {
  reload?: boolean
  onReload?: () => void
  testID?: string
  accessibilityLabel?: string
  textContainerStyle?: StyleProp<ViewStyle>
  containerStyle?: StyleProp<ViewStyle>
  reloadButtonStyleOverride?: StyleProp<ViewStyle>
  reloadButtonTextStyleOverride?: TextStyle
  reloadButtonInnerStyleOverride?: ViewStyle
  title?: string
  message?: string
  reloadText?: string
  extendCode?: string
  iconSize?: number
}

export const ErrorSectionV2 = (props: ErrorSectionV2Props) => {
  const {
    reload = true,
    onReload,
    testID = "ErrorSectionV2",
    accessibilityLabel = "ErrorSectionV2",
    textContainerStyle,
    containerStyle,
    reloadButtonStyleOverride,
    reloadButtonTextStyleOverride,
    reloadButtonInnerStyleOverride,
    title = translate("errorOverlay.variant3.title"),
    message = translate("errorOverlay.variant3.message"),
    reloadText = translate("errorOverlay.variant3.retry"),
    extendCode,
    iconSize = 120,
  } = props

  const handlePressReload = () => {
    onReload?.()
  }

  const errorData = useSelector(AemSelectors.getErrorsCommon)
  const matchedAem =
    extendCode && errorData?.length ? errorData.find((el: any) => el.code === extendCode) : null
  const finalTitle = matchedAem?.header || title
  const finalMessage = matchedAem?.subHeader || message
  const finalReloadText = matchedAem?.buttonLabel || reloadText
  const aemIconUri = mappingUrlAem(matchedAem?.icon)

  return (
    <View
      style={[styles.containerStyle, containerStyle]}
      testID={testID}
      accessibilityLabel={accessibilityLabel}
    >
      <View style={styles.defaultIconStyle}>
        {matchedAem?.icon ? (
          <BaseImage
            source={{ uri: aemIconUri }}
            style={{ width: iconSize, height: iconSize }}
            resizeMode="contain"
          />
        ) : (
          <ErrorCloudV2 width={iconSize} height={iconSize} />
        )}
      </View>
      <View style={[styles.textContainerStyle, textContainerStyle]}>
        <Text style={styles.titleTextStyle} text={finalTitle} />
        <Text style={styles.messageTextStyle} text={finalMessage} />
      </View>
      {reload && (
        <LinearGradient
          style={[styles.reloadButtonStyle, reloadButtonStyleOverride]}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 0 }}
          colors={reloadButtonColors}
        >
          <Button
            onPress={handlePressReload}
            sizePreset="large"
            textPreset="buttonLarge"
            typePreset="primary"
            text={finalReloadText}
            backgroundPreset="light"
            statePreset="default"
            textStyle={reloadButtonTextStyleOverride}
            style={reloadButtonInnerStyleOverride}
            testID={`${testID}__ButtonReload`}
            accessibilityLabel={`${accessibilityLabel}__ButtonReload`}
          />
        </LinearGradient>
      )}
    </View>
  )
}
