import { StyleSheet } from "react-native"
import { presets } from "app/elements/text"
import { color } from "app/theme"

export const styles = StyleSheet.create({
  containerStyle: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
  },
  textContainerStyle: {
    marginBottom: 24,
    width: "100%",
  },
  titleTextStyle: {
    ...presets.caption2BlackBold,
    color: color.palette.almostBlackGrey,
    marginBottom: 16,
    textAlign: "center",
  },
  messageTextStyle: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    textAlign: "center",
    alignSelf: "center",
    width: "100%",
  },
  reloadButtonStyle: {
    borderRadius: 60,
    paddingHorizontal: 24,
  },
  defaultIconStyle: {
    marginBottom: 16,
  },
})

export const reloadButtonColors = [
  color.palette.gradientColor1Start,
  color.palette.gradientColor1End,
]


