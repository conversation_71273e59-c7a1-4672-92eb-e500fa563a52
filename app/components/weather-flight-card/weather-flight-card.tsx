import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react"
import { ScrollView, TouchableOpacity, View } from "react-native"
import { styles } from "./weather-flight-card.styles"
import { Text } from "app/elements/text/text"
import {
  AccordionDown,
  AccordionUp,
  CircleArrowUpward,
  RightArrowCircle,
} from "ichangi-fe/assets/icons"
import { WeatherFlightCardProps } from "./weather-flight-card.props"
import BaseImage from "app/elements/base-image/base-image"
import { color } from "app/theme"
import moment from "moment"
import { env } from "app/config/env-params"
import { graphqlOperation } from "aws-amplify"
import restApi from "app/services/api/request"
import { getWeatherQuery } from "app/models/queries"
import { WeatherFlightProps } from "app/screens/fly/flights/flight-details/flight-detail.props"
import { roundTemperature } from "app/utils/fly-helper"
import ListWeatherPopup from "../list-weather-popup/list-weather-popup"
import { WeatherFlightCardLoading } from "./weather-flight-card-loading"
import { WeatherFlightCardError } from "./weather-flight-card-error"

// Dedicated test ID prefix for professional usage
const TEST_ID_PREFIX = "weatherFlightCard_"

const WeatherFlightCard = ({
  section,
  flightDetailsData,
  isFlightSaved,
  onSaveFlight,
}: WeatherFlightCardProps) => {
  const { title } = section
  const { viaAirportDetails } = flightDetailsData || {}

  const [isExpanded, setIsExpanded] = useState(Boolean(isFlightSaved))
  const [destinationsFocusing, setDestinationsFocusing] = useState<{
    name: string
    code: string
  }>()
  const [isLoadingWeatherData, setIsLoadingWeatherData] = useState(false)
  const [isErrorWeatherData, setIsErrorWeatherData] = useState(false)
  const [weatherDestinations, setWeatherDestinations] = useState<
    Record<string, WeatherFlightProps>
  >({})
  const [visibleWeatherPopup, setVisibleWeatherPopup] = useState(false)
  // the destination is SIN if the flight destination is ARR
  // otherwise, it is the airport code of the flight destination
  const [airportDestination, setAirportDestination] = useState({
    name: "",
    code: "",
  })

  useEffect(() => {
    setIsExpanded(Boolean(isFlightSaved))
  }, [isFlightSaved])

  const handleGetWeather = useCallback(async (input: { airport: string }) => {
    setIsErrorWeatherData(false)
    setIsLoadingWeatherData(true)
    try {
      const response = await restApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getWeatherQuery, { input: { airport: input.airport } }),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const result = response?.data?.data?.getWeather
      if (response?.statusCode === 200 && result?.status !== "ERROR") {
        setWeatherDestinations((prev) => ({
          ...prev,
          [input.airport]: result?.data,
        }))
      } else {
        setIsErrorWeatherData(true)
      }
    } catch (error) {
      setIsErrorWeatherData(true)
    } finally {
      setIsLoadingWeatherData(false)
    }
  }, [])

  useEffect(() => {
    if (flightDetailsData?.airportDetails?.name && flightDetailsData?.airportDetails?.code) {
      const airportDestinationCode = flightDetailsData?.direction === "ARR" ? "SIN" : flightDetailsData?.airportDetails?.code
      const airportDestinationName = flightDetailsData?.direction === "ARR" ? "Singapore" : flightDetailsData?.airportDetails?.name
      setAirportDestination({
        name: airportDestinationName,
        code: airportDestinationCode,
      })
      setDestinationsFocusing({
        name: airportDestinationName,
        code: airportDestinationCode,
      })
      handleGetWeather({
        airport: airportDestinationCode,
      })
    }
    if (flightDetailsData?.viaAirportDetails?.code) {
      handleGetWeather({ airport: flightDetailsData?.viaAirportDetails?.code })
    }
  }, [
    flightDetailsData?.airportDetails?.name,
    flightDetailsData?.airportDetails?.code,
    flightDetailsData?.viaAirportDetails?.code,
  ])

  const weatherData = useMemo(() => {
    if (destinationsFocusing) {
      return weatherDestinations[destinationsFocusing.code]
    }
    return null
  }, [destinationsFocusing, weatherDestinations])

  const collapTitles = useMemo(() => {
    if (!flightDetailsData) {
      return ""
    }
    let result = airportDestination?.name ?? ""
    if (viaAirportDetails) {
      result += `${result ? " | " : ""}${viaAirportDetails.name}`
    }
    return result
  }, [airportDestination, viaAirportDetails])

  const onPressExpandCollapse = () => {
    if (isFlightSaved) {
      setIsExpanded((prev) => !prev)
    } else {
      // If the flight is not saved, save the flight.
      onSaveFlight("Weather")
    }
  }

  const onPressSeeAll = () => {
    setVisibleWeatherPopup(true)
  }

  const renderCollapseWeather = () => {
    return (
      <View style={styles.containerCollapseTiles}>
        <CircleArrowUpward />
        <View style={styles.containerCollapseTilesTitle}>
          <Text
            preset="caption1Regular"
            text={collapTitles}
            style={styles.textCollapseTiles}
            testID={`${TEST_ID_PREFIX}collapseTitleText`}
            accessibilityLabel={`${TEST_ID_PREFIX}collapseTitleText`}
          />
        </View>
      </View>
    )
  }

  const renderWeatherDestinationTab = () => {
    if (!viaAirportDetails || !destinationsFocusing) {
      return null
    }
    const destinations = [
      {
        name: airportDestination.name,
        code: airportDestination.code,
      },
      {
        name: viaAirportDetails.name,
        code: viaAirportDetails.code,
      },
    ]

    const onPressTab = (item) => {
      setDestinationsFocusing(item)
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.containerTab}
        testID={`${TEST_ID_PREFIX}destinationScrollView`}
        accessibilityLabel={`${TEST_ID_PREFIX}destinationScrollView`}
      >
        {destinations.map((item, index) => {
          const isSelected = destinationsFocusing.name === item.name
          return (
            <TouchableOpacity
              key={index}
              style={[styles.btnTab, isSelected && styles.btnTabFocused]}
              onPress={() => onPressTab(item)}
              testID={`${TEST_ID_PREFIX}destinationTab_${index}`}
              accessibilityLabel={`${TEST_ID_PREFIX}destinationTab_${index}`}
            >
              <Text
                preset="caption1Bold"
                text={item.name}
                style={isSelected ? styles.labelTabFocused : styles.labelTab}
                testID={`${TEST_ID_PREFIX}destinationText_${index}`}
                accessibilityLabel={`${TEST_ID_PREFIX}destinationText_${index}`}
              />
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    )
  }

  const renderWeatherContent = () => {
    if (!weatherData?.dailyForecasts?.length || weatherData?.dailyForecasts?.length < 3) {
      return (
        <View
          style={styles.containerWeatherContent}
          testID={`${TEST_ID_PREFIX}weatherContent`}
          accessibilityLabel={`${TEST_ID_PREFIX}weatherContent`}
        >
          <Text
            preset="caption1Regular"
            tx="flightDetailV2.weatherFlightCard.unavailable"
            style={styles.textAlmostBackColor}
            testID={`${TEST_ID_PREFIX}unavailableLabel`}
            accessibilityLabel={`${TEST_ID_PREFIX}unavailableLabel`}
          />
        </View>
      )
    }
    const { dailyForecasts, isDayTime, currentTemperature } = weatherData
    const today = dailyForecasts[0]
    const tomorrow = dailyForecasts[1]
    const theNextTwoDays = dailyForecasts[2]
    return (
      <View
        style={styles.containerWeatherContent}
        testID={`${TEST_ID_PREFIX}weatherContent`}
        accessibilityLabel={`${TEST_ID_PREFIX}weatherContent`}
      >
        {/* Today */}
        <View style={styles.containerToday}>
          <View>
            <Text
              preset="caption1Regular"
              tx="flightDetailV2.weatherFlightCard.today"
              style={styles.textDarkestGrey}
              testID={`${TEST_ID_PREFIX}todayLabel`}
              accessibilityLabel={`${TEST_ID_PREFIX}todayLabel`}
            />
            <BaseImage
              source={{ uri: isDayTime ? today.dayIconUrl : today.nightIconUrl }}
              style={styles.imgToday}
              resizeMode="stretch"
            />
          </View>
          <Text
            preset="h3Regular"
            text={`${roundTemperature(currentTemperature)}`}
            style={styles.todayDegreeLabel}
            testID={`${TEST_ID_PREFIX}currentTemperature`}
            accessibilityLabel={`${TEST_ID_PREFIX}currentTemperature`}
          />
          <View style={styles.containerStatusToday}>
            <Text
              preset="caption1Regular"
              text={`H: ${roundTemperature(today.maximumTemperature)}    L: ${roundTemperature(
                today.minimumTemperature,
              )}`}
              style={styles.textDarkestGrey}
              testID={`${TEST_ID_PREFIX}todayHighLow`}
              accessibilityLabel={`${TEST_ID_PREFIX}todayHighLow`}
            />
            <Text
              preset="bodyTextBlack"
              text={isDayTime ? today.dayIconPhrase : today.nightIconPhrase}
              style={styles.textDarkestGrey}
              testID={`${TEST_ID_PREFIX}todayIconPhrase`}
              accessibilityLabel={`${TEST_ID_PREFIX}todayIconPhrase`}
            />
          </View>
        </View>
        {/* Tomorrow */}
        <View style={styles.containerNextDay}>
          <View style={styles.containerDegreeItemTmr}>
            <Text
              preset="caption1Regular"
              tx="flightDetailV2.weatherFlightCard.tomorrow"
              style={styles.textDarkestGrey}
              testID={`${TEST_ID_PREFIX}tomorrowLabel`}
              accessibilityLabel={`${TEST_ID_PREFIX}tomorrowLabel`}
            />
            <BaseImage
              source={{ uri: tomorrow.dayIconUrl }}
              style={styles.imgTmr}
              resizeMode="stretch"
            />
            <View style={styles.containerDegreeTmr}>
              <Text
                preset="subTitleBold"
                text={roundTemperature(tomorrow.maximumTemperature)}
                style={styles.textDarkestGrey}
                testID={`${TEST_ID_PREFIX}tomorrowMaxTemp`}
                accessibilityLabel={`${TEST_ID_PREFIX}tomorrowMaxTemp`}
              />
              <Text
                preset="caption2Bold"
                text={` / ${roundTemperature(tomorrow.minimumTemperature)}`}
                style={styles.textDarkestGrey}
                testID={`${TEST_ID_PREFIX}tomorrowMinTemp`}
                accessibilityLabel={`${TEST_ID_PREFIX}tomorrowMinTemp`}
              />
            </View>
            <Text
              preset="caption2Regular"
              text={tomorrow.dayIconPhrase}
              style={styles.weatherLabel}
              testID={`${TEST_ID_PREFIX}tomorrowIconPhrase`}
              accessibilityLabel={`${TEST_ID_PREFIX}tomorrowIconPhrase`}
            />
          </View>
          {/* 2 next days */}
          <View style={styles.containerDegreeItem}>
            <Text
              preset="caption1Regular"
              text={moment(theNextTwoDays.date).format("ddd, D MMM")}
              style={styles.textDarkestGrey}
              testID={`${TEST_ID_PREFIX}nextDayDate`}
              accessibilityLabel={`${TEST_ID_PREFIX}nextDayDate`}
            />
            <BaseImage
              source={{ uri: theNextTwoDays.dayIconUrl }}
              style={styles.imgTmr}
              resizeMode="stretch"
            />
            <View style={styles.containerDegreeTmr}>
              <Text
                preset="subTitleBold"
                text={roundTemperature(theNextTwoDays.maximumTemperature)}
                style={styles.textDarkestGrey}
                testID={`${TEST_ID_PREFIX}nextDayMaxTemp`}
                accessibilityLabel={`${TEST_ID_PREFIX}nextDayMaxTemp`}
              />
              <Text
                preset="caption2Bold"
                text={` / ${roundTemperature(theNextTwoDays.minimumTemperature)}`}
                style={styles.textDarkestGrey}
                testID={`${TEST_ID_PREFIX}nextDayMinTemp`}
                accessibilityLabel={`${TEST_ID_PREFIX}nextDayMinTemp`}
              />
            </View>
            <Text
              preset="caption2Regular"
              text={theNextTwoDays.dayIconPhrase}
              style={styles.weatherLabel}
              testID={`${TEST_ID_PREFIX}nextDayIconPhrase`}
              accessibilityLabel={`${TEST_ID_PREFIX}nextDayIconPhrase`}
            />
          </View>
          <TouchableOpacity
            style={styles.containerBtn}
            onPress={onPressSeeAll}
            testID={`${TEST_ID_PREFIX}seeAllButton`}
            accessibilityLabel={`${TEST_ID_PREFIX}seeAllButton`}
          >
            <View style={styles.containerLabel}>
              <View>
                <Text
                  preset="caption1Bold"
                  tx={"flightDetailV2.weatherFlightCard.seeMore"}
                  style={styles.btnSee5DaysLabel}
                  testID={`${TEST_ID_PREFIX}seeMoreText`}
                  accessibilityLabel={`${TEST_ID_PREFIX}seeMoreText`}
                />
              </View>
              <RightArrowCircle width={20} height={20} fill={color.palette.lightPurple} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const renderWeatherCard = () => {
    return (
      <>
        {renderWeatherDestinationTab()}
        {renderWeatherContent()}
      </>
    )
  }

  const renderExpandCollapseWeather = () => {
    if (isExpanded) {
      return renderWeatherCard()
    }
    return renderCollapseWeather()
  }

  const renderExpandCollapseIcon = () => {
    if (isExpanded) {
      return (
        <TouchableOpacity
          style={styles.containerExpandCollapseIcon}
          onPress={onPressExpandCollapse}
          testID={`${TEST_ID_PREFIX}expandCollapseIcon`}
          accessibilityLabel={`${TEST_ID_PREFIX}expandCollapseIcon`}
        >
          <AccordionUp />
        </TouchableOpacity>
      )
    }
    return (
      <TouchableOpacity
        style={styles.containerExpandCollapseIcon}
        onPress={onPressExpandCollapse}
        testID={`${TEST_ID_PREFIX}expandCollapseIcon`}
        accessibilityLabel={`${TEST_ID_PREFIX}expandCollapseIcon`}
      >
        <AccordionDown />
      </TouchableOpacity>
    )
  }

  const retryGetWeather = () => {
    if (airportDestination?.code) {
      handleGetWeather({ airport: airportDestination?.code })
    }
    if (flightDetailsData?.viaAirportDetails?.code) {
      handleGetWeather({ airport: flightDetailsData?.viaAirportDetails?.code })
    }
  }

  if (isLoadingWeatherData) {
    return <WeatherFlightCardLoading />
  }

  return (
    <>
      {title && (
        <View style={styles.titleContainer}>
          <Text
            text={title}
            preset="caption1Bold"
            numberOfLines={1}
            style={styles.textAlmostBackColor}
            testID={`${TEST_ID_PREFIX}titleText`}
            accessibilityLabel={`${TEST_ID_PREFIX}titleText`}
          />
        </View>
      )}
      {isErrorWeatherData ? (
        <View style={styles.viewError}>
          <WeatherFlightCardError onRetry={retryGetWeather} />
        </View>
      ) : (
        <View>
          {renderExpandCollapseWeather()}
          {renderExpandCollapseIcon()}
        </View>
      )}
      <ListWeatherPopup
        visible={visibleWeatherPopup}
        dailyForecasts={weatherData?.dailyForecasts ?? []}
        destinationName={destinationsFocusing?.name ?? ""}
        onClosed={() => setVisibleWeatherPopup(false)}
        onBackPressed={() => setVisibleWeatherPopup(false)}
        onModalHide={() => setVisibleWeatherPopup(false)}
        isDayTime={weatherData?.isDayTime ?? false}
      />
    </>
  )
}

export { WeatherFlightCard }
