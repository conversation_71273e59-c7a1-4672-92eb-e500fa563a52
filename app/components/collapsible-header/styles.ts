import { StyleSheet, Platform } from "react-native"
import { newPresets } from "app/elements/text"
import { color, shadow } from "app/theme"

export const FILTER_BORDER_RADIUS = 16

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerImageContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    overflow: "hidden",
  },
  backgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    width: "100%",
    resizeMode: "cover",
  },
  headerBar: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    zIndex: 1,
    ...shadow.filterHeaderShadow,
    shadowColor: Platform.select({
      android: color.palette.almostBlackGrey,
      ios: "rgba(18, 18, 18, 0.08)",
    }),
  },
  title: {
    ...newPresets.subTitleBold,
  },
  filterContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    top: -FILTER_BORDER_RADIUS,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: "hidden",
  },
  backBtn: {
    width: 28,
  },
  placeholder: {
    width: 28,
  },
})
