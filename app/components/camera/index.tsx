import { dtManualActionEvent, FE_LOG_PREFIX, convertStringValue } from "app/services/firebase/analytics"
import React, { useImperativeHandle, useRef } from "react"
import { Dimensions, Platform, StyleSheet } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import {
  Camera,
  CameraRuntimeError,
  CodeType,
  PhotoFile,
  TakePhotoOptions,
  useCameraDevice,
  useCameraFormat,
  useCameraPermission,
  useCodeScanner,
} from "react-native-vision-camera"

const { width: screenWidth } = Dimensions.get('screen')
export const BOARDING_PASS_ACCEPTED_SCAN_TYPE: CodeType[] = ['pdf-417', 'aztec', 'data-matrix', 'qr']

// Type for MyCamera ref
interface MyCameraRef {
  takePicture: (options?: TakePhotoOptions) => Promise<PhotoFile>
  requestPermission: () => Promise<boolean>
  hasPermission: boolean
  isCameraReady: boolean
}

interface MyCameraProps{
  isActive?: boolean,
  photo?: boolean
  video?: boolean
  photoRatio?: number
  flashMode?: boolean
  isCodeScanned?: boolean
  onCodeScanned?: (codes: any, frame: any) => void
  onCodeScannedTypes?: CodeType[]
  onStarted?: () => void 
}

const MyCamera = (props: MyCameraProps, ref: any) => {
  const {
    photo: isPhoto = true,
    video: isVideo = false,
    photoRatio,
    flashMode = false,
    isActive = true,
    isCodeScanned = false,
    onCodeScanned = () => {},
    onCodeScannedTypes = BOARDING_PASS_ACCEPTED_SCAN_TYPE,
    onStarted
  } = props
  const insets = useSafeAreaInsets();
  const SCREEN_HEIGHT = Platform.select<number>({
    android: Dimensions.get('screen').height - insets.bottom,
    ios: Dimensions.get('window').height,
  }) as number
  const defaultPhotoRatio = SCREEN_HEIGHT / screenWidth
  const cameraRef = useRef<Camera>(null)
  const [isCameraReady, setCameraReady] = React.useState(false)
  const [cameraKey, setCameraKey] = React.useState(0)
  const { hasPermission, requestPermission } = useCameraPermission()
  const device = useCameraDevice("back")
  const format = useCameraFormat(device, [{ photoAspectRatio: photoRatio ? photoRatio : defaultPhotoRatio }])
  const codeScanner = useCodeScanner({
    codeTypes: onCodeScannedTypes,
    onCodeScanned,
  })

  const onInitialized = () => {
    setCameraReady(true)
  }

  const onError = (error: CameraRuntimeError) => {
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}Camera_error`)
    dtAction.reportStringValue('code', `${error.code}`)
    dtAction.reportStringValue('message', `${convertStringValue(error?.message)}`)
    dtAction.leaveAction()
    console.error("MyCamera camera error", {
      code: error.code,
      message: error?.message,
    })
  }

  const takePicture = async (options: TakePhotoOptions = {}) => {
    const image = await cameraRef?.current?.takePhoto?.({
      flash: flashMode ? "on" : "off",
      ...options,
    })
    setCameraKey(prev => prev + 1)
    return image
  }

  // Expose functions to parent
  useImperativeHandle(ref, () => ({
    takePicture,
    requestPermission,
    hasPermission,
    isCameraReady,
  }))

  return !!device ? (
    <Camera
      key={cameraKey}
      ref={cameraRef}
      format={format}
      isActive={isActive}
      device={device}
      photo={isPhoto}
      video={isVideo}
      outputOrientation="preview"
      style={StyleSheet.absoluteFill}
      torch={flashMode ? "on" : "off"}
      onError={onError}
      onInitialized={onInitialized}
      codeScanner={!!isCodeScanned ? codeScanner : null}
      onStarted={onStarted}
    />
  ) : (
    <></>
  )
}

export default React.forwardRef(MyCamera)
export { CodeType, MyCameraRef }