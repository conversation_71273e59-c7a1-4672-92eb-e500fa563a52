import React, { use<PERSON>emo, useContext } from "react"
import { View, FlatList, TouchableOpacity, Dimensions } from "react-native"
import { UpComingEventProps } from "./upcoming-event-props"
import { styles } from "./styles"
import { NavigationConstants, UpComingEventType } from "app/utils/constants"
import { Text } from "app/elements/text"
import ArrowRight from "ichangi-fe/assets/icons/arrow-right.svg"
import { DateFormats, toDate, TimeFormats, dayIdentifier } from "app/utils/date-time/date-time"
import moment from "moment"
import { useNavigation } from "@react-navigation/native"
import { WalletDirection } from "app/models/consts"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { size, isEmpty } from "lodash"
import BaseImage from "app/elements/base-image/base-image"
import SavedFlightCardUpcomingEvent from "./saved-flight-card"
import { AccountContext } from "app/services/context/account"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import UpComingEventsViewMoreButtons from "./view-more-buttons"

const { width } = Dimensions.get("screen")
export const WIDTH_SINGLE_ITEM = width - 48
export const UpComingEventComponent = (props: UpComingEventProps) => {
  const { data, styleContainer } = props
  const dataLength = size(data)
  const navigation = useNavigation()

  const handleGlobalNavigate = (...args) => {
    navigation.navigate(...(args as never))
  }

  const firstSavedFlightIndex = useMemo(() => {
    return data.findIndex(i => i?.upcomingEventType === UpComingEventType.savedFlights)
  }, [data])

  const getMarginTop = () => {
    return { marginTop: 4 }
  }

  const getDateDataV2 = (item: any) => {
    return item?.type === "event" ? item?.upcomingStartScheduledTxt : item?.upcomingEndScheduledTxt
  }

  const getPlaypassEventItemTitle = (item: any) => {
    const itemKeyString = "titleV2"
    return item?.playpassEventCard?.[itemKeyString]
  }

  const getPlaypassEventItemImage = (item: any) => {
    const itemKeyString = "imageV2"
    return item?.playpassEventCard?.[itemKeyString]
  }

  const renderUpcomingEventItem = ({ item, index }) => {
    switch (item?.upcomingEventType) {
      case UpComingEventType.playpassBookings:
        return (
          <TouchableOpacity
            style={[
              styles.itemUpcomingEventStyles,
              { marginLeft: 0 },
              dataLength === 1 && index === 0 && { width: WIDTH_SINGLE_ITEM },
            ]}
            onPress={() => {
              trackAction(AdobeTagName.CAppHomeBookingTiles, {
                [AdobeTagName.CAppHomeBookingTiles]: `${getPlaypassEventItemTitle(item)}|Event`,
              })
              handleGlobalNavigate(NavigationConstants.playPassBookingDetail, {
                bookingKey: item?.bookingKey,
              })
            }}
          >
            <View style={styles.wrapIconImage}>
              <BaseImage
                style={styles.imageStyles}
                source={{ uri: getPlaypassEventItemImage(item) }}
              />
            </View>
            <View style={styles.rightItemView}>
              <Text style={styles.rightTitleItemStyles} numberOfLines={2}>
                {getPlaypassEventItemTitle(item)}
              </Text>
              <Text style={[styles.rightTextTimeStyles, getMarginTop()]}>
                {getDateDataV2(item)}
              </Text>
            </View>
          </TouchableOpacity>
        )
      case UpComingEventType.savedFlights:
        return (
          <SavedFlightCardUpcomingEvent
            item={item}
            index={index}
            isFirstOfList={index === firstSavedFlightIndex}
            navigation={navigation}
            isSingle={dataLength === 1 && index === 0}
          />
        )
      default:
        return <UpComingEventsViewMoreButtons handleGlobalNavigate={handleGlobalNavigate} />
    }
  }

  return (
    <FlatList
      data={data}
      renderItem={renderUpcomingEventItem}
      horizontal
      contentContainerStyle={styles.contentFlatlistStyles}
      showsHorizontalScrollIndicator={false}
      style={[styles.flatlistStyles, styleContainer]}
      keyExtractor={(_, index) => index.toString()}
    />
  )
}
