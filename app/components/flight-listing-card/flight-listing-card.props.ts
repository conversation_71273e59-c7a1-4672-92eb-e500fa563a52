import { AirportDetailsProps } from "app/screens/fly/flights/flight-details/flight-detail.props"

export enum FlightListingState {
  default = "default",
  loading = "loading",
}

export interface TransitCodesProps {
  id: number
  code: string
}

export interface FlightListingProps {
  state: FlightListingState
  logo: string
  flightNumber: string
  flightStatus?: string
  flightStatusMapping?: string
  beltStatusMapping?: string
  statusColor?: string
  codeShare?: string[]
  departingCode: string
  departingPlace: string
  destinationCode: string
  destinationPlace: string
  timeOfFlight: string
  transits: TransitCodesProps[]
  onPressed?: any
  onSaved?: any
  isSaved: boolean
  flightDate?: any
  scheduledDate?: any
  scheduledTime?: any
  isLoggedIn?: boolean
  isMSError?: boolean
  isSaveFlightLoading?: boolean
  getSavedFlightsLoading?: boolean
  itemIndex?: number
  sectionIndex?: number
  flightUniqueId?: string
  estimatedTimestamp?: string
  actualTimestamp?: string
  showGate?: boolean
  direction?: string
  terminal?: string
  checkInRow?: string
  displayBelt?: string
  displayTimestamp?: string
  viaAirportDetails?: any
  country?: string
  isFirstFlight?: boolean
  upcomingStatusMapping?: string,
  isFocused?: boolean,
  displayGate?: boolean,
  airportDetails?: AirportDetailsProps,
}
