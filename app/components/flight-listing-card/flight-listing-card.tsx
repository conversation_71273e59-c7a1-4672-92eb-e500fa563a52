import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react"
import {
  TouchableOpacity,
  View,
  ViewStyle,
  Dimensions,
  Platform,
  InteractionManager,
  StyleSheet,
} from "react-native"
import { Text } from "../../elements/text/text"
import { color, typography } from "../../theme"
import { FlightListingProps, FlightListingState } from "./flight-listing-card.props"
import ShimmerPlaceholder from "../../helpers/shimmer-placeholder"
import Plus from "./plus.svg"
import PlusDisabled from "./plus-disabled.svg"
import InfoFilled from "./info_filled.svg"
import { getDotUnicode, PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "../../utils/constants"
import { scale } from "react-native-size-matters"
import { translate } from "../../i18n"
import moment from "moment-timezone"
import { StorageKey } from "app/utils/storage/storage-key"
import { load, save } from "app/utils/storage"
import isEmpty from "lodash/isEmpty"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import Tooltip from "react-native-walkthrough-tooltip"
import { useRoute } from "@react-navigation/native"
import { useSelector, useDispatch } from "react-redux"
import { AemSelectors } from "app/redux/aemRedux"
import { handleCondition, ifAllTrue, ifOneTrue, simpleCondition } from "app/utils"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import BaseImage from "app/elements/base-image/base-image"
import { FlyCreators } from "app/redux/flyRedux"
import {
  FlightDepartureIconActive,
  FlightArrivalIconActive,
  CheckSavedFlight,
} from "ichangi-fe/assets/icons"
import { AnimatedText } from "../../elements/animations/AnimatedText"
import ViaArrow from "./via-arrow.svg"
import { store } from "app/redux/store"
import { useIsFocused } from "@react-navigation/native"
import { usePersistStore } from "app/zustand/persist"

const { width } = Dimensions.get("window")
const dotUnicode = getDotUnicode()

const buttonStyleSaved: ViewStyle = {
  width: 28,
  height: 28,
  borderRadius: 20,
  justifyContent: "center",
  backgroundColor: color.palette.lightPurple,
  alignItems: "center",
}

const styles = StyleSheet.create({
  bottomTextContainer: {
    alignItems: "center",
    flexDirection: "row",
    paddingBottom: 24,
    paddingLeft: 14,
    paddingTop: 18,
  },
  bottomTextStyle: {
    flex: 1,
  },
  buttonDisabledStyleAdd: {
    ...buttonStyleSaved,
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.midGrey,
    borderWidth: 2,
  },
  buttonStyleAdd: {
    ...buttonStyleSaved,
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.lightPurple,
    borderWidth: 2,
  },
  codeShareTextStyles: {
    color: color.palette.darkestGrey,
    fontFamily: typography.regular,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 18,
    textAlign: "left",
    textAlignVertical: "top",
  },
  codeShareView: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginLeft: 4,
  },
  container: {
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 16,
    elevation: 5,
    shadowColor: color.palette.almostBlackGrey,
    shadowOffset: {
      height: 6,
      width: 0,
    },
    shadowOpacity: 0.05,
    shadowRadius: 20,
    width: width - scale(40),
  },
  departingDestinationCode: {
    color: color.palette.almostBlackGrey,
  },
  dotUnicodeStyle: {
    alignSelf: "center",
    color: color.palette.almostBlackGrey,
    marginLeft: 4,
  },
  emptyContent: {
    paddingTop: 0,
  },
  fastImageStyle: {
    height: 25,
    width: 25,
  },
  flightNumberStyle: {
    alignSelf: "center",
    color: color.palette.almostBlackGrey,
    marginLeft: 8,
  },
  infoIcon: {
    marginRight: 4,
  },
  innerContainer: {
    flexDirection: "row",
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 16,
  },
  newBottomContainer: {
    flex: 1,
    flexDirection: "row",
    marginLeft: 16,
    marginRight: 16,
    marginTop: 20,
    marginBottom: 16,
    alignItems: "center",
  },
  newBottomGateAndCheckInContainer: {
    marginRight: 12,
  },
  newBottomRightContainer: {
    flexDirection: "row",
    flex: 1,
  },
  newBottomSaveFlightContainer: {
    alignItems: "flex-end",
    flex: 1,
  },
  newBottomTerminalContainer: {
    marginRight: 16,
  },
  newFlightIcon: {
    marginRight: 8,
  },
  newFlightInfoContainer: {
    flex: 1,
  },
  newFlightNumberInfoView: {
    flexDirection: "row",
    marginTop: 8,
  },
  newFlightPlaceInfoView: {
    flexDirection: "row",
  },
  newFlightPlaceText: {
    color: color.palette.almostBlackGrey,
  },
  newFlightPlaceView: {
    flex: 1,
  },
  newFlightStatus: {
    color: color.palette.almostBlackGrey,
    textAlign: "left",
  },
  newFlightTimeView: {
    marginRight: 12,
    width: 74,
  },
  numberDaysReTimedText: {
    color: color.palette.almostBlackGrey,
    marginTop: 2,
    textAlign: "left",
  },
  oldDateLineThrough: {
    backgroundColor: color.palette.baseRed,
    height: 2,
    position: "absolute",
    top: 8,
    width: 52,
  },
  oldDateView: {
    marginBottom: 2,
    position: "relative",
  },
  terminalTextColor: {
    color: color.palette.black,
  },
  viaAirportContainer: {
    marginBottom: 8,
    marginTop: 16,
  },
  viaAirportContentWrap: {},
  viaAirportTextContent: {
    color: color.palette.darkestGrey,
    fontFamily: typography.regular,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 18,
  },
  viaAirportTitle: {
    color: color.palette.darkestGrey,
    fontFamily: typography.bold,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
    paddingLeft: 4,
  },
  viaAirportTitleWrap: {
    alignItems: "center",
    flexDirection: "row",
  },
  wrapButtonToolTip: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    borderRadius: 30,
    height: 60,
    justifyContent: "center",
    width: 60,
  },
  wrapLogoFlight: {
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    justifyContent: "center",
    overflow: "hidden",
    width: 24,
  },
})

const lighterGreyLoadingColors = [
  color.palette.lighterGrey,
  color.background,
  color.palette.lighterGrey,
]

const loadingStyles = StyleSheet.create({
  bottomLoadingSection: {
    flexDirection: "row",
    flex: 1,
    paddingTop: 20,
    paddingBottom: 16,
  },
  bottomLoadingSectionCol: {
    paddingRight: 16,
  },
  codeShareViewLoading: {
    flexDirection: "row",
  },
  ellipseLoading1: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 50,
    height: 24,
    width: 24,
  },
  ellipseLoading2: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 50,
    height: 20,
    width: 20,
  },
  newFlightNumberInfoViewLoading: {
    flexDirection: "row",
  },
  newFlightPlaceInfoView: {
    flexDirection: "row",
    flex: 1,
    marginBottom: 8,
  },
  newFlightPlaceView: {
    flex: 1,
  },
  rectangleLoading1: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    height: 24,
    width: 52,
  },
  rectangleLoading2: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    height: 24,
    width: 56,
  },
  rectangleLoading3: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    height: 14,
    marginBottom: 2,
    width: 64,
  },
  rectangleLoading4: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    height: 14,
    width: 40,
  },
  rectangleLoadingMaxWidth: {
    backgroundColor: color.palette.lighterGrey,
    borderRadius: 4,
    height: 24,
    width: "100%",
  },
  wrapLogoFlightLoading: {
    justifyContent: "center",
    marginLeft: 2,
    marginRight: 8,
  },
})

function loadingView() {
  return (
    <>
      <View style={styles.innerContainer}>
        <View style={styles.newFlightTimeView}>
          <ShimmerPlaceholder
            duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
            shimmerColors={lighterGreyLoadingColors}
            shimmerStyle={loadingStyles.rectangleLoading1}
          />
        </View>
        <View style={styles.newFlightInfoContainer}>
          <View style={loadingStyles.newFlightPlaceInfoView}>
            <View style={styles.newFlightIcon}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.ellipseLoading1}
              />
            </View>
            <View style={loadingStyles.newFlightPlaceView}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoadingMaxWidth}
              />
            </View>
          </View>
          <View style={loadingStyles.newFlightNumberInfoViewLoading}>
            <View style={loadingStyles.wrapLogoFlightLoading}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.ellipseLoading2}
              />
            </View>
            <View style={loadingStyles.codeShareViewLoading}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoading2}
              />
            </View>
          </View>
          <View style={loadingStyles.bottomLoadingSection}>
            <View style={loadingStyles.bottomLoadingSectionCol}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoading3}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoading4}
              />
            </View>
            <View style={loadingStyles.bottomLoadingSectionCol}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoading3}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lighterGreyLoadingColors}
                shimmerStyle={loadingStyles.rectangleLoading4}
              />
            </View>
          </View>
        </View>
      </View>
    </>
  )
}

const handleTopAdjustment = (inset) => {
  if (Platform.OS === "android") {
    if (inset?.top && !isNaN(inset?.top)) {
      return -inset?.top
    }
    return 0
  }
  return 0
}

const renderFlightCode = (flightNumber, codeShares) => {
  if (codeShares?.length > 0) {
    return (
      <>
        <Text preset={"caption1Bold"} style={styles.flightNumberStyle} text={flightNumber} />
        <Text preset={"caption1Bold"} style={styles.dotUnicodeStyle} text={dotUnicode} />
        <View style={styles.codeShareView}>
          {handleCondition(
            codeShares.length > 1,
            <>
              <AnimatedText
                delay={2000}
                codeShares={codeShares}
                key={codeShares.length.toString()}
              />
              <Text text={` (+${codeShares.length - 1})`} style={styles.codeShareTextStyles} />
            </>,
            <Text text={codeShares[0]} style={styles.codeShareTextStyles} />,
          )}
        </View>
      </>
    )
  }
  return <Text preset={"caption1Bold"} style={styles.flightNumberStyle} text={`${flightNumber}`} />
}

const renderTransitCode = (transitCodes) => {
  if (transitCodes?.length > 0) {
    return (
      <View style={styles.bottomTextContainer}>
        <InfoFilled style={styles.infoIcon} />
        <Text
          preset={"caption1Italic"}
          style={styles.bottomTextStyle}
          text={translate("flightListing.infoText")}
        />
      </View>
    )
  }
  return <View style={styles.emptyContent} />
}

const checkFlightCanSave = (props) => {
  const flightStatusCheckSave = props?.flightStatus
  const status = flightStatusCheckSave?.toLowerCase()
  const priorityTime =
    props?.actualTimestamp ||
    props?.estimatedTimestamp ||
    `${props?.scheduledDate} ${props?.scheduledTime}`
  const currentTimeToUTC = moment().tz("Asia/Singapore")
  const flightTime = moment(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
  if (FlightDirection.departure === props?.direction) {
    switch (true) {
      case /departed/gim.test(status):
      case /cancelled/gim.test(status):
        return false
      default:
        return true
    }
  }
  switch (true) {
    case /cancelled/gim.test(status):
    case /landed/gim.test(status) &&
      moment(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") <
        currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
      return false
    default:
      return true
  }
}

const renderCheckInRow = (checkInRow) => {
  return (
    <View style={styles.newBottomGateAndCheckInContainer}>
      <Text
        preset={"caption2Regular"}
        tx={"flightDetails.checkInRow"}
        style={styles.terminalTextColor}
      />
      <Text preset={"caption2Bold"} text={checkInRow || "-"} style={styles.terminalTextColor} />
    </View>
  )
}
const renderBaggageGate = (displayBelt) => {
  return (
    <View style={styles.newBottomGateAndCheckInContainer}>
      <Text
        preset={"caption2Regular"}
        tx={"flightDetails.baggageGate"}
        style={styles.terminalTextColor}
      />
      <Text preset={"caption2Bold"} text={displayBelt || "-"} style={styles.terminalTextColor} />
    </View>
  )
}

const viaText = (viaAirportDetails) => {
  if (!viaAirportDetails) return <></>
  return (
    <View style={styles.viaAirportContainer}>
      <View style={styles.viaAirportTitleWrap}>
        <ViaArrow />
        <Text text="Via" style={styles.viaAirportTitle} />
      </View>
      <View style={styles.viaAirportContentWrap}>
        <Text text={viaAirportDetails?.name} style={styles.viaAirportTextContent} />
      </View>
    </View>
  )
}

function defaultView(props: FlightListingProps) {
  const {
    logo,
    flightNumber,
    codeShare,
    flightStatusMapping,
    transits: transitCodes,
    timeOfFlight,
    departingPlace,
    destinationPlace,
    isSaved,
    onPressed,
    onSaved,
    scheduledDate,
    isLoggedIn,
    isMSError,
    isSaveFlightLoading,
    itemIndex,
    statusColor,
    direction,
    terminal,
    checkInRow,
    displayBelt,
    displayTimestamp,
    beltStatusMapping,
    viaAirportDetails,
    isFirstFlight,
  } = props

  const [needShowToolTip, setShowToolTip] = useState(false)
  const inset = useSafeAreaInsets()
  const route = useRoute()
  const isFocused = useIsFocused()
  const activeToolTipAemData = usePersistStore((state) => state.activeToolTipAemData)
  const touchableCardRef = useRef(null)
  const touchableSaveRef = useRef(null)
  const isCanSavedFlight = checkFlightCanSave(props)
  const dispatch = useDispatch()

  const activeToolTip = useMemo(() => {
    return activeToolTipAemData?.list?.find((e) => e?.iconType === "plus")
  }, [activeToolTipAemData])

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (
        ifAllTrue([
          itemIndex === 0,
          route?.name === "flights",
          direction === FlightDirection.arrival,
        ])
      ) {
        const checkToolTip = async () => {
          const openFlyScreenBefore = await load(StorageKey.needDisableToolTipFlyScreen)
          if (ifAllTrue([!openFlyScreenBefore, !isEmpty(activeToolTipAemData?.list)])) {
            setShowToolTip(true)
            save(StorageKey.needDisableToolTipFlyScreen, true)
            const timeOutCloseToolTip = setTimeout(() => {
              // set condition to scroll to top
              dispatch(FlyCreators.setScrollToTopAfterShowTooltip(true))
              setShowToolTip(false)
            }, 5000)
            return () => {
              clearTimeout(timeOutCloseToolTip)
            }
          }
        }
        checkToolTip()
      }
    })
  }, [])

  const showFlightTime = () => {
    let mainTime = timeOfFlight
    let reTimeFlag = false
    let numberDaysDiff = 0
    if (displayTimestamp) {
      mainTime = displayTimestamp?.split(" ")[1]
      const mainDate = displayTimestamp?.split(" ")[0]
      if (ifOneTrue([scheduledDate !== mainDate, timeOfFlight !== mainTime])) {
        reTimeFlag = true
        numberDaysDiff = moment(mainDate).diff(moment(scheduledDate), "days")
      }
    }
    return (
      <>
        {simpleCondition({
          condition: reTimeFlag,
          ifValue: (
            <View style={styles.oldDateView}>
              <Text preset={"h3"} style={styles.departingDestinationCode} text={timeOfFlight} />
              <View style={styles.oldDateLineThrough}></View>
            </View>
          ),
          elseValue: <></>,
        })}
        <Text preset={"h3"} style={styles.departingDestinationCode} text={mainTime} />
        {simpleCondition({
          condition: ifAllTrue([reTimeFlag, !!numberDaysDiff]),
          ifValue: (
            <Text
              preset={"caption1Bold"}
              style={styles.numberDaysReTimedText}
              text={simpleCondition({
                condition: numberDaysDiff > 0,
                ifValue: `(+${numberDaysDiff})`,
                elseValue: `(${numberDaysDiff})`,
              })}
            />
          ),
          elseValue: <></>,
        })}
      </>
    )
  }

  const shouldShowToolTip = ifAllTrue([
    needShowToolTip,
    isFirstFlight,
    store.getState().flyReducer?.isShowTooltipAfterScroll,
    isFocused,
  ])

  const getBottomSaveFlightContainer: ViewStyle = useMemo(() => {
    if (shouldShowToolTip) {
      return {
        position: "absolute",
        right: -16,
        top: -16,
      }
    } else {
      return styles.newBottomSaveFlightContainer
    }
  }, [isFirstFlight, needShowToolTip])


  const renderNewBottomGateAndCheckInContainer = useCallback(() => {
    if (direction === FlightDirection.departure) {
      return renderCheckInRow(checkInRow)
    } else {
      return renderBaggageGate(displayBelt)
    }
  }, [direction, checkInRow, displayBelt])

  const onSavePress = useCallback(() => {
    if(isSaveFlightLoading){
      return;
    }
    onSaved(isSaved) 
  }, [onSaved, isSaved])

  const buttonSavedFlight = useCallback(() => {
    return (
      <TouchableOpacity
        onPress={onSavePress}
        style={handleCondition(
          isLoggedIn && isSaved,
          buttonStyleSaved,
          handleCondition(
            ifAllTrue([isCanSavedFlight]),
            styles.buttonStyleAdd,
            styles.buttonDisabledStyleAdd,
          ),
        )}
        disabled={ifOneTrue([isSaveFlightLoading])}
        ref={touchableSaveRef}
      >
        {handleCondition(
          isLoggedIn && isSaved,
          <CheckSavedFlight />,
          handleCondition(
            ifAllTrue([isCanSavedFlight]),
            <Plus />,
            <PlusDisabled color={color.palette.midGrey} />,
          ),
        )}
      </TouchableOpacity>
    )
  }, [isSaveFlightLoading, isLoggedIn, isSaved, isCanSavedFlight])

  return (
    <TouchableOpacity onPress={onPressed} disabled={isSaveFlightLoading} ref={touchableCardRef}>
      <View style={styles.innerContainer}>
        <View style={styles.newFlightTimeView}>{showFlightTime()}</View>
        <View style={styles.newFlightInfoContainer}>
          <View style={styles.newFlightPlaceInfoView}>
            <View style={styles.newFlightIcon}>
              {direction === FlightDirection.departure ? (
                <FlightDepartureIconActive />
              ) : (
                <FlightArrivalIconActive />
              )}
            </View>
            <View style={styles.newFlightPlaceView}>
              <Text
                preset={"h3"}
                style={styles.newFlightPlaceText}
                numberOfLines={2}
                text={simpleCondition({
                  condition: direction === FlightDirection.departure,
                  ifValue: destinationPlace,
                  elseValue: departingPlace,
                })}
              />
            </View>
          </View>
          {viaText(viaAirportDetails)}
          <View style={styles.newFlightNumberInfoView}>
            <View style={styles.wrapLogoFlight}>
              <BaseImage
                source={{ uri: logo }}
                style={styles.fastImageStyle}
                resizeMode={"cover"}
              />
            </View>
            {renderFlightCode(flightNumber, codeShare)}
          </View>
        </View>
      </View>
      <View style={styles.newBottomContainer}>
        <View style={styles.newFlightTimeView}>
          {statusColor && (
            <Text
              preset={"caption1Bold"}
              style={[styles.newFlightStatus, { color: statusColor?.toLowerCase() }]}
              text={simpleCondition({
                condition: isEmpty(beltStatusMapping),
                ifValue: flightStatusMapping,
                elseValue: beltStatusMapping,
              })}
              numberOfLines={2}
            />
          )}
        </View>
        <View style={styles.newBottomRightContainer}>
          <View style={styles.newBottomTerminalContainer}>
            <Text
              preset={"caption2Regular"}
              tx={"flightDetails.terminal"}
              style={styles.terminalTextColor}
            />
            <Text
              preset={"caption2Bold"}
              text={simpleCondition({
                condition: terminal,
                ifValue: terminal,
                elseValue: "-",
              })}
              style={styles.terminalTextColor}
            />
          </View>
          {renderNewBottomGateAndCheckInContainer()}
          <View style={getBottomSaveFlightContainer}>
            {isMSError ? null : (
              <Tooltip
                isVisible={shouldShowToolTip}
                content={
                  <Text
                    text={simpleCondition({
                      condition: activeToolTip?.tooltip,
                      ifValue: activeToolTip?.tooltip,
                      elseValue: translate("flyLanding.toolTipFly"),
                    })}
                    preset={"caption1Regular"}
                  />
                }
                displayInsets={{ top: 44, bottom: 24, left: width * 0.44, right: 20 }}
                placement="bottom"
                onClose={() => {
                  // set condition to scroll to top
                  dispatch(FlyCreators.setScrollToTopAfterShowTooltip(true))
                  setShowToolTip(false)
                }}
                disableShadow={true}
                topAdjustment={handleTopAdjustment(inset)}
              >
                {shouldShowToolTip ? (
                  <View style={styles.wrapButtonToolTip}>
                    <View style={styles.buttonStyleAdd}>
                      <Plus />
                    </View>
                  </View>
                ) : (
                  buttonSavedFlight()
                )}
              </Tooltip>
            )}
          </View>
        </View>
      </View>
      {renderTransitCode(transitCodes)}
    </TouchableOpacity>
  )
}

const FlightListingCard = (props: FlightListingProps) => {
  const isLoading = props.state === FlightListingState.loading || props?.getSavedFlightsLoading
  return <View style={styles.container}>{isLoading ? loadingView() : defaultView(props)}</View>
}

export default React.memo(FlightListingCard)
