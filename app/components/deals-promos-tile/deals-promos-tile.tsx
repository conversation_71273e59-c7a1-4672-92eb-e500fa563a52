import { Text } from "app/elements/text"
import { LayoutChangeEvent, View } from "react-native"
import { styles } from "../staff-perk-tile-v2/staff-perk-tile-v2.styles"
import BaseImage from "app/elements/base-image/base-image"
import moment from "moment"
import { DateFormats } from "app/utils/date-time/date-time"
import { MultimediaTouchableOpacity } from "../multimedia-touchable-opacity/multimedia-touchable-opacity"
import { useMemo } from "react"
import { StaffPerkClockIcon, StarPerk } from "ichangi-fe/assets/icons"
import { color } from "app/theme"
import { DealsPromosCategory } from "app/sections/deals-promos/deals-promos-filter-bar/deals-promos-filter-bar.constants"
import StaffPerkPromotionDetailController from "../staff-perk-promotion-detail/staff-perk-promotion-detail-controller"
import { NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"

interface PropsType {
  dataLength?: number
  index?: number
  item?: any
  navigation?: any
  onLayout?: (event: LayoutChangeEvent) => void
}

const COMPONENT_NAME = "DealsPromosTile"

const DealsPromosTile = (props: PropsType) => {
  const { dataLength, index, item, navigation, onLayout } = props

  const startDateValue = item?.campaignStartDate
    ? moment(item?.campaignStartDate).format(DateFormats.DayMonthYear)
    : ""
  const showLimitedOffer = item?.type?.toLowerCase() === "seasonal"
  const showNewlyAdded = item?.categories?.some?.((val) => val === DealsPromosCategory.NewlyAdded)
  const containerStyle = useMemo(() => {
    const result: any[] = [styles.containerStyle]
    if (index === 0) {
      result.push(styles.firstItemContainerStyle)
    }
    if (index === dataLength - 1) {
      result.push(styles.lastItemContainerStyle)
    }
    return result
  }, [dataLength, index])

  const handleOpenDetail = () => {
    StaffPerkPromotionDetailController.showModal(navigation, { item })
  }

  return (
    <MultimediaTouchableOpacity
      accessibilityLabel={`${COMPONENT_NAME}_Container`}
      onLayout={onLayout}
      onPress={handleOpenDetail}
      style={containerStyle}
      testID={`${COMPONENT_NAME}_Container`}
    >
      <BaseImage
        accessibilityLabel={`${COMPONENT_NAME}_Image`}
        source={{ uri: item?.imageUrl }}
        style={styles.imageStyle}
        testID={`${COMPONENT_NAME}_Image`}
      />
      <View style={styles.contentContainerStyle}>
        <Text
          accessibilityLabel={`${COMPONENT_NAME}_TenantName`}
          style={styles.tenantNameTextStyle}
          testID={`${COMPONENT_NAME}_TenantName`}
          text={item?.tenantName || item?.merchantName}
          numberOfLines={1}
        />
        {showLimitedOffer && (
          <View
            accessibilityLabel={`${COMPONENT_NAME}_Label_LimitedOffer`}
            style={styles.perkLabelContainerStyle}
            testID={`${COMPONENT_NAME}_Label_LimitedOffer`}
          >
            <StaffPerkClockIcon color={color.palette.lightOrange} height={12} width={12} />
            <Text style={styles.perkLabelTextStyle} tx="dealsPromosListing.item.limitedOffer" />
          </View>
        )}
        {showNewlyAdded && (
          <View
            accessibilityLabel={`${COMPONENT_NAME}_Label_NewlyAdded`}
            style={styles.perkLabelContainerStyle}
            testID={`${COMPONENT_NAME}_Label_NewlyAdded`}
          >
            <StarPerk color={color.palette.lightOrange} height={12} width={12} />
            <Text style={styles.perkLabelTextStyle} tx="dealsPromosListing.item.newlyAdded" />
          </View>
        )}
        <Text
          accessibilityLabel={`${COMPONENT_NAME}_Title`}
          numberOfLines={3}
          style={styles.titleTextStyle}
          testID={`${COMPONENT_NAME}_Title`}
          text={item?.title}
        />
        <Text
          accessibilityLabel={`${COMPONENT_NAME}_StartDate`}
          numberOfLines={3}
          style={styles.startDateTextStyle}
          testID={`${COMPONENT_NAME}_StartDate`}
          tx={startDateValue ? "dealsPromosListing.item.fromDateMsg" : ""}
          txOptions={{ date: startDateValue }}
        />
      </View>
    </MultimediaTouchableOpacity>
  )
}

export default DealsPromosTile
