import { Dynatrace } from "@dynatrace/react-native-plugin"
import analytics from "@react-native-firebase/analytics"
import DeviceInfo from "react-native-device-info"
import {Platform} from 'react-native'
import { getEnvSetting } from "app/utils/env-settings"

const Settings = getEnvSetting()

const defaultEventType = `ChangiApp-${Settings?.ENV?.toUpperCase()}-${Platform.OS.toUpperCase()}`;

 const deviceId = DeviceInfo.getUniqueIdSync();

 const deviceName = DeviceInfo.getDeviceNameSync();

export enum ANALYTICS_LOG_EVENT_NAME {
  LANDING = "landing",
  FLIGHT_LANDING = "flight_landing",
  FLIGHT_DEP_LIST = "flight_dep_list",
  FLIGHT_ARR_LIST = "flight_arr_list",
  SAVED_FLIGHT = "saved_flight",
  CHANGI_PAY_LANDING = "changi_pay_landing",
  PLAYPASS_LANDING = "playpass_landing",
  PBE_LANDING = "pbe_landing",
  APPSCAPADE_LANDING = "appscapade_landing",
  SAVEFLIGHT_DEPARTURE = "saveflight_departure",
  SAVEFLIGHT_ARRIVAL = "saveflight_arrival",
  STAFF_PERKS_LISTING_TITLE_CLICKING = "staff_perks_listing_tile_clicking",
  STAFF_PERKS_LISTING_TITLE_OLD = "staff_perks_listing_tile_old",
  STAFF_PERKS_LISTING_TITLE_NEW = "staff_perks_listing_tile_new",
}

export enum ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME {
  FACILITIES_SERVICES = "facilities_services",
  FACILITIES_SERVICES_VIEW_ALL = "fs_view_all",
  FLIGHT_INFO_DETAILS = "flight_info_details",
  GETTING_AROUND_CHANGI_AIRPORT = "getting_around_changi",
  GETTING_TO_CHANGI_AIRPORT = "getting_to_changi",
  SHOP_TAB = "shop_tab",
  DINE_TAB = "dine_tab",
  FACILITIES_TAB = "facilities_tab",
  USER_PROFILE_SELECTOR = "profile_selector",
  SAVE_FLIGHT_BUTTON = "save_button",
  UNSAVE_FLIGHT_BUTTON = "unsave_button",
  ONLINE_CHECK_IN = "flight_check_in",
  EARLY_CHECK_IN = "flight_check_in",
  SHARE_FLIGHT_BUTTON = "share_button",
  ATOMS = "atoms",
  TRACK_BAGGAGE = "track_baggage",
}

export enum DT_ANALYTICS_LOG_EVENT_NAME {
  DT_FLY_SCREEN_FOCUS = "dt_fly_screen_focus",
  DT_GET_EXPLORE_DATA = "dt_get_explore_data",
  DT_FLIGHT_LANDING_ARRIVAL = "dt_flight_landing_arrival",
  DT_FLIGHT_LANDING_DEPARTURE = "dt_flight_landing_departure",
  DT_GET_LATEST_HAPPENING = "dt_get_latest_happening",
  DT_GET_EXPLORE_SHORTCUT_LINK = "dt_get_explore_shortcut_link",
  DT_GET_JUST_FOR_YOU = "dt_get_just_for_you",
  DT_GET_FLY_TICKER_BAND = "dt_get_fly_ticker_band",
  DT_GET_PAGE_CONFIG = "dt_get_page_config",
  DT_GET_FLIGHT_FILTER_OPTIONS = "dt_get_flight_filter_options",
  DT_GET_NOTIFICATION_COUNT = "dt_get_notification_count",
  DT_GET_SAVED_FLIGHT = "dt_get_saved_flight",
  DT_GET_CITY_CODES = "dt_get_city_codes",
  DT_GET_CONFIGURATIONS = "dt_get_configurations",
  DT_GET_RECOMMENDED_PRODUCTS = "dt_get_recommended_products",
  DT_PUT_USER_DEVICE_TOKEN = "dt_put_user_device_token",
  DT_GET_USER_NOTIFICATIONS = "dt_get_user_notifications",
  DT_GET_REWARD_DETAIL = "dt_get_reward_detail",
  DT_GET_PLAYPASS_BOOKINGS = "dt_get_playpass_bookings",
  DT_GET_PENDING_PLAYPASS_BOOKING = "dt_get_pending_playpass_bookings",
  DT_POST_VALIDATE_TOKEN = "dt_post_validate_token",
  DT_GET_UPCOMING_EVENT = "dt_get_upcoming_event",
  DT_GET_UPCOMING_FLIGHTS = "dt_get_upcoming_flights",
  DT_GET_ACCOUNT_PROFILE = "dt_get_account_profile",
  DT_GET_CONSOLIDATED_STRUCTURES = "dt_get_consolidated_Structures",
  DT_GET_CONSOLIDATED_AEM_GROUP_ONE = "dt_get_consolidated_aem_group_one",
  DT_GET_CONSOLIDATED_AEM_GROUP_TWO = "dt_get_consolidated_aem_group_two",
  DT_POST_REGISTER = "dt_post_register",
  DT_POST_UPDATE_LOCATION_PREFERENCE = "dt_post_update_location_preference",

  DT_AEM_GET_FORCED_UPDATE = "dt_aem_get_forced_update",
  DT_AEM_GET_ATTRACTIONS_FILTER = "dt_aem_get_attractions_filter",
  DT_AEM_GET_ACTIVE_TOOLTIP = "dt_aem_get_active_tooltip",

  DT_RETRO_CLAIM_GET_CONFIGS = "dt_retro_claim_get_configs",
  DT_RETRO_CLAIM_SUBMIT_IMAGE = "dt_retro_claim_submit_image",
  DT_RETRO_CLAIM_TAKE_PHOTO = "dt_retro_claim_take_photo",
  DT_RETRO_CLAIM_OPEN_GALLERY = "dt_retro_claim_open_gallery",

  DT_BAGGAGE_TRACKER_GET_CONFIGS = "dt_baggage_tracker_get_configs",
  DT_BAGGAGE_TRACKER_TAKE_PHOTO = "dt_baggage_tracker_take_photo",
  DT_BAGGAGE_TRACKER_OPEN_GALLERY = "dt_baggage_tracker_open_gallery",
}

export enum SECURITY_EVENT_KEYS {
  STATUS = "event.status",
  ERROR_CODE = "event.errorCode",
  LAST_LOGGED_IN_EMAIL = "lastLoggedInemail",
  LAST_LOGGED_IN_UID = "lastLoggedInUid",
  LAST_LOGGED_IN_TIMESTAMP = "lastLoggedIntimestamp",
  UID = "currentUid",
  CURRENT_EMAIL = "currentEmail",
  DETAIL = "detail",
}
export enum DT_SECURITY_EVENT_NAME {
  DT_EMULATOR_DETECTED_FOR_EMAIL_LOGIN_SUCCESS = "dt_emulator_detected_for_email_login_success",
  DT_EMULATOR_DETECTED_FOR_EMAIL_SIGNUP_EMAIL_VERIFICATION = "dt_emulator_detected_for_email_signup_email_verification",
  DT_SIGN_UP_ACCOUNT_AND_PROFILE_CREATED = "dt_sign_up_account_and_profile_created",
  DT_ROOTED_DEVICE_DETECTED = "rooted_device_detected",
  DT_EMAIL_LOGIN_SUCCESS = "dt_email_login_success",
}
export const DT_SECURITY_EVENT ="dt_security_event";

export const FE_LOG_PREFIX = "FElog__"

export const convertStringValue = (value) => {
  if (value) {
    return value?.toString().replace(/[^a-zA-Z0-9]/g, '_')
  } else if (value === 0) {
    return "0"
  }
  return ""
}
export const convertBooleanValue = (currentNeedToRefreshStatus) => {
  if (typeof currentNeedToRefreshStatus !== "boolean" ) {
    return "null"
  } else {
    if (currentNeedToRefreshStatus) {
      return "true"
    } else {
      return "false"
    }
  }
}

export const analyticsLogEvent = (name: ANALYTICS_LOG_EVENT_NAME | string, params = null) => {
  if (params) {
    return analytics().logEvent(name, params)
  }
  return analytics().logEvent(name)
}

export const dtACtionLogEvent = (name: ANALYTICS_LOG_EVENT_NAME | DT_ANALYTICS_LOG_EVENT_NAME | string, params = null) => {

  if(params){
    let myAction = Dynatrace.enterAutoAction(name)
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
          myAction.reportStringValue(key, params[key]);
      }
  }
  myAction.leaveAction()
  }
  if(name){
    let myAction = Dynatrace.enterAutoAction(name)
    myAction.leaveAction()
  }
 }

export const dtActionEvent = (name: ANALYTICS_LOG_EVENT_NAME | DT_ANALYTICS_LOG_EVENT_NAME | string, params = null) => {
  // ìf using this function, you should use leaveAction to cancel it
  const myAction = Dynatrace.enterAutoAction(name)
  if (params) {
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        myAction.reportStringValue(key, params[key])
      }
    }
  }
  return myAction
}

export const dtManualActionEvent = (name: ANALYTICS_LOG_EVENT_NAME | DT_ANALYTICS_LOG_EVENT_NAME | string) => {
  const action = Dynatrace.enterManualAction(name)
  action.reportStringValue("device_name", convertStringValue(deviceName))
  action.reportStringValue("device_id", deviceId)
  return action
}

export const dtLeaveActionEvent = (myAction) => {
  myAction?.leaveAction()
}

export const dtBizEvent = (event_provider: string, event_name: string,event_category: string, event_details = null )=>{

  Dynatrace.sendBizEvent(defaultEventType,{
    "event.category" : event_category,
    "event.name" : event_name,
    "screen": event_provider,
   // "uid": uid,
    "device.name": deviceName,
    "device.id": deviceId,
    ...event_details
  });
}

