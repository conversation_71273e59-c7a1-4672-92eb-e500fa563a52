import remoteConfig from "@react-native-firebase/remote-config"

// the caching time - 5 minutes - milliseconds
const FREQUENCY = 5 * 60 * 1000

// connection timeout  - 5 seconds - milliseconds
const TIME_OUT = 5 * 1000

export enum REMOTE_FLAG_VALUE {
  ON = "ON",
  OFF = "OFF",
}

export enum REMOTE_CONFIG_FLAGS {
  APPSCAPADE_FLIGHTDETAILS = "APPSCAPADE_FLIGHTDETAILS",
  APPSCAPADE_FLIGHTLANDING = "APPSCAPADE_FLIGHTLANDING",
  ATOMS_MAP = "ATOMS_MAP",
  EXPLORE_SCROLLBUDDY = "EXPLORE_SCROLLBUDDY",
  COLLECT_CLICKSTREAM = "COLLECT_CLICKSTREAM",
  EXPLORE_JUSTFORYOU = "EXPLORE_JUSTFORYOU",
  EXPLORE_STAFFPERKS = "EXPLORE_STAFFPERKS",
  GROUND_TRANSPORT_TIMINGS_DISPLAY = "GROUND_TRANSPORT_TIMINGS_DISPLAY",
  SHOP_JUSTFORYOU = "SHOP_JUSTFORYOU",
  TENANTDETAILS_STAFFPERKS = "TENANTDETAILS_STAFFPERKS",
  ECI_DYNAMIC_DISPLAY = "ECI_DYNAMIC_DISPLAY",
  BAGGAGE_PREDICTION_TIMING = "BAGGAGE_PREDICTION_TIMING",
  CSM_CM24 = "CSM_CM24",
  ACCOUNT_V2_CM24 = "ACCOUNT_V2_CM24",
  APPRATING_SAVEFLIGHT = "APPRATING_SAVEFLIGHT",
  CIAM_UNLINKSOCIAL= "CIAM_UNLINKSOCIAL",
  TXNHISTORY_RETROCLAIMS = "TXNHISTORY_RETROCLAIMS",
  TXNHISTORY_PENDINGCLAIMS = "TXNHISTORY_PENDINGCLAIMS",
  L2_ANNOUNCEMENT = "L2_ANNOUNCEMENT",
  NOTIFICATION_INBOX_V2 = "NOTIFICATION_INBOX_V2",
  STAFFPERKS_TILESV2 = "STAFFPERKS_TILESV2",
  CIAM_SOCIALLINKING = "CIAM_SOCIALLINKING",
  ACCOUNT_CONTACTUS = "ACCOUNT_CONTACTUS",
  VCEA_LIVECHAT = "VCEA_LIVECHAT",
  NOTIFICATION_PREFERENCES_V2 = "NOTIFICATION_PREFERENCES_V2",
  CSM_GROUPBUY = "CSM_GROUPBUY",
  CIAM_BIOMETRICS = "CIAM_BIOMETRICS",
  L1_ANNOUNCEMENT = "L1_ANNOUNCEMENT",
  FLIGHT_DETAILS_P1 = "FLIGHT_DETAILS_P1",
  ATOMS_ROUTING = "ATOMS_ROUTING",
  SHOPDINE_EPIC = "SHOPDINE_EPIC",
  SEARCHV2_EPIC = "SEARCHV2_EPIC",
  PROJECTFIRST_FLIGHTDETAILS = "PROJECTFIRST_FLIGHTDETAILS",
  MIFFYGAME_VPR = "MIFFYGAME_VPR",
  PROJECTFIRST_FLIGHTJOURNEY = "PROJECTFIRST_FLIGHTJOURNEY",
  MIFFYGAME_ACCOUNT = "MIFFYGAME_ACCOUNT",
  DRIVE_PARKING = "DRIVE_PARKING",
  CSM_CARPARSS = "CSM_CARPASS",
  NATIVE_ONBOARDING_SCREEN = "NATIVE_ONBOARDING_SCREEN",
  ACCOUNT_PROMOCODES = "ACCOUNT_PROMOCODES",
  DRIVE_CSAT = "DRIVE_CSAT",
  FLIGHT_SAVEPROMPT = "FLIGHT_SAVEPROMPT",
  SHOPDINE_V2 = "SHOPDINE_V2",
  SHOPDINE_V2_JUSTFORYOU = "SHOPDINE_V2_JUSTFORYOU",
  FLY_LANDING = "FLY_LANDING",
  EXPLORE_V2 = "EXPLORE_V2"
}

const DEFAULT_VALUE = {
  [REMOTE_CONFIG_FLAGS.EXPLORE_SCROLLBUDDY]: "",
  [REMOTE_CONFIG_FLAGS.ATOMS_MAP]: "",
  [REMOTE_CONFIG_FLAGS.SHOP_JUSTFORYOU]: "",
  [REMOTE_CONFIG_FLAGS.EXPLORE_JUSTFORYOU]: "",
  [REMOTE_CONFIG_FLAGS.COLLECT_CLICKSTREAM]: "",
}

remoteConfig().setConfigSettings({
  minimumFetchIntervalMillis: FREQUENCY,
  fetchTimeMillis: TIME_OUT,
})

remoteConfig().setDefaults(DEFAULT_VALUE)

export const remoteConfigGetAllValue = () => {
  // if the system don't `fetch` data, the cache will always be there 
  const newObj = {}
  const parameters = remoteConfig().getAll()
  Object.entries(parameters).forEach((item) => {
    const [key, entry] = item
    newObj[key] = { value: entry.asString(), source: entry.getSource() }
  })
  return newObj
}

export const remoteConfigGetValue = (key) => {
  // if the system don't `fetch` data, the cache will always be there
  return remoteConfig().getValue(key).asString()
}

export const rmFetchAndActive = async () => {
  try {
    // fetchedRemotely - true - Configs were retrieved from the backend and activated
    // fetchedRemotely - false - No configs were fetched from the backend, and the local configs were already activated
    const fetchedRemotely = await remoteConfig().fetchAndActivate()
    const parameters = remoteConfig().getAll()
    const remoteAllData = {}
    Object.entries(parameters).forEach((item) => {
      const [key, entry] = item
      remoteAllData[key] = { value: entry.asString(), source: entry.getSource() }
    })

    return { status: true, fetchedRemotely: fetchedRemotely, remoteData: remoteAllData, }
  } catch (error) {
    return { status: false, fetchedRemotely: null, remoteData: null }
  }
}

export const isFlagON = (flagName) => {
  const getRemoteConfig = remoteConfigGetValue(flagName)
  return (
    getRemoteConfig &&
    typeof getRemoteConfig === "string" &&
    getRemoteConfig.trim() === REMOTE_FLAG_VALUE.ON
  )
}

export const isFlagOnCondition = (flag) =>
  flag && typeof flag === "string" && flag.trim() === REMOTE_FLAG_VALUE.ON

export const getFeatureFlagInit = (flagName: string, value: string | null = null) => {
  try {
    // #1 from context
    if (value !== null) {
      return Boolean(isFlagOnCondition(value))
    }
    // #2 get from local storage or remote
    const result = Boolean(isFlagON(flagName))
    return result
  } catch (error) {
    return false // Default fallback value
  }
}