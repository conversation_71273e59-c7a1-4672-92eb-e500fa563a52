{"getAccountProfileDetails": "/users", "getOcidToken": "POST /getocidtoken", "getResetPasswordUrl": "GET /reset_password", "signUpAPI": "POST /account-signup", "signUpContent": "GET /bin/ichangi/account/email-sign-up", "getAccountAemContent": "GET /account_aem", "updateAccountProfile": "POST /update_account_profile", "checkExistEmail": "GET /check_email", "getRedemptionDetails": "GET /get-redeem-reward-details", "getRedemptionProgrammePoint": "POST /get-redeem-programme-point", "getLoginAemContent": "GET /bin/ichangi/account/login-page-content", "getProfileAemContent": "GET /bin/ichangi/account/profile/edit-profile", "getDeleteAemContent": "GET /bin/ichangi/account/delete-account", "errorHandlingPath": "GET /bin/ichangi/errors", "resendActivationLink": "POST /resend_activation_link", "getRedeemSuccessfullContent": "GET /bin/ichangi/account/changi-rewards/redeemtions/flow", "getRedemptionCatalogue": "GET /get-redemption-catalogue", "getRedemptionHeroImage": "GET /bin/ichangi/foryou/redemptionheroimage", "getForYouMoreOptions": "GET /bin/ichangi/foryou/for-you-ice-cream-sticks", "getYourBenefit": "GET /bin/ichangi/foryou/horizontalcontentcard", "getRewardDetail": "GET /get-reward-detail", "getChangiRewardsMember": "GET /bin/ichangi/foryou/changirewardsmember/v2", "getRedemptionNewReward": "GET /get-redeem-new-rewards", "getChangiReward": "GET /changi-rewards", "getPrivilegesList": "GET /privileges-list", "getShortcutLinks": "GET /bin/ichangi/account/shortcut-links", "getFlyContentShortLink": "GET /bin/ichangi/fly/shortcut-links-fly", "getAirportContentShortLink": "GET /bin/ichangi/fly/shortcut-links-airport", "getPrivacyPolicy": "GET /bin/ichangi/account/terms-and-conditions/privacy-policy", "getTermsOfUsePolicy": "GET /bin/ichangi/account/terms-and-conditions/terms-of-use-policy", "getExploreMoreRewards": "GET /get-explore-more-rewards", "getAemCommonData": "GET /bin/ichangi/app-common-data", "getSuggestionAndFeedback": "GET /bin/ichangi/account/suggestions-and-feedback", "getFlyLandingDynamicBlock": "GET /bin/ichangi/fly/timeline-tiles-v3-content", "getRedeemExploreMoreRewards": "POST /get-redeem-explore-more-rewards", "getSpecialAssistances": "GET /bin/ichangi/fly/card-with-illustrations-section", "getDataPageLevel": "GET /bin/ichangi/page-level/v2", "getFacilitiesServices": "GET /bin/ichangi/fly/facilities-services", "getFlightDetailPerks": "GET /bin/ichangi/fly/perk", "getMobileGst": "GET /bin/ichangi/fly/mobile-gst", "getAirportContent": "GET /bin/ichangi/fly/airport-l2-content", "suggestionAndFeedback": "POST /suggestion-feedback", "getTransactions": "POST /transaction-details", "getPendingPoints": "GET /pending-points", "getLocationFiltersFlight": "GET /bin/ichangi/fly/filter", "getLocationFilterFacilitiesService": "GET /bin/ichangi/facilities-service/filter", "getTermAndConditionForOldUser": "GET /bin/ichangi/account/terms-and-conditions/old-user", "updateUserDetails": "POST /update-account-details", "filterPlayPassAccount": "GET /bin/ichangi/account/filter", "termsPlayPassAccount": "GET /bin/ichangi/foryou/how-to-use-and-terms-conditions/wallet-landing", "swipeToRedeem": "POST /swipe-to-redeem", "getAccountProfile": "GET /get_account_profile", "horizontalContentCardDetail": "GET /bin/ichangi/foryou/horizontalcontentcard/detail", "getParkingPrivileges": "GET /bin/ichangi/promo/rewardsinfo/parking", "getFlyDetailPage": "GET /bin/ichangi/fly/flydetailssectionbreak", "upComingFlightExplore": "GET /bin/ichangi/explore/card-swimlane", "getDataToolTip": "GET /bin/ichangi/tooltip/active-tooltip", "getListMarketPlace": "GET /bin/ichangi/dineandshop/marketplaces", "getTickerbandFly": "GET /bin/ichangi/fly/maintenance-mode/v2", "getExploreChangiLocation": "GET /bin/ichangi/attractions/filter", "getPageLevelConfig": "GET /bin/ichangi/page-level/v2", "getJewelPriviligeCard": "GET /bin/ichangi/foryou/horizontalcontentcard/privilegecard", "dineReservationQuickLink": "GET /bin/ichangi/dineandshop/shortcut-links", "getFilterDineShop": "GET /bin/ichangi/dineandshop/filter", "getConfigurations": "POST /get-configurations", "getDataPlayWithJewelMembership": "GET /bin/ichangi/foryou/contentcard/play-at-jewel-membership", "getErrorMaintenance": "GET /bin/ichangi/error-maintenance", "getRecommendedProducts": "GET /get-recommended-products", "shortcutLinksAirportSectionLevel": "GET /bin/ichangi/section-level/airport/shortcutLinks", "announcementsAirportSectionLevel": "GET /bin/ichangi/section-level/airport/announcements", "mainCategoryAirportSectionLevel": "GET /bin/ichangi/section-level/airport/mainCategory", "horizontalCardFlyAirportSectionLevel": "GET /bin/ichangi/section-level/airport/horizontalCardFly", "flyContentCardAirportSectionLevel": "GET /bin/ichangi/section-level/airport/flyContentCard", "cardWithIllustrationsAirportSectionLevel": "GET /bin/ichangi/section-level/airport/cardWithIllustrations", "facilitiesServicesAirportSectionLevel": "GET /bin/ichangi/section-level/airport/facilitiesServices", "shortcutLinksAccountSectionLevel": "GET /bin/ichangi/section-level/account/shortcutLinks", "playAndWinAccountSectionLevel": "GET /bin/ichangi/section-level/account/playAndWin", "askMaxAccountSectionLevel": "GET /bin/ichangi/section-level/account/askMax", "moreOptionsAccountSectionLevel": "GET /bin/ichangi/section-level/account/moreOptions", "getPopularKeySearch": "GET /bin/ichangi/popular-keywords", "forcedUpdated": "GET /bin/ichangi/force-update", "forcedUpdatedV2": "GET /bin/ichangi/force-update/v2", "postRegisterDevices": "POST /register-devices", "postAppSetting": "POST /get-app-settings", "postValidateToken": "POST /validate-token", "postAppVersion": "POST /get-app-version", "getStaffPerkTopBanner": "GET /bin/ichangi/staff-perks/top-banner", "getBirthdayGiftPerk": "GET /bin/ichangi/foryou/birthday-gift-perk", "getFlyAppscapade": "GET /bin/ichangi/fly/appscapade-banner", "getChristmasGifts": "GET /bin/ichangi/foryou/christmas-gifts", "getScrollBuddy": "GET /bin/ichangi/explore/scroll-buddy", "getCNYPromo": "GET /bin/ichangi/foryou/cny-promo", "getMonarchTierForCRCard": "GET /bin/ichangi/foryou/horizontalcontentcard/monarch-tier", "getMonarchTierForCRCardV2": "GET /bin/ichangi/foryou/horizontalcontentcard/monarch-tier/v2", "getMonarchOnboardingOverlay": "GET /bin/ichangi/foryou/monarch-onboarding-overlay", "getMonarchPerksAndPrivileges": "GET /bin/ichangi/section-level/account/monarchPerksAndPrivileges", "getMonarchPrivilegesScreen": "GET /bin/ichangi/foryou/monarch-privileges", "getSplashScreenSetting": "GET /bin/ichangi/splash-screen-setting", "getAutoCompleteKeyword": "GET /autocomplete", "getYouMayAlsoLike": "GET /you-may-also-like", "getJewelPrivileges": "GET /bin/ichangi/foryou/jewel-privileges", "sendResetEmail": "POST /sendresetemail", "sendLoginRequest": "POST /login", "postVerifyToken": "POST /verifytoken", "getAccountInfo": "POST /getaccountinfo", "sendSignUpRequest": "POST /register", "sendOTPCode": "POST /otpsendcode", "verifyOTPCode": "POST /otpverifycode", "submitMissingData": "POST /registercompletion", "linkAccount": "POST /linkaccounts", "delinkAccount": "POST /delinkaccounts", "getFieldsAccount": "POST /getfields", "verifyCredentials": "POST /verifycredentials", "socialLogin": "POST /social-login", "getCrPrivilegesContentCards": "GET /bin/ichangi/foryou/changi-rewards-privileges", "getBaggagePredictionL3Page": "GET /bin/ichangi/fly/baggage-predictor-timing-l3-page-link", "getAccountCM24CardInfo": "GET /bin/ichangi/banners", "getL2Announcement": "GET /bin/ichangi/announcements/notifications/advisory", "submitRetroClaims": "POST /submit-retro-claims", "getiSCEntryPoints": "GET /bin/ichangi/foryou/isc-entry-points", "getDineShopEpicLandingPageData": "GET /bin/ichangi/dineandshop/marketplaces/epic-landing-page", "getDineShopPromotionTiles": "GET /bin/ichangi/dineandshop/marketplaces/epic-promotion-tiles", "checkblocklist": "POST /checkblocklist", "getEpicPerkV2": "POST /get-epic-perks", "getConsolidatedStructures": "POST /get-consolidated-structures-v2", "getConsolidatedAemGroupOne": "POST /get-consolidated-aem-group-one", "getConsolidatedAemGroupTwo": "POST /get-consolidated-aem-group-two", "updateLocationPreference": "POST /update-location-preference", "getFreeParkingPromos": "POST /parking-promotions", "getEcouponsParking": "GET /get-carpass-coupons", "getParkingLandingBenefit": "POST /get-carpass-benefits-summary", "getParkingEntitlementData": "POST /parking-entitlement", "getParkingQuickLinks": "GET /bin/ichangi/drive/quicklinks", "getMoreServicesParking": "POST /get-carpass-services", "getParkingBenefitsMonarchFAQs": "GET /bin/ichangi/drive/monarch-faqs", "getFAQChips": "GET /bin/ichangi/drive/faq-chips", "getDataDriveOnboarding": "GET /bin/ichangi/drive/onboardings"}