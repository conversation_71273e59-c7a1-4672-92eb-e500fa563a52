import { Platform } from "react-native"
import { <PERSON><PERSON><PERSON>, ACPIdentity, ACPMobileVisitorAuthenticationState } from "@adobe/react-native-acpcore"
import { ACPTarget, ACPTargetParameters, ACPTargetPrefetchObject, ACPTargetRequestObject } from "@adobe/react-native-acptarget"
import { ACPCampaign } from "@adobe/react-native-acpcampaign"
import { getWeekDay } from "app/utils/date-time/date-time"
import { isEmpty } from "lodash"
import { getEnvSetting } from "app/utils/env-settings"

const EnvSettings = getEnvSetting()

type ObjectData = {
  [key: string]: string
}

type HighLevelObjectData = {
  [key: string]: {
    [key: string]: string
  }
}

export enum AdobeAuthenticationState {
  authenticated = "authenticated",
  unknown = "unknown",
  loggedOut = "loggedOut",
}

export enum AdobeIdentifierType {
  uid = "UID",
}

type SyncIdentifier = {
  identifierType?: string
  identifierValue?: string
  authenticationState: AdobeAuthenticationState
}

export enum AdobeTagName {
  CAppNotificationPopUp = "CApp_Notification_Pop_Up",
  MobileDeviceID = "Mobile_Device_ID",
  CAppAppOpenState = "CApp_App_Open_State",
  // Widget
  CAppWidget = "CApp_Widget",
  // Home Page
  CAppHomePage = "CApp_Home_Page",
  CAppHomeECard = "CApp_Home_ECard",
  CAppHomeSearch = "CApp_Home_Search",
  CAppHomeCRPoints = "CApp_Home_CR_Points",
  CAppHomeLogin = "CApp_Home_Login",
  CAppHomeBookingTiles = "CApp_Home_BookingTiles",
  CAppHomeWallet = "CApp_Home_Wallet",
  CAppHomeQuicklinks = "CApp_Home_Quicklinks",
  CAppHomeQuicklinksPersonalization = "CApp_Home_Quicklinks_Personalization",
  CAppHomeSummaryTimelineTiles = "CApp_Home_SummaryTimelineTiles",
  CAppHomeLatestHappenings = "CApp_Home_Latest_Happenings",
  CAppLatestHappeningsDetails = "CApp_Latest_Happenings_Details",
  CAppHomeiSCProductSwimlane = "CApp_Home_iSCProductSwimlane",
  CAppHomePlaypassLandingCategory = "CApp_Home_PlaypassLanding_Category",
  CAppHomePlaypassLandingTiles = "CApp_Home_PlaypassLanding_Tiles",
  CAppHomePlaypassLandingFirst = "CApp_Home_PlaypassLanding_First",
  HomeExploreNotificationBanner = "Home_Explore_Notification_Banner",

  // Airport Landing
  CAppAirportLanding = "CApp_Airport_Landing",

  // flight landing
  CAppFlightLanding = "CApp_Flight_Landing",
  CAppFlyQuickLinks = "CApp_Fly_Quicklinks",
  CAppFlyTopMenuToggle = "CApp_Fly_TopMenuToggle",
  CAppFlyFlightSearchSearchBar = "CApp_Fly_FlightSearch_SearchBar",
  CAppFlyFlightSearchScanBoardingPass = "CApp_Fly_FlightSearch_ScanBoardingPass",
  CAppFlyFlightListDepArrToggle = "CApp_Fly_FlightList_DepArrToggle",
  CAppFlyFlightListEarlierFlights = "CApp_Fly_FlightList_EarlierFlights",
  CAppFlyFlightListFlightCardClicked = "CApp_Fly_FlightList_FlightCardClicked",
  CAppFlyFlightListSaveFlight = "CApp_Fly_FlightList_SaveFlight",
  CAppFlyFlightListRemoveFlight = "CApp_Fly_FlightList_RemoveFlight",
  CAppFlyFlightListViewAllDepFlights = "CApp_Fly_FlightList_ViewAllDepFlights",
  CAppFlyFlightListViewFlightDetails = "CApp_Fly_FlightList_ViewFlightDetails",
  CAppFlyFlightListFilters = "CApp_Fly_FlightList_Filters",
  CAppFlyContentSwimlaneTitle = "CApp_Fly_ContentSwimlaneTitle",
  CAppFlyFacilitiesServices = "CApp_Fly_Facilities&Services",
  CAppFlyFacilitiesServicesViewAll = "CApp_Fly_Facilities&Services_ViewAll",
  CAppFlightLandingFilter = "CApp_Flight_Landing_Filter",
  // flight listing
  CAppFlightListing = "CApp_FlightListing",
  CAppFlightListingDepArrToggle = "CApp_FlightListing_DepArrToggle",
  CAppFlightListingFlightSearch = "CApp_FlightListing_FlightSearch",
  CAppFlightListingScanBoardingPass = "CApp_FlightListing_ScanBoardingPass",
  CAppFlightListingEarlierFlights = "CApp_FlightListing_EarlierFlights",
  CAppFlightListingFlightListFilter = "CApp_FlightListing_FlightList_Filter",
  CAppFlightListingViewFlightDetails = "CApp_FlightListing_ViewFlightDetails",
  CAppFlightListingFlightCardClicked = "CApp_FlightListing_FlightCardClicked",
  CAppFlightListingSaveFlight = "CApp_FlightListing_SaveFlight",
  CAppFlightListingRemoveFlight = "CApp_FlightListing_RemoveFlight",
  CAppFlightListingFlightsearch = "capp_flightlisting_flightsearch",
  // flight details page
  CAppFlyFlightDetail = "CApp_Fly_FlightDetail",
  CAppFlyFlightDetailTopNavigationMenu = "CApp_Fly_FlightDetail_TopNavigationMenu",
  CAppFlyFlightDetailFlyProfile = "CApp_Fly_FlightDetail_FlyProfile",
  CAppFlyFlightDetailFlightCardLinks = "CApp_Fly_FlightDetail_FlightCardLinks",
  CAppNavigationMapsEnter = "CApp_navigationMapsEnter",
  CAppFlyFlightDetailPerks = "CApp_Fly_FlightDetail_Perks",
  CAppFlyFlightDetailTimelineTiles = "CApp_Fly_FlightDetail_TimelineTiles",
  CAppFlyFlightDetailSaveFlight = "CApp_Fly_FlightDetail_SaveFlight",
  CAppFlyFlightDetailRemoveFlight = "CApp_Fly_FlightDetail_RemoveFlight",
  CAppFlyFlightDetailFacilitiesServices = "CApp_Fly_FlightDetail_Facilities&Services",
  CAppFlyFlightDetailFacilitiesServicesViewAll = "CApp_Fly_FlightDetail_Facilities&Services_ViewAll",
  FlyFlightDetailsPageBottomDetails = "Flight_Details_Page_Bottom_Details",
  CAppFlightPersonalizationSaveFlight = "CApp_Flight_Personalization_Save_Flight",
  // CApp_Account_Folio_Menu_Toggle
  CAppFolioTopMenuToggle = "CApp_Folio_TopMenuToggle",
  // CApp_Account_Folio_Passes
  CAppAccountFolioPasses = "CApp_Account_Folio_Passes",
  CAppFolioPassesFilter = "CApp_Folio_Passes_Filter",
  CAppFolioPassesSort = "CApp_Folio_Passes_Sort",
  CAppFolioActivePassList = "CApp_Folio_ActivePassList",
  CAppFolioActivePassLoadMore = "CApp_Folio_ActivePass_LoadMore",
  CAppFolioPastPassesLoadMore = "CApp_Folio_PastPasses_LoadMore",
  // CApp_Account_Folio_Travel
  CAppAccountFolioTravel = "CApp_Account_Folio_Travel",
  CAppFolioTravel = "CApp_Folio_Travel",
  // CApp_Account_Folio_Credits
  CAppAccountFolioCredits = "CApp_Account_Folio_Credits",
  // CApp_Account_Folio_Perks
  CAppAccountFolioPerks = "CApp_Account_Folio_Perks",
  // CApp_Account_Folio_Orders
  CAppAccountFolioOrders = "CApp_Account_Folio_Orders",
  // CApp_Account_Landing
  CAppAccountProfileEdit = "CApp_Account_ProfileEdit",
  CAppAccountLogIn = "CApp_Account_LogIn",
  CAppAccountSettings = "CApp_Account_Settings",
  CAppAccountNotifications = "CApp_Account_Notifications",
  CAppAccountCRCard = "CApp_Account_CRCard",
  CAppAccountQuickLinks = "CApp_Account_QuickLinks",
  CAppLoginFlow = "CApp_Login_Flow",
  CAppSignupFlow = "CApp_Signup_Flow",
  // CApp_Jewel_Privilege
  CAppJewelPrivilege = "CApp_Jewel_Privilege",
  // Gamification
  CAppAccountGamification = "CApp_Account_Gamification",
  // CAppAccountAskMaxStartChat = "CApp_Account_AskMax_StartChat",
  CAppAccountMoreSection = "CApp_Account_MoreSection",
  CAppAccountLogOut = "CApp_Account_LogOut",
  // Dine Shop Toggle Menu
  CAppShopDineTopMenuToggle = "CApp_ShopDine_TopMenuToggle",
  CAppMarketplaceTiles = "CApp_Marketplace_Tiles",
  // Dine Landing Page
  CAppDineLanding = "CApp_Dine_Landing",
  CAppDineSearch = "CApp_Dine_Search",
  CAppDineSearchFilter = "CApp_Dine_Search_Filter",
  CAppDineSearchFilterDirectory = "CApp_Dine_Search_FilterDirectory",
  CAppDineFilterOptions = "CApp_Dine_Filter_Options",
  CAppDineHeroCarousel = "CApp_Dine_HeroCarousel",
  CAppDineNewlyOpened = "CApp_Dine_NewlyOpened",
  CAppDineRecommendedForYouVouchers = "CApp_Dine_RecommendedForYou_Vouchers",
  CAppDineRecommendedForYouOutlets = "CApp_Dine_RecommendedForYou_Outlets",
  CAppDineContentSwimlaneTiles = "CApp_Dine_ContentSwimlaneTiles",
  CAppDineSocial = "CApp_Dine_Social",
  CAppDineExploreMoreOutlets = "CApp_Dine_ExploreMore_Outlets",
  CAppDineExploreMoreViewAll = "CApp_Dine_ExploreMore_ViewAll",
  CAppDineExploreMoreFilters = "CApp_Dine_ExploreMore_Filters",
  EPICDineAndShop = "EPIC_Dine_and_Shop",
  // Shop Landing Page
  CAppShopLanding = "CApp_Shop_Landing",
  CAppShopSearch = "CApp_Shop_Search",
  CAppShopSearchFilter = "CApp_Shop_Search_Filter",
  CAppShopSearchFilterDirectory = "CApp_Shop_Search_FilterDirectory",
  CAppShopFilterOptions = "CApp_Shop_Filter_Options",
  CAppShopTenantPromoSwimlane = "CApp_Shop_TenantPromoSwimlane",
  CAppShopHeroCarousel = "CApp_Shop_HeroCarousel",
  CAppShopNewlyOpened = "CApp_Shop_NewlyOpened",
  CAppShopRecommendedForYouVouchers = "CApp_Shop_RecommendedForYou_Vouchers",
  CAppShopRecommendedForYouOutlets = "CApp_Shop_RecommendedForYou_Outlets",
  CAppShopContentSwimlaneTile = "CApp_Shop_ContentSwimlaneTile",
  CAppShopSocial = "CApp_Shop_Social",
  CAppShopExploreMoreOutlets = "CApp_Shop_ExploreMore_Outlets",
  CAppShopExploreMoreViewAll = "CApp_Shop_ExploreMore_ViewAll",
  CAppShopExploreMoreFilters = "CApp_Shop_ExploreMore_Filters",
  // CApp Dine Detail page
  CAppDineDetail = "CApp_Dine_Detail",
  CAppDineDetailNew = "CApp_DineDetail",
  CAppDineDetailShare = "CApp_DineDetail_Share",
  CAppDineDetailCarouselImage = "CApp_DineDetail_CarouselImage",
  CAppDineDetailDineInformation = "CApp_DineDetail_DineInformation",
  CAppDineDetailDineInformationGetDirection = "CApp_DineDetail_DineInformation_GetDirection",
  CAppDineDetailDineInformationPhoneNumber = "CApp_DineDetail_DineInformation_PhoneNumber",
  CAppDineDetailDineInformationViewWebsite = "CApp_DineDetail_DineInformation_ViewWebsite",
  CAppDineDetailDineInformationViewMenu = "CApp_DineDetail_DineInformation_ViewMenu",
  CAppDineDetailDineInformationBooking = "CApp_DineDetail_DineInformation_Booking",
  CAppDineDetailDealsPromo = "CApp_DineDetail_Deals&Promo",
  CAppDineDetailAbout = "CApp_DineDetail_About",
  CAppDineDetailSimilarTo = "CApp_DineDetail_SimilarTo",
  CAppDineDetailExploreMore = "CApp_Dine_Detail_ExploreMore",
  CAppDineDetailBlogsReviews = "CApp_DineDetail_Blogs&Reviews",
  CAppDineDetailRewardsTab = "CApp_DineDetail_RewardsTab",
  CAppDineDetailExploreMoreViewAll = "CApp_Dine_Detail_ExploreMore_ViewAll",
  CAppDineDetailExploreMoreCuisine = "CApp_Dine_Detail_ExploreMoreCuisine",
  CAppDineDetailDineInformationReserve = "CApp_DineDetail_DineInformation_Reserve",
  // CApp Shop Detail page
  CAppShopDetail = "CApp_Shop_Detail",
  CAppShopDetailShare = "CApp_ShopDetail_Share",
  CAppShopDetailCarouselImage = "CApp_ShopDetail_CarouselImage",
  CAppShopDetailShopInformation = "CApp_ShopDetail_ShopInformation",
  CAppShopDetailShopInformationGetDirection = "CApp_ShopDetail_ShopInformation_GetDirection",
  CAppShopDetailShopInformationPhoneNumber = "CApp_ShopDetail_ShopInformation_PhoneNumber",
  CAppShopDetailShopInformationViewWebsite = "CApp_ShopDetail_ShopInformation_ViewWebsite",
  CAppShopDetailShopInformationViewiSC = "CApp_ShopDetail_ShopInformation_ViewiSC",
  CAppShopDetailDealsPromo = "CApp_ShopDetail_Deals&Promo",
  CAppShopDetailAbout = "CApp_ShopDetail_About",
  CAppShopDetailSimilarTo = "CApp_ShopDetail_SimilarTo",
  CAppShopDetailExploreMore = "CApp_ShopDetail_ExploreMore",
  CAppShopDetailExploreMoreViewAll = "CApp_ShopDetail_ExploreMore_ViewAll",
  CAppShopDetailExploreMoreShops = "CApp_ShopDetailExploreMoreShops",
  CAppShopDetailBlogsReviews = "CApp_ShopDetail_Blogs&Reviews",
  CAppShopDetailRewardsTab = "CApp_ShopDetail_RewardsTab",
  // CApp Notifications Listing
  CAppNotificationsListing = "CApp_Notifications_Listing",
  CAppNotificationLanding = "CApp_Notification_Landing",
  CAppNotificationsListingDeleteAll = "CApp_Notifications_Listing_DeleteAll",
  CAppNotificationsListingDeleteAllConfirm = "CApp_Notifications_Listing_DeleteAll_Confirm",
  CAppNotificationsListingNotificationsClicked = "CApp_Notifications_Listing_NotificationsClicked",
  CAppNotificationsListingTopMenuToggle = "CApp_Notifications_Listing_TopMenuToggle",
  CAppNotificationsListingEnableNotification = "CApp_Notifications_Listing_EnableNotification",
  CAppNotificationCenter = "CApp_Notification_Center",
  // CApp Carpark
  CAppCarparkAvailabilityToggleMenu = "CApp_Carpark_Availability_ToggleMenu",
  CAppCarparkAvailabilityParkingPriviledges = "CApp_Carpark_Availability_ParkingPriviledges",
  CAppCarparkAvailabilityFiltersApplied = "CApp_Carpark_Availability_FiltersApplied",
  CAppCarparkCalculatorCarPickChosen = "CApp_Carpark_Calculator_CarPickChosen",
  CAppCarparkFindMyCarRequestCar = "CApp_Carpark_FindMyCar_RequestCar",
  CAppCarparkFindMyCarSelectCar = "CApp_Carpark_FindMyCar_SelectCar",
  // CApp Navigation
  CAppNavigationRouteSearchMode = "CApp_Navigation_RouteSearchMode",
  CAppNavigationLevels = "CApp_Navigation_Levels",
  CAppNavigationRouteSearch = "CApp_Navigation_RouteSearch",
  CAppNavigationOpenPOI = "CApp_Navigation_OpenPOI",
  // CApp Search Result page
  CAppSearchResult = "CApp_SearchResult",
  CAppSearchEvent = "CApp_SearchEvent",
  CAppSearchResultCategoryToggle = "CApp_SearchResult_CategoryToggle",
  CAppSearchResultClicked = "CApp_SearchResultClicked",
  CAppSearchResultsClickedRank = "CApp_Search_Results_Clicked_Rank",
  CAppSaveFlight = "CApp_saveFlight",
  CAppRemoveFlight = "CApp_removeFlight",
  CAppSearchResultFlySaveFlight = "CApp_SearchResult_Fly_SaveFlight",
  CAppSearchResultFlyRemoveFlight = "CApp_SearchResult_Fly_RemoveFlight",
  CAppSearchResultFlyScanBoardingPass = "CApp_SearchResult_Fly_ScanBoardingPass",
  CAppSearchResultFlyViewAll = "CApp_SearchResult_Fly_ViewAll",
  CAppSearchResultFlyFilter = "CApp_SearchResult_Fly_Filter",
  CAppSearchByCategory = "CApp_SearchByCategory",
  CAppPopularSearches = "CApp_Popular_Searches",
  CAppSearchAutoComplete = "CApp_Search_Auto_Complete",
  CAppSearchYAML = "CApp_Search_YAML",
  CAppSearchYMAL = "CApp_Search_YMAL",
  CAppSearchResultFlySaveFlightRank = "CApp_SearchResult_Fly_SaveFlight_Rank",
  SearchISCProductSwimlane = "Search_iSC_Product_Swimlane",
  CAppSearchResultFilter = "CApp_Search_Result_Filter",

  // CApp Redeem
  CAppRedeem = "CApp_Redeem",
  AccountYourPoints = "Account_Your_Points",

  // CApp_VPR
  CAppVPR = "CApp_VPR",

  // CApp Rewards
  CAppRewardsCatalogue = "CApp_Rewards_Catalogue",
  CAppRewardsCard = "CApp_Rewards_Card",
  CAppRewardsMembershipTier = "CApp_Rewards_Membership_Tier",
  RewardCatalogueFilter = "Reward_Catalogue_Filter",

  // tagName for bottom tab
  CAppNavigationBarHome = "CApp_NavigationBar_Home",
  CAppNavigationBarFly = "CApp_NavigationBar_Fly",
  CAppNavigationBarDineShop = "CApp_NavigationBar_DineShop",
  CAppNavigationBarCPay = "CApp_NavigationBar_CPay",
  CAppNavigationBarAccount = "CApp_NavigationBar_Account",

  // Tag name for unique visitors
  cappAppId = "capp.appId",
  cagLoginmethod = "cag.loginmethod",
  cappUserLoginSource = "capp.userLoginSource",
  cagUsername = "cag.username",
  cappChangiRewardsTier = "capp.changiRewardsTier",
  cagLoginstatus = "cag.loginstatus",
  cappUID = "capp.UID",
  cappNewLogin = "capp.newLogin",
  cappFirstTimeVisit = "capp.firstTimeVisit",
  cappVisitId = "capp.visitId",
  cappVisitSource = "capp.visitSource",

  // Tag name for common
  common = "common",
  cagPagename = "cag.pagename",
  cagSiteSection = "cag.sitesection",
  cagLanguage = "cag.language",
  cagPagePath = "cag.pagepath",
  cagSite = "cag.site",
  cagPreviousPage = "cag.previouspage",
  cagDayOfWeek = "cag.dayofweek",
  cagEcid = "cag.ecid",
  cagBusinessUnit = "cag.businessunit",
  cagPageview = "cag.pageview",
  appWide = "App-wide",
  cagRedeemYourPoint = "cag.redeemYourPoint",
  cagOtherPerksAndPrivileges = "cag.otherPerksAndPrivileges",

  // Restructure AA Tags
  CAppAccount = "CApp_Account",
  Campaign = "Campaign",

  // ATOMS
  CAppATOMSEntryClick = "capp_atom_entry_clicked",
  CAppATOMSSearchKeyword = "capp_atom_search_keyword",
  CAppATOMSSearchResults = "capp_atom_search_results",
  CAppATOMSMapClicks = "capp_atom_map_clicks",
  CAppATOMSLevelSelector = "capp_atom_levelSelector",
  CAppATOMsMap = "CApp_ATOMs_Map",

  // Playpass
  CAppPlaypassPassDetail = "CApp_Playpass_Pass_Detail",
  CAppHomePlaypassStickyCart = "CApp_Home_Playpass_Sticky_Cart",

  // StaffPerk
  CAppGalaxyEntry = "CApp_Galaxy_Entry",
  CAppGalaxyLanding = "CApp_Galaxy_Landing",
  CAppGalaxyDetail = "CApp_Galaxy_Detail",
  CAppShopDetailStaffPerks = "CApp_ShopDetail_StaffPerks",
  CAppGalaxyLandingTiles = "CApp_Galaxy_Landing_Tiles",
  CAppGalaxyFilters = "CApp_Galaxy_Filters",

  // Appacapade
  CAppAppscapadeBannerEntry = "CApp_Appscapade_Banner_Entry",

  // Notification Settings
  CAppNotificationSettings = "CApp_Notification_Settings",

  // Parking Landing
  CAppParkingLanding = "CApp_Parking_Landing",
  CAppL3FAQPage = "CApp_L3_FAQ_Page",
  CAppBenefitsSummary = "CApp_Benefits_Summary",
  // Parking onboarding
  CAppFeatureBuddy = "CApp_Feature_Buddy",
  // Promos code
  CAppPromoCodes = "CApp_Promo_Codes",
}

export enum AdobeValueByTagName {
  //  For App Open State
  AppOpenStateOn = "On",
  AppOpenStateOff = "Off",
  AppOpenStateLinked = "Linked",
  AppOpenStateNotAvailable = "Not Available",
  AppOpenStateEnableFaceID = "Enable [FaceID]",
  AppOpenStateEnableFingerprint = "Enable [FingerprintID]",
  AppOpenStateLinkedSocialAccounts = "Linked Social Accounts",
  AppOpenStateBiometricAuthentication = "Biometric Authentication",

  // For notification center
  CAppNotificationCenterFilter = "Filter | ",
  CAppNotificationCenterSelected = " | Selected",
  CAppNotificationCenterUnselected = " | Unselected",

  // For L2 Announcements
  HomeExploreNotificationBannerDefault = "Default | ",
  HomeExploreNotificationBannerInterstitial = "Interstitial | ",
  HomeExploreNotificationBannerSingle = "Single | ",
  HomeExploreNotificationBannerMultiple = "Multiple | ",
  HomeExploreNotificationBannerViewMessage = "View Message | ",
  HomeExploreNotificationBannerDelete = "Delete | ",
  HomeExploreNotificationBannerDeleteAll = "Delete All",

  // For CAppAccount
  CAppAccountAskMaxStartChat = "Ask Max Start Chat",
  CAppAccountCRCard = "CRCard",
  CAppAccountGamification = "Gamification",
  CAppAccountLogIn = "LogIn",
  CAppAccountLogOut = "LogOut",
  CAppAccountMoreSection = "More Section | ",
  CAppAccountNotifications = "Notifications",
  CAppAccountProfileEdit = "Profile Edit",
  CAppAccountQuickLinks = "QuickLinks | ",
  CAppAccountSettings = "Settings",
  CAppAccountTopNavigation = "Top Navigation",
  CAppAccountCopyMemberCode = "Copy Member Code",
  CAppAccountQuicklinks = "Quicklinks | ",
  CAppDineDetailDineInformation = "Dine Information | ",
  CAppDineDetailAbout = "About | ",
  CAppDineDetailExploreMoreCuisine = "Explore More Cuisine | ",

  // For Miffy Gamification
  CAppAccountMiffyGameEnterQuest = "Logged in: Miffy & Friends | Enter Quest",
  CAppAccountMiffyGameGiftFriend = "Logged in: Miffy & Friends | Gift A Friend",
  CAppAccountMiffyGameLoginToJoinQuest = "Not logged in: Miffy & Friends | Login to Join Quest",

  // For CAppRedeem
  CAppRedeemBack = "Back",
  CAppRedeemSeeAllExpiry = "See All Expiry",
  CAppRedeemPoints = "Points",
  CAppRedeemPerks = "Perks",
  CAppRedeemSubmitAClaim = "Submit a claim",
  // AccountYourPoints
  AccountYourPointsTabs = "Tab | ",
  AccountYourPointsTransaction = "Points Transaction",
  AccountYourPendingPoints = "Pending Points",
  AccountYourPointsExpiry = "Points Expiry",

  // For CApp Banner
  CAppBannerAccountLanding = "Account Landing",
  CAppBannerVPR = "Vouchers, Prizes & Redemptions",

  // For CApp VPR
  CAppVPRBack = "Back",
  
  CAppLoginPage = "Login Page | ",
  CAppSignUpPage = "Sign Up Page | ",
  CAppVerifyEmailPage = "Verify Email Page | ",
  CAppUserDetailsPage = "User Details Page | ",

  // For CApp Settings
  CAppSettingsManageLogin = "Manage Login | ",
  CAppSettingsUnlink = " | Unlink",
  CAppSettingsEnableFaceID = "Enable FaceID",
  CAppSettingsEnableFingerprintID = "Enable FingerprintID",
  CAppSettingsToggleOn = " | Toggle On",
  CAppSettingsToggleOff = " | Toggle Off",
  CAppSettingsPasswordSuccess = " Password Success",

  // For Flight Details
  FlightDetailsPageTravelling = "Travelling",
  FlightDetailsPagePickingSomeone = "Picking Someone",

  // For CApp ATOMs Map
  CAppATOMSMapSearchKeyword = "Search Keyword | ",
  CAppATOMSMapSearchResult = "Search Result | ",
  CAppATOMSMapLevelSelector = "Level Selector | ",
  CAppATOMSMapMapClicks = "Map Clicks | ",

  CAppPopularSearchesClearAll = "Clear All",
  CAppSearchResultFlySaveFlight = "Save",
  CAppSearchResultFlyRemoveFlight = "Remove",
  CAppSearchResultIncompleteScan = "Incomplete Scan",

  // For Search V2 all results
  iShopChangiLogoEntry = "iShopChangi Logo Entry",
  iShopChangiSearch = "iShopChangi Search | ",
  iShopChangiProduct = "iShopChangi Product | ",

  // For CApp Rewards
  CAppRewardsNonLoggedIn = "Non-logged in",
  RewardCatalogueClearAll = "Clear All",
  RewardCatalogueApplyFilters = "Apply Filters | ",
}

export enum NotificationSettingToggleValue {
  On = "On",
  Off = "Off",
}

export enum GAMIFICATION_TRACKING_VALUES {
  LOGGED_IN_PREFIX = "Logged in:",
  NOT_LOGGED_IN_PREFIX = "Not logged in:",
  DAILY_COMET_STREAK = "Daily Comet Streak",
  CLAIM_TODAY_COMETS = "Claim Today's Comets",
  LOGIN_TO_CLAIM_TODAY_COMETS = "Login to Claim Today's Comets",
  MISSION_PASS = "Mission Pass",
  ASTEROID_CHALLENGE = "Asteroid Challenge",
  PLAY_NOW = "Play Now",
  LAUNCH_NOW = "Launch Now",
  LOGIN_TO_PLAY = "Login to Play",
}

// for action
export const trackAction = (
  action?: AdobeTagName,
  contextData?: ObjectData | HighLevelObjectData,
) => {
  ACPCore.trackAction(action, contextData)
}

// for pages
export const trackState = (state?: AdobeTagName, contextData?: ObjectData) => {
  ACPCore.trackState(state, contextData)
}

export const getExperienceCloudId = () => {
  return ACPIdentity.getExperienceCloudId()
}

export const commonTrackingScreen = async (
  currentScreen: string,
  previousScreen: string,
  isLoggedIn: boolean,
) => {
  const ecid = await getExperienceCloudId()
  trackAction(AdobeTagName.common, {
    [AdobeTagName.cagPagename]: currentScreen,
    [AdobeTagName.cagSiteSection]: "",
    [AdobeTagName.cagLanguage]: "EN",
    [AdobeTagName.cagSite]: `Mobile App (${Platform.OS})`,
    [AdobeTagName.cagPreviousPage]: isEmpty(previousScreen) ? "Explore" : previousScreen,
    [AdobeTagName.cagDayOfWeek]: getWeekDay(),
    [AdobeTagName.cagEcid]: ecid,
    [AdobeTagName.cagBusinessUnit]: "ChangiApp",
    [AdobeTagName.cagPageview]: "1",
    [AdobeTagName.cappNewLogin]: isLoggedIn ? "yes" : "no",
  })
}

/**
 * @param obj {{}}
 */
export const adobeCampaignSetLinkageFields = (obj = {}) => {
  if (!Object.entries(obj).length) return null
  console.log("adobeCampaignSetLinkageFields", obj)
  ACPCampaign.setLinkageFields(obj)
}

export const adobeCampaignResetLinkageFields = () => {
  ACPCampaign.resetLinkageFields()
}

export const adobeUpdateConfiguration = (obj) => {
  // console.log("adobe__adobeUpdateConfiguration", obj)
  ACPCore.updateConfiguration(obj)
}

export const adobePrefetchContent = async (profile = {}) => {
  try {
    const ECID = await getExperienceCloudId()
    // environment - dev - uat - preprod - prod
    const mBoxParameters1 = { environment: "dev", channel: 'changiapp' }
    const profileParameters1 = {
      ...profile,
      ECID,
    }
    // console.log('adobe__PrefetchContent_mBoxParameters1', mBoxParameters1)
    // console.log('adobe__PrefetchContent_profileParameters1', profileParameters1)
    const targetParameters1 = new ACPTargetParameters(mBoxParameters1, profileParameters1, null, null)

    const prefetch1 = new ACPTargetPrefetchObject("JustForYouLanding", targetParameters1)
    const prefetchList = [prefetch1]
    const success = await ACPTarget.prefetchContent(prefetchList, null)
    // console.log('adobe__PrefetchContent_success', success)
    return success
  } catch (error) {
    // console.log("adobe__PrefetchContent_error", error)
  }
}

export const DEFAULT_LOCATION_CONTENT = 'null'
const mBoxNameDefault = "changi-category-recs-mbox"
export const adobeRetrieveLocationContent = async (profileParams = {}, params = {}, boxName = ''): Promise<string> => {
  const ECID = await getExperienceCloudId()
  const mBoxParameters1 = { environment: EnvSettings.ENV, channel: 'changiapp', ...params }
  const profileParameters1 = {
    ...profileParams,
    ECID,
  }
  const mBoxName = boxName || mBoxNameDefault
  // console.log('adobe__RetrieveLocationContent_mBoxName', mBoxName)
  // console.log('adobe__RetrieveLocationContent_mBoxParameters1', mBoxParameters1)
  // console.log('adobe__RetrieveLocationContent_profileParameters1', profileParameters1)
  const params1 = new ACPTargetParameters(mBoxParameters1, profileParameters1, null, null)
  // console.log('adobe__RetrieveLocationContent_ACPTargetParameters_params', params1)
  return new Promise((resolve, reject) => {
    const request1 = new ACPTargetRequestObject(mBoxName, params1, DEFAULT_LOCATION_CONTENT,
      (error, content) => {
        if (error) {
          // console.error('adobe__RetrieveLocationContent_ACPTargetRequestObject_error', error)
          resolve(JSON.stringify({ code: 'adobeError', message: error?.message }))
        } else {
          // console.log('adobe__RetrieveLocationContent_ACPTargetRequestObject_content:' + content)
          resolve(content)
        }
      }
    )
    return ACPTarget.retrieveLocationContent([request1], null)
  })
}

export const adobeClickedLocation = async (params = {}, boxName = '') => {
  const ECID = await getExperienceCloudId()
  const mBoxParameters1 = { environment: "dev", channel: 'changiapp' }
  const profileParameters1 = {
    ...params,
    ECID,
  }
  const mBoxName = boxName || mBoxNameDefault
  const params1 = new ACPTargetParameters(mBoxParameters1, profileParameters1, null, null)
  return ACPTarget.locationClickedWithName(mBoxName, params1)
}

export const adobeSyncIdentifier = ({
  identifierType,
  identifierValue,
  authenticationState,
}: SyncIdentifier) => {
  const authenticationStates = {
    unknown: ACPMobileVisitorAuthenticationState.UNKNOWN,
    loggedOut: ACPMobileVisitorAuthenticationState.LOGGED_OUT,
    authenticated: ACPMobileVisitorAuthenticationState.AUTHENTICATED,
  }

  ACPIdentity.syncIdentifier(identifierType || "", identifierValue || "", authenticationStates[authenticationState])
}
