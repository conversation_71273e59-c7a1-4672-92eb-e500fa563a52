import { createContext } from "react"

const defaultContextValue = {
  accountCM24FeatureFlag: null,
  pointsTxnRetroClaimsFeatureFlag: null,
  pendingPointsRetroClaims: null,
  accountContactUsFlag: null,
  vceaLiveChatFlag: null,
  l2AnnouncementFeatureFlag: null,
  notificationPreferencesV2FeatureFlag: null,
  notificationInboxV2: null,
  ciamUnlinkSocial: null,
  ciamSocialLinking: null,
  cm24Flag: null,
  groupBuy: null,
  ciamBiometrics: null,
  l1AnnouncementFeatureFlag: null,
  enableRoutingMap: null,
  enableSearchV2: null,
  miffyGameFeatureFlag: null,
  miffygameVPR: null,
  nativeOnboardingScreen: null,
  accountPromoCodesFF: null,
  flyLandingFeatureFlag: null,
}

export const AccountContext = createContext(defaultContextValue)