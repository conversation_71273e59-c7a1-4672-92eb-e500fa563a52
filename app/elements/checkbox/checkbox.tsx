import { Check } from "ichangi-fe/assets/icons"
import { flatten, mergeAll } from "ramda"
import * as React from "react"
import { TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { color } from "../../theme"
import { Text } from "../text/text"
import { CheckboxProps } from "./checkbox.props"
import { boldFontStyle, regularFontStyle } from "../text"

const ROOT: ViewStyle = {
  flexDirection: "row",
  alignSelf: "flex-start",
}

const ALIGN_START: ViewStyle = {
  alignItems: "flex-start",
}

const ALIGN_CENTER: ViewStyle = {
  alignItems: "center",
}

const LABEL: TextStyle = {
  marginLeft: 12,
}

const DIMENSIONS = { width: 16, height: 16 }

const OUTLINE: ViewStyle = {
  ...DIMENSIONS,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 2,
  borderColor: color.primaryLighter,
  borderRadius: 8,
  width: 24,
  height: 24,
}

const FILL: ViewStyle = {
  ...OUTLINE,
  backgroundColor: color.palette.lightPurple,
}

export function Checkbox(props: CheckboxProps) {
  const numberOfLines = props.multiline ? 0 : 1

  const rootStyle = mergeAll(
    flatten([ROOT, props.multiline ? ALIGN_START : ALIGN_CENTER, props.style]),
  )
  const outlineStyle = mergeAll(flatten([OUTLINE, props.outlineStyle]))
  const fillStyle = mergeAll(flatten([FILL, props.fillStyle]))

  const onPress = props.onToggle ? () => props.onToggle && props.onToggle(!props.value) : null
  const outlineDisabledStyle: ViewStyle = {
    ...outlineStyle,
    opacity: 0.3,
  }
  return (
    <TouchableOpacity
      activeOpacity={1}
      disabled={!props.onToggle}
      onPress={onPress}
      style={rootStyle}
      testID={props.testID}
      accessibilityLabel={props.accessibilityLabel}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <View style={!props.onToggle ? outlineDisabledStyle : outlineStyle}>
        {!!props?.value && (
          <View style={fillStyle}>
            {!!props?.icon ? (
              props.icon
            ) : (
              <Check fill={color.palette.whiteGrey} width={33} height={33} />
            )}
          </View>
        )}
      </View>
      {(props.text || props.tx) && (
        <Text
          text={props.text}
          tx={props.tx}
          numberOfLines={numberOfLines}
          style={[
            LABEL,
            props.textStyle,
            {
              opacity: props.onToggle ? 1 : 0.4,
              width: "100%",
              ...(props.value && !props?.preventHighlightTextOnCheck
                ? boldFontStyle
                : regularFontStyle),
            },
          ]}
        />
      )}
    </TouchableOpacity>
  )
}
