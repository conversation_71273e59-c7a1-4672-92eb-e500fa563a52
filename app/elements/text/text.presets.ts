import Responsive from "app/utils/responsive"
import { TextStyle, Platform } from "react-native"
import { color, typography } from "../../theme"

/**
 * All text will start off looking like this.
 */
const BASE: TextStyle = {
  fontFamily: typography.regular,
  color: color.text,
  fontSize: 15,
}

export const regularFontStyle: TextStyle = {
  fontFamily: typography.regular,
}

export const boldFontStyle: TextStyle = {
  fontFamily: typography.bold,
}

/**
 * All the variations of text styling within the app.
 *
 * You want to customize these to whatever you need in your app.
 */
export const presets = {
  /**
   * The default text styles.
   */
  default: BASE,

  /**
   * A bold version of the default text.
   */
  bold: { fontFamily: typography.bold, color: color.text, fontSize: 15, fontWeight: Platform.select({ ios: "700", android: "normal" }) } as TextStyle,

  /**
   * Large headers.
   */
  header: { fontFamily: typography.bold, fontSize: 24, fontWeight: Platform.select({ ios: "700", android: "normal" }), color: color.text } as TextStyle,

  /**
   * Field labels that appear on forms above the inputs.
   */
  fieldLabel: { ...BASE, fontSize: 13, color: color.text } as TextStyle,

  /**
   * A smaller piece of secondard information.
   */
  secondary: { ...BASE, fontSize: 9, color: color.text } as TextStyle,

  /**
   * CAG Text Presets
   */
  h4: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 18,
    lineHeight: 24,
    textAlign: "left",
    textAlignVertical: "top",
    letterSpacing: 1.08,
    textTransform: "uppercase",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  caption1Regular: {
    ...BASE,
    fontSize: Responsive.getFontSize(14),
    lineHeight: Responsive.getFontSize(18),
    textAlign: "left",
    textAlignVertical: "top",
    fontStyle: "normal",
    fontWeight: "400",
    letterSpacing: 0,
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption1Italic: {
    ...BASE,
    fontSize: 14,
    lineHeight: 18,
    textAlign: "left",
    textAlignVertical: "top",
    fontWeight: "400",
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
    fontFamily: typography.italic,
  } as TextStyle,

  caption1BoldItalic: {
    ...BASE,
    fontSize: 14,
    lineHeight: 18,
    textAlign: "left",
    textAlignVertical: "top",
    fontWeight: "700",
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
    fontFamily: typography.italic,
  } as TextStyle,

  XSmallBold: {
    ...BASE,
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    textTransform: "uppercase",
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: 11,
    lineHeight: 14,
  } as TextStyle,

  XSmallRegular: {
    ...BASE,
    fontSize: 11,
    lineHeight: 14,
    fontStyle: "normal",
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  caption2Regular: {
    ...BASE,
    fontFamily: typography.regular,
    fontSize: Responsive.getFontSize(12),
    fontStyle: "normal",
    fontWeight: "400",
    lineHeight: Responsive.getFontSize(16),
    letterSpacing: 0,
    textAlign: "left",
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption2Italic: {
    ...BASE,
    fontFamily: typography.italic,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16,
    letterSpacing: 0,
    textAlign: "left",
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption2Bold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 12,
    lineHeight: 16,
    textAlign: "left",
    textAlignVertical: "center",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  bodyTextBold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    textAlign: "left",
    textAlignVertical: "top",
    fontStyle: "normal",
    letterSpacing: 0,
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  subTitleBold: {
    fontFamily: typography.bold,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: Responsive.getFontSize(18),
    fontStyle: "normal",
    lineHeight: Responsive.getFontSize(22),
    letterSpacing: 0,
    textAlign: "left",
  } as TextStyle,

  bodyTextRegular: {
    ...BASE,
    fontFamily: typography.regular,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    textAlign: "left",
    textAlignVertical: "top",
    fontStyle: "normal",
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  bodyTextItalic: {
    ...BASE,
    fontFamily: typography.italic,
    fontSize: 16,
    lineHeight: 20,
    textAlign: "left",
    textAlignVertical: "top",
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  bodyTextBlackRegular: {
    ...BASE,
    fontFamily: typography.regular,
    fontSize: 16,
    lineHeight: 22,
    textAlign: "left",
    textAlignVertical: "top",
    fontStyle: "normal",
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.almostBlackGrey,
  } as TextStyle,

  textLink: {
    ...BASE,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(22),
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    color: color.palette.lightPurple,
    fontFamily: typography.bold,
  } as TextStyle,

  caption1Bold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
    textAlign: "center",
    color: color.palette.whiteGrey,
  } as TextStyle,

  caption1BoldSmall: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: Responsive.getFontSize(12),
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: Responsive.getFontSize(16),
    color: color.palette.whiteGrey,
  } as TextStyle,

  caption2BlackBold: {
    ...BASE,
    fontFamily: typography.black,
    fontSize: Responsive.getFontSize(18),
    lineHeight: Responsive.getFontSize(22),
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    textAlign: "center",
    color: color.palette.whiteGrey,
  } as TextStyle,

  h3: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 20,
    lineHeight: 22,
    textAlign: "left",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    letterSpacing: 0.005,
    color: color.palette.lightestGrey,
  } as TextStyle,

  h1: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 28,
    lineHeight: 36,
    textAlign: "left",
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  h2: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 24,
    lineHeight: 32,
    textAlign: "left",
    textAlignVertical: "top",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontStyle: "normal",
    letterSpacing: 0.006,
    color: color.palette.almostBlackGrey,
  } as TextStyle,

  h6: {
    ...BASE,
    fontFamily: typography.black,
    fontSize: 20,
    lineHeight: 28,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
  } as TextStyle,

  h2Tabs: {
    fontFamily: typography.bold,
    fontSize: 24,
    lineHeight: 32,
    textAlign: "left",
    textAlignVertical: "top",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontStyle: "normal",
    letterSpacing: 0.006,
    textTransform: "none",
  } as TextStyle,

  tabsSmall: {
    textTransform: "none",
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(22),
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
  } as TextStyle,

  caption1Black: {
    ...BASE,
    fontFamily: typography.black,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 18,
    color: color.palette.whiteGrey,
  } as TextStyle,

  bodyTextBlack: {
    ...BASE,
    fontFamily: typography.black,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    fontStyle: "normal",
    letterSpacing: 0,
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
  } as TextStyle,

  h3Regular: {
    ...BASE,
    fontFamily: typography.regular,
    fontSize: Responsive.getFontSize(32),
    lineHeight: Responsive.getFontSize(40),
    fontStyle: "normal",
    letterSpacing: 0,
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
  } as TextStyle,
}

export const newPresets = {
  /**
   * The default text styles.
   */
  default: BASE,

  /**
   * A bold version of the default text.
   */
  bold: { fontFamily: typography.bold, color: color.text, fontSize: 15, fontWeight: Platform.select({ ios: "700", android: "normal" }) } as TextStyle,

  /**
   * Large headers.
   */
  header: { fontFamily: typography.bold, fontSize: 24, fontWeight: Platform.select({ ios: "700", android: "normal" }), color: color.text } as TextStyle,

  /**
   * Field labels that appear on forms above the inputs.
   */
  fieldLabel: { ...BASE, fontSize: 13, color: color.text } as TextStyle,

  /**
   * A smaller piece of secondard information.
   */
  secondary: { ...BASE, fontSize: 9, color: color.text } as TextStyle,

  /**
   * CAG Text Presets
   */
  h4: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 18,
    lineHeight: 24,
    letterSpacing: 1.08,
    textTransform: "uppercase",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  caption1Regular: {
    ...BASE,
    fontSize: Responsive.getFontSize(14),
    lineHeight: Responsive.getFontSize(18),
    fontStyle: "normal",
    fontWeight: "400",
    letterSpacing: 0,
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption1Italic: {
    ...BASE,
    fontSize: 14,
    lineHeight: 18,
    fontWeight: "400",
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
    fontFamily: typography.italic,
  } as TextStyle,

  caption1BoldItalic: {
    ...BASE,
    fontSize: 14,
    lineHeight: 18,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
    fontFamily: typography.italic,
  } as TextStyle,

  title1Bold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(18),
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    letterSpacing: 0,
    color: color.palette.almostBlackGrey,
  } as TextStyle,

  XSmallBold: {
    ...BASE,
    fontFamily: typography.bold,
    color: color.palette.lightPurple,
    textTransform: "uppercase",
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: 11,
    lineHeight: 14,
  } as TextStyle,

  XSmallRegular: {
    ...BASE,
    fontSize: 11,
    lineHeight: 14,
    fontStyle: "normal",
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  caption2Regular: {
    ...BASE,
    fontFamily: typography.regular,
    fontSize: Responsive.getFontSize(12),
    fontStyle: "normal",
    fontWeight: "400",
    lineHeight: Responsive.getFontSize(16),
    letterSpacing: 0,
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption2Italic: {
    ...BASE,
    fontFamily: typography.italic,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16,
    letterSpacing: 0,
    color: color.palette.darkestGrey,
  } as TextStyle,

  caption2Bold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 12,
    lineHeight: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  bodyTextBold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    fontStyle: "normal",
    letterSpacing: 0,
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  subTitleBold: {
    fontFamily: typography.bold,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontSize: Responsive.getFontSize(18),
    fontStyle: "normal",
    lineHeight: Responsive.getFontSize(22),
    letterSpacing: 0,
  } as TextStyle,

  bodyTextRegular: {
    ...BASE,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    fontStyle: "normal",
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  bodyTextItalic: {
    ...BASE,
    fontFamily: typography.italic,
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.whiteGrey,
  } as TextStyle,

  bodyTextBlackRegular: {
    ...BASE,
    fontSize: 16,
    lineHeight: 22,
    fontStyle: "normal",
    letterSpacing: 0,
    fontWeight: "400",
    color: color.palette.almostBlackGrey,
  } as TextStyle,

  bodyTextBlackBold: {
    ...BASE,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(20),
    fontStyle: "normal",
    letterSpacing: 0,
    fontFamily: typography.black,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    color: color.palette.whiteGrey,
  } as TextStyle,

  smallTextBlackBold: {
    fontFamily: typography.black,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    fontSize: 12,
    lineHeight: 16,
    fontStyle: "normal",
    letterSpacing: 0,
  } as TextStyle,

  textLink: {
    ...BASE,
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(22),
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    color: color.palette.lightPurple,
    fontFamily: typography.bold,
  } as TextStyle,

  caption1Bold: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 14,
    fontStyle: "normal",
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
    color: color.palette.whiteGrey,
  } as TextStyle,

  h3: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 20,
    lineHeight: 22,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    letterSpacing: 0.005,
    color: color.palette.lightestGrey,
  } as TextStyle,

  h1: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 28,
    lineHeight: 36,
    color: color.palette.whiteGrey,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
  } as TextStyle,

  h2: {
    ...BASE,
    fontFamily: typography.bold,
    fontSize: 24,
    lineHeight: 32,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontStyle: "normal",
    letterSpacing: 0.006,
    color: color.palette.almostBlackGrey,
  } as TextStyle,

  h2Tabs: {
    fontFamily: typography.bold,
    fontSize: 24,
    lineHeight: 32,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontStyle: "normal",
    letterSpacing: 0.006,
    textTransform: "none",
  } as TextStyle,

  tabsSmall: {
    textTransform: "none",
    fontSize: Responsive.getFontSize(16),
    lineHeight: Responsive.getFontSize(22),
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    fontFamily: typography.bold,
  } as TextStyle,
}

/**
 * A list of preset names.
 */
export type TextPresets = keyof typeof presets
