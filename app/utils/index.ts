import { env } from "app/config/env-params"
import moment from "moment-timezone"
import { DateFormats } from "./date-time/date-time"
import { Dimensions, StatusBar } from "react-native"

const mappingUrlAem = (path: string, url = "") => {
  if (path?.length > 0 && !path?.startsWith?.("http")) {
    if (url) {
      return `${url}${path}`
    }
    return `${env()?.AEM_URL}${path}`
  }
  return path
}
/**
 * Return accessbility and testID for react elements
 * @param {{ testID: string, accessibilityLabel: string, OS: string }}
 * @returns {{ testID?: string, accessibilityLabel?: string }}
 */
const accessibility = ({ testID, accessibilityLabel, OS }) => {
  if (OS === "ios") {
    return {
      testID,
    }
  } else if (OS === "android") {
    return {
      accessibilityLabel,
    }
  } else {
    return {
      testID,
      accessibilityLabel,
    }
  }
}

const conditionFormat = (data) => {
  if (typeof data === "object" && !Array.isArray(data) && data !== null) {
    if (
      Object.keys(data).includes("condition") &&
      Object.keys(data).includes("ifValue") &&
      Object.keys(data).includes("elseValue")
    ) {
      return true
    } else {
      return false
    }
  } else {
    return false
  }
}

const simpleCondition = (conditions) => {
  const { condition, ifValue: initIfValue, elseValue: initElseValue } = conditions
  let ifValue: any
  if (!conditionFormat(initIfValue)) {
    ifValue = initIfValue
  } else {
    if (initIfValue.condition) {
      ifValue = initIfValue.ifValue
    } else {
      ifValue = initIfValue.elseValue
    }
  }
  let elseValue: any
  if (!conditionFormat(initElseValue)) {
    elseValue = initElseValue
  } else {
    if (initElseValue.condition) {
      elseValue = initElseValue.ifValue
    } else {
      elseValue = initElseValue.elseValue
    }
  }
  return condition ? ifValue : elseValue
}

const handleCondition = (state, condition1, condition2) => {
  if (state) return condition1
  return condition2
}

const parseJson = (data) => {
  try {
    return JSON.parse(data)
  } catch (err) {
    return null
  }
}

const getQueryParams = (url) => {
  const paramArr = url.slice(url.indexOf("?") + 1).split("&")
  const params: any = {}
  paramArr.forEach((param) => {
    const [key, val] = param.split("=")
    params[key] = decodeURIComponent(val)
  })
  return params
}

const convertToURLFormat = (url) => {
  let newUrl
  if (url.startsWith("https://") || url.startsWith("http://")) {
    newUrl = url
  } else if (url.startsWith("https:/") && !url.includes("https://")) {
    newUrl = "https://" + url.split("https:/")[1]
  } else {
    newUrl = "https://" + url
  }
  return newUrl
}

/**
 * Return true if all array params are true
 * @param array {[]}
 * @returns boolean
 */
const ifAllTrue = (array = []) => {
  return array.every((data) => !!data)
}

/**
 * Return true if one of params is true
 * @param array {[]}
 * @returns boolean
 */
const ifOneTrue = (array = []) => {
  return array.some((data) => !!data)
}

/**
 * Capitalize first char of string
 * @param string
 * @returns string
 */
function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * Capitalize first char of string
 * @param Object {{}}
 * @returns string
 */
const serializeObjectToUrlParameter = (obj) => {
  return Object.entries(obj)
    .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
    .join("&")
}

/**
 * Compare Version
 * @param string
 * @param string
 * @returns number
 */
const compareVersion = (v1, v2) => v1.localeCompare(v2, undefined, { numeric: true, sensitivity: "base" })

const toLocaleNumber = (rawValue?: any): string => {
  const refinedValue = `${rawValue}`.replaceAll(',', '');
  if ([null, undefined].includes(rawValue) || Number.isNaN(Number(refinedValue))) return '0';
  return Number(refinedValue).toLocaleString();
}

const toNumber = (rawValue?: any): number => {
  const refinedValue = `${rawValue}`.replaceAll(',', '');
  if ([null, undefined].includes(rawValue) || Number.isNaN(Number(refinedValue))) return 0;
  return Number(refinedValue)
}

const getCurrentTimeInSingapore = () => {
  const currentSingaporeTimeFormat = moment()
    .tz("Asia/Singapore")
    .format(DateFormats.DateTimeSeconds)
  return moment(currentSingaporeTimeFormat)
}

const compareDates = (date1, date2) => {
  const momentDate1 = moment.isMoment(date1) ? date1 : moment(date1)
  const momentDate2 = moment.isMoment(date2) ? date2 : moment(date2)
  const timestamp1 = momentDate1.toDate().getTime()
  const timestamp2 = momentDate2.toDate().getTime()
  return timestamp1 - timestamp2
}
const isAfter = (date1, date2) => compareDates(date1, date2) > 0
const isBefore = (date1, date2) => compareDates(date1, date2) < 0
const isSameOrAfter = (date1, date2) => compareDates(date1, date2) >= 0

const decodeHtml = (encodedStr) => {
  return encodedStr
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
}

const getNavBarHeight = () => {
  const windowHeight = Dimensions.get('window').height;
  const screenHeight = Dimensions.get('screen').height;
  const statusBarHeight = StatusBar.currentHeight || 0;
  return screenHeight - windowHeight - statusBarHeight;
}

/**
 * Joins an array of texts with a separator
 *
 * @param {string | string[] | number | number[]} texts - String, number, or array of string/number values
 * @param {string} separator - String to insert between text items
 * @returns {string} Joined text string or original value if not an array
 */
const joinTexts = (texts: string | string[], separator = ""): string => {
  if (!Array.isArray(texts)) {
    return texts
  }

  return texts
    .filter(Boolean) // Filter out null/undefined values
    .map((text) => {
      if (typeof text === "string" || typeof text === "number") {
        return `${text}`
      }
      return ""
    })
    .join(separator)
}

const validateImageLottieAEM = (link) => {
  if (!link) return false
  return link?.toString().endsWith(".json") || link?.toString().endsWith(".lottie")
}

const parseJsonWebviewMessage = (message) => {
  try {
    return JSON.parse(message?.nativeEvent?.data)
  } catch (err) {
    return {}
  }
}

export {
  mappingUrlAem,
  accessibility,
  simpleCondition,
  handleCondition,
  parseJson,
  getQueryParams,
  convertToURLFormat,
  ifAllTrue,
  ifOneTrue,
  capitalizeFirstLetter,
  serializeObjectToUrlParameter,
  compareVersion,
  toLocaleNumber,
  toNumber,
  getCurrentTimeInSingapore,
  isAfter,
  isBefore,
  isSameOrAfter,
  decodeHtml,
  getNavBarHeight,
  joinTexts,
  validateImageLottieAEM,
  parseJsonWebviewMessage,
}
