import { store } from "app/redux/store"
import { toNumber } from "app/utils"
import { ParkingECouponType } from "app/screens/parking-landing/parking-landing.constants"

export const getParkingLandingTrackingValue = (parkingLandingCardPayload) => {
  const { isParked } = parkingLandingCardPayload || {}

  const forYouReducer: any = store.getState()?.forYouReducer
  const currentTier = forYouReducer?.rewardsPayload?.reward?.currentTierInfo || "null"
  const isLoggedIn = store.getState()?.nativeAuthReducer?.isLoggedIn
  const vehicleIU = store.getState()?.profileReducer?.profilePayload?.vehicleIU

  const loginStatus = isLoggedIn ? "Logged-in" : "Non-logged in"
  const memberShipTier = currentTier && isLoggedIn ? currentTier : "null"
  const registrationStatus = isLoggedIn
    ? vehicleIU
      ? "IU registered"
      : "No IU registered"
    : "null"
  const parkingStatus = vehicleIU ? (isParked ? "Parked" : "Not parked") : "null"

  const parkNFlyData = parkingLandingCardPayload?.coupons?.find?.(
    (item) => item?.couponType === ParkingECouponType.ParkNFly,
  )
  const parkNWorkData = parkingLandingCardPayload?.coupons?.find?.(
    (item) => item?.couponType === ParkingECouponType.ParkNWork,
  )
  const totalParkNFlyECoupons =
    toNumber(parkNFlyData?.numActivatedCoupons) + toNumber(parkNFlyData?.numDeactivatedCoupons)
  const totalParkNWorkECoupons =
    toNumber(parkNWorkData?.numActivatedCoupons) + toNumber(parkNWorkData?.numDeactivatedCoupons)
  const parkNFlyECouponsValue = !!totalParkNFlyECoupons ? "Park & Fly" : ""
  const parkNWorkECouponsValue = !!totalParkNWorkECoupons ? "Park & Work" : ""
  const eCouponsTrackingValue =
    [parkNFlyECouponsValue, parkNWorkECouponsValue].filter(Boolean).join(", ") || ""
  const parkingCoupon = eCouponsTrackingValue || "No Parking coupon"

  const hasActiveParkNFlyECoupons = !!parkNFlyData?.activated
  const hasActiveParkNWorkECoupons = !!parkNWorkData?.activated
  const benefitsStatus =
    hasActiveParkNFlyECoupons || hasActiveParkNWorkECoupons
      ? "Active Benefits"
      : "No Parking benefits"

  return `${loginStatus} | ${memberShipTier} | ${registrationStatus} | ${parkingStatus} | ${parkingCoupon} | ${benefitsStatus}`
}
