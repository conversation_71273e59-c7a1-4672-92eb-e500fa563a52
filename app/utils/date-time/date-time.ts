import moment, { Moment } from "moment"
import * as MomentTimeZone from "moment-timezone"
import { handleCondition } from ".."
import { translate } from "../../i18n"

export enum DateVariations {
  "specificDate" = "specificDate",
  "dateRange" = "dateRange",
  "eventDate" = "eventDate",
}

export enum TimeFormats {
  "24Hours" = "HH:mm",
  "12Hours" = "hh:mm A",
}

export enum Locales {
  "en" = "en",
  "zh" = "zh-cn",
}

export enum DateFormats {
  "MMMyyy" = "MMM yyyy",
  "DayDateMonth" = "ddd, DD MMM",
  "DayDateMonthYear" = "ddd, DD MMM YYYY",
  "YearMonthDay" = "YYYY-MM-DD",
  "DayMonthYear" = "DD MMM YYYY",
  "DateWithDayMonthYear" = "ddd DD MMMM YYYY",
  "DayMonthYearWithSlash" = "DD/MM/YYYY",
  "MonthYear" = "MMMM YYYY",
  "DateTime" = "YYYY-MM-DD[T]HH:mm:ss.SSSSS",
  "flyModuleUpdatedTime" = "HH:mm, DD MMM yyyy",
  "Year" = "YYYY",
  "DayOfWeek" = "dddd",
  "DayMonth" = "(DD MMM)",
  "DateMonth" = "DD MMM",
  "YearMonthDayTime" = "YYYY-MM-DD HH:mm",
  "HoursMinutesSeconds" = "HH:mm:ss.SSSSS",
  "HoursMinutes" = "HH:mm",
  "DateTimeSeconds" = "YYYY-MM-DD HH:mm:ss",
  "CalendarEventUTC" = "YYYY-MM-DDTHH:mm:ss.SSS[Z]",
}

function convertTimestampToDate(
  inputDate: moment.Moment,
  dateVariation: DateVariations = DateVariations.specificDate,
  timeFormat: TimeFormats = TimeFormats["24Hours"],
  isOnlyDateRequired = false,
  isOnlyTimeRequired = false,
  dateFormat: DateFormats = DateFormats.MMMyyy,
  locale: Locales = Locales.en,
) {
  inputDate = moment(inputDate).utc()
  const today = moment().format("YYYY-MM-DD")
  const tomorrow = moment().add(1, "day").toISOString()
  const time = moment(inputDate).locale(locale).utc().format(timeFormat)
  const date = moment(inputDate).locale(locale).format(dateFormat)
  if (isOnlyTimeRequired) {
    return time
  }
  if (isOnlyDateRequired) {
    return date
  }
  if (dateVariation === DateVariations.eventDate) {
    if (moment(today).isSame(moment(inputDate).format(DateFormats.YearMonthDay), "day")) {
      return `${translate("upcomingEvent.today")} · ${time}`
    } else if (moment(tomorrow).isSame(moment(inputDate).format(DateFormats.YearMonthDay), "day")) {
      return `${translate("upcomingEvent.tomorrow")} · ${time}`
    } else {
      return `${date} · ${time}`
    }
  }
  return false
}

function toDate(
  inputDate: moment.Moment | string,
  dateFormat: DateFormats = DateFormats.DayDateMonthYear,
  locale: Locales = Locales.en,
) {
  inputDate = moment(inputDate)
  const date = moment(inputDate).locale(locale).format(dateFormat)
  return date
}

const formatMonthYear = (
  inputDate: string,
  dateFormat: DateFormats = DateFormats.MonthYear,
  locale: Locales = Locales.en,
) => {
  const date = moment(inputDate, DateFormats.DayMonthYear, locale).format(dateFormat)
  return date
}

function dateRangeFormatting(
  startDate: moment.Moment,
  endDate: moment.Moment,
  locale: Locales = Locales.en,
) {
  startDate = moment(startDate).utc()
  endDate = moment(endDate).utc()
  const today = moment().utc().format("YYYY-MM-DD")
  const tomorrow = moment().utc().add(1, "day").toISOString()

  const isStartDateToday = moment(today).isSame(startDate.format(DateFormats.YearMonthDay), "day")
  const isStartDateTomorrow = moment(tomorrow).isSame(
    startDate.format(DateFormats.YearMonthDay),
    "day",
  )
  const isEndDateToday = moment(today).isSame(endDate.format(DateFormats.YearMonthDay), "day")
  const isEndDateTomorrow = moment(tomorrow).isSame(endDate.format(DateFormats.YearMonthDay), "day")
  const isStartDateValid = moment(startDate).isValid()
  const isEndDateValid = moment(endDate).isValid()

  if (!isStartDateValid) return ""

  if ((isStartDateToday && !isEndDateValid) || (isStartDateToday && isEndDateToday)) {
    // if startdate == Today && end date is null then return "Today"
    return `${translate("upcomingEvent.today")}`
  } else if (
    (isStartDateTomorrow && !isEndDateValid) ||
    (isStartDateTomorrow && isEndDateTomorrow)
  ) {
    // if startdate == Tomorrow && end date is null then return "Tomorrow"
    return `${translate("upcomingEvent.tomorrow")}`
  } else if (
    !(isStartDateToday || isStartDateTomorrow) &&
    (!isEndDateValid || startDate.isSame(endDate))
  ) {
    // if startdate !== (today or tomorrow) and end date is null then return specific
    return `${startDate.locale(locale).format(DateFormats.DayDateMonthYear)}`
  } else if (isStartDateValid && isEndDateValid) {
    // if startdate !== (today or tomorrow) and end date is not null then return date range
    return `${startDate.locale(locale).format(DateFormats.DayMonthYear)} - ${endDate
      .locale(locale)
      .format(DateFormats.DayMonthYear)}`
  } else {
    return ""
  }
}

function flyModuleDateFormatting(
  date: any,
  dateFormat: DateFormats = DateFormats.DayMonthYear,
  locale: Locales = Locales.en,
) {
  const dateCheck = moment(date, DateFormats.YearMonthDay)
  if (dateCheck.isValid()) {
    const getIdentifiedDay = dayIdentifier(date)
    let constructDate = moment(date).locale(locale).format(dateFormat)
    if (getIdentifiedDay) {
      constructDate = moment(date).locale(locale).format(DateFormats.DayMonthYear)
      return `${getIdentifiedDay}, ${constructDate}`
    } else {
      constructDate = moment(date).locale(locale).format(DateFormats.DayMonthYear)
      return constructDate
    }
  } else {
    return ""
  }
}
function dayIdentifier(date: moment.Moment) {
  const yesterday = moment().subtract(1, "day")
  const today = moment()
  const tomorrow = moment().add(1, "day")
  const flightDate = moment(date)
  if (moment(tomorrow).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
    return `${translate("upcomingEvent.tomorrow")}`
  } else if (moment(today).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
    return `${translate("upcomingEvent.today")}`
  } else if (moment(yesterday).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
    return `${translate("upcomingEvent.yesterday")}`
  } else {
    return false
  }
}

function flyModuleUpdatedTime(locale: Locales = Locales.en) {
  return moment(new Date()).locale(locale).format(DateFormats.flyModuleUpdatedTime)
}

const eventCardDateFormatting = (
  eventStart: string,
  eventEnd: string,
  locale: Locales = Locales.en,
) => {
  const startDateCheck = moment(eventStart, DateFormats.YearMonthDay)
  const endDateCheck = moment(eventEnd, DateFormats.YearMonthDay)
  if (startDateCheck.isValid() && endDateCheck.isValid()) {
    const currentDate = moment().format(DateFormats.YearMonthDay)
    const nextDayDate = moment().add(1, "days").format(DateFormats.YearMonthDay)

    let formattedDateString = ""
    if (eventStart === eventEnd) {
      const dayOfWeek = handleCondition(
        eventStart === currentDate,
        translate("upcomingEvent.today"),
        handleCondition(
          eventStart === nextDayDate,
          translate("upcomingEvent.tomorrow"),
          moment(eventStart).locale(locale).format(DateFormats.DayOfWeek),
        ),
      )
      formattedDateString = `${dayOfWeek}, ${moment(eventStart)
        .locale(locale)
        .format(DateFormats.DayMonthYear)}`
    } else {
      formattedDateString =
        moment(eventStart).locale(locale).format(DateFormats.Year) ===
          moment(eventEnd).locale(locale).format(DateFormats.Year)
          ? `${moment(eventStart).locale(locale).format(DateFormats.DateMonth)} - ${moment(eventEnd)
            .locale(locale)
            .format(DateFormats.DayMonthYear)}`
          : `${moment(eventStart).locale(locale).format(DateFormats.DayMonthYear)} - ${moment(
            eventEnd,
          )
            .locale(locale)
            .format(DateFormats.DayMonthYear)}`
    }
    return formattedDateString
  } else {
    return ""
  }
}

function flyGetRetimedDate(date: Moment, locale: Locales = Locales.en) {
  return moment(date).locale(locale).format(DateFormats.DayMonth)
}

const convertTimeFrom24to12Hrs = (timeString, locale: Locales = Locales.en) => {
  const formattedTime = moment(timeString, TimeFormats["24Hours"])
    .locale(locale)
    .format(TimeFormats["12Hours"])
  return formattedTime
}

const getCurrentTimeSingapore = () => {
  return MomentTimeZone.tz(moment(), "Asia/Singapore").format("YYYY-MM-DD HH:mm")
}

const getCurrentDateSingapore = () => {
  return MomentTimeZone.tz(moment(), "Asia/Singapore").format("YYYY-MM-DD")
}

const getDateSingapore = (datetime) => {
  return MomentTimeZone.tz(datetime, "Asia/Singapore").format("YYYY-MM-DD")
}

const getCurrentTimeStampSingapore = () => {
  return MomentTimeZone.tz("Asia/Singapore").valueOf()
}

const convertDateTimeToTimeStampSingapore = (datetime, format = "YYYY-MM-DD HH:mm") => {
  return MomentTimeZone.tz(moment(datetime, format), "Asia/Singapore")?.valueOf()
}

const convertDateTimeToSingapore = (datetime, format, toFormat) => {
  return MomentTimeZone.tz(moment(datetime, format || "YYYY-MM-DD HH:mm"), "Asia/Singapore")?.format(toFormat)
}

function dateToFromNow(date) {
  return moment(date).calendar(null, {
    lastWeek: `ddd, ${DateFormats.DayMonthYear}`,
    lastDay: `[Yesterday], ${DateFormats.DayMonthYear}`,
    sameDay: `[Today], ${DateFormats.DayMonthYear}`,
    nextDay: `[Tomorrow], ${DateFormats.DayMonthYear}`,
    nextWeek: `ddd, ${DateFormats.DayMonthYear}`,
    sameElse: `ddd, ${DateFormats.DayMonthYear}`,
  })
}

const convertGMTToSingapore = (timeStamp: string) => {
  const currentTimeToSingapore = MomentTimeZone.utc(timeStamp)
    .tz("Asia/Singapore")
    .format(DateFormats.YearMonthDayTime)
  return currentTimeToSingapore
}

const getCurrentDate = () => {
  const today: any = new Date()
  let dd: any = today.getDate()
  let mm: any = today.getMonth() + 1 // January is 0!

  const yyyy = today.getFullYear()
  if (dd < 10) {
    dd = "0" + dd
  }
  if (mm < 10) {
    mm = "0" + mm
  }
  return yyyy + "-" + mm + "-" + dd
}

const getWeekDay = () => {
  const today = new Date()
  const day = today.getDay()
  if (day === 0 || day === 6) {
    return "WeekEnd"
  }
  return "WeekDay"
}
const getScheduledTime = (days: number,hours: number, minutes: number): string => {
  const now = moment();
 return (now.clone().startOf('day').add(days, 'day').add(hours, 'hour').add(minutes, 'minute')).toISOString();
}

/**
 * Format campaign date range for display
 * @param start Campaign start date
 * @param end Campaign end date
 * @returns Formatted date string or undefined if both dates are empty
 */
const formatCampaignDateRange = (start, end) => {
  if (!start && !end) {
    return
  } else if (!start) {
    return moment(end).format(DateFormats.DayMonthYear)
  } else if (!end) {
    return moment(start).format(DateFormats.DayMonthYear)
  }
  const startYear = moment(start).format(DateFormats.Year)
  const endYear = moment(end).format(DateFormats.Year)
  let startTime = ""
  let endTime = ""
  if (startYear !== endYear) {
    startTime = moment(start).format(DateFormats.DayMonthYear)
    endTime = moment(end).format(DateFormats.DayMonthYear)
  } else {
    startTime = moment(start).format(DateFormats.DateMonth)
    endTime = moment(end).format(DateFormats.DayMonthYear)
  }
  return `${startTime} - ${endTime}`
}

export {
  dayIdentifier,
  convertTimestampToDate,
  toDate,
  dateToFromNow,
  dateRangeFormatting,
  flyModuleDateFormatting,
  formatMonthYear,
  flyModuleUpdatedTime,
  flyGetRetimedDate,
  eventCardDateFormatting,
  convertTimeFrom24to12Hrs,
  getCurrentTimeSingapore,
  getCurrentDateSingapore,
  getDateSingapore,
  getCurrentTimeStampSingapore,
  convertDateTimeToTimeStampSingapore,
  convertGMTToSingapore,
  getCurrentDate,
  getWeekDay,
  convertDateTimeToSingapore,
  getScheduledTime,
  formatCampaignDateRange,
}
