import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import {
  AEM_FILTER_TYPES,
  AppConfigPermissionTypes,
  CM24RedirectSource,
  NavigationConstants,
  NavigationInAppValueSpecial,
  SOURCE_SYSTEM,
  StateCode,
} from "./constants"
import { useDispatch, useSelector } from "react-redux"
import { useNavigation as useReactNavigation } from "@react-navigation/native"
import { useCurrentScreen, useGeneratePlayPassUrl } from "app/utils/screen-hook"
import { getChangiGameUrl, getDeepLink, getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { ProfileSelectors } from "app/redux/profileRedux"
import React, { useContext, useEffect, useState } from "react"
import { BAGGAGE_TRACKER_CONTEXT } from "app/services/context/baggage-tracker"
import { WebViewHeaderTypes } from "app/models/enum"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { AppEventsLogger } from "react-native-fbsdk-next"
import { FB_EVENT_NAME } from "app/services/facebook/event-name"
import { analyticsLogEvent, ANALYTICS_LOG_EVENT_NAME, dtACtionLogEvent, dtBizEvent } from "app/services/firebase"
import {
  childReservation,
  filterState,
  itemReservation,
  ReservationValues,
  resultFilterTitles,
} from "app/sections/dine-reservation/dine-reservation"
import DineCreators from "app/redux/dineRedux"
import { load, StorageKey } from "./storage"
import { InteractionManager, Linking } from "react-native"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { isMonarchTier } from "./screen-helper"
import { getExperienceCloudId } from "app/services/adobe"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS, isFlagON } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import _set from 'lodash/set'
import SystemActions from "app/redux/systemRedux"
import { useRewardTier } from "app/hooks/useRewardTier"
import { store } from "app/redux/store"
import { checkLoginState } from "./authentication"
import { useRetroClaims } from "app/hooks/useRetroClaims"
import { DINE_SHOP_TAB_SCREENS } from "app/screens/dine-shop/constants"
import { translate } from "app/i18n"
import { useCPay } from "app/helpers/changipay"
import { useGetConfigurationPermissionHelper } from "./get-configuration-permission"
import { ifAllTrue } from "."
import { DriveContext } from "app/services/context/drive"
import moment from 'moment'
import ModalManagerActions from "app/redux/modalManagerRedux"
import { getISCLinkRedirectTarget } from "app/helpers/deeplink/deeplink-parameter"

const SCREEN_NAME = "APPSCAPADE_SCREEN"
enum NavigationTypes {
  walletPasses = "wallet_passes",
  walletTravel = "wallet_travel",
  walletCredits = "wallet_credits",
  walletPerks = "wallet_perks",
  walletOrders = "wallet_orders",
  exploreChangi = "exploreChangi",
  fly = "fly",
  flights = "flights",
  airport = "airport",
  nativeLoginScreen = "nativeLoginScreen",
  monarchPrivilegesScreen = "monarchprivilegesscreen",
  changiRewardPrivileges = "changirewardprivileges",
  previousPage = "previousPage",
  contactUs = "contactus",
  parkingMyIU = "parkingmyiu",
  parkingAvailability = "parkingavailability",
  parkingFreePromos = "parkingfreepromos",
  parkingCoupons = "parkingcoupons",
  parkingCalculator = "parkingcalculator",
  parkingOtherServices = "parkingotherservices",
  parkingFindMyCar = "parkingfindmycar",
}
export enum ChangiGameValues {
  TravelPersonality = "travelpersonality",
  HatchBingo = "hatch_bingo",
  ChangiMillionaire = "changimillionaire",
}

export enum NavigationValueDeepLink {
  appscapade = "appscapade",
  appscapadePlayAndWin = "appscapadeplayandwin",
  ishopchangi = "ishopchangi",
  baggagetracker = "baggage_tracker",
  monarchConciergeWA = "monarch_concierge_wa",
  ishopchangi_pagelink = "ishopchangi_pagelink",
  gUserConciergeWA = "guser_concierge_wa",
  chatBot = "chatbot",
  gameMain = "game_main",
  gameAsteroid = "game_asteroid",
  gameMissionpass = "game_missionpass",
  maingamelandingClaimcomets = "maingamelanding_claimcomets",
  missionpass = "missionpass",
  leaderboardgame = "leaderboardgame",
  retroclaim = "retroclaim",
  marketplace = "marketplace",
  gameSkillGame = "game_skillgame",
  gameShare = "game_share",
  carpass = "carpass",
  ishopchangiPromoCode = "ishopchangi_promo_code",
}

export enum NavigationPageSource {
  ScrollBuddy = "scrollBuddy",
  LatestHappenings = "latestHappenings",
  FlightLanding = "FlightLanding",
  accountLanding = "accountLanding",
  perksTab = "perksTab",
  staffPerkPromotionDetailModal = "staffPerkPromotionDetailModal",
  Epic = "epic",
}

export enum NavigationAATag {
  ScrollBuddy = "Explore | Scroll Buddy",
  LatestHappenings = "Explore | Latest Happenings | ",
  FlightLanding = "Flight Landing | Launch Details",
  FlightLandingNotLoggedIn = "Flight Landing | Launch Details Non-Logged In",
  claimTodayComets = "Daily Comets Streak | Claim Today's Comets",
  claimTodayCometsNonLogin = "Daily Comets Streak | Login to Claim Today's Comets",
  missionPass = "Mission Pass | Launch Now",
  missionPassNonLogin = "Mission Pass | Login to Play",
  asteroidChallenge = "Asteroid Challenge | Play Now",
  asteroidChallengeNonLogin = "Asteroid Challenge | Login to Play",
  redeemPerks = "Redeem Perks | ",
  offerDetails = "Offer Details | ",
  Epic = "EPIC | ",
  miffyGameMainEnterQuest = "Miffy & Friends | Enter Quest",
  miffyGameMainLoginToJoin = "Miffy & Friends | Login to Join Quest",
  miffyGameMainGiftFriend = "Miffy & Friends | Gift A Friend",
}

const navigationValueNeedAuth = [
  NavigationTypes.walletPasses,
  NavigationTypes.walletTravel,
  NavigationTypes.walletCredits,
  NavigationTypes.walletPerks,
  NavigationTypes.walletOrders,
  "transactions",
  NavigationConstants.profileScreen,
  NavigationConstants.redeemRewardDetailScreen,
  NavigationConstants.redemptionCatalogueScreen,
  NavigationTypes.parkingMyIU,
]
export const handleOpenMonarchConciergeWA = async () => {
  const res = await load(StorageKey.conciergeWAEndpoint)
  if (res) {
    Linking.openURL(res)
  }
}

export const handleOpenGUserConciergeWA = async () => {
  const res = await getDeepLinkV2({
    stateCode: StateCode.GUSER_CONCIERGE_WA,
  })
  const whatsAppLink = res?.redirectUri
  if (whatsAppLink) {
    Linking.openURL(whatsAppLink)
  }
}

export const useNavigation = () => {
  const navigation = useReactNavigation()

  const navigate = (...args) => navigation.navigate(...args as never)

  return { ...navigation, navigate }
}

export const handleNavigationForIShopChangi = async (stateCode, input: any = {}, navigation, fallbackURL?: string) => {
  const ecid = await getExperienceCloudId()
  const CSMInput = {
    ...input,
    ecid,
  }
  let response: any = {}
  GlobalLoadingController.showLoading(true)
  response = await getDeepLinkV2({
    stateCode,
    input: CSMInput,
  })
  GlobalLoadingController.hideLoading()
  if (response?.redirectUri) {
    navigation.navigate(NavigationConstants.playpassWebview, {
      uri: response?.redirectUri,
      needBackButton: true,
      needCloseButton: true,
      headerType: WebViewHeaderTypes.default,
      basicAuthCredential: response?.basicAuth,
      isIShopChangi: true
    })
  } else if (fallbackURL){
    navigation.navigate(NavigationConstants.webview, {
      uri: fallbackURL,
    })
  }
}

export const handleNavigationForIShopChangiPromoCode = (stateCode, input: any = {}, navigation) => {
  const newInput = getISCLinkRedirectTarget(input?.redirect)
  handleNavigationForIShopChangi(stateCode, newInput, navigation)
}

export const handleModeCarPassCsmAPI = async (navigation, dispatch, stateCode) => {
  const enableFlightCSM_CarParss = isFlagON(REMOTE_CONFIG_FLAGS.CSM_CARPARSS)
  try {
    const payload = {
      stateCode: enableFlightCSM_CarParss ? StateCode?.CARPASS_DRIVE : StateCode?.CARPASS,
      input: {
        stateCode: stateCode,
        ecid: await getExperienceCloudId(),
      },
    }
    const getLink = await getDeepLinkV2(payload, true)
    navigation.navigate(NavigationConstants.playpassWebview, {
      uri: getLink?.redirectUri,
      basicAuthCredential: getLink?.basicAuth,
      needCloseButton: true,
      needBackButton: true,
      headerType: WebViewHeaderTypes.default,
    })
  } catch (error) {
    dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
  }
}

export const handleModeCarPassCsmFDL = async (navigation, dispatch, navigationInput) => {
  const stateCode = navigationInput?.stateCode
  const enableDriveParking = isFlagON(REMOTE_CONFIG_FLAGS.DRIVE_PARKING)
  if (enableDriveParking) {
    navigation.navigate(NavigationConstants.parkingLanding, { stateCode: `${stateCode}TIME-${moment().valueOf()}` })
  } else {
    handleModeCarPassCsmAPI(navigation, dispatch, stateCode)
  }
}

export const useHandleNavigation = (typeCode?, newNavigation = null) => {
  const [isOpenChangiPay, setOpenChangiPay] = useState(false)
  const { cm24Flag, accountContactUsFlag } = React.useContext(AccountContext)
  const {driveParkingFeatureFlag: driveParkingFlag, csmCarPassFeatureFlag} = React.useContext(DriveContext)
  const navigation = newNavigation || useReactNavigation()
  const dispatch = useDispatch()
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const uid = profilePayload?.id
  const { getCommonLoginModule } = useGeneratePlayPassUrl(typeCode, false, newNavigation)
  const BAGGAGE_HANDLERS = useContext(BAGGAGE_TRACKER_CONTEXT).Handlers
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const isMonarch = isMonarchTier(rewardsData?.reward?.currentTierInfo)
  const {memberIconInfo} = useRewardTier()
  const { accessRetroClaims } = useRetroClaims({ navigation })
  const moreOptionsData = useSelector(ForYouSelectors.moreOptionsData)
  const { getConfigApp, notifyDisableChangiPay } =
      useGetConfigurationPermissionHelper()
  const currentScreen = useCurrentScreen(navigation)

  const getChangiGame = async (navigationVal) => {
    GlobalLoadingController.showLoading(true)
    const response = await getChangiGameUrl({
      qrCodeId: "",
      flightNo: "",
      gameCode: navigationVal,
      crCardNo: profilePayload?.cardNo,
    })
    GlobalLoadingController.hideLoading()
    if (response?.data?.getChangiGames?.url) {
      navigation.navigate(NavigationConstants.playpassWebview, {
        uri: response?.data?.getChangiGames?.url,
        needBackButton: true,
        needCloseButton: true,
      })
    }
  }

  const getCM24DeepLinkInput = (options) => {
    const redirectFrom = options?.redirectFrom
    let newInput = {}
    switch (redirectFrom) {
      case CM24RedirectSource.QuickLinks:
        _set(newInput, "landingPage", "main")
        _set(newInput, "utmParameters.source", "ChangiApp")
        _set(newInput, "utmParameters.medium", "app_quicklink")
        _set(newInput, "utmParameters.term", "button")
        break
      case CM24RedirectSource.LatestHappenings:
        _set(newInput, "landingPage", "main")
        _set(newInput, "utmParameters.source", "ChangiApp")
        _set(newInput, "utmParameters.medium", "app_latesthappenings")
        _set(newInput, "utmParameters.term", "tile")
        break
      case CM24RedirectSource.AccountCM24:
        _set(newInput, "landingPage", "main")
        _set(newInput, "utmParameters.source", "ChangiApp")
        _set(newInput, "utmParameters.medium", "app_account")
        _set(newInput, "utmParameters.term", "button")
        break
      case CM24RedirectSource.VPR:
        _set(newInput, "landingPage", "wallet")
        _set(newInput, "utmParameters.source", "ChangiApp")
        _set(newInput, "utmParameters.medium", "app_account")
        _set(newInput, "utmParameters.term", "button")
        break
      default:
        _set(newInput, "landingPage", "main")
        _set(newInput, "utmParameters.source", "ChangiApp")
        break
    }

    return newInput
  }

  const getChangiMillionaireUrl = async (input, options) => {
    GlobalLoadingController.showLoading(true)
    const isCM24 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.CSM_CM24, cm24Flag)
    let response = null
    if (isCM24) {
      try {
        const updatedInput = getCM24DeepLinkInput(options)
        response = await getDeepLinkV2({
          stateCode: StateCode.CM24,
          input: updatedInput,
        }, true)
      } catch {
        GlobalLoadingController.hideLoading()
        dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
        return
      }
    } else {
      response = await getDeepLink({
        stateCode: StateCode.CHANGI_MILLIONAIRE,
        input,
      })
    }
    GlobalLoadingController.hideLoading()
    if (response?.redirectUri) {
      navigation.navigate(NavigationConstants.playpassWebview, {
        uri: response?.redirectUri,
        needBackButton: true,
        needCloseButton: true,
        headerType: WebViewHeaderTypes.changiMillionaire,
        basicAuthCredential: response?.basicAuth,
        ...options?.routeParams || {}
      })
    }
  }

  const handleNavigationForChangiGame = (navigationVal) => {
    const isLoggedIn = checkLoginState()
    if (!isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => getChangiGame(navigationVal),
        callBackAfterLoginCancel: () => null,
      })
    } else getChangiGame(navigationVal)
  }

  const handleNavigationForChangiMillionaire = (input, options) => {
    const isCM24 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.CSM_CM24, cm24Flag)
    const isLoggedIn = checkLoginState()
    if (!isCM24 && !isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.CHANGI_MILLIONAIRE,
        callBackAfterLoginSuccess: () => getChangiMillionaireUrl(input, options),
        callBackAfterLoginCancel: () => null,
      })
    } else getChangiMillionaireUrl(input, options)
  }

  const handleNavigationForCLM = (stateCode, notRequireLogin = false) => {
    const isLoggedIn = checkLoginState()
    if (notRequireLogin) {
      getCommonLoginModule(stateCode)
    } else {
      if (isLoggedIn) {
        getCommonLoginModule(stateCode)
        return
      }
      const sourceSystem = stateCode === StateCode.CARPASS ? SOURCE_SYSTEM.OTHERS : ""
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem,
        callBackAfterLoginSuccess: () => getCommonLoginModule(stateCode),
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const handleNavigationForAppscapade = async (stateCode) => {
    GlobalLoadingController.showLoading(true)
    const response = await getDeepLink({
      stateCode,
      input: { uid },
    })
    GlobalLoadingController.hideLoading()
    if (response?.redirectUri) {
      AppEventsLogger.logEvent(FB_EVENT_NAME.APPSCAPADE_LANDING, null)
      analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.APPSCAPADE_LANDING)
      dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.APPSCAPADE_LANDING)
      dtBizEvent(SCREEN_NAME,ANALYTICS_LOG_EVENT_NAME.APPSCAPADE_LANDING, 'TAP-ON-APPSCAPADE-LP',{})
      navigation.navigate(NavigationConstants.playpassWebview, {
        uri: response?.redirectUri,
        needBackButton: true,
        needCloseButton: true,
        headerType: WebViewHeaderTypes.appscapadeLP,
        basicAuthCredential: response?.basicAuth,
      })
    }
  }
  const handleNavigationForAppscapadePlayAndWin = async (input) => {
    GlobalLoadingController.showLoading(true)
    const { chanceId } = input || false
    const response = await getDeepLink({
      stateCode: StateCode.APPSCAPADE_PLAY_AND_WIN_LP,
      input: { chanceId },
    })
    GlobalLoadingController.hideLoading()
    if (response?.redirectUri) {
      navigation.push(NavigationConstants.playpassWebview, {
        uri: response?.redirectUri,
        needBackButton: true,
        needCloseButton: true,
        headerType: WebViewHeaderTypes.appscapadeInstantWinLP,
        basicAuthCredential: response?.basicAuth,
      })
    }
  }

  const handleOpenAskMaxChatbot = async(navigationInput?) => {
    GlobalLoadingController.showLoading(true)
    GlobalLoadingController.setCurrentRetryLabel(translate("common.close"))
    try{
      const response = await getDeepLinkV2({
        stateCode: "CHATBOT",
        input: {},
        params: ""
      }, true)
      GlobalLoadingController.hideLoading()
      if (response?.redirectUri) {
        GlobalLoadingController.hideRetryBottomRef()
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.appscapadeInstantWinLP,
          basicAuthCredential: response?.basicAuth,
          onCloseBtnPress: navigationInput?.onCloseWebviewBtnPress,
        })
      } else {
        GlobalLoadingController.hideLoading()
        GlobalLoadingController.showRetryBottomRef()
        // Comment here to revert if need 1
        // handleOpenAskMaxChatbot.arguments = [navigationInput]
        GlobalLoadingController.setCurrentRetry(
          // handleOpenAskMaxChatbot
          GlobalLoadingController.hideRetryBottomRef
        )
      }
    } catch (error){
      GlobalLoadingController.hideLoading()
      GlobalLoadingController.showRetryBottomRef()
      // Comment here to revert if need 1
      // handleOpenAskMaxChatbot.arguments = [navigationInput]
      GlobalLoadingController.setCurrentRetry(
        // handleOpenAskMaxChatbot
        GlobalLoadingController.hideRetryBottomRef
      )
    } 
  }

  const getGamificationLandingPage = (navValue) => {
    switch (navValue) {
      case NavigationValueDeepLink.gameMain:
        return "maingamelanding"
      case NavigationValueDeepLink.gameAsteroid:
        return "leaderboardgame"
      case NavigationValueDeepLink.gameMissionpass:
        return "missionpass"
      case NavigationValueDeepLink.maingamelandingClaimcomets:
        return "maingamelanding_claimcomets"
      case NavigationValueDeepLink.missionpass:
        return "missionpass"
      case NavigationValueDeepLink.leaderboardgame:
        return "leaderboardgame"
      case NavigationValueDeepLink.gameSkillGame:
        return "skillgame"
      case NavigationValueDeepLink.gameShare:
        return "share"
      default:
        return undefined
    }
  }

  const getGamificationUtmParameters = (navValue, input) => {
    const result: any = {
      source: "changiapp",
      campaign: input?.utmCampaign || "spaceappxplorer24",
    }
    switch (input?.pageSource) {
      case NavigationPageSource.ScrollBuddy:
        result.medium = "app_explore_scrollbuddy"
        break
      case NavigationPageSource.LatestHappenings:
        result.medium = "app_explore_latesthappenings"
        break
      case NavigationPageSource.FlightLanding:
        result.medium = "app_flightlandingpage"
        break
      case NavigationPageSource.accountLanding:
        result.medium = "app_account"
        result.campaign = "miffybirthdaybash25"
        break
      case NavigationPageSource.perksTab:
        result.medium = "app_pp_perk"
        break
      case NavigationPageSource.staffPerkPromotionDetailModal:
        result.medium = "app_staffperks"
        break
      case NavigationPageSource.Epic:
        result.medium = "app_pp_perk_epic"
        result.campaign = "miffybirthdaybash25"
        break;
      default:
        break
    }
    switch (navValue) {
      case NavigationValueDeepLink.gameMain:
        result.content = input?.isLoggedInAtTriggerTime
          ? "maingamelanding"
          : "maingamelanding_nonlogin"
        break
      case NavigationValueDeepLink.gameAsteroid:
        result.content = input?.isLoggedInAtTriggerTime
          ? "leaderboardgame"
          : "leaderboardgame_nonlogin"
        break
      case NavigationValueDeepLink.gameMissionpass:
        result.content = input?.isLoggedInAtTriggerTime
          ? "missionpass"
          : "missionpass_nonlogin"
        break
      case NavigationValueDeepLink.maingamelandingClaimcomets:
          result.content = input?.isLoggedInAtTriggerTime
          ? "dailycheckin"
          : "dailycheckin_nonlogin"
        break
      case NavigationValueDeepLink.missionpass:
          result.content = input?.isLoggedInAtTriggerTime
          ? "missionpass"
          : "missionpass_nonlogin"
        break
      case NavigationValueDeepLink.leaderboardgame:
          result.content = input?.isLoggedInAtTriggerTime
          ? "leaderboardgame"
          : "leaderboardgame_nonlogin"
        break
      case NavigationValueDeepLink.gameSkillGame:
        result.content = input?.isLoggedInAtTriggerTime
          ? "skillgame"
          : "skillgame_nonlogin"
        break
      case NavigationValueDeepLink.gameShare:
        result.content = "share"
        break
      default:
        break
    }
    if (
      ifAllTrue([
        [NavigationPageSource.ScrollBuddy, NavigationPageSource.LatestHappenings].some(
          (val) => val === input?.pageSource,
        ),
        navValue === NavigationValueDeepLink.gameMain,
      ]) ||
      ifAllTrue([
        input?.pageSource === NavigationPageSource.LatestHappenings,
        [NavigationValueDeepLink.gameMissionpass, NavigationValueDeepLink.gameSkillGame].some(
          (val) => val === navValue,
        ),
      ])
    ) {
      result.campaign = "miffybirthdaybash25"
    }

    return result
  }

  const getGamificationLink = async (...args: any[]) => {
    const [navValue, input] = args || []
    const ecid = await getExperienceCloudId()
    const gamificationInput : any = {
      aaTag: input?.aaTag,
      ecid,
      landingPage: getGamificationLandingPage(navValue),
      staff: store.getState()?.profileReducer?.profilePayload?.airportStaff ? 1 : 0,
      utmParameters: getGamificationUtmParameters(navValue, input)
    }
    if (input?.taskCode) {
      gamificationInput.taskCode = input?.taskCode
    }
    let response = null
    GlobalLoadingController.showLoading(true)
    try {
      response = await getDeepLinkV2({
        stateCode: StateCode.GAMIFICATION,
        input: gamificationInput,
      }, true)
    } catch {
      GlobalLoadingController.hideLoading()
      dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
      return
    }
    GlobalLoadingController.hideLoading()
    if (response?.redirectUri) {
      navigation.navigate(NavigationConstants.playpassWebview, {
        uri: response?.redirectUri,
        needBackButton: true,
        needCloseButton: true,
        basicAuthCredential: response?.basicAuth,
      })
    }
  }

  const handleNavigationForGamification = (...args: any[]) => {
    const isLoggedIn = checkLoginState()
    if (!isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => getGamificationLink(...args),
        callBackAfterLoginCancel: () => null,
        sourceSystem: SOURCE_SYSTEM.GAME_2024,
      })
    } else getGamificationLink(...args)
  }
  const {openChangiPay} = useCPay()

  const getCarPassLink = async (...args: any[]) => {
    const [input] = args || []
    const vehicleIU = store.getState()?.profileReducer?.profilePayload?.vehicleIU
    const isCmsCarPassOn = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.CSM_CARPARSS, csmCarPassFeatureFlag)

    if (!vehicleIU) {
      input?.noVehicleIUcallback?.()
      return;
    }

    const ecid = await getExperienceCloudId()
    const carpassInput = {
      ecid,
      stateCode: input?.stateCode,
    }
    let response = null
    GlobalLoadingController.showLoading(true)
    try {
      response = await getDeepLinkV2({
        stateCode: isCmsCarPassOn ? StateCode.CARPASS_DRIVE : StateCode.CARPASS,
        input: carpassInput,
      }, true)
    } catch {
      GlobalLoadingController.hideLoading()
      dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
      return
    }
    GlobalLoadingController.hideLoading()
    if (response?.redirectUri) {
      navigation.navigate(NavigationConstants.playpassWebview, {
        uri: response?.redirectUri,
        needBackButton: true,
        needCloseButton: true,
        basicAuthCredential: response?.basicAuth,
        onBackBtnPress: () => {
          navigation.goBack()
          input?.refreshData?.()
        },
        onCloseBtnPress: () => {
          navigation.goBack()
          input?.refreshData?.()
        },
      })
    }
  }

  const handleNavigationForCarParking = (...args: any[]) => {
    const isLoggedIn = checkLoginState()
    if (!isLoggedIn) {
      navigation.navigate(NavigationConstants.authScreen, {
        callBackAfterLoginSuccess: () => getCarPassLink(...args),
        callBackAfterLoginCancel: () => null,
        sourceSystem: SOURCE_SYSTEM.OTHERS,
      })
    } else getCarPassLink(...args)
  }

  useEffect(() => {
    if (isOpenChangiPay && profilePayload) {
      setOpenChangiPay(false)
      getConfigApp({
        configKey: AppConfigPermissionTypes.changiappWalletEnabled,
        callbackSuccess: () => {
          openChangiPay()
        },
        callbackFailure: () => notifyDisableChangiPay(),
      })
    }
  }, [isOpenChangiPay, profilePayload])
  const handleNavigationChangiPay = () => {
    const isLoggedIn = checkLoginState()
    if (isLoggedIn && profilePayload) {
      InteractionManager.runAfterInteractions(() => {
        setOpenChangiPay(true)
      })
    } else {
      //@ts-ignore
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.CPAY,
        callBackAfterLoginSuccess: () => {
          InteractionManager.runAfterInteractions(() => {
            setOpenChangiPay(true)
          })
        },
        callBackAfterLoginCancel: () => null,
      })
    }
  }

  const handleNavigationInApp = (navigationValue, options: any = {}) => {
    const {
      adobeTagName,
      handleParkingCoupons,
      handleParkingMyIU,
      handleParkingPromotions,
      handleParkingServices,
    } = options
    switch (navigationValue?.toLowerCase()) {
      case NavigationInAppValueSpecial.dine?.toLowerCase():
      case NavigationInAppValueSpecial.shop?.toLowerCase():
        navigation?.popToTop?.()
        navigation?.navigate("dineShop", {
          screen: navigationValue,
        })
        break
      case NavigationTypes.walletPasses:
        navigation?.navigate(NavigationConstants.bookingsOrdersScreen)
        break
      case NavigationTypes.walletTravel:
        navigation?.navigate(NavigationConstants.saveFlightsScreen)
        break
      case NavigationTypes.walletCredits:
        navigation?.navigate(NavigationConstants.creditsScreen)
        break
      case NavigationTypes.walletPerks:
        navigation?.navigate(NavigationConstants.redemptionCatalogueScreen, {
          screen: NavigationConstants.perksTab,
        })
        break
      case NavigationTypes.walletOrders:
        navigation?.navigate(NavigationConstants.bookingsOrdersScreen)
        break
      case NavigationTypes.exploreChangi.toLowerCase():
        navigation?.popToTop?.()
        navigation?.navigate(NavigationConstants.explore, { isScrollToExploreChangiSection: true })
        break
      case NavigationTypes.fly:
        navigation?.popToTop?.()
        navigation?.navigate(NavigationConstants.fly)
        break
      case NavigationTypes.flights:
        navigation?.navigate(NavigationConstants.fly, { screen: NavigationTypes.flights })
        break
      case NavigationTypes.airport:
        navigation?.navigate(NavigationConstants.fly, { screen: NavigationTypes.airport })
        break
      case NavigationTypes.monarchPrivilegesScreen:
        if (isMonarch) {
          navigation?.navigate(NavigationConstants.monarchPrivileges)
        }
        break
      case ReservationValues.reservations:
        dispatch(DineCreators.dineHandleFilterDetails(itemReservation, childReservation))
        dispatch(DineCreators.dineSetFilterTitles(resultFilterTitles))
        dispatch(DineCreators.startRequestFilter(true))
        navigation.navigate(NavigationConstants.dineFilterResultsScreen, {
          filteredData: filterState,
        })
        break
      case NavigationTypes.changiRewardPrivileges:
        InteractionManager.runAfterInteractions(() => {
          navigation.navigate(NavigationConstants.changiRewardsPrivilegesScreen, {
            tierMember: memberIconInfo?.title,
          })
        })
        break
      case NavigationTypes.nativeLoginScreen:
      case NavigationTypes.nativeLoginScreen.toLowerCase():
        navigation.navigate(NavigationConstants.authScreen, {
          sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP,
          callBackAfterLoginSuccess: () => navigation.navigate(NavigationConstants.explore),
        })
        break
      case NavigationTypes.previousPage:
      case NavigationTypes.previousPage.toLowerCase():
        navigation.goBack()
        break
      case NavigationTypes.contactUs:
        const isAccountContactUsOn = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.ACCOUNT_CONTACTUS, accountContactUsFlag)
        if (isAccountContactUsOn) {
          dispatch(ModalManagerActions.openModal("bottomSheetVcea"))
          dispatch(SystemActions.setBottomSheetVceaData({ adobeTagName }))
        } else {
          const contactUsInfo = moreOptionsData?.find?.(
            (ele) => !!ele?.fragmentTags?.find(i => i?.filterType === AEM_FILTER_TYPES.CONTACT_US_VARIATION)
          )
          if (contactUsInfo?.navigationValue && contactUsInfo?.navigationType) {
            handleNavigation(contactUsInfo?.navigationType, contactUsInfo?.navigationValue)
          }
        }
        break
      case NavigationTypes.parkingMyIU:
        if (currentScreen === NavigationConstants.parkingLanding) {
          handleParkingMyIU?.()
        } else {
          navigation.navigate(NavigationConstants.parkingLanding, {
            initiateParkingMyIURedirection: new Date().getTime(),
          })
        }
        break
      case NavigationTypes.parkingAvailability:
        navigation?.navigate?.(NavigationConstants.carPark)
        break
      case NavigationTypes.parkingFreePromos:
        if (currentScreen === NavigationConstants.parkingLanding) {
          handleParkingPromotions?.()
        } else {
          navigation.navigate(NavigationConstants.parkingLanding, {
            initiateParkingFreePromosRedirection: new Date().getTime(),
          })
        }
        break
      case NavigationTypes.parkingCoupons:
        if (currentScreen === NavigationConstants.parkingLanding) {
          handleParkingCoupons?.()
        } else {
          navigation.navigate(NavigationConstants.parkingLanding, {
            initiateParkingCouponsRedirection: new Date().getTime(),
          })
        }
        break
      case NavigationTypes.parkingCalculator:
        navigation?.navigate?.(NavigationConstants.carPark, { activeTab: 1 })
        break
      case NavigationTypes.parkingOtherServices:
        if (currentScreen === NavigationConstants.parkingLanding) {
          handleParkingServices?.()
        } else {
          navigation.navigate(NavigationConstants.parkingLanding, {
            initiateParkingMoreServicesRedirection: new Date().getTime(),
          })
        }
        break
      case NavigationTypes.parkingFindMyCar:
        navigation?.navigate?.(NavigationConstants.carPark, { activeTab: 2 })
        break
      default:
        navigation?.navigate(navigationValue)
    }
  }

  const removeTrailingSlash = (url: string): string => {
    return url.endsWith('/') ? url.slice(0, -1) : url;
  };

  const handleNavigation = (navigationType, navigationValue, input: any = {}, options: any = {}) => {
    const navigationTyp = navigationType?.toLowerCase()
    const navigationVal = navigationValue?.toLowerCase()
    const isLoggedIn = checkLoginState()
    if (input?.redirectTarget) {
      input.redirectTarget = removeTrailingSlash(input.redirectTarget)
    }
    // Handle in-app navigation
    if (navigationTyp === NavigationTypeEnum.inApp) {
      const isDriveParkingOn = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.DRIVE_PARKING, driveParkingFlag)
      if (navigationVal === "car_pass") {
        if (isDriveParkingOn) {
          navigation.navigate(NavigationConstants.parkingLanding as never)
        } else {
          handleNavigationForCLM(StateCode.CARPASS)
        }
        return true
      } else if (navigationVal === "parking_onboarding") {
        navigation.navigate(NavigationConstants.DriveOnboarding as never)
        return true
      } else if (navigationVal === 'pay') {
        handleNavigationChangiPay()
        return true
      } else if (
        [
          NavigationTypes.parkingAvailability,
          NavigationTypes.parkingCalculator,
          NavigationTypes.parkingCoupons,
          NavigationTypes.parkingFindMyCar,
          NavigationTypes.parkingFreePromos,
          NavigationTypes.parkingMyIU,
          NavigationTypes.parkingOtherServices,
        ].some((val) => val === navigationVal) &&
        !isDriveParkingOn
      ) {
        // If Drive Parking FF is OFF, stop navigation
        return
      } else if (!isLoggedIn && navigationValueNeedAuth?.includes(navigationVal)) {
        navigation.navigate(NavigationConstants.authScreen, {
          callBackAfterLoginSuccess: () =>
            handleNavigationInApp(navigationValue, options),
        })
        return
      }
      handleNavigationInApp(navigationValue, options)
    // Handle external navigation
    } else if (navigationTyp === NavigationTypeEnum.external) {
      if (input?.goToExternalLink) {
        input.goToExternalLink(navigationValue)
      } else {
        navigation.navigate(NavigationConstants.webview, { uri: navigationValue, onGoBack: input?.onGoBack })
      }
    // Handle deep link navigation
    } else if (
      navigationVal === ChangiGameValues.HatchBingo ||
      navigationVal === ChangiGameValues.TravelPersonality
    ) {
      handleNavigationForChangiGame(navigationVal)
    } else if (navigationVal === ChangiGameValues.ChangiMillionaire) {
      handleNavigationForChangiMillionaire(input, options)
    } else if (navigationVal === NavigationValueDeepLink.baggagetracker) {
      BAGGAGE_HANDLERS.baggage_tracker_request({ navigation, sourceSystem: SOURCE_SYSTEM.OTHERS })
    } else if (navigationVal === NavigationValueDeepLink.ishopchangi) {
      handleNavigationForIShopChangi(StateCode.ISHOPCHANGI, input, navigation)
    } else if (navigationVal === NavigationValueDeepLink.ishopchangiPromoCode) {
      handleNavigationForIShopChangiPromoCode(StateCode.ISHOPCHANGI, input, navigation)
    } else if (navigationVal === NavigationValueDeepLink.ishopchangi_pagelink) {
      handleNavigationForIShopChangi(StateCode.ISHOPCHANGI_PAGELINK, input, navigation)
    } else if (navigationVal === NavigationValueDeepLink.appscapade) {
      handleNavigationForAppscapade(StateCode.APPSCAPADE_LP)
    } else if (navigationVal === NavigationValueDeepLink.appscapadePlayAndWin) {
      handleNavigationForAppscapadePlayAndWin(input)
    } else if (navigationVal === NavigationValueDeepLink.monarchConciergeWA) {
      handleOpenMonarchConciergeWA()
    } else if (navigationVal === NavigationValueDeepLink.gUserConciergeWA) {
      handleOpenGUserConciergeWA()
    } else if (navigationVal === NavigationValueDeepLink.chatBot) {
      handleOpenAskMaxChatbot(input)
    } else if (
      [
        NavigationValueDeepLink.gameMain,
        NavigationValueDeepLink.gameAsteroid,
        NavigationValueDeepLink.gameMissionpass,
        NavigationValueDeepLink.maingamelandingClaimcomets,
        NavigationValueDeepLink.missionpass,
        NavigationValueDeepLink.leaderboardgame,
        NavigationValueDeepLink.gameSkillGame,
        NavigationValueDeepLink.gameShare,
      ].some((val) => val === navigationVal)
    ) {    
      handleNavigationForGamification(navigationVal, input)
    } else if (navigationVal === NavigationValueDeepLink.retroclaim) {
      accessRetroClaims()
    } else if (navigationVal === NavigationValueDeepLink.marketplace) {
      navigation.navigate(NavigationConstants.dineShop, {screen: DINE_SHOP_TAB_SCREENS.marketplace})
    }  else if (navigationVal === NavigationValueDeepLink.carpass) {
      handleNavigationForCarParking(input)
    } else {
      handleNavigationForCLM(isLoggedIn, navigationValue)
    }
  }

  return {
    handleNavigation,
    handleOpenGUserConciergeWA,
    handleOpenMonarchConciergeWA,
  }
}
