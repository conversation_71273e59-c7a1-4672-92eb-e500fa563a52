import { ISearchYouMayAlsoLikeItem } from "app/sections/search-you-may-also-like/search-you-may-also-like"
import { get, isEmpty, cloneDeep, chunk } from "lodash"
import {
  ISHOPCHANGI_ITEMS_POSITION,
  MAX_ISHOPCHANGI_ITEMS,
  SEARCH_SOURCES,
} from "app/screens/search-v2/constants"
import moment from "moment-timezone"
import { getCurrentDateSingapore, getDateSingapore } from "app/utils/date-time/date-time"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { FlyList } from "app/redux/types/fly/fly"
import { FlightListingState } from "app/components/flight-listing-card/flight-listing-card.props"

const filterFlightsSearchResult = (flightsSearchResultData, searchingKeyword) => {
  if (!flightsSearchResultData?.length) {
    return flightsSearchResultData
  }
  let todayFlightsOnlyData = []
  let tomorrowFlightsOnlyData = []
  for (let i = 0; i < flightsSearchResultData.length; i++) {
    const flightElement = flightsSearchResultData[i]
    const { direction, flightNumber, scheduledDate } = flightElement || {}
    const isArrivalFlight = direction === FlightDirection.arrival
    const isDepartureFlight = direction === FlightDirection.departure

    const todaySingaporeDate = getCurrentDateSingapore()
    const isScheduledForToday = moment(scheduledDate).isSame(todaySingaporeDate, "day")
    const tomorrowSingaporeDate = getDateSingapore(moment().add(1, "day"))
    const isScheduledForTomorrow = moment(scheduledDate).isSame(tomorrowSingaporeDate, "day")
    if (`${flightNumber}`.toLowerCase() === `${searchingKeyword}`.toLowerCase()) {
      const isArrivalFlightScheduledForToday = isArrivalFlight && isScheduledForToday
      const isArrivalFlightScheduledForTomorrow = isArrivalFlight && isScheduledForTomorrow
      const isDepartureFlightScheduledForToday = isDepartureFlight && isScheduledForToday
      const isDepartureFlightScheduledForTomorrow = isDepartureFlight && isScheduledForTomorrow

      const shouldReturnElement =
        isArrivalFlightScheduledForToday ||
        (!isArrivalFlightScheduledForToday && isDepartureFlightScheduledForToday) ||
        (!isDepartureFlightScheduledForToday && isArrivalFlightScheduledForTomorrow) ||
        (!isArrivalFlightScheduledForTomorrow && isDepartureFlightScheduledForTomorrow)
      if (shouldReturnElement) {
        return [flightElement]
      }
    }
    if (isScheduledForToday) {
      todayFlightsOnlyData = [...todayFlightsOnlyData, flightElement]
    }
    if (isScheduledForTomorrow) {
      tomorrowFlightsOnlyData = [...tomorrowFlightsOnlyData, flightElement]
    }
  }
  if (todayFlightsOnlyData?.length) {
    return todayFlightsOnlyData
  }
  if (tomorrowFlightsOnlyData?.length) {
    return tomorrowFlightsOnlyData
  }
  return flightsSearchResultData
}

const convertDataSectionList = (
  dataConvert: any,
  type: string,
  savedFlight: any,
  textSearch: string,
) => {
  const isFlightsType = type === "flights"
  const data = dataConvert?.items?.map((item) => {
    if (isFlightsType) {
      const index = savedFlight?.getMyTravelFlightDetails?.findIndex(
        (flight) =>
          `${flight?.flightNumber}_${flight?.scheduledDate}` ===
            `${item?.flight_number}_${item?.scheduled_date}` &&
          flight?.direction === item?.direction,
      )
      let departingCodeIndex = { code: "", name: "" }
      let destinationCodeIndex = { code: "", name: "" }
      if (item?.direction === "ARR") {
        departingCodeIndex = {
          code: item?.airport_details?.code,
          name: item?.airport_details?.name,
        }
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      } else if (item?.direction === "DEP") {
        destinationCodeIndex = {
          code: item?.airport_details?.code,
          name: item?.airport_details?.name,
        }
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      }
      return {
        actualTimestamp: item?.actual_timestamp,
        estimatedTimestamp: item?.estimated_timestamp,
        logo: item?.airline_details?.logo_url,
        flightNumber: item.flight_number,
        departingCode: departingCodeIndex?.code,
        destinationCode: destinationCodeIndex?.code,
        direction: item?.direction,
        flightDate: item.scheduled_date,
        scheduledDate: item.scheduled_date,
        state: type,
        codeShare: item.slave_flights,
        destinationPlace: destinationCodeIndex?.name,
        departingPlace: departingCodeIndex?.name,
        timeOfFlight: item.scheduled_time,
        flightStatus: item.flight_status === "hide" ? "" : item.flight_status,
        isSaved: isNaN(index) ? false : index !== -1,
        isMSError: false,
        transits: undefined,
        flightStatusMapping: item.status_mapping?.listing_status_en,
        beltStatusMapping: item.status_mapping?.belt_status_en,
        statusColor: item.status_mapping?.status_text_color,
        showGate: item.status_mapping?.show_gate,
        flightUniqueId: `${item.flight_number}_${item.scheduled_date}`,
        checkInRow: item?.check_in_row,
        terminal: item?.terminal,
        boardingGate: item?.current_gate || item?.display_gate,
        displayBelt: item?.display_belt,
        sectionType: type,
        displayTimestamp: item?.display_timestamp_mapping,
        viaAirportDetails: item?.via_airport_details,
        country: item?.airport_details?.country,
      }
    }
    return {
      ...item,
      areaDisplay: item?.area_display,
      dietary: item?.aemTenantDetails?.dietary,
      categories: item?.categories,
      sectionType: type,
    }
  })

  if (type === "facilities") {
    return chunk(data?.slice(0, 4), 2)
  }

  if (["flights", "dines", "shops", "cities", "airlines"].includes(type)) {
    if (isFlightsType) {
      const filteredData = filterFlightsSearchResult(data, textSearch)
      return filteredData?.slice(0, 3)
    }

    return data?.slice(0, 3)
  }

  return data
}

const getIsViewMore = (dataConvert: any, maxLengthData: number = 0) => {
  const data = get(dataConvert, "items", [])
  return data?.length > maxLengthData
}

export const convertDataSearchResultV2 = (data) => {
  return data?.map((item) => {
    return FlyList.convertDto(
      item,
      FlightListingState.default,
    )
  });
}

const convertSearchAllV2Data = (searchResult) => {
  const items = searchResult?.items
  const totalFlights =
    (searchResult?.total_flights ?? 0) +
    (searchResult?.total_airlines ?? 0) +
    (searchResult?.total_airports ?? 0)

  if (!items?.length) return { items: [], totalFlights, facets: {} }

  const nonIscCaItems = []
  const caItems = []
  const iscItems = []

  for (const item of items) {
    if (item?.source === SEARCH_SOURCES.EVENTS && item?.visible_flag !== "true") {
      continue
    }

    if (item?.source === SEARCH_SOURCES.ISC) {
      iscItems.push(item)
    } else if (item?.source === SEARCH_SOURCES.CACONTENT) {
      caItems.push(item)
    } else {
      nonIscCaItems.push(item)
    }
  }

  const insertIndex =
    nonIscCaItems.length >= ISHOPCHANGI_ITEMS_POSITION
      ? ISHOPCHANGI_ITEMS_POSITION - 1
      : nonIscCaItems.length

  const result = [...nonIscCaItems, ...caItems]
  if (iscItems?.length) {
    result.splice(insertIndex, 0, iscItems.slice(0, MAX_ISHOPCHANGI_ITEMS))
  }

  searchResult?.facets?.locationList?.sort?.()

  return {
    items: result,
    totalFlights,
    facets: searchResult?.facets,
  }
}

const convertSearchAllData = (searchResult: any, savedFlight: any, textSearch: string) => {
  const groupListData = []
  for (const property in searchResult) {
    if (!isEmpty(searchResult[property])) {
      const groupName = property
      const isViewMore = getIsViewMore(searchResult[property])
      const isDepartureFightData = searchResult[property].isDepartureFightData || false

      const itemSectionList = {
        title: groupName,
        data: convertDataSectionList(searchResult[property], groupName, savedFlight, textSearch),
        isViewMore,
        isDepartureFightData: isDepartureFightData,
      }
      groupListData.push(itemSectionList)
    }
  }
  return groupListData
}

const getDataUpdateInsertFlight = (infoInsertFlight) => {
  if (
    infoInsertFlight?.data?.insertMyTravelFlightDetail?.status?.success ||
    infoInsertFlight?.data?.recordExist
  ) {
    return {
      isSaved: true,
      isMSError: false,
      isSaveFlightLoading: false,
    }
  }
  return {
    isSaved: false,
    isMSError: true,
    isSaveFlightLoading: false,
  }
}

const getDataUpdateRemoveFlight = (infoRemoveFlight) => {
  if (
    infoRemoveFlight?.data?.deleteMyTravelFlightDetail?.status?.success ||
    infoRemoveFlight?.data?.recordRemoved
  ) {
    return {
      isSaved: false,
      isMSError: false,
      isSaveFlightLoading: false,
    }
  }
  return {
    isSaved: false,
    isMSError: true,
    isSaveFlightLoading: false,
  }
}

const convertFlight = (flights, item, dataUpdate) => {
  return flights.map((flight) =>
    flight?.flightUniqueId === item?.flightUniqueId && flight?.direction === item?.direction
      ? {
          ...flight,
          ...dataUpdate,
        }
      : flight,
  )
}

const updateFlightSearchAll = (searchAllResult, action, dataUpdate) => {
  const flight = get(action, "flightData.item")
  const searchData = cloneDeep(get(searchAllResult, "searchData", []))

  if (!Array.isArray(searchData) || isEmpty(searchData)) {
    return null
  }

  return searchData?.map((item) => {
    if (item?.title === "flights" && item?.data?.length > 0) {
      return { ...item, data: convertFlight(item?.data, flight, dataUpdate) }
    }
    return item
  })
}

const getExcludeIdToGetYMAL = (listDataSearch: any[]): string => {
  if (!isEmpty(listDataSearch)) {
    const listExcludeId = listDataSearch.map((item) => item.id).join(",")
    return listExcludeId
  } else {
    return ""
  }
}

const mapingYMALDataResponse = (payLoad: any[]) => {
  const dataList: ISearchYouMayAlsoLikeItem[] = payLoad.map((item) => {
    return {
      id: item?.id,
      tenantId: item?.tenantId || item?.contentId || item?.attractionId,
      logoImage: item?.logoImage,
      title: item?.title,
      locationDisplay: item?.location_display || item?.locationDisplayText || "",
      navigation: item?.navigation || null,
      source: item?.source,
      type: item?.type,
    }
  })
  return dataList
}

export {
  convertSearchAllData,
  convertSearchAllV2Data,
  getDataUpdateInsertFlight,
  getDataUpdateRemoveFlight,
  updateFlightSearchAll,
  getExcludeIdToGetYMAL,
  mapingYMALDataResponse,
}
