import { NavigationWalletTypes } from "app/models/enum"
import { defaultSystemFonts } from "react-native-render-html"

export const LOGIN_SCREEN_IMAGE =
  "data:image/jpeg;base64,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"
export const PLACEHOLDER_ANIMATION_SPEED_IN_MS = 1300
const DOT_UNICODE = "U+000B7"
export const EXPIRY_DURATION = 5
export const TOAST_MESSAGE_DURATION = 5000
export const COOKIE_NAME = "sessionid"
export const RECENT_SEARCH_APP = "RECENT_SEARCH_APP"

export const NavigationConstants = {
  mainStack: "mainStack",
  webview: "webview",
  dineShop: "dineShop",
  playpassWebview: "playpassWebview",
  attractionPageView: "attractionPageView",
  shopDetailsScreen: "shopDetailsScreen",
  signupWithEmailFinalScreen: "signupWithEmailFinalScreen",
  dineShopOfferDetailsScreen: "dineShopOfferDetailsScreen",
  changiRewardsDetailScreen: "changiRewardsDetailScreen",
  redemptionCatalogueScreen: "redemptionCatalogueScreen",
  redeemRewardDetailScreen: "redeemRewardDetailScreen",
  aboutChangiLifeScreen: "aboutChangiLifeScreen",
  rateAirportExperienceScreen: "rateAirportExperienceScreen",
  forYouScreen: "forYouScreen",
  changiRewardsEcardScreen: "ChangiEcards",
  settingScreen: "settingScreen",
  profileScreen: "profileScreen",
  loginAndSecurityScreen: "loginAndSecurityScreen",
  forYouErrorScreen: "forYouErrorScreen",
  linkedMembershipsScreen: "linkedMembershipsScreen",
  loginManagement: "loginManagement",
  settingNotificationsScreen: "settingNotificationsScreen",
  restaurantDetailScreen: "restaurantDetailScreen",
  dineFilterResultsScreen: "dineFilterResults",
  shopFilterResultsScreen: "shopFilterResults",
  authScreen: "nativeLoginScreen",
  eventDetailsScreen: "eventDetailsScreen",
  redeemRewardSuccessScreen: "redeemRewardSuccessScreen",
  privilegesScreen: "privilegesScreen",
  paymentConfirmationScreen: "paymentConfirmationScreen",
  notificationsScreen: "notificationsScreen",
  notificationDetailScreen: "notificationDetailScreen",
  termsOfUsePolicy: "termsOfUsePolicy",
  privacyPolicy: "privacyPolicy",
  submitSuggestionsAndFeedBack: "submitSuggestionsAndFeedBack",
  orderDetailPage: "orderDetailPage",
  depature: "Departure",
  arrival: "Arrival",
  explore: "explore",
  search: "search",
  searchResult: "searchResult",
  bottomNavigation: "bottomNavigation",
  loginWebview: "nativeLoginScreen",
  mapScreen: "mapScreen",
  toilets: "toilets",
  askMax: "askMax",
  changiMillionaire: "ChangiMillionaire",
  fly: "fly",
  changiEcards: "ChangiEcards",
  changiMap: "atomMap",
  staffPerkListing: "staffPerkListing",
  dealsPromosListing: "dealsPromosListing",
  yourReward: "yourReward",
  offerDetail: "offerDetail",
  appscapadeScanBoadingPass: "appscapadeScanBoadingPass",
  yourRewardRedeemSuccess: "yourRewardRedeemSuccess",
  monarchPrivileges: "monarchPrivilegesScreen",
  flightDetails: "flightDetails",
  pointsTransaction: "pointsTransaction",
  pendingPoints: "pendingPoints",
  pointsExpiry: "pointsExpiry",
  transactions: "transactions",
  pointsTab: "pointsTab",
  perksTab: "perksTab",
  creditsScreen: "creditsScreen",
  changiRewardsPrivilegesScreen: "changiRewardsPrivilegesScreen",
  changiRewardsPrivilegesMemberTab: "changiRewardsPrivilegesMemberTab",
  changiRewardsPrivilegesGoldTab: "changiRewardsPrivilegesGoldTab",
  changiRewardsPrivilegesPlatinumTab: "changiRewardsPrivilegesPlatinumTab",
  changiRewardsPrivilegesMonarchTab: "changiRewardsPrivilegesMonarchTab",
  detailPerkScreen: "detailPerkScreen",
  vouchersPrizesRedemptionsScreen: "vouchersPrizesRedemptionsScreen",
  bookingsOrdersScreen: "bookingsOrdersScreen",
  orderDetailsScreen: "orderDetailsScreen",
  playPassBookingDetail: "playPassBookingDetail",
  saveFlightsScreen: "saveFlightsScreen",
  account: "account",
  authSupplementDataScreen: 'authSupplementDataScreen',
  retroClaimsTakePhotoScreen: "retroClaimsTakePhotoScreen",
  retroClaimsSubmittedScreen: "retroClaimsSubmittedScreen",
  l2AnnouncementDetails: "l2AnnouncementDetails",
  retroClaimsNotifications: "retroClaimsNotifications",
  passwordBiometrics: "passwordBiometrics",
  l1AdvisoryDetails: "l1AdvisoryDetails",
  appWideWelcome: "AppWide_Welcome",
  welcome: "welcome",
  dine: "dine",
  shop: "shop",
  marketplace: "marketplace",
  searchFlightsV2Result: "searchFlightsV2Result",
  searchFlightsV2Modal: "searchFlightsV2Modal",
  parkingLanding: "parkingLanding",
  ParkingLandingBenefit: "ParkingLandingBenefit",
  ParkingLandingBenefitActive: "ParkingLandingBenefitActive",
  ParkingLandingBenefitInActive: "ParkingLandingBenefitInActive",
  ParkingLandingBenefitExpire: "ParkingLandingBenefitExpire",
  carPark: "carPark",
  parkingBenefitsMonarch: "parkingBenefitsMonarch",
  FAQLanding: "FAQLanding",
  DriveOnboarding: "DriveOnboarding",
  promoCodes: "PromoCodes",
  DineShopDirectory: "DineShopDirectory",
  facilitiesServices: "facilitiesServices",
  flightListing: "flightListing"
}

export const DisplayScreens = {
  forYouLandingTop: "for_you_landing_top",
  forYouLandingBottom: "for_you_landing_bottom",
  forYouRewardsDetails: "for_you_rewards_details",
  forYouPrivileges: "for_you_privileges",
}

export const getDotUnicode = () => {
  let dotUnicode = DOT_UNICODE
  dotUnicode = dotUnicode.substr(3)
  dotUnicode = String.fromCharCode(parseInt(dotUnicode, 16))
  return dotUnicode
}

export enum ShortcutLinksLocation {
  foryou = "foryou",
  fly = "fly",
  explore = "explore",
}

export enum NavigationInAppValueSpecial {
  dine = "dine",
  shop = "shop",
  changibingo = "changibingo",
  carPark = "carPark",
}

export enum UpComingEventType {
  playpassBookings = "PlaypassBookings",
  savedFlights = "SavedFlights",
  savedFlightsMaintenanceMode = "SavedFlightsMaintenanceMode",
}

export const RequiredLoggedScreens = [
  NavigationConstants.profileScreen,
  NavigationConstants.redeemRewardDetailScreen,
  NavigationConstants.redemptionCatalogueScreen,
  NavigationWalletTypes.walletPasses,
  NavigationWalletTypes.walletTravel,
  NavigationWalletTypes.walletCredits,
  NavigationWalletTypes.walletPerks,
  NavigationWalletTypes.walletOrders,
]

export enum TrackingScreenName {
  Explore = "Home_Page",
  FlyLanding = "Fly_Landing",
  FlightResultDeparture = "Flight_Result_Departure",
  FlightResultArrival = "Flight_Result_Arrival",
  FlyAirportLanding = "Fly_AirportLanding",
  MarketPlace = "MarketPlace",
  DineLanding = "Dine_Landing",
  ShopLanding = "Shop_Landing",
  AccountLanding = "Account_Landing",
  AccountChangiRewardDetail = "Account_ChangiRewardDetail",
  AccountExpiry = "Account_Expiry",
  RedemptionCatalogueV2 = "Redemption_Catalogue_V2",
  Credits = "Credits",
  VouchersPrizesRedemptions = "Vouchers_Prizes_Redemptions",
  BookingsOrders = "Bookings_Orders",
  ChangiRewardsPrivileges = "Changi_Rewards_Privileges",
  StaffPerkListing = "Staff_Perk_Listing",
  DealsPromosListing = "Deals_Promos_Listing",
  PlayPassWebView = "PlayPass_WebView",
  WebView = "WebView",
  ChangiMap = "Changi_Map"
}

export const ScreenListShowPopupRating = [
  TrackingScreenName.FlyLanding,
  TrackingScreenName.FlightResultDeparture,
  TrackingScreenName.FlightResultArrival,
  TrackingScreenName.FlyAirportLanding,
  TrackingScreenName.MarketPlace,
  TrackingScreenName.DineLanding,
  TrackingScreenName.ShopLanding,
  TrackingScreenName.AccountLanding,
  TrackingScreenName.AccountChangiRewardDetail,
] as string[]

export const ShowAppRatingOnSaveFlightScreenList = [
  TrackingScreenName.FlyLanding,
  TrackingScreenName.FlightResultDeparture,
  TrackingScreenName.FlightResultArrival,
  TrackingScreenName.FlyAirportLanding,
  TrackingScreenName.MarketPlace,
  TrackingScreenName.DineLanding,
  TrackingScreenName.ShopLanding,
  TrackingScreenName.AccountLanding,
] as string[]

export enum StateCode {
  PPEVENT = "PPEVENT",
  PPCART = "PPCART",
  PPEDIT = "PPEDIT",
  PPEVENT3 = "PPEVENT3",
  PPCART3 = "PPCART3",
  PPEDIT3 = "PPEDIT3",
  PPRECEIPT3 = "PPRECEIPT3",
  CARPASS = "CARPASS",
  CARPASS_DRIVE = "CARPASS_DRIVE",
  PPRECEIPT = "PPRECEIPT",
  CARPASSBOOKING = "CARPASSBOOKING",
  CHANGI_MILLIONAIRE = "CHANGI_MILLIONAIRE",
  ISHOPCHANGI = "ISHOPCHANGI",
  ISHOPCHANGI_PAGELINK = "ISHOPCHANGI_PAGELINK",
  CHANGI_EATS = "CHANGI_EATS",
  CHANGI_PASSPORT = "CHANGI_PASSPORT",
  APPSCAPADE_LP = "APPSCAPADE_LP",
  APPSCAPADE_PLAY_AND_WIN_LP = "APPSCAPADE_PLAY_AND_WIN_LP",
  GUSER_CONCIERGE_WA = "GUSER_CONCIERGE_WA",
  PPCANCEL3 = "PPCANCEL3",
  PPEDITNEW3 = "PPEDITNEW3",
  CM24 = "CM24",
  GAMIFICATION = "GAMIFICATION",
  GROUPBUY = "GROUPBUY"
}
export const listCodeOfPriorityCountry = ["65", "62", "60", "86", "91"]

export const NOTIFICATION_TYPES = {
  ALL: "ALL",
  FLIGHTS: "FLIGHTS",
  ADVISORIES: "ADVISORIES",
  EVENTS_PROMOTIONS: "EVENTS_PROMOTIONS",
  APP_UPDATE: "APP_UPDATE",
  EVENT_PP_NEW_PERK: "EVENT_PP_NEW_PERK",
  EVENT_PP_EXPIRING_PERK: "EVENT_PP_EXPIRING_PERK",
  EVENT_PP_UPCOMING_BOOKING: "EVENT_PP_UPCOMING_BOOKING",
  EVENT_PP_NEW_CREDITS: "EVENT_PP_NEW_CREDITS",
  EVENT_PP_EXPIRING_CREDITS: "EVENT_PP_EXPIRING_CREDITS",
  FLIGHTS_APPSCAPADE: "FLIGHTS_APPSCAPADE",
  L2_ANNOUNCEMENT: "ANNOUNCEMENTS",
  RETRO_CLAIM: "RETRO_CLAIM",
  L1_ADVISORIES: "L1_ADVISORIES",
  EVENTS_PROMOTIONS_EPIC: "EVENTS_PROMOTIONS_EPIC",
  EVENTS_PROMOTIONS_ADVISORIES: "EVENTS_PROMOTIONS_ADVISORIES",
  EVENTS_PROMOTIONS_TRAVEL: "EVENTS_PROMOTIONS_TRAVEL",
  EVENTS_PROMOTIONS_SERVICES: "EVENTS_PROMOTIONS_SERVICES", 
}

export enum NotificationL1Page {
  Explore = "explore",
  Flights = "flights",
  Dine = "dine",
  Account = "account",
  Parking = "parking",
}

export enum UpdateProfileSectionKey {
  PERSONAL_DETAILS = "PERSONAL_DETAILS",
  ADDRESS = "ADDRESS",
  VEHICLE_DETAILS = "VEHICLE_DETAILS",
  AIRPORT_STAFF = "AIRPORT_STAFF",
  PHONE_NUMBER = "PHONE_NUMBER",
  EMAIL_ADDRESS = "EMAIL_ADDRESS",
  KRIS_FLYER = "KRIS_FLYER",
  KF_CONVERSION = "KF_CONVERSION",
}

export enum ChangiPayStatusCode {
  PROFILE_UPDATE_REQUIRED = 100,
  REDIRECT_ISHOPCHANGI = 401,
  REDIRECT_CHANGIMILLIONAIRE_402 = 402,
  REDIRECT_CHANGIMILLIONAIRE_403 = 403,
}

export enum AppConfigPermissionTypes {
  changiappWalletEnabled = "changiapp_wallet_enabled",
  changiappCREnabled = "changiapp_cr_enabled",
  changiappEcardEnabled = "changiapp_ecard_enabled",
}

export const screenTagName = {
  flightsListing: "flightsListing",
  marketplace: "marketplace",
  dine: "dine",
  shop: "shop",
  editProfile: "editProfile",
  rewardsCatalogue: "rewardsCatalogue",
  passes: "passes",
  travel: "travel",
  credits: "credits",
  perks: "perks",
  orders: "orders",
  all: "all",
  flights: "flights",
  advisories: "advisories",
  eventsPromotions: "eventsPromotions",
  submitSuggestionsFeedback: "submitSuggestionsFeedback",
}

export const sectionTagName = {
  exploreChangi: "exploreChangi",
  departure: "departure",
  arrival: "arrival",
  availability: "availability",
  calculator: "calculator",
  findMyCar: "findMyCar",
  krisflyer: "krisflyer",
  jewelDoubleRewards: "jewelDoubleRewards",
  parkingEntitlement: "parking-entitlement",

}

export const DINE_SHOP_COMPONENT_NAME = {
  DINE: "DineFilterResults",
  SHOP: "ShopFilterResults",
}

export const DINE_DIETARY_TYPE = {
  GLUTEN_FREE: "Gluten Free",
  GLUTEN_FREE_OPTIONS: "Gluten Free Options",
  HALAL: "Halal",
  VEGAN: "Vegan",
  VEGAN_OPTIONS: "Vegan Options",
  VEGETARIAN: "Vegetarian",
  VEGETARIAN_FRIENDLY: "Vegetarian Friendly",
}

export const DINE_SHOP_AREA = {
  PUBLIC: "Public",
  PUBLIC_LOWERCASE: "public",
  TRANSIT: "Transit",
  TRANSIT_LOWERCASE: "transit",
}

export const SOURCE_SYSTEM = {
  ANYTHING_IN_APP: 'ChangiApp_General',
  CHANGI_MILLIONAIRE: 'ChangiApp_Cm',
  APPSCAPADE: 'ChangiApp_Appscapade',
  PLAY_PASS: 'ChangiApp_Playpass',
  SIFT_AND_PICK: 'ChangiApp_Sift&pick',
  OTHERS: 'ChangiApp_Others',
  GAME_2024: 'ChangiApp_Game2024',
  CPAY: 'ChangiApp_Cpay',
  FLIGHTS: 'ChangiApp_Flights',
  ECARD: 'ChangiApp_Ecard',
}

export const USER_TYPES = {
  TYPES: {
    FLY_AIRPORTVISIT: "fly-airportvisit",
    FLY_HOME: "fly-home",
    FLY_AIRPORT: "fly-airport",
    GENERAL: "general",
    MONARC: "monarc",
  },
  VALUES: {
    UNDEFINED: "Undefined",
    FLY_AIRPORTVISIT: "No Saved Flights + In Airport",
    FLY_HOME: "Save Flights + Out of Airport",
    FLY_AIRPORT: "Saved Flights + In Airport",
    GENERAL: "No Saved Flights + Out of Airport",
    MONARC: "Monarch",
  },
}

export const API_FAILURE_KIND = {
  BAD_DATA: "bad-data",
  CANNOT_CONNECT: "cannot-connect",
  FORBIDDEN: "forbidden",
  MALFORMED_EXCEPTION: "malformed-exception",
  NOT_FOUND: "not-found",
  REJECTED: "rejected",
  SERVER: "server",
  TIMEOUT: "timeout",
  UNAUTHORIZED: "unauthorized",
  UNKNOWN: "unknown",
}

export enum FlightDirection {
  Departure = "DEP",
  Arrival = "ARR",
}

export enum TechnicalFlightStatus1 {
  AB = "AB",
  BD = "BD",
  CX = "CX",
  DV = "DV",
  ES = "ES",
  EX = "EX",
  FB = "FB",
  FC = "FC",
  FS = "FS",
  FX = "FX",
  GA = "GA",
  GC = "GC",
  GO = "GO",
  LB = "LB",
  LC = "LC",
  LD = "LD",
  NI = "NI",
  NO = "NO",
  OB = "OB",
  OT = "OT",
  OV = "OV",
  RS = "RS",
  SH = "SH",
  ZN = "ZN",
  XF = "XF",
}

export enum TechnicalFlightStatus2 {
  ADD = "ADD",
  ARR = "ARR",
  BOR = "BOR",
  CAN = "CAN",
  DEP = "DEP",
  DEL = "DEL",
  DIV = "DIV",
  DLI = "DLI",
  EAR = "EAR",
  EEE = "EEE",
  EMA = "EMA",
  EMB = "EMB",
  EMC = "EMC",
  EMD = "EMD",
  FCL = "FCL",
  GAT = "GAT",
  GCH = "GCH",
  GCL = "GCL",
  GOP = "GOP",
  INC = "INC",
  LAS = "LAS",
  LND = "LND",
  ONT = "ONT",
  PRO = "PRO",
  TEC = "TEC",
}

export const BottomTabScreens = [
  "explore", 
  'fly', "flights", "airport", 
  "pay", 
  "dineShop", "dine", "shop", "marketplace",
  "forYouScreen", "account",
]

export const AccountTabScreen = [
  "forYouScreen", "account",
]

export enum CM24RedirectSource {
  QuickLinks = "QuickLinks",
  LatestHappenings = "LatestHappenings",
  AccountCM24 = "AccountCM24",
  VPR = "VPR",
}

export const WIDGET_QUERY_STRING = {
  TYPES: {
    TYPE: "type",
    NAME: "name",
    LABEL: "label",
    TIER: "tier",
  },
  VALUES: {
    WIDGET: "widget",
  },
}

export const SHORTCUT_LINK_TYPES = {
  PLAYPASS: "ichangi:shortcut-link-type/playPass",
}

export enum SOCIAL_PROVIDER {
  GOOGLE = "googleplus",
  FACEBOOK = "facebook",
  APPLE = "apple"
}
export enum SPACING {
  SMALL = 8,
  STANDARD = 16,
  LARGE = 32,
  EXTRA_LARGE = 64,
}

export enum PerkType {
  ChangiRewardPerk = "ChangiRewardPerk",
  InstantWinPrize = "InstantWinPrize",
  GamificationPrize = "GamificationPrize",
  PlaypassPerk = "PlaypassPerk"
}

export enum APP_DEEPLINKS {
  EXPLORE_CHANGI = "cagichangi://explore",
  FLY_DEPARTURE = "cagichangi://flydeparture",
  DINESHOP_DINE_PAGE = "cagichangi://dineshop",
  STAFF_PERK_LISTING = "cagichangi://staffperklisting",
  REDEEMABLE_PRIZES = "cagichangi://vpr",
  REDEEM_POINTS = "cagichangi://redeempoints",
  RETRO_CLAIM_DETAILS = "cagichangi://retro_claims_details",
  DEEP_MODE = "cagichangi://deep_mode", // will replace FDL for some cases from in-app functions
  EXTERNAL_LINK = "cagichangi://external_link", // open with in-app browser
  WEBVIEW = "cagichangi://webview", // open with webview
  EPICPAGE_PROD = "cagichangi://epicpage_prod",
  ACCOUNT = "cagichangi://account",
  MY_ACCOUNT = "cagichangi://myaccount",
  PARKING = "cagichangi://parking", // replace carpass
  LOCATION_PERMISSION = "cagichangi://location_permission",
}

export const GAMI_DEEPLINKS = [
  APP_DEEPLINKS.EXPLORE_CHANGI,
  APP_DEEPLINKS.FLY_DEPARTURE,
  APP_DEEPLINKS.DINESHOP_DINE_PAGE,
  APP_DEEPLINKS.STAFF_PERK_LISTING,
  APP_DEEPLINKS.REDEEMABLE_PRIZES,
  APP_DEEPLINKS.REDEEM_POINTS,
]

export enum HTTP_CODE {
  UNAUTHORIZED = 403
}

export enum HTTP_ERROR_CODE {
  UNAUTHORIZED = 403005
}

export const EXTERNAL_LINKS = {
  CONTACT_US: "https://www.ishopchangi.com/en/support/contact-us",
  CONTACT_LIVE_AGENT: "https://web-staging-changi.voncierge.tech",
}

export enum AEM_FILTER_TYPES {
  CONTACT_US_VARIATION = "contact-us-variation",
}

export const GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS = 3
export const GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS_DEFAULT = 1

export const SHIMMER_FLATLIST_DATA = Array(6);
export const SHIMMER_TRAVEL_LIST_DATA = Array(4);

export const CONTACT_EMAIL = "<EMAIL>"

export const SKYTRAIN_GATES_OBJ = {
  A16: "A16",
  A17: "A17",
  A18: "A18",
  A19: "A19",
  A20: "A20",
  A21: "A21",
};

export const systemFonts = [...defaultSystemFonts, "Lato-Regular", "Lato-Bold"]

export enum PARKING_BENEFITS_CTA_TYPES {
  NO_IU = "no_iu",
  NON_LOGIN = "non_login",
  VIEW_DETAILS = "view_details",
  VIEW_PARKING_PROMOS = "view_parking_promos",
}

export const PROMO_CODE_INVENTORY_TYPE_CODE = "Instant Promo Code Issuance";