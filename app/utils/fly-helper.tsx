import { handleCondition } from "."
 
export const FLIGHT_STATUS_REGEXS = {
  GATE_OPEN: /gate open/gim,
  BOARDING: /boarding/gim,
  GATE_CLOSING: /gate closing/gim,
  LAST_CALL: /last call/gim,
  GATE_CLOSED: /gate closed/gim,
}
 
 
const isFirstBagOrLastBagStatus = (status) => {
  return status === "Last Bag on Belt" || status === "First Bag on Belt"
}
 
export const mappingTimeWithFlightText = (direction, duration, item) => {
  const isFirstBagOrLastBag = isFirstBagOrLastBagStatus(item?.beltStatusMapping)
  const isDEP = direction === "DEP"
  const durationInHour = Math.floor(duration / 60)
  const durationInDay = Math.floor(duration / (60 * 24))
  const isDeparted = item?.upcomingStatusMapping === "Departed"
  const isLaned = item?.upcomingStatusMapping?.includes("Landed")
 
  const upcomingStatusMapping = item?.upcomingStatusMapping?.toLowerCase()
  const isGateOpen = /gate open/gim.test(upcomingStatusMapping)
  const isBoarding = /boarding/gim.test(upcomingStatusMapping)
  const isGateClosing = /gate closing/gim.test(upcomingStatusMapping)
  const isLastCall = /last call/gim.test(upcomingStatusMapping)
  const isGateOpenBoardingGateClosingLastCall = isGateOpen || isBoarding || isGateClosing || isLastCall
  const isGateClosed = FLIGHT_STATUS_REGEXS.GATE_CLOSED.test(upcomingStatusMapping)
 
  const durationInHourShowText = durationInHour > 0 ? `${durationInHour}h` : ""
  const durationInMinuteShowText = duration % 60 > 0 ? ` ${duration % 60}m` : ""
  const durationInHourMinuteShowText = `${durationInHourShowText}${durationInMinuteShowText}`
 
  if (isDEP && item?.showGate && (isGateClosed || isGateOpenBoardingGateClosingLastCall)) {
    if (isGateClosed) {
      return "Departing soon"
    }
    if (isGateOpenBoardingGateClosingLastCall) {
      return `Go to Gate ${item?.displayGate}`
    }
  } else if (duration < 0 || isLaned || isDeparted) {
    const HHMM = item?.displayTimestamp?.split(" ")[1]
    return handleCondition(
      isDEP,
      handleCondition(isDeparted, "Departed", "Departing soon"),
      handleCondition(
        isLaned,
        handleCondition(isFirstBagOrLastBag, `Arrived in SIN ${HHMM}`, "Arrived in SIN"),
        "Arriving in SIN soon",
      ),
    )
  } else if (duration <= 30 && !isDEP) {
    return "Arriving in SIN soon"
  } else if (durationInHour < 24) {
    return `${handleCondition(isDEP, "Departure", "SIN arrival")} in ${durationInHourMinuteShowText}`
  } else if (durationInHour < 48) {
    return `${handleCondition(isDEP, "Departure", "SIN arrival")} in ${durationInDay} day`
  } else {
    return `${handleCondition(isDEP, "Departure", "SIN arrival")} in ${durationInDay} days`
  }
}

export const roundTemperature = (temp: number): string => {
  const decimalPart = temp - Math.floor(temp)
  const rounded = decimalPart > 0.5 ? Math.ceil(temp) : Math.floor(temp)
  return `${rounded}°C`
}

export const formatMinutes = (totalMinutes) => {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  let result = '';

  if (hours > 0) {
    result += `${hours} hour${hours > 1 ? 's' : ''}`;
  }

  if (minutes > 0) {
    if (hours > 0) result += ' ';
    result += `${minutes} min${minutes > 1 ? 's' : ''}`;
  }

  if (result === '') {
    result = '0 mins';
  }

  return result;
};
