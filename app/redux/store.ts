import { applyMiddleware, combineReducers, compose, createStore } from "redux"
import { persistReducer, persistStore } from "redux-persist"
import { PersistConfig } from "redux-persist/es/types"
import createSagaMiddleware from "redux-saga"
import { resettableReducer } from "reduxsauce"
import Config from "app/config/debugConfig"
import Reactotron from "app/config/reactotronConfig"
import rootSaga from "app/sagas"
import SignUpActions from "app/redux/signUpRedux"
import ExploreCreators from "app/redux/exploreRedux"
import ProfileActions from "app/redux/profileRedux"
import { FlyCreators } from "./flyRedux"
import AirportLandingCreator from "app/redux/airportLandingRedux"
import NativeAuthActions from "app/redux/nativeAuthRedux"
import { removeMMKVStorage, setUserAgent } from "app/utils/storage/mmkv-storage"
import {reducer} from "./mytravelRedux"
import { reducer as modalManagerReducer } from "./modalManagerRedux"
import { reducer as imageManagerReducer } from "./imageManagerRedux"
import { MMKVPersistStorage } from "app/utils/storage/mmkv-persist"
import { dtManualActionEvent, FE_LOG_PREFIX } from "app/services/firebase/analytics"
import { loadFromEncryptedStorage, removeFromEncryptedStorage, StorageKey } from "app/utils/storage"
import { getAuthTokenPayload, setAuthTokenPayload } from "app/utils/storage/mmkv-encryption-storage"
import { getUserAgent } from "react-native-device-info"
removeMMKVStorage()

/* ------------- Redux Configuration ------------- */

const enhancers = []
const middlewares = []

/* ------------- Redux-Persist Configuration ------------- */

const configAEMRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "aemReducer",
}
const configFlyRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "flyReducer",
  blacklist: [
    "flightSearchDate",
    "flightSearchTerminal",
    "flightSearchKeyWord",
    "arrivalListLoading",
    "departureListLoading",
  ],
}
const configAuthRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "authReducer",
}
const imageManagerRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "imageManagerReducer",
}
const configNativeAuthRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "nativeAuthReducer",
}
const configPageConfigRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "pageConfigReducer",
}
const configChangiRewardsRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "changiRewardsReducer",
}
const configProfileRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "profileReducer",
  blacklist: [
    "profileUpdateError",
  ],
}
const configAirportLandingRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "airportLandingReducer",
}
const configNotificationRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "notificationsReducer",
  blacklist: [
    "listUserNotications",
  ],
}
const configAemGroupTwoRedux: PersistConfig<any> = {
  storage: MMKVPersistStorage,
  key: "aemGroupTwoReducer",
}
/* ------------- Resettable Reducers  ------------- */

const resetUserState = resettableReducer("NATIVE_AUTH_LOGOUT")

/* ------------- Assemble & Combine Reducers ------------- */

const rootReducer = combineReducers({
  testReducer: require("./testRedux").reducer,
  dineReducer: require("./dineRedux").reducer,
  authReducer: persistReducer(configAuthRedux, require("./authRedux").reducer),
  nativeAuthReducer: persistReducer(configNativeAuthRedux, require("./nativeAuthRedux").reducer),
  aemReducer: persistReducer(configAEMRedux, require("./aemRedux").reducer),
  shopReducer: require("./shopRedux").reducer,
  signUpReducer: require("./signUpRedux").reducer,
  pageConfigReducer: persistReducer(configPageConfigRedux, require("./pageConfigRedux").reducer),
  forYouReducer: require("./forYouRedux").reducer,
  flyReducer: persistReducer(configFlyRedux, require("./flyRedux").reducer),
  changiRewardsReducer: persistReducer(configChangiRewardsRedux, resetUserState(require("./changiRewardsRedux").reducer)),
  transactionsReducer: resetUserState(require("./transactionsRedux").reducer),
  profileReducer: persistReducer(configProfileRedux, resetUserState(require("./profileRedux").reducer)),
  exploreReducer: resetUserState(require("./exploreRedux").reducer),
  redemptionCatalogueReducer: resetUserState(require("./redemptionCatalogueRedux").reducer),
  redeemRewardDetailReducer: resetUserState(require("./redeemRewardDetailRedux").reducer),
  searchReducer: require("./searchRedux").reducer,
  aboutChangiLifeReducer: require("./aboutChangiLifeRedux").reducer,
  privilegesReducer: resetUserState(require("./privilegesRedux").reducer),
  airportLandingReducer: persistReducer(configAirportLandingRedux, require("./airportLandingRedux").reducer),
  walletReducer: require("./walletRedux").reducer,
  marketPlaceReducer: require("./marketPlaceRedux").reducer,
  notificationsReducer: persistReducer(configNotificationRedux, require("./notificationRedux").reducer),
  modalReducer: require("./modalRedux").reducer,
  systemReducer: require("./systemRedux").reducer,
  staffPerkReducer: resetUserState(require("./staffPerkRedux").reducer),
  mytravelReducer: reducer,
  modalManagerReducer: modalManagerReducer,
  imageManagerReducer: persistReducer(imageManagerRedux, imageManagerReducer),
  aemGroupTwoReducer: persistReducer(configAemGroupTwoRedux, require("./aemGroupTwo").reducer),
  flightListingReducer: require("./flightListingRedux").reducer,
})
export type RootState = ReturnType<typeof rootReducer>

/* ------------- Redux Persist ------------- */

const reducers = rootReducer

/* ------------- Saga Middleware ------------- */

const sagaMonitor = null
const sagaMiddleware = createSagaMiddleware({ sagaMonitor })
middlewares.push(sagaMiddleware)

// if (__DEV__) {
//   /* ------------- Create Logger ------------- */
//   const logger = createLogger({
//     // ...options
//   })
//   middlewares.push(logger)
// }

/* ------------- Assemble Middleware ------------- */

enhancers.push(applyMiddleware(...middlewares))

// if Reactotron is enabled (default for __DEV__), we'll create the store through Reactotron
if (Config.useReactotron) {
  enhancers.push(Reactotron.createEnhancer())
}

export const store = createStore(reducers, compose(...enhancers))
sagaMiddleware.run(rootSaga)

export const persistor = persistStore(store, null, async () => {
  const oldToken = await loadFromEncryptedStorage(StorageKey.authTokenPayload)
  const newToken = getAuthTokenPayload()
  if (oldToken && !newToken) {
    setAuthTokenPayload(oldToken)
    removeFromEncryptedStorage(StorageKey.authTokenPayload)
  }
  getUserAgent().then((userAgent) => {
    setUserAgent(userAgent)
  })

  // clear all AEM
  store.dispatch({
    type: "CLEAR_AEM_ALL",
  })
  // reset filters
  store.dispatch({
    type: "DINE_RESET_FILTER_ITEMS",
  })
  store.dispatch({
    type: "SHOP_RESET_FILTER_ITEMS",
  })
  store.dispatch({
    type: "DINE_FILTER_PAGINATION_RESET",
  })
  store.dispatch({
    type: "FLY_DEPARTURE_RESET_FILTER",
  })
  store.dispatch({
    type: "FLY_ARRIVAL_RESET_FILTER",
  })
  store.dispatch({
    type: "FLY_GET_INTO_AIRPORT_RESET",
  })

  // Clear login state
  store.dispatch(NativeAuthActions.nativeAuthLoginReset())

  // Clear profile state
  store.dispatch(ProfileActions.profileReset())

  // Clear expolore changi categories data
  store.dispatch(ExploreCreators.exploreDataReset())
  // clear crt benefit data
  store.dispatch(FlyCreators.flyFlightDetailsCrtReset())

  // clear playpass url
  store.dispatch(AirportLandingCreator.getPlayPassUrlResetAll())

  store.dispatch(NativeAuthActions.getAccountInfoReset())
  store.dispatch(NativeAuthActions.clearLoginStatus())
  store.dispatch(NativeAuthActions.clearValidatePromoCodeStatus())
  store.dispatch(NativeAuthActions.getFieldsReset())
  store.dispatch(NativeAuthActions.clearVerifyOtpCodeStatus())
  store.dispatch(NativeAuthActions.setConnectionStatus(true))
  store.dispatch(NativeAuthActions.setFinishBiometric(true))
})
