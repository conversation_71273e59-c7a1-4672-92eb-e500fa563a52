import { createActions, createReducer } from "reduxsauce"
import Immutable, { ImmutableObject } from "seamless-immutable"
import { RootState } from "./store"

const { Types, Creators } = createActions({
  openModal: ["modalKey"],
  closeModal: ["modalKey"],
  openPendingModal: [],
})

export const ModalTypes = Types
export default Creators

export interface IModalKey {
  cappUpdate?: boolean,
  bottomSheetVcea?: boolean,
  searchTabFlightFilter?: boolean,
  searchAllCategoryFilter?: boolean,
  flightResultCategoryFilter?: boolean,
  parkingEntitlements?: boolean,
  updateVehicleIUError?: boolean,
  saveConnectingFlightSearch?: boolean,
  saveConnectingFlightSearchAll?: boolean,
  saveConnectingFlightLanding?: boolean,
  saveConnectingFlightDetail?: boolean,
  saveConnectingFlightResultArr?: boolean,
  saveConnectingFlightResultDep?: boolean,
  saveFlightTravelOptionLandingArr: boolean,
  saveFlightTravelOptionLandingdep: boolean,
  saveFlightTravelOptionResultArr: boolean,
  saveFlightTravelOptionResultDep: boolean,
  saveFlightTravelOptionSearchAll: boolean,
  saveFlightTravelOptionSearchFlight: boolean,
  shopDetailError: boolean,
  dineDetailError: boolean,
  modalSaveAndShare: boolean,
  flightDetailSaveTravelOption?: boolean,
  saveFlightTravelOptionLightListingV2?: boolean,
  saveConnectingFlightListingV2?: boolean,
  searchDepartureFlight?: boolean,
  facilitiesServicesFilter?: boolean
}

export interface IModalRedux extends IModalKey {
  pendingModalKey?: string,
}

export const INITIAL_STATE: ImmutableObject<IModalRedux> = Immutable({
  cappUpdate: false,
  bottomSheetVcea: false,
  searchTabFlightFilter: false,
  searchAllCategoryFilter: false,
  flightResultCategoryFilter: false,
  pendingModalKey: "",
  saveConnectingFlightSearch: false,
  saveConnectingFlightSearchAll: false,
  saveConnectingFlightLanding: false,
  saveConnectingFlightDetail: false,
  saveConnectingFlightResultArr: false,
  saveConnectingFlightResultDep: false,
  saveFlightTravelOptionLandingArr: false,
  saveFlightTravelOptionLandingdep: false,
  saveFlightTravelOptionResultArr: false,
  saveFlightTravelOptionResultDep: false,
  saveFlightTravelOptionSearchAll: false,
  saveFlightTravelOptionSearchFlight: false,
  shopDetailError: false,
  dineDetailError: false,
  modalSaveAndShare: false,
  flightDetailSaveTravelOption: false,
  saveConnectingFlightListingV2: false,
  saveFlightTravelOptionLightListingV2: false,
  searchDepartureFlight: false
})


/* ------------- Selectors ------------- */
export const ModalManagerSelectors = {
  cappUpdate: (state: RootState) => state.modalManagerReducer.cappUpdate,
  bottomSheetVcea: (state: RootState) => state.modalManagerReducer.bottomSheetVcea,
  searchTabFlightFilter: (state: RootState) => state.modalManagerReducer.searchTabFlightFilter,
  searchAllCategoryFilter: (state: RootState) => state.modalManagerReducer.searchAllCategoryFilter,
  flightResultCategoryFilter: (state: RootState) => state.modalManagerReducer.flightResultCategoryFilter,
  parkingEntitlements: (state: RootState) => state.modalManagerReducer.parkingEntitlements,
  updateVehicleIUError: (state: RootState) => state.modalManagerReducer.updateVehicleIUError,
  saveConnectingFlightSearch: (state: RootState) => state.modalManagerReducer.saveConnectingFlightSearch,
  saveConnectingFlightSearchAll: (state: RootState) => state.modalManagerReducer.saveConnectingFlightSearchAll,
  saveConnectingFlightLanding: (state: RootState) => state.modalManagerReducer.saveConnectingFlightLanding,
  saveConnectingFlightDetail: (state: RootState) => state.modalManagerReducer.saveConnectingFlightDetail,
  saveConnectingFlightResultArr: (state: RootState) => state.modalManagerReducer.saveConnectingFlightResultArr,
  saveConnectingFlightResultDep: (state: RootState) => state.modalManagerReducer.saveConnectingFlightResultDep,
  saveFlightTravelOptionLandingArr: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionLandingArr,
  saveFlightTravelOptionLandingdep: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionLandingdep,
  saveFlightTravelOptionResultArr: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionResultArr,
  saveFlightTravelOptionResultDep: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionResultDep,
  saveFlightTravelOptionSearchAll: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionSearchAll,
  saveFlightTravelOptionSearchFlight: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionSearchFlight,
  shopDetailError: (state: RootState) => state.modalManagerReducer.shopDetailError,
  dineDetailError: (state: RootState) => state.modalManagerReducer.dineDetailError,
  modalSaveAndShare: (state: RootState) => state.modalManagerReducer.modalSaveAndShare,
  flightDetailSaveTravelOption: (state: RootState) => state.modalManagerReducer.flightDetailSaveTravelOption,
  saveFlightTravelOptionLightListingV2: (state: RootState) => state.modalManagerReducer.saveFlightTravelOptionLightListingV2,
  saveConnectingFlightListingV2: (state: RootState) => state.modalManagerReducer.saveConnectingFlightListingV2,
  searchDepartureFlight: (state: RootState) => state.modalManagerReducer.searchDepartureFlight,
  facilitiesServicesFilter: (state: RootState) => state.modalManagerReducer.facilitiesServicesFilter,
}

/* ------------- Reducers ------------- */

const openModal = (state, {modalKey}) => {
  let isValidOpen = true
  const {pendingModalKey, ...rest} = state
  Object.entries(rest).forEach(([key, value]) => {
    if(key !== modalKey && value){
      isValidOpen = false
      return;
    }
  });
  if(!isValidOpen){
    return {
      ...INITIAL_STATE,
      pendingModalKey: modalKey
    }
  }
  return {
    ...INITIAL_STATE,
    [modalKey]: true,
  }
}

const closeModal = (state, {modalKey}) => {
  return {
    ...state,
    [modalKey]: false,
  }
}

const openPendingModal = (state) => {
  const pendingModalKey = state.pendingModalKey
  if(!pendingModalKey){
    return state
  }
  return {
    ...INITIAL_STATE,
    [pendingModalKey]: true
  }
}


/* ------------- Hookup Reducers To Types ------------- */
export const reducer = createReducer<IModalRedux>(INITIAL_STATE, {
  [Types.OPEN_MODAL]: openModal,
  [Types.CLOSE_MODAL]: closeModal,
  [Types.OPEN_PENDING_MODAL]: openPendingModal
})
