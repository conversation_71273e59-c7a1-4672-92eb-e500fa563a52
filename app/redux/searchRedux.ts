/**
 *   REDUX for Search page
 */

import { createActions, createReducer } from "reduxsauce"
import Immutable, { ImmutableObject } from "seamless-immutable"
import { TenantListingHorizontalType } from "app/components/tenant-listing-horizontal/tenant-listing-horizontal"
import { get, isEmpty } from "lodash"
import {
  getDataUpdateInsertFlight,
  getDataUpdateRemoveFlight,
  mapingYMALDataResponse,
  updateFlightSearchAll,
} from "app/utils/search-data-helper"
import { capitalizeFirstLetter } from "app/utils"
import { save, StorageKey } from "app/utils/storage"
import { IItemAutocomplete } from "app/screens/search/autocomplete-search"
import { ISearchYouMayAlsoLikeItem } from "app/sections/search-you-may-also-like/search-you-may-also-like"
import { GROUP_TYPE } from "app/screens/search/tabs/searchIndex"

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  searchAllReset: null,
  searchAllRequest: ["textSearch", "savedFlight"],
  searchAllSuccess: ["payload"],
  searchAllFailure: null,
  searchAllV2Reset: null,
  searchAllV2Request: ["textSearch"],
  searchAllV2Success: ["payload"],
  searchAllV2Failure: null,
  updateInsertFlightSearchAll: ["payload", "flightData"],
  updateRemoveFlightSearchAll: ["payload", "flightData"],
  updateInsertRemoveFlightFailure: ["flightData"],
  updateStatusFlight: ["flightData"],

  dineSearchRequest: ["params"],
  dineSearchPaginate: ["params"],
  dineSearchFailure: null,
  dineSearchSuccess: ["payload"],
  dineSearchReset: null,

  airportSearchRequest: ["text", "pagingRequest"],
  airportSearchSuccess: ["payload"],
  airportSearchFailure: ["payload"],
  airportSearchReset: null,

  shopSearchRequest: ["params"],
  shopSearchPaginate: ["params"],
  shopSearchFailure: null,
  shopSearchSuccess: ["payload"],
  shopSearchReset: null,

  attractionsSearchRequest: ["params"],
  attractionsSearchPaginate: ["params"],
  attractionsSearchFailure: null,
  attractionsSearchSuccess: ["payload"],
  attractionsSearchReset: null,

  eventsSearchRequest: ["params"],
  eventsSearchPaginate: ["params"],
  eventsSearchFailure: null,
  eventsSearchSuccess: ["payload"],
  eventsSearchReset: null,

  setSearchKeyword: ["keyword"],
  setViewAllFlight: ["payload"],
  setIsDepartureData: ["payload"],

  popularSearchKeywordRequest: ["payload"],
  popularSearchKeywordSuccess: ["payload"],
  popularSearchKeywordFailure: ["payload"],

  resetSearchKeywordCollection: [],
  addSearchKeywordToCollection: ["keyword", "numberResult"],
  sendSearchKeywordCollection: ["screenType", "sourceType"],
  sendSearchKeywordCollectionSuccess: [],

  handleSearchKeywordForAppState: ["screenType", "sourceType"],
  getAutoCompleteKeywordRequest: ["params"],
  getAutoCompleteKeywordSuccess: ["payload"],
  getAutoCompleteKeywordFailure: [""],
  resetListAutoCompleteKeyword: [""],

  getYouMayAlsoLikeRequest: ["params"],
  getYouMayAlsoLikeSuccess: ["payload"],
  resetListYouMayAlsoLikeData: [""],

  searchFlightsV2Reset: null,
  setSearchFlightsV2: ["payload"],
  searchFlightsV2Request: ["keyword", "date", "direction", "terminal", "airport", "airline"],
  searchFlightsV2Success: ["payload"],
  searchFlightsV2Failure: null,

  setLastSearchKeyword: ["keyword"],
  getAutoCompleteFlightRequest: ["keyword"],
  getAutoCompleteFlightSuccess: ["payload"],
  getAutoCompleteFlightFailure: [""],
  resetAutoCompleteFlight: [],

  setFirstTimeEnterFlightSearch: null,
})

export const SearchTypes = Types
export default Creators

// /* ------------- Interface ------------- */
export interface Search {
  searchAllLoading: boolean
  searchAllResult: {
    searchError: boolean
    searchData: any[]
  }
  searchAllV2Result: {
    searchError: boolean
    searchData: any[]
  }
  searchFlightsV2Result: {
    searchError: boolean
    searchData: any[]
  }
  airportSearchPayload: {
    data: any[]
    loading: boolean
    error: any
    query: any
  }
  dineSearchResults: any
  dineSearchResultsFetching: boolean
  dineSearchResultsPaginating: boolean
  dineSearchTotalItems: number
  dineSearchCurrentPage: number
  dineSearchError: boolean
  dineSearchErrorMessage: string
  dineSearchLastKeyword: string

  shopSearchResults: any
  shopSearchResultsFetching: boolean
  shopSearchResultsPaginating: boolean
  shopSearchTotalItems: number
  shopSearchCurrentPage: number
  shopSearchError: boolean
  shopSearchErrorMessage: string
  shopSearchLastKeyword: string

  attractionsSearchResults: any
  attractionsSearchResultsFetching: boolean
  attractionsSearchResultsPaginating: boolean
  attractionsSearchTotalItems: number
  attractionsSearchCurrentPage: number
  attractionsSearchError: boolean
  attractionsSearchErrorMessage: string
  attractionsSearchLastKeyword: string
  attractionsSearchExistingData: any

  eventsSearchResults: any
  eventsSearchResultsFetching: boolean
  eventsSearchResultsPaginating: boolean
  eventsSearchTotalItems: number
  eventsSearchCurrentPage: number
  eventsSearchError: boolean
  eventsSearchErrorMessage: string
  eventsSearchLastKeyword: string
  eventsSearchExistingData: any

  searchKeyword: string
  listRecentSearch: any[]

  viewAllFlight: boolean
  isDepartureData: boolean

  popularSearchKeywordList: any[]

  searchKeywordCollection: any[]
  getAutoCompleteKeywordLoading: boolean
  autoCompleteKeywordList: IItemAutocomplete[]

  getYouMayAlsoLikeLoading: boolean
  listSearchYouMayAlsoLike: []

  isFirstTimeEnterFlightSearch: boolean
  setLastSearchKeyword: string
  getAutoCompleteFlightRequest: string
}

/* ------------- Initial State ------------- */

export const INITIAL_STATE: ImmutableObject<Search> = Immutable({
  searchAllLoading: false,
  searchAllResult: {
    searchError: false,
    searchData: null,
  },
  searchAllV2Result: {
    searchError: false,
    searchData: null,
  },
  searchFlightsV2Result: {
    searchError: false,
    searchData: null,
  },
  airportSearchPayload: {
    data: null,
    loading: false,
    error: null,
    query: {},
  },
  dineSearchResults: [],
  dineSearchResultsFetching: false,
  dineSearchResultsPaginating: false,
  dineSearchTotalItems: null,
  dineSearchCurrentPage: null,
  dineSearchError: false,
  dineSearchErrorMessage: null,
  dineSearchExistingData: [],
  dineSearchLastKeyword: "",

  shopSearchResults: [],
  shopSearchResultsFetching: false,
  shopSearchResultsPaginating: false,
  shopSearchTotalItems: null,
  shopSearchCurrentPage: 1,
  shopSearchError: false,
  shopSearchErrorMessage: null,
  shopSearchExistingData: [],
  shopSearchLastKeyword: "",

  attractionsSearchResults: [],
  attractionsSearchResultsFetching: false,
  attractionsSearchResultsPaginating: false,
  attractionsSearchTotalItems: null,
  attractionsSearchCurrentPage: null,
  attractionsSearchError: false,
  attractionsSearchErrorMessage: null,
  attractionsSearchExistingData: [],
  attractionsSearchLastKeyword: "",

  eventsSearchResults: [],
  eventsSearchResultsFetching: false,
  eventsSearchResultsPaginating: false,
  eventsSearchTotalItems: null,
  eventsSearchCurrentPage: null,
  eventsSearchError: false,
  eventsSearchErrorMessage: null,
  eventsSearchExistingData: [],
  eventsSearchLastKeyword: "",

  searchKeyword: "",
  listRecentSearch: [],
  isDepartureData: true,

  popularSearchKeywordList: [],

  searchKeywordCollection: [],
  getAutoCompleteKeywordLoading: false,
  autoCompleteKeywordList: [],
  getYouMayAlsoLikeLoading: false,
  listSearchYouMayAlsoLike: [],

  isFirstTimeEnterFlightSearch: false,

  lastSearchKeyword: "",
  searchFlightCollection: [],
  getAutoCompleteFlightLoading: false,
  autoCompleteFlightList: [],
})

/* ------------- Selectors ------------- */

export const SearchSelectors = {
  searchAllLoading: (state) => state.searchReducer.searchAllLoading,
  searchAllResult: (state) => state.searchReducer.searchAllResult,
  searchAllV2Result: (state) => state.searchReducer.searchAllV2Result,

  dineSearchResults: (state) => state.searchReducer.dineSearchResults,
  dineSearchResultsFetching: (state) => state.searchReducer.dineSearchResultsFetching,
  dineSearchResultsPaginating: (state) => state.searchReducer.dineSearchResultsPaginating,
  dineSearchTotalItems: (state) => state.searchReducer.dineSearchTotalItems,
  dineSearchCurrentPage: (state) => state.searchReducer.dineSearchCurrentPage,
  dineSearchError: (state) => state.searchReducer.dineSearchError,
  dineSearchErrorMessage: (state) => state.searchReducer.dineSearchErrorMessage,
  dineSearchLastKeyword: (state) => state.searchReducer.dineSearchLastKeyword,

  shopSearchResults: (state) => state.searchReducer.shopSearchResults,
  shopSearchResultsFetching: (state) => state.searchReducer.shopSearchResultsFetching,
  shopSearchResultsPaginating: (state) => state.searchReducer.shopSearchResultsPaginating,
  shopSearchTotalItems: (state) => state.searchReducer.shopSearchTotalItems,
  shopSearchCurrentPage: (state) => state.searchReducer.shopSearchCurrentPage,
  shopSearchError: (state) => state.searchReducer.shopSearchError,
  shopSearchErrorMessage: (state) => state.searchReducer.shopSearchErrorMessage,
  shopSearchLastKeyword: (state) => state.searchReducer.shopSearchLastKeyword,

  attractionsSearchResults: (state) => state.searchReducer.attractionsSearchResults,
  attractionsSearchResultsFetching: (state) => state.searchReducer.attractionsSearchResultsFetching,
  attractionsSearchResultsPaginating: (state) =>
    state.searchReducer.attractionsSearchResultsPaginating,
  attractionsSearchTotalItems: (state) => state.searchReducer.attractionsSearchTotalItems,
  attractionsSearchCurrentPage: (state) => state.searchReducer.attractionsSearchCurrentPage,
  attractionsSearchError: (state) => state.searchReducer.attractionsSearchError,
  attractionsSearchErrorMessage: (state) => state.searchReducer.attractionsSearchErrorMessage,
  attractionsSearchLastKeyword: (state) => state.searchReducer.attractionsSearchLastKeyword,

  eventsSearchResults: (state) => state.searchReducer.eventsSearchResults,
  eventsSearchResultsFetching: (state) => state.searchReducer.eventsSearchResultsFetching,
  eventsSearchResultsPaginating: (state) => state.searchReducer.eventsSearchResultsPaginating,
  eventsSearchTotalItems: (state) => state.searchReducer.eventsSearchTotalItems,
  eventsSearchCurrentPage: (state) => state.searchReducer.eventsSearchCurrentPage,
  eventsSearchError: (state) => state.searchReducer.eventsSearchError,
  eventsSearchErrorMessage: (state) => state.searchReducer.eventsSearchErrorMessage,
  eventsSearchLastKeyword: (state) => state.searchReducer.eventsSearchLastKeyword,

  airportSearchPayload: (state) => get(state, "searchReducer.airportSearchPayload", {}),
  airportSearchQuery: (state) => get(state, "searchReducer.airportSearchPayload.query", {}),

  searchKeyword: (state) => state.searchReducer.searchKeyword,
  lastSearchKeyword: (state) => state.searchReducer.lastSearchKeyword,
  listRecentSearch: (state) => state.searchReducer.listRecentSearch,

  viewAllFlight: (state) => state.searchReducer.viewAllFlight,
  isDepartureData: (state) => state.searchReducer?.isDepartureData,

  popularSearchKeywordList: (state) => state.searchReducer?.popularSearchKeywordList,

  searchKeywordCollection: (state) => state.searchReducer.searchKeywordCollection,
  getAutoCompleteKeywordLoading: (state) => state.searchReducer.getAutoCompleteKeywordLoading,
  autoCompleteKeywordList: (state) => state.searchReducer.autoCompleteKeywordList,
  autoCompleteFlightList: (state) => state.searchReducer.autoCompleteFlightList,
  getYouMayAlsoLikeLoading: (state) => state.searchReducer.getYouMayAlsoLikeLoading,
  listSearchYouMayAlsoLike: (state) => state.searchReducer.listSearchYouMayAlsoLike,
  searchFlightsV2Result: (state) => state.searchReducer.searchFlightsV2Result,
  isFirstTimeEnterFlightSearch: (state) => state.searchReducer.isFirstTimeEnterFlightSearch,
}

/* ------------- Reducers ------------- */

const dineSearchRequest = (state, { params }) => {
  return {
    ...state,
    dineSearchResultsFetching: true,
    dineSearchExistingData: [...state.dineSearchResults],
    dineSearchResults: loadingPayload,
  }
}

const dineSearchSuccess = (state, { payload }) => {
  const items = payload?.items
  const total = payload?.total
  const page = payload?.page_number

  if (state.searchKeyword === "") {
    // ignore arriving redux payload if search bar is already cleared (related I30C-2800)
    return { ...state }
  }

  return {
    ...state,
    dineSearchResults:
      page === 1
        ? mapTenantFields(items)
        : [...state.dineSearchExistingData, ...mapTenantFields(items)],
    dineSearchResultsFetching: false,
    dineSearchResultsPaginating: false,
    dineSearchTotalItems: total,
    dineSearchCurrentPage: page,
    dineSearchError: false,
    dineSearchLastKeyword: state.searchKeyword,
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult:
              page === 1
                ? mapTenantFields(items)?.length
                : [...state.dineSearchExistingData, ...mapTenantFields(items)].length,
          },
        ]
      : state.searchKeywordCollection,
  }
}
const dineSearchFailure = (state, { errors }) => {
  return {
    ...state,
    dineSearchResults: Immutable.asMutable(state.dineSearchResults),
    dineSearchTotalItems: state.dineSearchTotalItems,
    dineSearchCurrentPage: state.dineSearchCurrentPage,
    dineSearchError: true,
    dineSearchResultsFetching: false,
    dineSearchResultsPaginating: false,
    dineSearchErrorMessage: getErrorMessage("searchDineShopQuery", errors),
  }
}
const dineSearchPaginate = (state) => {
  return {
    ...state,
    dineSearchExistingData: [...state.dineSearchResults],
    dineSearchResultsPaginating: true,
  }
}
const dineSearchReset = (state) => {
  return {
    ...state,
    dineSearchResults: [],
    dineSearchExistingData: [],
    dineSearchResultsFetching: false,
    dineSearchResultsPaginating: false,
    dineSearchTotalItems: null,
    dineSearchCurrentPage: null,
    dineSearchError: false,
    dineSearchLastKeyword: "",
  }
}

const shopSearchRequest = (state, { params }) => {
  return {
    ...state,
    shopSearchExistingData: [...state.shopSearchResults],
    shopSearchResults: loadingPayload,
    shopSearchResultsFetching: true,
  }
}
const shopSearchSuccess = (state, { payload }) => {
  const items = payload?.items
  const total = payload?.total
  const page = payload?.page_number

  if (state.searchKeyword === "") {
    return { ...state }
  }

  return {
    ...state,
    shopSearchResults:
      page === 1
        ? mapTenantFields(items)
        : [...state.shopSearchExistingData, ...mapTenantFields(items)],
    shopSearchResultsFetching: false,
    shopSearchResultsPaginating: false,
    shopSearchTotalItems: total,
    shopSearchCurrentPage: page,
    shopSearchError: false,
    shopSearchLastKeyword: state.searchKeyword,
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult:
              page === 1
                ? mapTenantFields(items).length
                : [...state.shopSearchExistingData, ...mapTenantFields(items)].length,
          },
        ]
      : state.searchKeywordCollection,
  }
}
const shopSearchFailure = (state, { errors }) => {
  return {
    ...state,
    shopSearchError: true,
    shopSearchResultsFetching: false,
    shopSearchResultsPaginating: false,
    shopSearchResults: Immutable.asMutable(state.shopSearchResults),
    shopSearchTotalItems: state.shopSearchTotalItems,
    shopSearchCurrentPage: state.shopSearchCurrentPage,
    shopSearchErrorMessage: getErrorMessage("searchDineShopQuery", errors),
  }
}
const shopSearchPaginate = (state) => {
  return {
    ...state,
    shopSearchExistingData: [...state.shopSearchResults],
    shopSearchResultsPaginating: true,
  }
}
const shopSearchReset = (state) => {
  return {
    ...state,
    shopSearchResults: [],
    shopSearchExistingData: [],
    shopSearchResultsFetching: false,
    shopSearchResultsPaginating: false,
    shopSearchTotalItems: null,
    shopSearchCurrentPage: null,
    shopSearchError: false,
    shopSearchLastKeyword: "",
  }
}

const searchAllRequest = (state, { textSearch }) => {
  return {
    ...state,
    searchAllResult: {
      searchError: false,
      searchData: null,
    },
    searchAllLoading: true,
  }
}

const searchAllReset = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllResult: {
      searchError: false,
      searchData: null,
    },
  }
}

const searchAllV2Request = (state, { textSearch }) => {
  return {
    ...state,
    searchAllV2Result: {
      searchError: false,
      searchData: null,
    },
    searchAllLoading: true,
  }
}

const searchAllV2Reset = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllV2Result: {
      searchError: false,
      searchData: null,
    },
  }
}

const updateInsertFlightSearchAll = (state, action) => {
  const searchAllResult = get(state, "searchAllResult")
  const data = get(action, "payload")

  const dataUpdate = getDataUpdateInsertFlight(data)
  const searchData = updateFlightSearchAll(searchAllResult, action, dataUpdate)

  if (isEmpty(searchData)) {
    return state
  }

  return {
    ...state,
    searchAllResult: { ...searchAllResult, searchData },
  }
}

const updateRemoveFlightSearchAll = (state, action) => {
  const searchAllResult = get(state, "searchAllResult")
  const data = get(action, "payload")

  const dataUpdate = getDataUpdateRemoveFlight(data)
  const searchData = updateFlightSearchAll(searchAllResult, action, dataUpdate)

  if (isEmpty(searchData)) {
    return state
  }

  return {
    ...state,
    searchAllResult: { ...searchAllResult, searchData },
  }
}

const updateStatusFlight = (state, action) => {
  const searchAllResult = get(state, "searchAllResult")

  const dataUpdate = {
    isSaved: false,
    isMSError: false,
    isSaveFlightLoading: true,
  }

  const searchData = updateFlightSearchAll(searchAllResult, action, dataUpdate)

  if (isEmpty(searchData)) {
    return state
  }

  return {
    ...state,
    searchAllResult: { ...searchAllResult, searchData },
  }
}

const updateInsertRemoveFlightFailure = (state, action) => {
  const searchAllResult = get(state, "searchAllResult")

  const dataUpdate = {
    isSaved: false,
    isMSError: true,
    isSaveFlightLoading: false,
  }

  const searchData = updateFlightSearchAll(searchAllResult, action, dataUpdate)

  if (isEmpty(searchData)) {
    return state
  }

  return {
    ...state,
    searchAllResult: { ...searchAllResult, searchData },
  }
}

const searchAllSuccess = (state, { payload }) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllResult: {
      searchError: false,
      searchData: payload,
    },
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult: getNumberResultSearchTabAll(payload),
          },
        ]
      : state.searchKeywordCollection,
  }
}

const searchAllFailure = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllResult: {
      searchError: true,
      searchData: [],
    },
  }
}

const searchAllV2Success = (state, { payload }) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllV2Result: {
      searchError: false,
      searchData: payload,
      searchKeyword: state.searchKeyword,
    },
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult: payload?.total,
          },
        ]
      : state.searchKeywordCollection,
  }
}

const searchAllV2Failure = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchAllV2Result: {
      searchError: true,
      searchData: [],
    },
  }
}

const airportSearchRequest = (state, { text }) => {
  const data = get(state, "airportSearchPayload.data") || []

  return {
    ...state,
    airportSearchPayload: { ...state.airportSearchPayload, loading: true, data, error: null },
  }
}

const airportSearchSuccess = (state, { payload }) => {
  const items = get(payload, "data.items") || []
  const total = get(payload, "data.total", 0)
  const query = get(payload, "query", {})
  const pagingRequest = get(query, "pagingRequest", false)
  const data = get(state, "airportSearchPayload.data") || []
  const newData = pagingRequest ? [...data, ...items] : items

  return {
    ...state,
    airportSearchPayload: {
      ...state.airportSearchPayload,
      loading: false,
      error: null,
      data: newData,
      query: { ...query, total },
    },
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult: newData?.length,
          },
        ]
      : state.searchKeywordCollection,
  }
}

const airportSearchFailure = (state, { payload }) => {
  const error = payload?.error
  const query = payload?.query

  return {
    ...state,
    airportSearchPayload: { ...state.airportSearchPayload, loading: false, error, query },
  }
}

const airportSearchReset = (state) => {
  return {
    ...state,
    airportSearchPayload: { loading: false, data: null, error: null, query: {} },
  }
}

const setSearchKeyword = (state, { keyword }) => {
  return {
    ...state,
    searchKeyword: keyword,
  }
}

const setLastSearchKeyword = (state, { keyword }) => {
  return {
    ...state,
    lastSearchKeyword: keyword,
  }
}

const setViewAllFlight = (state, action) => {
  return {
    ...state,
    viewAllFlight: action.payload,
  }
}

const setIsDepartureData = (state, action) => {
  return {
    ...state,
    isDepartureData: action.payload,
  }
}
// attractions
const attractionsSearchRequest = (state, { params }) => {
  return {
    ...state,
    attractionsSearchResultsFetching: true,
    attractionsSearchExistingData: [...state.attractionsSearchResults],
    attractionsSearchResults: loadingPayload,
  }
}
const attractionsSearchSuccess = (state, { payload }) => {
  const items = payload?.items
  const total = payload?.total
  const page = payload?.page_number

  if (state.searchKeyword === "") {
    // ignore arriving redux payload if search bar is already cleared (related I30C-2800)
    return { ...state }
  }

  return {
    ...state,
    attractionsSearchResults:
      page === 1 ? items : [...state.attractionsSearchExistingData, ...items],
    attractionsSearchResultsFetching: false,
    attractionsSearchResultsPaginating: false,
    attractionsSearchTotalItems: total,
    attractionsSearchCurrentPage: page,
    attractionsSearchError: false,
    attractionsSearchLastKeyword: state.searchKeyword,
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult:
              page === 1
                ? items?.length
                : [...state.attractionsSearchExistingData, ...items].length,
          },
        ]
      : state.searchKeywordCollection,
  }
}
const attractionsSearchFailure = (state, { errors }) => {
  return {
    ...state,
    attractionsSearchResults: Immutable.asMutable(state.attractionsSearchResults),
    attractionsSearchTotalItems: state.attractionsSearchTotalItems,
    attractionsSearchCurrentPage: state.attractionsSearchCurrentPage,
    attractionsSearchError: true,
    attractionsSearchResultsFetching: false,
    attractionsSearchResultsPaginating: false,
    attractionsSearchErrorMessage: getErrorMessage("searchDineShopQuery", errors),
  }
}
const attractionsSearchPaginate = (state) => {
  return {
    ...state,
    attractionsSearchExistingData: [...state.attractionsSearchResults],
    attractionsSearchResultsPaginating: true,
  }
}
const attractionsSearchReset = (state) => {
  return {
    ...state,
    attractionsSearchResults: [],
    attractionsSearchExistingData: [],
    attractionsSearchResultsFetching: false,
    attractionsSearchResultsPaginating: false,
    attractionsSearchTotalItems: null,
    attractionsSearchCurrentPage: null,
    attractionsSearchError: false,
    attractionsSearchLastKeyword: "",
  }
}
// end attractions

// events
const eventsSearchRequest = (state, { params }) => {
  return {
    ...state,
    eventsSearchResultsFetching: true,
    eventsSearchExistingData: [...state.eventsSearchResults],
    eventsSearchResults: loadingPayload,
  }
}
const eventsSearchSuccess = (state, { payload }) => {
  const items = payload?.items
  const total = payload?.total
  const page = payload?.page_number

  if (state.searchKeyword === "") {
    // ignore arriving redux payload if search bar is already cleared (related I30C-2800)
    return { ...state }
  }

  return {
    ...state,
    eventsSearchResults: page === 1 ? items : [...state.eventsSearchExistingData, ...items],
    eventsSearchResultsFetching: false,
    eventsSearchResultsPaginating: false,
    eventsSearchTotalItems: total,
    eventsSearchCurrentPage: page,
    eventsSearchError: false,
    eventsSearchLastKeyword: state.searchKeyword,
    searchKeywordCollection: !isEmpty(state.searchKeyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: state.searchKeyword,
            numberResult:
              page === 1 ? items.length : [...state.eventsSearchExistingData, ...items].length,
          },
        ]
      : state.searchKeywordCollection,
  }
}
const eventsSearchFailure = (state, { errors }) => {
  return {
    ...state,
    eventsSearchResults: Immutable.asMutable(state.eventsSearchResults),
    eventsSearchTotalItems: state.eventsSearchTotalItems,
    eventsSearchCurrentPage: state.eventsSearchCurrentPage,
    eventsSearchError: true,
    eventsSearchResultsFetching: false,
    eventsSearchResultsPaginating: false,
    eventsSearchErrorMessage: getErrorMessage("searchDineShopQuery", errors),
  }
}
const eventsSearchPaginate = (state) => {
  return {
    ...state,
    eventsSearchExistingData: [...state.eventsSearchResults],
    eventsSearchResultsPaginating: true,
  }
}
const eventsSearchReset = (state) => {
  return {
    ...state,
    eventsSearchResults: [],
    eventsSearchExistingData: [],
    eventsSearchResultsFetching: false,
    eventsSearchResultsPaginating: false,
    eventsSearchTotalItems: null,
    eventsSearchCurrentPage: null,
    eventsSearchError: false,
    eventsSearchLastKeyword: "",
  }
}

// end events

const popularSearchKeywordRequest = (state) => {
  return {
    ...state,
  }
}
const popularSearchKeywordSuccess = (state, { payload }) => {
  return {
    ...state,
    popularSearchKeywordList: payload?.list || [],
  }
}
const popularSearchKeywordFailure = (state) => {
  return popularSearchKeywordRequest(state)
}

const addSearchKeywordToCollection = (state, { keyword, numberResult }) => {
  return {
    ...state,
    searchKeywordCollection: !isEmpty(keyword)
      ? [
          ...state.searchKeywordCollection,
          {
            keySearch: keyword,
            numberResult: numberResult,
          },
        ]
      : state.searchKeywordCollection,
  }
}

const sendSearchKeywordCollection = (state) => {
  return {
    ...state,
  }
}
const sendSearchKeywordCollectionSuccess = (state) => {
  return {
    ...state,
    searchKeywordCollection: [],
  }
}

const resetSearchKeywordCollection = (state) => {
  return sendSearchKeywordCollectionSuccess(state)
}

const handleSearchKeywordForAppState = (state) => {
  const keywordCollection = state?.searchKeywordCollection
  save(StorageKey.keywordSearchMissingByAppState, keywordCollection)
  return popularSearchKeywordRequest(state)
}

const getAutoCompleteKeywordRequest = (state: Search) => {
  return {
    ...state,
    getAutoCompleteKeywordLoading: true,
  }
}

const getAutoCompleteKeywordSuccess = (state: Search, { payload }) => {
  const listDataPayload: IItemAutocomplete[] = mapingDataResponse(payload)
  return {
    ...state,
    getAutoCompleteKeywordLoading: false,
    autoCompleteKeywordList: listDataPayload,
  }
}

const getAutoCompleteKeywordFailure = (state: Search) => {
  return {
    ...state,
    getAutoCompleteKeywordLoading: false,
    autoCompleteKeywordList: [],
  }
}

const resetListAutoCompleteKeyword = (state: Search) => {
  return {
    ...state,
    getAutoCompleteKeywordLoading: false,
    autoCompleteKeywordList: [],
  }
}

const getAutoCompleteFlightRequest = (state: Search) => ({
  ...state,
  getAutoCompleteFlightLoading: true,
})

const getAutoCompleteFlightSuccess = (state: Search, { payload }) => ({
  ...state,
  getAutoCompleteFlightLoading: false,
  autoCompleteFlightList: mapingDataResponse(payload),
})

const getAutoCompleteFlightFailure = (state: Search) => ({
  ...state,
  getAutoCompleteFlightLoading: false,
  autoCompleteFlightList: [],
})

const resetAutoCompleteFlight = (state: Search) => ({
  ...state,
  lastSearchKeyword: "",
  getAutoCompleteFlightLoading: false,
  autoCompleteFlightList: [],
})

const getYouMayAlsoLikeRequest = (state: Search) => {
  return {
    ...state,
    getYouMayAlsoLikeLoading: true,
    listSearchYouMayAlsoLike: [],
  }
}

const getYouMayAlsoLikeSuccess = (state: Search, { payload }) => {
  const listDataPayload: ISearchYouMayAlsoLikeItem[] = mapingYMALDataResponse(payload)
  return {
    ...state,
    getYouMayAlsoLikeLoading: false,
    listSearchYouMayAlsoLike: listDataPayload,
  }
}

const resetListYouMayAlsoLikeData = (state: Search) => {
  return {
    ...state,
    getYouMayAlsoLikeLoading: false,
    listSearchYouMayAlsoLike: [],
  }
}

const searchFlightsV2Request = (state) => {
  return {
    ...state,
    searchFlightsV2Result: {
      searchError: false,
      searchData: null,
    },
    searchAllLoading: true,
  }
}

const searchFlightsV2Reset = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchFlightsV2Result: {
      searchError: false,
      searchData: null,
    },
  }
}

const setSearchFlightsV2 = (state, { payload }) => {
  return {
    ...state,
    searchAllLoading: false,
    searchFlightsV2Result: payload
  }
}

const searchFlightsV2Success = (state, { payload }) => {
  return {
    ...state,
    searchAllLoading: false,
    searchFlightsV2Result: {
      searchError: false,
      searchData: payload,
    },
  }
}

const searchFlightsV2Failure = (state) => {
  return {
    ...state,
    searchAllLoading: false,
    searchFlightsV2Result: {
      searchError: true,
      searchData: [],
    },
  }
}

const setFirstTimeEnterFlightSearch = (state) => {
  return {
    ...state,
    isFirstTimeEnterFlightSearch: true,
  }
}

/* ------------- Hookup Reducers To Types ------------- */
export const reducer = createReducer(INITIAL_STATE, {
  [Types.SEARCH_ALL_REQUEST]: searchAllRequest,
  [Types.SEARCH_ALL_SUCCESS]: searchAllSuccess,
  [Types.SEARCH_ALL_FAILURE]: searchAllFailure,
  [Types.SEARCH_ALL_RESET]: searchAllReset,
  [Types.SEARCH_ALL_V2_REQUEST]: searchAllV2Request,
  [Types.SEARCH_ALL_V2_SUCCESS]: searchAllV2Success,
  [Types.SEARCH_ALL_V2_FAILURE]: searchAllV2Failure,
  [Types.SEARCH_ALL_V2_RESET]: searchAllV2Reset,
  [Types.UPDATE_INSERT_FLIGHT_SEARCH_ALL]: updateInsertFlightSearchAll,
  [Types.UPDATE_REMOVE_FLIGHT_SEARCH_ALL]: updateRemoveFlightSearchAll,
  [Types.UPDATE_STATUS_FLIGHT]: updateStatusFlight,
  [Types.UPDATE_INSERT_REMOVE_FLIGHT_FAILURE]: updateInsertRemoveFlightFailure,

  [Types.DINE_SEARCH_REQUEST]: dineSearchRequest,
  [Types.DINE_SEARCH_FAILURE]: dineSearchFailure,
  [Types.DINE_SEARCH_SUCCESS]: dineSearchSuccess,
  [Types.DINE_SEARCH_RESET]: dineSearchReset,
  [Types.DINE_SEARCH_PAGINATE]: dineSearchPaginate,

  [Types.SHOP_SEARCH_REQUEST]: shopSearchRequest,
  [Types.SHOP_SEARCH_FAILURE]: shopSearchFailure,
  [Types.SHOP_SEARCH_SUCCESS]: shopSearchSuccess,
  [Types.SHOP_SEARCH_RESET]: shopSearchReset,
  [Types.SHOP_SEARCH_PAGINATE]: shopSearchPaginate,

  [Types.AIRPORT_SEARCH_REQUEST]: airportSearchRequest,
  [Types.AIRPORT_SEARCH_SUCCESS]: airportSearchSuccess,
  [Types.AIRPORT_SEARCH_FAILURE]: airportSearchFailure,
  [Types.AIRPORT_SEARCH_RESET]: airportSearchReset,

  [Types.ATTRACTIONS_SEARCH_REQUEST]: attractionsSearchRequest,
  [Types.ATTRACTIONS_SEARCH_FAILURE]: attractionsSearchFailure,
  [Types.ATTRACTIONS_SEARCH_SUCCESS]: attractionsSearchSuccess,
  [Types.ATTRACTIONS_SEARCH_RESET]: attractionsSearchReset,
  [Types.ATTRACTIONS_SEARCH_PAGINATE]: attractionsSearchPaginate,

  [Types.EVENTS_SEARCH_REQUEST]: eventsSearchRequest,
  [Types.EVENTS_SEARCH_FAILURE]: eventsSearchFailure,
  [Types.EVENTS_SEARCH_SUCCESS]: eventsSearchSuccess,
  [Types.EVENTS_SEARCH_RESET]: eventsSearchReset,
  [Types.EVENTS_SEARCH_PAGINATE]: eventsSearchPaginate,

  [Types.SET_SEARCH_KEYWORD]: setSearchKeyword,
  [Types.SET_VIEW_ALL_FLIGHT]: setViewAllFlight,
  [Types.SET_IS_DEPARTURE_DATA]: setIsDepartureData,

  [Types.POPULAR_SEARCH_KEYWORD_REQUEST]: popularSearchKeywordRequest,
  [Types.POPULAR_SEARCH_KEYWORD_SUCCESS]: popularSearchKeywordSuccess,
  [Types.POPULAR_SEARCH_KEYWORD_FAILURE]: popularSearchKeywordFailure,

  [Types.RESET_SEARCH_KEYWORD_COLLECTION]: resetSearchKeywordCollection,
  [Types.ADD_SEARCH_KEYWORD_TO_COLLECTION]: addSearchKeywordToCollection,
  [Types.SEND_SEARCH_KEYWORD_COLLECTION]: sendSearchKeywordCollection,
  [Types.SEND_SEARCH_KEYWORD_COLLECTION_SUCCESS]: sendSearchKeywordCollectionSuccess,

  [Types.HANDLE_SEARCH_KEYWORD_FOR_APP_STATE]: handleSearchKeywordForAppState,

  [Types.GET_AUTO_COMPLETE_KEYWORD_REQUEST]: getAutoCompleteKeywordRequest,
  [Types.GET_AUTO_COMPLETE_KEYWORD_SUCCESS]: getAutoCompleteKeywordSuccess,
  [Types.GET_AUTO_COMPLETE_KEYWORD_FAILURE]: getAutoCompleteKeywordFailure,
  [Types.RESET_LIST_AUTO_COMPLETE_KEYWORD]: resetListAutoCompleteKeyword,
  [Types.GET_YOU_MAY_ALSO_LIKE_REQUEST]: getYouMayAlsoLikeRequest,
  [Types.GET_YOU_MAY_ALSO_LIKE_SUCCESS]: getYouMayAlsoLikeSuccess,
  [Types.RESET_LIST_YOU_MAY_ALSO_LIKE_DATA]: resetListYouMayAlsoLikeData,
  [Types.SEARCH_FLIGHTS_V2_RESET]: searchFlightsV2Reset,
  [Types.SEARCH_FLIGHTS_V2_REQUEST]: searchFlightsV2Request,
  [Types.SET_SEARCH_FLIGHTS_V2]: setSearchFlightsV2,
  [Types.SEARCH_FLIGHTS_V2_SUCCESS]: searchFlightsV2Success,
  [Types.SEARCH_FLIGHTS_V2_FAILURE]: searchFlightsV2Failure,
  [Types.SET_FIRST_TIME_ENTER_FLIGHT_SEARCH]: setFirstTimeEnterFlightSearch,
  [Types.SET_LAST_SEARCH_KEYWORD]: setLastSearchKeyword,
  [Types.GET_AUTO_COMPLETE_FLIGHT_REQUEST]: getAutoCompleteFlightRequest,
  [Types.GET_AUTO_COMPLETE_FLIGHT_SUCCESS]: getAutoCompleteFlightSuccess,
  [Types.GET_AUTO_COMPLETE_FLIGHT_FAILURE]: getAutoCompleteFlightFailure,
  [Types.RESET_AUTO_COMPLETE_FLIGHT]: resetAutoCompleteFlight,
})

const emptyItem = {
  id: undefined,
  tenantName: undefined,
  location: undefined,
  logoUrl: undefined,
  categories: undefined,
  price: undefined,
  rewards: undefined,
  type: TenantListingHorizontalType.loading,
}

const loadingPayload = [...Array.from(Array(4)).map(() => emptyItem)]
const PRICE_TYPE = "price"

export const setLocationString = (item) => {
  let categoryFormat = ""
  const locationFormat = item?.location_display || ""
  let priceFormat = ""

  if (item?.category && item?.category?.length) {
    categoryFormat = item?.category?.reduce((cur, acc, ind) => {
      let newCat = cur
      if (ind > 0) {
        newCat += ", " + capitalizeFirstLetter(acc)
      } else {
        newCat += capitalizeFirstLetter(acc)
      }
      return newCat
    }, "")
  }
  if (item?.price) {
    priceFormat = item.price?.find((newItem) => newItem?.filterType === PRICE_TYPE)?.tagTitle || ""
  }
  return `${locationFormat}${categoryFormat ? " · " + categoryFormat : ""}${
    priceFormat ? " · " + priceFormat : ""
  }`
}

export const mapTenantFields = (items) => {
  return items?.length > 0
    ? Array.isArray(items) &&
        items.map((item: any) => {
          return {
            areaDisplay: item?.area_display,
            categories: item?.categories || [],
            category: item?.category,
            dietary: item?.aemTenantDetails?.dietary,
            id: item?.id,
            location: setLocationString(item),
            locationDisplay: item?.location_display,
            locationList: item?.location_list,
            logoUrl: item?.logoImage,
            price: item?.price,
            rewardTitle: item?.rewardTitle,
            tenantName: item?.title,
            type: TenantListingHorizontalType.default,
          }
        })
    : []
}

const getErrorMessage = (pathName: string, errors: any) => {
  if (Array.isArray(errors)) {
    return errors.find((err) => err?.path?.includes(pathName))?.message
  }

  return null
}

const mapingDataResponse = (payLoad: any[]) => {
  const dataList: IItemAutocomplete[] = payLoad.map((item) => {
    return {
      name: item.name,
      code: item.code,
      dataType: item.data_type,
      navigation: item.navigation,
      image: item.image,
      text: item.text,
      locationFilter: item.location_filter,
      direction: item.direction,
      originalKeyword: item.original_keyword,
    }
  })
  return dataList
}

const getNumberResultSearchTabAll = (data: any): number => {
  if (!isEmpty(data)) {
    return data.reduce((resultCount: number, section: any) => {
      const iCheck = [GROUP_TYPE.facilities, GROUP_TYPE.attractions].includes(section?.title)
      const sectionLength = iCheck
        ? section?.data?.flatMap((data) => data).length
        : section.data?.length
      return resultCount + (sectionLength || 0)
    }, 0)
  }
  return 0
}
