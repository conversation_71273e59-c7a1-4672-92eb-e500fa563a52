/**
 *   REDUX for Flight Listing Module
 */
import { SearchFlightsOptions } from "app/screens/search-v2/search-result/search-flights-result"
import { createActions, createReducer } from "reduxsauce"
import Immutable, { ImmutableObject } from "seamless-immutable"

/* ------------- Types and Action Creators ------------- */
const { Types, Creators } = createActions({
  setFlightListingFilter: ["payload"],
})

export const FlightListingTypes = Types
export { Creators as FlightListingCreators }

/* ------------- Interface ------------- */
export interface FlightListingState {
  flightListingFilter: SearchFlightsOptions
}

/* ------------- Initial State ------------- */
export const INITIAL_STATE: ImmutableObject<FlightListingState> = Immutable({
   flightListingFilter: {}
})

/* ------------- Selectors ------------- */
export const FlightListingSelectors = {
  flightListingFilter: (state) => state.flightListingReducer.flightListingFilter,
}

/* ------------- Reducers ------------- */
export const setFlightListingFilter = (state: FlightListingState, { payload }) => {
  return {
    ...state,
    flightListingFilter: payload,
  }
}

/* ------------- Hookup Reducers To Types ------------- */
export const reducer = createReducer(INITIAL_STATE, {
  [Types.SET_FLIGHT_LISTING_FILTER]: setFlightListingFilter,
})
