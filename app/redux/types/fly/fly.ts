import {
  FlightListingProps,
  FlightListingState,
} from "app/components/flight-listing-card/flight-listing-card.props"
import {
  FlightDetailsCardType,
  FlightDetailsCardProps,
  FlightStatus,
} from "app/components/flight-details-card/flight-details-card.props"
import {
  FlightHeroImageProps,
  FlightHeroImageType,
  TravelInfo,
} from "app/components/flight-hero-image/flight-hero-image.props"
import {
  TimelineSectionProps,
  TimelineTileProps,
  TimelineTileSectionType,
  TimelineSectionData,
} from "app/sections/flight-timeline-section/flight-timeline-section-props"
import { DateFormats, flyModuleDateFormatting, Locales } from "app/utils/date-time/date-time"
import { StaticFiles, writeFile } from "app/utils/file"
import { CityData } from "ichangi-fe/assets/data"
import moment from "moment"
import momentTimezone from "moment-timezone"
import { FlyBaseClass } from "../base/fly-base-class"
import { isArray, get, isEmpty } from "lodash"
import {
  CardWithLinkProps,
  CardWithLinksState,
  CardWithLinksType,
} from "app/components/card-with-links/card-with-links.props"
import { SectionImageComponentType } from "app/components/section-image/section-image"
import { TimelineFlyTilesVariations } from "app/components/timeline-fly-tiles/timeline-fly-tiles.enum"
import {
  SavedFlightCardProps,
  SavedFlightCardState,
  SavedFlightErrorCodes,
} from "app/components/saved-flight-card/saved-flight-card.props"
import {
  BenefitsCardState,
  BenefitsCardType,
} from "app/components/benefits-card/benefits-card.props"
import { FlightRequestType } from "app/screens/fly/flights/flight-props"

import { mappingUrlAem } from "app/utils"

const returnDateGroupSort = (groupArrays) => {
  return groupArrays.sort((a, b) =>
    moment(a.sortedDate, DateFormats.DayMonthYearWithSlash).diff(
      moment(b.sortedDate, DateFormats.DayMonthYearWithSlash),
    ),
  )
}

const convertDtoForFly = (
  //
  data: any,
  type: FlightListingState,
  _flyCodes?: any,
  isSaved?: boolean,
  isMSError?: boolean,
  isFirstFlight?: boolean,
): FlightListingProps => {
  if (Object.keys(data).length > 0) {
    let departingCodeIndex = { code: "", name: "" }
    let destinationCodeIndex = { code: "", name: "" }

    if (data?.direction === "ARR") {
      departingCodeIndex = {
        code: data?.airport_details?.code,
        name: data?.airport_details?.name,
      }
      destinationCodeIndex = {
        code: "SIN",
        name: "Singapore",
      }
    } else if (data?.direction === "DEP") {
      destinationCodeIndex = {
        code: data?.airport_details?.code,
        name: data?.airport_details?.name,
      }
      departingCodeIndex = {
        code: "SIN",
        name: "Singapore",
      }
    }
    return {
      actualTimestamp: data?.actual_timestamp,
      estimatedTimestamp: data?.estimated_timestamp,
      logo: data?.airline_details?.logo_url,
      flightNumber: data.flight_number,
      departingCode: departingCodeIndex?.code,
      destinationCode: destinationCodeIndex?.code,
      flightDate: data.scheduled_date,
      scheduledDate: data.scheduled_date,
      state: type,
      codeShare: data.slave_flights,
      destinationPlace: destinationCodeIndex?.name,
      departingPlace: departingCodeIndex?.name,
      timeOfFlight: data.scheduled_time,
      flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
      isSaved: isSaved,
      isMSError: isMSError,
      transits: undefined,
      flightStatusMapping: data.status_mapping?.listing_status_en,
      beltStatusMapping: data?.status_mapping?.belt_status_en,
      statusColor: data.status_mapping?.status_text_color,
      showGate: data.status_mapping?.show_gate,
      direction: data?.direction,
      flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
      terminal: data?.terminal,
      checkInRow: data?.check_in_row,
      displayBelt: data?.display_belt,
      displayTimestamp: data?.display_timestamp_mapping,
      viaAirportDetails: data?.via_airport_details,
      country: data?.airport_details?.country,
      isFirstFlight: isFirstFlight,
      upcomingStatusMapping: data.status_mapping?.details_status_en,
    }
  }
  return {
    actualTimestamp: undefined,
    estimatedTimestamp: undefined,
    state: type,
    logo: undefined,
    flightNumber: undefined,
    flightStatus: undefined,
    codeShare: undefined,
    departingCode: undefined,
    departingPlace: undefined,
    destinationCode: undefined,
    destinationPlace: undefined,
    timeOfFlight: undefined,
    transits: undefined,
    onPressed: undefined,
    onSaved: undefined,
    isSaved: undefined,
    flightDate: undefined,
    scheduledDate: undefined,
    flightStatusMapping: undefined,
    beltStatusMapping: undefined,
    statusColor: undefined,
    showGate: undefined,
    direction: undefined,
    flightUniqueId: undefined,
    terminal: undefined,
    checkInRow: undefined,
    displayBelt: undefined,
    displayTimestamp: undefined,
    viaAirportDetails: undefined,
    country: undefined
  }
}

const enumerateDaysBetweenDates = (startDate, endDate) => {
  const date = []
  while (moment(startDate) <= moment(endDate)) {
    date.push(startDate)
    startDate = moment(startDate).add(1, "days").format("YYYY-MM-DD")
  }
  return date
}

class FlyLanding extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected data: FlightListingProps[] | []
  protected type: FlightListingState
  protected nextToken: string
  protected flightRequestType: FlightRequestType
  protected previousToken: string
  constructor(
    data?: FlightListingProps[] | any[],
    errorFlag?: boolean,
    errorPayload?: [],
    nextToken?: string,
    flightRequestType?: FlightRequestType,
    type?: FlightListingState,
    previousToken?: string,
  ) {
    super()
    this.data = data
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.nextToken = nextToken
    this.flightRequestType = flightRequestType || FlightRequestType.FlightDefault
    this.type = type
    this.previousToken = previousToken
  }

  static dateGroupV1 = (data, today: string, _tomorrow: string) => {
    const groups = data.reduce((groupsummary, game) => {
      const date = game.flightDate?.split("T")[0]
      if (!groupsummary[date]) {
        groupsummary[date] = []
      }
      groupsummary[date].push(game)
      return groupsummary
    }, {})
    if (!data.length && !Object.keys(groups)?.includes(today)) {
      groups[today] = []
    }
    const groupArrays = Object.keys(groups).map((date) => {
      return {
        date: flyModuleDateFormatting(moment(date)),
        flightLanding: groups[date],
        sortedDate: moment(date).format(DateFormats.DayMonthYearWithSlash),
      }
    })
    return returnDateGroupSort(groupArrays)
  }

  static dateGroup = (data, today: string, _tomorrow: string) => {
    const groups = data.reduce((groupSummary, game) => {
      const date = game.flightDate?.split("T")[0]
      if (!groupSummary[date]) {
        groupSummary[date] = []
      }
      groupSummary[date].push(game)
      return groupSummary
    }, {})
    if (!Object.keys(groups)?.includes(today)) {
      groups[today] = []
    }
    const listMomentDateGroup = Object.keys(groups).map((d) => moment(d))
    const minDate = moment.min(listMomentDateGroup).format(DateFormats.YearMonthDay)
    const maxDate = moment.max(listMomentDateGroup).format(DateFormats.YearMonthDay)
    enumerateDaysBetweenDates(minDate, maxDate).forEach((date) => {
      if (!groups[date]) {
        groups[date] = []
      }
    })
    const groupArrays = Object.keys(groups).map((date) => {
      return {
        date: flyModuleDateFormatting(moment(date)),
        flightLanding: groups[date],
        sortedDate: moment(date).format(DateFormats.DayMonthYearWithSlash),
        currentDate: moment(date).format(DateFormats.YearMonthDay),
      }
    })
    return returnDateGroupSort(groupArrays)
  }

  static convertDto = (
    data: any,
    type: FlightListingState,
    _flyCodes?: any,
    isSaved?: boolean,
    isMSError?: boolean,
    isFirstFlight?: boolean,
  ): FlightListingProps => {
    if (Object.keys(data).length > 0) {
      let departingCodeIndex = { code: "", name: "" }
      let destinationCodeIndex = { code: "", name: "" }
      if (data?.direction === "ARR") {
        departingCodeIndex = {
          code: data?.airport_details?.code,
          name: data?.airport_details?.name,
        }
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      } else if (data?.direction === "DEP") {
        destinationCodeIndex = {
          code: data?.airport_details?.code,
          name: data?.airport_details?.name,
        }
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      }
      const newObject = {
        logo:
          data.airline_details?.logo_url ||
          `https://d15qtai6ju84a6.cloudfront.net/airlines/${data.airline}.gif`,
        flightNumber: data.flight_number,
        departingCode: departingCodeIndex?.code,
        destinationCode: destinationCodeIndex?.code,
        flightDate: data.scheduled_date,
        scheduledDate: data.scheduled_date,
        state: type,
        codeShare: data.slave_flights,
        destinationPlace: destinationCodeIndex?.name,
        departingPlace: departingCodeIndex?.name,
        timeOfFlight: data.scheduled_time,
        flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
        flightStatusMapping: data.status_mapping?.listing_status_en,
        beltStatusMapping: data.status_mapping?.belt_status_en,
        statusColor: data.status_mapping?.status_text_color,
        showGate: data.status_mapping?.show_gate,
        isSaved: isSaved,
        isMSError: isMSError,
        transits: undefined,
        flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
        estimatedTimestamp: data?.estimated_timestamp,
        actualTimestamp: data?.estimated_timestamp,
        direction: data?.direction,
        terminal: data?.terminal,
        checkInRow: data?.check_in_row,
        displayBelt: data?.display_belt,
        displayTimestamp: data?.display_timestamp_mapping,
        viaAirportDetails: data?.via_airport_details,
        country: data?.airport_details?.country,
        isFirstFlight: isFirstFlight,
        upcomingStatusMapping: data.status_mapping?.details_status_en,
        technicalFlightStatus1: data?.technical_flight_status1,
        technicalFlightStatus2: data?.technical_flight_status2,
        displayGate: data?.display_gate,
      }
      return newObject
    } else {
      return {
        state: type,
        logo: undefined,
        flightNumber: undefined,
        flightStatus: undefined,
        flightStatusMapping: undefined,
        beltStatusMapping: undefined,
        codeShare: undefined,
        departingCode: undefined,
        departingPlace: undefined,
        destinationCode: undefined,
        destinationPlace: undefined,
        timeOfFlight: undefined,
        transits: undefined,
        onPressed: undefined,
        onSaved: undefined,
        isSaved: undefined,
        flightDate: undefined,
        scheduledDate: undefined,
        flightUniqueId: undefined,
        estimatedTimestamp: undefined,
        actualTimestamp: undefined,
        statusColor: undefined,
        showGate: undefined,
        direction: undefined,
        terminal: undefined,
        checkInRow: undefined,
        displayBelt: undefined,
        displayTimestamp: undefined,
        viaAirportDetails: undefined,
        country: undefined,
        isFirstFlight: undefined,
      }
    }
  }

  public failure(data: any) {
    const { flightRequestType } = data
    const payload = [
      {
        date: "",
        flightLanding: [
          ...Array.from(Array(5)).map(() => FlyLanding.convertDto({}, FlightListingState.loading)),
        ],
      },
    ]
    return new FlyLanding(payload, true, [], null, flightRequestType, FlightListingState.loading)
  }

  public request() {
    const payload = [
      {
        date: "",
        flightLanding: [
          ...Array.from(Array(5)).map(() => FlyLanding.convertDto({}, FlightListingState.loading)),
        ],
      },
    ]
    return new FlyLanding(payload, false, [], null, null, FlightListingState.loading)
  }

  public success(action?: any, flyCodes?: any, savedFlights?: any) {
    const { data, errors } = action.payload
    if (data?.getFlights === null && errors?.length > 0) {
      const extractErrors = FlyLanding.checkErrors("getFlights", errors)
      const payload = [
        {
          date: "",
          flightLanding: [
            ...Array.from(Array(5)).map(() =>
              FlyLanding.convertDto({}, FlightListingState.loading),
            ),
          ],
        },
      ]
      return new FlyLanding(payload, extractErrors[0], extractErrors[1], null)
    } else {
      const today = moment().format(DateFormats.YearMonthDay)
      const tomorrow = moment(today)
        .locale(Locales.en)
        .add(1, "day")
        .format(DateFormats.YearMonthDay)
      let dataArray = []
      dataArray = [
        ...(data?.getFlights?.flights || []).map((item, index) => {
          const indexSaveFlight = savedFlights?.getMyTravelFlightDetails?.findIndex(
            (flight) =>
              `${flight?.flightNumber}_${flight?.scheduledDate}` ===
              `${item?.flight_number}_${item?.scheduled_date}`,
          )
          const isFirstFlight = index === 0
          return FlyLanding.convertDto(
            item,
            FlightListingState.default,
            flyCodes,
            isNaN(indexSaveFlight) ? false : indexSaveFlight !== -1,
            savedFlights?.errorFlag,
            isFirstFlight,
          )
        }),
      ]
      let payload = []
      if (action?.featureFlag === 'v1') {
        payload = FlyLanding.dateGroupV1(dataArray, today, tomorrow)
      } else {
        payload = FlyLanding.dateGroup(dataArray, today, tomorrow)
      }
      return new FlyLanding(
        payload,
        false,
        [],
        data?.getFlights?.next_token,
        null,
        FlightListingState.default,
        data?.getFlights?.previous_token,
      )
    }
  }
}

class FlyList extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected data: FlightListingProps[] | []
  protected type: FlightListingState
  protected nextToken: string
  protected previousToken: string
  protected flightRequestType: FlightRequestType
  constructor(
    data?: FlightListingProps[] | any[],
    errorFlag?: boolean,
    errorPayload?: [],
    nextToken?: string,
    previousToken?: string,
    flightRequestType?: FlightRequestType,
    type?: FlightListingState,
  ) {
    super()
    this.data = data
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.nextToken = nextToken
    this.previousToken = previousToken
    this.flightRequestType = flightRequestType || FlightRequestType.FlightDefault
    this.type = type
  }

  static convertDto = (
    data: any,
    type: FlightListingState,
    _flyCodes?: any,
    isSaved?: boolean,
    isMSError?: boolean,
    isFirstFlight?: boolean,
  ): FlightListingProps => {
    if (Object.keys(data).length > 0) {
      let departingCodeIndex = { code: "", name: "" }
      let destinationCodeIndex = { code: "", name: "" }

      if (data?.direction === "ARR") {
        departingCodeIndex = {
          code: data?.airport_details?.code,
          name: data?.airport_details?.name,
        }
        destinationCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      } else if (data?.direction === "DEP") {
        destinationCodeIndex = {
          code: data?.airport_details?.code,
          name: data?.airport_details?.name,
        }
        departingCodeIndex = {
          code: "SIN",
          name: "Singapore",
        }
      }
      return {
        actualTimestamp: data?.actual_timestamp,
        estimatedTimestamp: data?.estimated_timestamp,
        logo: data?.airline_details?.logo_url,
        flightNumber: data.flight_number,
        departingCode: departingCodeIndex?.code,
        destinationCode: destinationCodeIndex?.code,
        flightDate: data.scheduled_date,
        scheduledDate: data.scheduled_date,
        state: type,
        codeShare: data.slave_flights,
        destinationPlace: destinationCodeIndex?.name,
        departingPlace: departingCodeIndex?.name,
        timeOfFlight: data.scheduled_time,
        flightStatus: data.flight_status === "hide" ? "" : data.flight_status,
        isSaved: isSaved,
        isMSError: isMSError,
        transits: undefined,
        flightStatusMapping: data.status_mapping?.listing_status_en,
        beltStatusMapping: data.status_mapping?.belt_status_en,
        statusColor: data.status_mapping?.status_text_color,
        showGate: data.status_mapping?.show_gate,
        flightUniqueId: `${data.flight_number}_${data.scheduled_date}`,
        direction: data?.direction,
        terminal: data?.terminal,
        checkInRow: data?.check_in_row,
        displayBelt: data?.display_belt,
        displayTimestamp: data?.display_timestamp_mapping || data?.display_timestamp,
        viaAirportDetails: data?.via_airport_details,
        country: data?.airport_details?.country,
        isFirstFlight: isFirstFlight,
        upcomingStatusMapping: data.status_mapping?.details_status_en,
        displayGate: data?.display_gate,
        airportDetails: data?.airport_details,
      }
    }
    return {
      actualTimestamp: undefined,
      estimatedTimestamp: undefined,
      state: type,
      logo: undefined,
      flightNumber: undefined,
      flightStatus: undefined,
      codeShare: undefined,
      departingCode: undefined,
      departingPlace: undefined,
      destinationCode: undefined,
      destinationPlace: undefined,
      timeOfFlight: undefined,
      transits: undefined,
      onPressed: undefined,
      onSaved: undefined,
      isSaved: undefined,
      flightDate: undefined,
      scheduledDate: undefined,
      flightStatusMapping: undefined,
      beltStatusMapping: undefined,
      statusColor: undefined,
      showGate: undefined,
      flightUniqueId: undefined,
      terminal: undefined,
      checkInRow: undefined,
      displayBelt: undefined,
      displayTimestamp: undefined,
      viaAirportDetails: undefined,
      country: undefined,
      isFirstFlight: undefined,
      displayGate: undefined,
      airportDetails: undefined
    }
  }

  static dateGroup = (data) => {
    const groups = data.reduce((groupsummary, game) => {
      const date = game.flightDate.split("T")[0]
      if (!groupsummary[date]) {
        groupsummary[date] = []
      }
      groupsummary[date].push(game)
      return groupsummary
    }, {})
    const groupArrays = Object.keys(groups).map((date) => {
      return {
        date: flyModuleDateFormatting(moment(date), DateFormats.DateWithDayMonthYear),
        flightListingData: groups[date],
        sortedDate: moment(date).format(DateFormats.YearMonthDay),
        currentDate: moment(date).format(DateFormats.YearMonthDay),
      }
    })
    return returnDateGroupSort(groupArrays)
  }

  public request() {
    const payload = [
      {
        date: "",
        flightListingData: [],
      },
    ]
    return new FlyList(payload, false)
  }

  public failure(data: any) {
    const { flightRequestType } = data
    return new FlyList([], true, undefined, undefined, flightRequestType)
  }

  public success(action?: any, flyCodes?: any, savedFlights?: any) {
    const { data, errors } = action.payload

    if (data?.getFlights === null && errors?.length > 0) {
      const extractErrors = FlyList.checkErrors("getFlights", errors)
      const payload = [
        {
          date: "",
          flightLanding: [
            ...Array.from(Array(5)).map(() => FlyList.convertDto({}, FlightListingState.loading)),
          ],
        },
      ]
      return new FlyList(payload, extractErrors[0], extractErrors[1], null)
    } else {
      if (data?.getFlights?.flights?.length) {
        let dataArray = []
        dataArray = [
          ...data?.getFlights?.flights.map((item) => {
            const index = savedFlights?.getMyTravelFlightDetails?.findIndex(
              (flight) =>
                `${flight?.flightNumber}_${flight?.scheduledDate}` ===
                `${item?.flight_number}_${item?.scheduled_date}`,
            )
            return FlyList.convertDto(
              item,
              FlightListingState.default,
              flyCodes,
              isNaN(index) ? false : index !== -1,
              savedFlights?.errorFlag,
            )
          }),
        ]
        const payload: any = FlyList.dateGroup(dataArray)
        return new FlyList(
          payload,
          false,
          [],
          data?.getFlights?.next_token,
          data?.getFlights?.previous_token,
        )
      } else {
        return new FlyList([], false, [], null)
      }
    }
  }
}

class FlyDetailSearch extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected data: FlightListingProps[] | []
  protected loading: boolean
  protected query: any
  protected errorPaging: boolean
  protected shouldNavigateToDepartureTab: boolean

  constructor(
    data?: FlightListingProps[] | any[],
    loading?: boolean,
    query?: any,
    errorFlag?: boolean,
    errorPayload?: [],
    errorPaging?: boolean,
    shouldNavigateToDepartureTab?: boolean,
  ) {
    super()
    this.data = data
    this.loading = loading
    this.query = query
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.errorPaging = errorPaging
    this.shouldNavigateToDepartureTab = shouldNavigateToDepartureTab
  }

  public request(action) {
    const { data } = action

    return new FlyDetailSearch([...data], true, {}, false, null, false)
  }

  public failure(action) {
    const isPagingRequest = get(action, "error.query.isPagingRequest")

    return new FlyDetailSearch([], false, {}, true, null, isPagingRequest)
  }

  public reset() {
    return new FlyDetailSearch(null, false, {}, false, null, false)
  }

  public success(action?: any, flyCodes?: any, savedFlights?: any) {
    const { data, errors, currentData = [], query, shouldNavigateToDepartureTab } = action.payload
    const items = get(data, "flights.items", [])
    const total = get(data, "flights.total", 0)
    const pageNumber = get(query, "pageNumber", 1)
    const isPagingRequest = get(query, "isPagingRequest")
    const pageSize = get(query, "pageSize", 5)
    const hasLoadMore = total > pageSize * pageNumber

    let payload = []

    if (errors?.length > 0) {
      const extractErrors = FlyDetailSearch.checkErrors("flights", errors)
      const flightData = isPagingRequest ? currentData : []

      return new FlyDetailSearch(
        flightData,
        false,
        query,
        extractErrors[0],
        extractErrors[1],
        isPagingRequest,
      )
    }
    if (items?.length) {
      payload = items.map((item, index) => {
        const indexSaveFlight = savedFlights?.getMyTravelFlightDetails?.findIndex(
          (flight) =>
            `${flight?.flightNumber}_${flight?.scheduledDate}` ===
            `${item?.flight_number}_${item?.scheduled_date}` &&
            flight?.direction === item?.direction,
        )
        const isFirstFlight = index === 0
        return convertDtoForFly(
          item,
          FlightListingState.default,
          flyCodes,
          isNaN(indexSaveFlight) ? false : indexSaveFlight !== -1,
          savedFlights?.errorFlag,
          isFirstFlight,
        )
      })
    }
    return new FlyDetailSearch(
      currentData.concat(payload),
      false,
      { ...query, total, hasLoadMore },
      false,
      null,
      false,
      shouldNavigateToDepartureTab,
    )
  }
}
interface CitiesOrAirLineProps {
  code: string
  logo?: string | null
  name: string
  transferCounters: string
}

interface FlySearchProps {
  cities?: CitiesOrAirLineProps[]
  airlines?: CitiesOrAirLineProps[]
  flights?: FlightListingProps[]
  totalFlight?: number
  isDepartureFightData?: boolean
}
class FlySearch extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected data: FlySearchProps

  constructor(data?: FlySearchProps, errorFlag?: boolean, errorPayload?: []) {
    super()
    this.data = data
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
  }

  static convertCitiesOrAirt(items): CitiesOrAirLineProps[] {
    if (Array.isArray(items) && items.length > 0) {
      return items?.map((item) => ({
        code: item?.code,
        logo: item?.logo_url,
        name: item?.name,
        transferCounters: item?.transfer_counters,
      }))
    }
    return []
  }

  static convertFlights(data: FlySearchProps, flyCodes?: any, savedFlights?: any) {
    const items = get(data, "flights.items", [])

    if (Array.isArray(items) && items?.length > 0) {
      return items?.map((item) => {
        const index = savedFlights?.getMyTravelFlightDetails?.findIndex(
          (flight) =>
            `${flight?.flightNumber}_${flight?.scheduledDate}` ===
            `${item?.flight_number}_${item?.scheduled_date}` &&
            flight?.direction === item?.direction,
        )

        return convertDtoForFly(
          item,
          FlightListingState.default,
          flyCodes,
          isNaN(index) ? false : index !== -1,
          savedFlights?.errorFlag,
        )
      })
    }
    return []
  }

  public request() {
    return new FlySearch({}, false)
  }

  public failure() {
    return new FlySearch({}, true, undefined)
  }

  public reset() {
    return new FlySearch({}, false, null)
  }

  public success(action?: any, flyCodes?: any, savedFlights?: any) {
    const { data, errors } = action.payload

    if (errors?.length > 0) {
      const extractErrors = FlySearch.checkErrors("flights", errors)
      return new FlySearch({}, extractErrors[0], extractErrors[1])
    }
    if (!flyCodes) {
      flyCodes = CityData
    }
    const airlinesItems = get(data, "airlines.items", [])
    const citiesItems = get(data, "cities.items", [])
    const totalFlight = get(data, "flights.total", 0)
    const isDepartureFightData = get(data, "flights.isDepartureFightData")
    const flights = FlySearch.convertFlights(data, flyCodes, savedFlights)
    const airlines = FlySearch.convertCitiesOrAirt(airlinesItems)
    const cities = FlySearch.convertCitiesOrAirt(citiesItems)
    return new FlySearch(
      { flights, cities, airlines, totalFlight, isDepartureFightData },
      false,
      [],
    )
  }
}

class FlyCodes extends FlyBaseClass {
  protected errorFlag = undefined
  protected errorPayload = undefined
  protected data: []
  constructor(data?: any) {
    super()
    this.data = data
  }

  public failure() {
    return new FlyCodes(CityData)
  }

  public request() {
    return new FlyCodes(CityData)
  }

  public success(action?: any) {
    const { data } = action.payload
    if (data?.data?.getAirports?.length > 0) {
      writeFile(StaticFiles.city, data?.data?.getAirports)
      return data?.data?.getAirports
    } else {
      return new FlyCodes(CityData)
    }
  }
}

class FlightDetails extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected flightDetailsData: FlightDetailsCardProps
  protected heroImageData: FlightHeroImageProps
  protected flightRequestType: FlightRequestType
  constructor(
    flightDetailsData?: FlightDetailsCardProps,
    heroImageData?: FlightHeroImageProps,
    errorFlag?: boolean,
    errorPayload?: [],
    flightRequestType?: FlightRequestType,
  ) {
    super()
    this.flightDetailsData = flightDetailsData
    this.heroImageData = heroImageData
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.flightRequestType = flightRequestType || FlightRequestType.FlightDefault
  }

  static getEstimatedTime = (data: any) => {
    const status = data?.flightStatus?.toLowerCase()
    if (
      (status === FlightStatus.Delayed && data?.scheduledTime === data?.estimatedTime) ||
      status === FlightStatus.Cancelled
    ) {
      return ""
    }
    return data?.estimatedTime
  }

  static getTerminalValue = (data: any) => {
    const status = data?.flightStatus?.toLowerCase()
    if (status === FlightStatus.Cancelled) {
      return ""
    }
    return data?.displayTerminal
  }

  static convertFlightDetails = (
    data: any,
    type: FlightDetailsCardType,
  ): FlightDetailsCardProps => {

    const getFlightDetail = {
      ...data?.getFlightDetail?.flightMoreDetail,
      ...data?.getFlightDetail?.flightMainInfo,
      ...data?.getFlightDetail?.myTravelInfo,
    }
    if (getFlightDetail && Object.keys(getFlightDetail).length > 0) {
      const newObject = {
        type: type,
        slaves: getFlightDetail?.codeShares,
        logoUrl: getFlightDetail?.logo,
        status: getFlightDetail?.flightStatus === "hide" ? "" : getFlightDetail.flightStatus,
        terminal: FlightDetails.getTerminalValue(getFlightDetail),
        gate: getFlightDetail?.displayGate,
        baggageBelt: getFlightDetail?.displayBelt,
        baggageBeltStatus: getFlightDetail?.bagStatus,
        estimatedTime: FlightDetails.getEstimatedTime(getFlightDetail),
        onlineCheckIn: data?.getOnlineCheckin,
        earlyCheckIn: data?.getEarlyCheckin,
        statusMapping: getFlightDetail?.statusMapping,
        country: getFlightDetail?.airportDetails?.country,
        groundTransport: data?.getFlightDetail?.groundTransport
      }
      return {
        ...getFlightDetail,
        ...newObject,
      }
    }
    return {
      flightNumber: undefined,
      scheduledDate: undefined,
      scheduledTime: undefined,
      estimatedDate: undefined,
      estimatedTime: undefined,
      logoUrl: undefined,
      status: undefined,
      slaves: undefined,
      isFlightSaved: undefined,
      onButtonPressed: undefined,
      type: type,
      state: undefined,
      terminal: undefined,
      checkInRow: undefined,
      gate: undefined,
      checkInRowCoordinates: undefined,
      gateCoordinates: undefined,
      onlineCheckIn: undefined,
      earlyCheckIn: undefined,
      baggageBelt: undefined,
      baggageBeltCoordinates: undefined,
      baggageBeltStatus: undefined,
      statusMapping: undefined,
      isSaved: false,
      country: undefined,
      groundTransport: undefined,
    }
  }

  static getCityNameFromCode = (code: string, flyCodes?: any): string => {
    const dataFlyCodes = isArray(flyCodes) ? flyCodes : flyCodes?.data
    const codeIndex = dataFlyCodes && dataFlyCodes?.find((item) => item.code === code)
    return codeIndex?.name ?? ""
  }

  static convertHeroImage = (
    data: any,
    type: FlightHeroImageType,
    flyCodes?: any,
  ): FlightHeroImageProps => {
    if (!flyCodes) {
      flyCodes = CityData
    }
    if (data && Object.keys(data).length > 0) {
      const transits: TravelInfo[] = []
      data?.transits?.map((item: any) =>
        transits.push({
          airportCode: item?.code,
          city: FlightDetails.getCityNameFromCode(item?.code, flyCodes),
        }),
      )
      const newObject = {
        type: type,
        imageUrl: data?.heroImage,
        travelInfo: [
          {
            airportCode: data.departingCode,
            city: data?.departingPlace,
          },
          ...transits,
          {
            airportCode: data.destinationCode,
            city: data?.destinationPlace,
          },
        ],
      }
      return {
        ...data,
        ...newObject,
      }
    }
    return {
      type: type,
      state: undefined,
      imageUrl: "",
      travelInfo: undefined,
    }
  }

  public failure(data: any) {
    const { flightRequestType } = data
    return new FlightDetails(
      FlightDetails.convertFlightDetails({}, FlightDetailsCardType.loading),
      FlightDetails.convertHeroImage({}, FlightHeroImageType.loading),
      true,
      [],
      flightRequestType,
    )
  }

  public request() {
    return new FlightDetails(
      FlightDetails.convertFlightDetails({}, FlightDetailsCardType.loading),
      FlightDetails.convertHeroImage({}, FlightHeroImageType.loading),
      false,
      [],
    )
  }

  public success(action?: any, flyCodes?: any) {
    const { data, errors } = action.payload
    const dataHandled = FlightDetails.formatResponseFlightDetails(
      !isEmpty(data?.getFlights?.flights) ? data?.getFlights?.flights[0] : {},
      flyCodes,
      data?.getEarlyCheckin,
      data?.getOnlineCheckin,
    )
    if (dataHandled?.getFlightDetail === null && errors?.length > 0) {
      const extractErrors = FlightDetails.checkErrors("getFlightDetail", errors)
      return new FlightDetails(
        FlightDetails.convertFlightDetails({}, FlightDetailsCardType.loading),
        FlightDetails.convertHeroImage({}, FlightHeroImageType.loading),
        true,
        extractErrors[0],
      )
    }
    return new FlightDetails(
      FlightDetails.convertFlightDetails(dataHandled, FlightDetailsCardType.default),
      FlightDetails.convertHeroImage(
        {
          ...dataHandled?.getFlightDetail?.flightMoreDetail,
          ...dataHandled?.getFlightDetail?.flightMainInfo,
        },
        FlightHeroImageType.default,
        flyCodes,
      ),
      false,
      [],
    )
  }

  static formatResponseFlightDetails = (response, _flyCodes, getEarlyCheckin, getOnlineCheckin) => {
    let departingCodeIndex = null
    let destinationCodeIndex = null
    if (response?.direction === "ARR") {
      departingCodeIndex = {
        code: response?.airport_details?.code,
        name: response?.airport_details?.name,
      }
      destinationCodeIndex = {
        code: "SIN",
        name: "Singapore",
      }
    } else if (response?.direction === "DEP") {
      destinationCodeIndex = {
        code: response?.airport_details?.code,
        name: response?.airport_details?.name,
      }
      departingCodeIndex = {
        code: "SIN",
        name: "Singapore",
      }
    }
    return {
      getEarlyCheckin: getEarlyCheckin,
      getFlightDetail: {
        flightMainInfo: {
          logo:
            response.logo ||
            `https://d2xm3qu3nwgbrt.cloudfront.net/airlines/${response?.airline}.gif`,
          codeShares: response?.slave_flights,
          slaves: response?.slave_flights,
          airline: response?.airline,
          airlineDetails: response?.airline_details,
          departingCode: departingCodeIndex?.code,
          departingPlace: departingCodeIndex?.name,
          destinationCode: destinationCodeIndex?.code,
          destinationPlace: destinationCodeIndex?.name,
          flightDate: null,
          flightNumber: response?.flight_number,
          flightStatus: response?.flight_status,
          flightTime: null,
          flightUniqueId: `${response?.flight_number}_${response?.scheduled_date}`,
          scheduledDate: response?.scheduled_date,
          scheduledTime: response?.scheduled_time,
          direction: response?.direction,
          transits: response?.via
            ? [
              {
                code: response?.via,
                id: 1,
              },
            ]
            : [],
          via: response?.via,
          viaAirportDetails: response?.via_airport_details,
          originDepDate: response?.origin_dep_date,
          nearestCarpark: response?.nearest_carpark,
          actualTimestamp: response?.actual_timestamp,
          estimatedTimestamp: response?.estimatedTimestamp,
          displayTimestamp: response?.display_timestamp_mapping,
          statusMapping: response?.status_mapping,
          airport: response?.airport,
          airportDetails: response?.airport_details,
          technicalFlightStatus1: response?.technical_flight_status1,
          baggageTracking: response?.baggageTracking,
          upcomingStatusMapping: response?.status_mapping?.details_status_en,
          displayGate: response?.display_gate,
          showGate: response.status_mapping?.show_gate,
          beltStatusMapping: response?.status_mapping?.belt_status_en,
        },
        flightMoreDetail: {
          bagStatus: null,
          checkInRow: response?.check_in_row,
          displayBelt: response?.display_belt,
          displayGate: response?.display_gate,
          displayTerminal: response?.terminal,
          displayTerminalDisclaimer: response?.terminal_disclaimer,
          estimatedDate: response?.estimated_timestamp?.split(" ")?.[0],
          estimatedTime: response?.estimated_timestamp?.split(" ")?.[1],
          heroImage: response?.hero_image,
        },
        myTravelInfo: {
          flightPax: null,
          isSaved: response?.isSaved,
          recordSource: null,
          isPassenger: response?.isPassenger,
        },
        groundTransport: response?.groundTransport
      },
      getOnlineCheckin: getOnlineCheckin,
    }
  }
}

class FlightTimelineTiles extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected timelineTileData: TimelineSectionData
  constructor(timelineTileData?: TimelineSectionData, errorFlag?: boolean, errorPayload?: []) {
    super()
    this.timelineTileData = timelineTileData
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
  }

  static convertDto = (
    data: any,
    type: TimelineTileSectionType,
    sectionImageType: SectionImageComponentType,
    appSettingsData = null,
  ): TimelineSectionData => {
    if (data && Array.isArray(data) && data.length > 0) {
      const sectionData: TimelineSectionProps[] = []

      data?.map((item) => {
        const tileData: TimelineTileProps[] = []
        item?.variations?.map((tile) => {
          if (tile?.type === TimelineFlyTilesVariations.variation1) {
            return tileData.push({
              ...tile,
              image: mappingUrlAem(tile?.image, appSettingsData?.AEM_URL),
              firstLink: {
                label: tile?.link1Text,
                subLabel: tile?.link1Label,
                navigationType: tile?.link1Navigation?.type,
                navigationValue: tile?.link1Navigation?.value,
              },
            })
          }
          if (tile?.type === TimelineFlyTilesVariations.variation2) {
            return tileData.push({
              ...tile,
              image: mappingUrlAem(tile?.image, appSettingsData?.AEM_URL),
              firstLink: {
                label: tile?.link1Text,
                subLabel: tile?.link1Label,
                navigationType: tile?.link1Navigation?.type,
                navigationValue: tile?.link1Navigation?.value,
              },
              secondLink: {
                label: tile?.link2Text,
                subLabel: tile?.link2Label,
                navigationType: tile?.link2Navigation?.type,
                navigationValue: tile?.link2Navigation?.value,
              },
            })
          }
          if (tile?.type === TimelineFlyTilesVariations.variation3) {
            return tileData.push({
              ...tile,
              contents: tile?.contents?.map((itemContent) => ({
                ...itemContent,
                imageUrl: mappingUrlAem(itemContent?.image, appSettingsData?.AEM_URL),
                titleName: itemContent?.title,
                navigationType: itemContent?.navigation?.type,
                navigationValue: itemContent?.navigation?.value,
              })),
            })
          }
          return tileData.push({
            ...tile,
          })
        })
        return sectionData?.push({
          ...item,
          tileItems: tileData,
        })
      })

      return {
        type: type,
        sectionImageType: sectionImageType,
        timelineTiles: sectionData,
      }
    }
    return {
      type: type,
      sectionImageType: sectionImageType,
      timelineTiles: undefined,
    }
  }

  public failure() {
    return new FlightTimelineTiles(
      FlightTimelineTiles.convertDto(
        [],
        TimelineTileSectionType.default,
        SectionImageComponentType.defaultDarkGradient,
      ),
      true,
      [],
    )
  }

  public request() {
    return new FlightTimelineTiles(
      FlightTimelineTiles.convertDto(
        [],
        TimelineTileSectionType.loading,
        SectionImageComponentType.loading,
      ),
      false,
      [],
    )
  }

  public success(action?: any) {
    const { data, errors } = action.payload
    const appSettingsData = action?.env
    if (errors?.length > 0) {
      const extractErrors = FlightTimelineTiles.checkErrors("getTravelChecklists", errors)
      return new FlightTimelineTiles(
        FlightTimelineTiles.convertDto(
          [],
          TimelineTileSectionType.default,
          SectionImageComponentType.defaultDarkGradient,
          appSettingsData,
        ),
        true,
        extractErrors[0],
      )
    }
    return new FlightTimelineTiles(
      FlightTimelineTiles.convertDto(
        data?.getTravelChecklists,
        TimelineTileSectionType.default,
        SectionImageComponentType.defaultDarkGradient,
        appSettingsData,
      ),
      false,
      [],
    )
  }
}

class GetIntoCityOrAirport extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected getIntoCityOrAirport: CardWithLinkProps
  constructor(getIntoCityOrAirport?: CardWithLinkProps, errorFlag?: boolean, errorPayload?: []) {
    super()
    this.getIntoCityOrAirport = getIntoCityOrAirport
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
  }

  static convertDtoForGetIntoCityOrAirport = (item: any) => {
    if (item && Object?.keys(item).length > 0) {
      return {
        state: CardWithLinksState.withIcons,
        type: CardWithLinksType.default,
        title: item.title,
        label1: item.label1,
        text1: item.link1 ? item.link1 : "N/A",
        link1: item.link1 ? item.link1 : "N/A",
        label2: item.label2,
        text2: item.link2 ? item.link2 : "N/A",
        link2: item.link2 ? item.link2 : "N/A",
        label3: item.label3,
        text3: item.link3 ? item.link3 : "N/A",
        link3: item.link3 ? item.link3 : "N/A",
        data1: item.data1,
        data2: item.data2,
        data3: item.data3,
      }
    }
    return {
      state: CardWithLinksState.withIcons,
      type: CardWithLinksType.loading,
      title: undefined,
      label1: undefined,
      text1: undefined,
      link1: undefined,
      label2: undefined,
      text2: undefined,
      link2: undefined,
      label3: undefined,
      text3: undefined,
      link3: undefined,
      data1: undefined,
      data2: undefined,
      data3: undefined,
    }
  }

  public failure() {
    return new GetIntoCityOrAirport(
      GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}),
      true,
      [],
    )
  }

  public request() {
    return new GetIntoCityOrAirport(
      GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}),
      false,
      [],
    )
  }

  public success(action?: any) {
    const { data, errors } = action.payload
    if (data?.getIntoCityOrAirport === null && errors?.length > 0) {
      const extractErrors = GetIntoCityOrAirport.checkErrors("getIntoCityOrAirport", errors)
      return new GetIntoCityOrAirport(
        GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport({}),
        true,
        extractErrors[0],
      )
    }
    return new GetIntoCityOrAirport(
      GetIntoCityOrAirport.convertDtoForGetIntoCityOrAirport(data?.getIntoCityOrAirport),
      false,
      [],
    )
  }
}
class FlightDetailsCRT extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected flightDetailsCRTData: any
  protected type: BenefitsCardType
  constructor(
    type?: BenefitsCardType,
    flightDetailsCRTData?: any,
    errorFlag?: boolean,
    errorPayload?: [],
  ) {
    super()
    this.type = type
    this.flightDetailsCRTData = flightDetailsCRTData
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
  }

  static convertDtoForFlightDetailsCRT = (item: any) => {
    if (item === null || item === undefined || (item && Object?.keys(item).length === 0)) {
      return null
    }
    if (item && Object?.keys(item).length > 0) {
      return {
        type: BenefitsCardType.default,
        state: BenefitsCardState.crtBenefits,
        title: item.title,
        data: item.highlights
          ? Array.from(item.highlights).map((value, index) => {
            return { id: index, checkIconText: value }
          })
          : [],
        url: item.url,
        icon: item.icon,
      }
    }
    return {
      type: BenefitsCardType.loading,
      state: undefined,
      title: undefined,
      data: undefined,
      url: undefined,
      icon: undefined,
    }
  }

  public failure() {
    return new FlightDetailsCRT(null, FlightDetailsCRT.convertDtoForFlightDetailsCRT({}), true, [])
  }

  public request() {
    return new FlightDetailsCRT(
      BenefitsCardType.loading,
      FlightDetailsCRT.convertDtoForFlightDetailsCRT({
        type: BenefitsCardType.loading,
        state: undefined,
        title: undefined,
        data: undefined,
        url: undefined,
        icon: undefined,
      }),
      false,
      [],
    )
  }

  public success(action?: any) {
    const { data, errors } = action.payload
    if (data?.getCRTBenefitCard === null && errors?.length > 0) {
      const extractErrors = FlightDetailsCRT.checkErrors("getCRTBenefitCard", errors)
      return new FlightDetailsCRT(
        null,
        FlightDetailsCRT.convertDtoForFlightDetailsCRT({}),
        true,
        extractErrors[0],
      )
    }
    return new FlightDetailsCRT(
      BenefitsCardType.default,
      FlightDetailsCRT.convertDtoForFlightDetailsCRT(data?.getCRTBenefitCard),
      false,
      [],
    )
  }
}

class MyTravelInsertFlight extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected insertFlightData: any
  protected loading: boolean
  protected recordExist: boolean
  protected isInsertSuccessfully: boolean
  constructor(
    insertFlightData?: any,
    errorFlag?: boolean,
    errorPayload?: [],
    loading?: boolean,
    recordExist?: boolean,
    isInsertSuccessfully?: boolean,
  ) {
    super()
    this.insertFlightData = insertFlightData
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.loading = loading
    this.recordExist = recordExist
    this.isInsertSuccessfully = isInsertSuccessfully
  }

  public failure() {
    return new MyTravelInsertFlight({}, true, [], false, false, false)
  }

  public request() {
    return new MyTravelInsertFlight({}, false, [], true, false, false)
  }

  public success(action?: any) {
    const { data, errors } = action.payload
    if (data?.insertMyTravelFlightDetail === null && errors?.length > 0) {
      const extractErrors = MyTravelInsertFlight.checkErrors("insertMyTravelFlightDetail", errors)
      return new MyTravelInsertFlight({}, true, extractErrors[0], false, false, false)
    }
    const status = data?.insertMyTravelFlightDetail?.status
    if (status?.message === SavedFlightErrorCodes.RecordAlreadyExist) {
      return new MyTravelInsertFlight({}, false, [], false, true, true)
    }
    if (status?.success) {
      return new MyTravelInsertFlight(status, false, [], false, false, true)
    }
    return new MyTravelInsertFlight({}, true, [], false, false, false)
  }
}

class GetMyTravelFlightDetails extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected getMyTravelFlightDetails: SavedFlightCardProps[]
  protected loading: boolean
  constructor(
    getMyTravelFlightDetails?: SavedFlightCardProps[],
    errorFlag?: boolean,
    errorPayload?: [],
    loading?: boolean,
  ) {
    super()
    this.getMyTravelFlightDetails = getMyTravelFlightDetails
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.loading = loading
  }

  static getCityNameFromCode = (code: string, flyCodes?: any): string => {
    const dataFlyCodes = isArray(flyCodes) ? flyCodes : flyCodes?.data
    const codeIndex = isArray(flyCodes)
      ? dataFlyCodes?.find((item) => item.code === code)
      : dataFlyCodes?.data?.find((item) => item.code === code)
    return codeIndex?.name ?? ""
  }

  static getCityNameAndCode = (airport: any): any => {
    let departing: any = {}
    let destination: any = {}
    if (airport?.flightDirection === "ARR") {
      departing = {
        code: airport?.airportDetails?.code,
        name: airport?.airportDetails?.name,
      }
      destination = {
        code: "SIN",
        name: "Singapore",
      }
    } else if (airport?.flightDirection === "DEP") {
      destination = {
        code: airport?.airportDetails?.code,
        name: airport?.airportDetails?.name,
      }
      departing = {
        code: "SIN",
        name: "Singapore",
      }
    }
    return {
      departingCode: departing?.code,
      departingPlace: departing?.name,
      destinationCode: destination?.code,
      destinationPlace: destination?.name,
    }
  }

  static convertDto = (data: any, type: SavedFlightCardState) => {
    if (data && Object?.keys(data).length > 0) {
      const flight = data?.flightMainInfo
      const dataCityAndAiportCode: any = GetMyTravelFlightDetails.getCityNameAndCode(data)
      return {
        state: type,
        ...data,
        ...flight,
        ...dataCityAndAiportCode,
        direction: data?.flightDirection,
      }
    }
    return {
      flightDate: undefined,
      state: type,
      logo: null,
      flightNumber: null,
      status: null,
      codeShare: null,
      departingCode: null,
      departingPlace: null,
      destinationCode: null,
      destinationPlace: null,
      timeOfFlight: null,
      transitCodes: null,
      onPressed: null,
      onCloseButtonPressed: null,
    }
  }

  public failure() {
    return new GetMyTravelFlightDetails([], true, [], false)
  }

  public request() {
    return new GetMyTravelFlightDetails([], false, [], true)
  }

  public mappingFlightItem(item: any, isLanded = false) {
    const priorityTime =
      item?.actualTimeStamp ||
      item?.estimatedTimestamp ||
      `${item?.scheduledDate} ${item?.scheduledTime}`
    const currentTimeToUTC = momentTimezone().tz("Asia/Singapore")
    const flightTime = moment(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore")
    if (
      (currentTimeToUTC.format("YYYY-MM-DD HH:mm") > flightTime.format("YYYY-MM-DD HH:mm") &&
        !isLanded) ||
      (currentTimeToUTC.format("YYYY-MM-DD HH:mm") >
        moment(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") &&
        isLanded)
    ) {
      return { ...item, needShowInSavedFlight: false }
    }
    return { ...item, needShowInSavedFlight: true }
  }

  public success(action?: any) {
    const { data, errors } = action?.payload
    if (data?.getMyTravelFlightDetails === null && errors?.length > 0) {
      const extractErrors = GetMyTravelFlightDetails.checkErrors("getMyTravelFlightDetails", errors)
      return new GetMyTravelFlightDetails([], true, extractErrors[0], true)
    }
    const payload: SavedFlightCardProps[] = []
    if (isArray(data?.getMyTravelFlightDetails?.data)) {
      data?.getMyTravelFlightDetails?.data?.forEach((item) => {
        payload.push(
          GetMyTravelFlightDetails.convertDto(
            { ...item, needShowInSavedFlight: true },
            SavedFlightCardState.default,
          ),
        )
      })
    }
    return new GetMyTravelFlightDetails(payload, false, [], false)
  }
}

class MyTravelRemoveFlight extends FlyBaseClass {
  protected errorFlag: boolean
  protected errorPayload: []
  protected removeFlightData: any
  protected loading: boolean
  protected recordRemoved: boolean
  protected isRemovedSuccessFully: boolean
  constructor(
    removeFlightData?: any,
    errorFlag?: boolean,
    errorPayload?: [],
    loading?: boolean,
    recordRemoved?: boolean,
    isRemovedSuccessFully?: boolean,
  ) {
    super()
    this.removeFlightData = removeFlightData
    this.errorFlag = errorFlag
    this.errorPayload = errorPayload
    this.loading = loading
    this.recordRemoved = recordRemoved
    this.isRemovedSuccessFully = isRemovedSuccessFully
  }

  public failure() {
    return new MyTravelRemoveFlight({}, true, [], false, false, false)
  }

  public request() {
    return new MyTravelRemoveFlight({}, false, [], true, false, false)
  }

  public success(action?: any) {
    const { data, errors } = action.payload
    if (data?.deleteMyTravelFlightDetail === null && errors?.length > 0) {
      const extractErrors = MyTravelRemoveFlight.checkErrors("deleteMyTravelFlightDetail", errors)
      return new MyTravelRemoveFlight({}, true, extractErrors[0], false, false, false)
    }
    const status = data?.deleteMyTravelFlightDetail?.status
    if (status?.message === SavedFlightErrorCodes.RecordAlreadyRemoved) {
      return new MyTravelRemoveFlight({}, false, [], false, true, true)
    }
    if (status?.success) {
      return new MyTravelRemoveFlight(status, false, [], false, false, true)
    }

    return new MyTravelRemoveFlight({}, true, [], false, false, false)
  }
}

export {
  FlyLanding,
  FlySearch,
  FlyDetailSearch,
  FlyCodes,
  FlyList,
  FlightDetails,
  FlightTimelineTiles,
  GetIntoCityOrAirport,
  GetMyTravelFlightDetails,
  MyTravelInsertFlight,
  MyTravelRemoveFlight,
  FlightDetailsCRT,
}
