import { createActions, createReducer } from "reduxsauce"
import { RootState } from "./store"
import { SavedFlightCardProps } from "app/components/saved-flight-card/saved-flight-card.props"
import { GetMyTravelFlightDetails, MyTravelInsertFlight, MyTravelRemoveFlight } from "./types/fly/fly"
import {
  ANALYTICS_LOG_EVENT_NAME,
  analyticsLogEvent,
  dtACtionLogEvent,
} from "app/services/firebase"
import { DateFormats } from "app/utils/date-time/date-time"
import Immutable from "seamless-immutable"
import moment from "moment-timezone"

interface MyTravelFlightDetails {
  errorFlag: boolean
  errorPayload: []
  getMyTravelFlightDetails: SavedFlightCardProps[]
  loading: boolean,
  done: boolean,
}

interface IInsertMytravelFlight {
  errorFlag: boolean
  errorPayload: any
  insertFlightData: any
  loading: boolean
  recordExist: boolean
  isInsertSuccessfully: boolean,
  flightData?: any,
  referralFlow?: boolean
}

interface IRemoveMytravelFlight {
  errorFlag: boolean
  errorPayload: []
  removeFlightData: any
  loading: boolean
  recordRemoved: boolean
  isRemovedSuccessFully: boolean
}

interface MytravelState {
  myTravelFlightsPayload?: MyTravelFlightDetails
  insertFlightPayload?: IInsertMytravelFlight
  removeFlightPayload?: IRemoveMytravelFlight
}

const { Types, Creators } = createActions({
  flyClearMyTravelFlight: [],
  resetMyTravelFlights: [],
  flyClearInsertFlightPayload: [],
  flyMyTravelFlightsPreRequest: [],
  flyMyTravelFlightsRequest: ["enterpriseUserId"],
  flyMyTravelFlightsFailure: [],
  flyMyTravelFlightsSuccess: ["payload"],
  flyMyTravelFlightsUpdateSubcription: ["data"],

  flyMyTravelInsertFlightRequest: ["input", "payload"],
  flyMyTravelInsertFlightSuccess: ["payload", "flightData"],
  flyMyTravelInsertFlightFailure: ["flightData"],

  flyMyTravelRemoveFlightRequest: ["input", "payload"],
  flyMyTravelRemoveFlightSuccess: ["payload", "flightData"],
  flyMyTravelRemoveFlightFailure: ["flightData"],
})

export const MytravelTypes = Types
export { Creators as MytravelCreators }

/* ------------- Initial State ------------- */
export const INITIAL_STATE: MytravelState = {
  myTravelFlightsPayload: null,
  insertFlightPayload: null,
  removeFlightPayload: null,
}

/* ------------- Selectors ------------- */
export const MytravelSelectors = {
  myTravelFlightsPayload: (state: RootState) => state.mytravelReducer.myTravelFlightsPayload,
  countSavedFlight: (state: RootState) => state.mytravelReducer.myTravelFlightsPayload?.getMyTravelFlightDetails?.length ?? 0,
  insertFlightPayload: (state: RootState) => state.mytravelReducer.insertFlightPayload,
  removeFlightPayload: (state: RootState) => state.mytravelReducer.removeFlightPayload,
}

export const flyClearMyTravelFlight = (state) => {
  return {
    ...state,
    myTravelFlightsPayload: [],
  }
}

export const resetMyTravelFlights = (state) => {
  return {
    ...state,
    myTravelFlightsPayload: null,
  }
}

export const flyClearInsertFlightPayload = (state) => {
  return {
    ...state,
    insertFlightPayload: null,
    removeFlightPayload: null,
  }
}

export const flyMyTravelFlightsPreRequest = (state: MytravelState) => {
  return {
    ...state,
    myTravelFlightsPayload: {
      errorFlag: false,
      errorPayload: [],
      loading: true,
      done: false
    },
  }
}

export const flyMyTravelFlightsRequest = (state: MytravelState, payload) => {
  const requestData = new GetMyTravelFlightDetails().request()
  return {
    ...state,
    myTravelFlightsPayload: {
      ...requestData,
      getMyTravelFlightDetails: state.myTravelFlightsPayload?.getMyTravelFlightDetails,
      done: false
    },
  }
}

export const flyMyTravelFlightsFailure = (state: MytravelState, payload) => {
  const failureData = new GetMyTravelFlightDetails().failure()
  return {
    ...state,
    myTravelFlightsPayload: {
      ...failureData,
      getMyTravelFlightDetails: state.myTravelFlightsPayload?.getMyTravelFlightDetails,
      done: true
    },
  }
}

export const flyMyTravelFlightsSuccess = (state: MytravelState, payload) => {
  const successData: any = new GetMyTravelFlightDetails().success(payload)
  return {
    ...state,
    myTravelFlightsPayload: {...successData, done: true},
  }
}

export const flyMyTravelFlightsUpdateSubcription = (state: MytravelState, payload) => {
  const {data} = payload
  return {
    ...state,
    myTravelFlightsPayload: {
      errorFlag: state.myTravelFlightsPayload.errorFlag,
      errorPayload: state.myTravelFlightsPayload.errorPayload,
      getMyTravelFlightDetails: data,
      loading: state.myTravelFlightsPayload.loading,
    },
  }
}

export const flyMyTravelInsertFlightRequest = (state, action) => {
  const payload = new MyTravelInsertFlight().request()
  return {
    ...state,
    insertFlightPayload: payload,
  }
}

export const flyMyTravelInsertFlightSuccess = (state, action) => {
  const payload = new MyTravelInsertFlight().success(action)
  const { payload: successData, flightData } = action
  analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVED_FLIGHT, { value: flightData?.item?.direction })
  dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVED_FLIGHT, { value: flightData?.item?.direction })
  if (
    successData?.data?.insertMyTravelFlightDetail?.status?.success ||
    successData?.data?.recordExist
  ) {
    const list = getUpdatedSavedFlights(flightData, state, true)
    return {
      ...state,
      insertFlightPayload: {
        ...payload,
        flightData,
        referralFlow: successData?.data?.insertMyTravelFlightDetail?.referralFlow,
      },
      myTravelFlightsPayload: list,
      removeFlightPayload: null,
    }
  } else {
    return {
      ...state,
      insertFlightPayload: payload,
      removeFlightPayload: null,
    }
  }
}

export const flyMyTravelInsertFlightFailure = (state, action) => {
  const payload = new MyTravelInsertFlight().failure()
  return {
    ...state,
    insertFlightPayload: payload,
  }
}

export const flyMyTravelRemoveFlightRequest = (state, action) => {
  const payload = new MyTravelRemoveFlight().request()
  return {
    ...state,
    removeFlightPayload: payload,
  }
}

export const flyMyTravelRemoveFlightSuccess = (state, action) => {
  const payload = new MyTravelRemoveFlight().success(action)
  const { payload: successData, flightData } = action
  if (
    successData?.data?.deleteMyTravelFlightDetail?.status?.success ||
    successData?.data?.recordRemoved
  ) {
    const list = getUpdatedSavedFlights(flightData, state, false)
    return {
      ...state,
      removeFlightPayload: { ...payload, flightData },
      myTravelFlightsPayload: list,
      insertFlightPayload: null,
    } 
  }
  return {
    ...state,
    removeFlightPayload: payload,
    insertFlightPayload: null,
  }
}

export const flyMyTravelRemoveFlightFailure = (state, action) => {
  const payload = new MyTravelRemoveFlight().failure()
  return {
    ...state,
    removeFlightPayload: payload,
  }
}

export const reducer = createReducer<MytravelState>(INITIAL_STATE, {
  [Types.FLY_CLEAR_MY_TRAVEL_FLIGHT]: flyClearMyTravelFlight,
  [Types.RESET_MY_TRAVEL_FLIGHTS]: resetMyTravelFlights,
  [Types.FLY_CLEAR_INSERT_FLIGHT_PAYLOAD]: flyClearInsertFlightPayload,
  [Types.FLY_MY_TRAVEL_FLIGHTS_PRE_REQUEST]: flyMyTravelFlightsPreRequest,
  [Types.FLY_MY_TRAVEL_FLIGHTS_REQUEST]: flyMyTravelFlightsRequest,
  [Types.FLY_MY_TRAVEL_FLIGHTS_FAILURE]: flyMyTravelFlightsFailure,
  [Types.FLY_MY_TRAVEL_FLIGHTS_SUCCESS]: flyMyTravelFlightsSuccess,
  [Types.FLY_MY_TRAVEL_FLIGHTS_UPDATE_SUBCRIPTION]: flyMyTravelFlightsUpdateSubcription,
  [Types.FLY_MY_TRAVEL_INSERT_FLIGHT_REQUEST]: flyMyTravelInsertFlightRequest,
  [Types.FLY_MY_TRAVEL_INSERT_FLIGHT_SUCCESS]: flyMyTravelInsertFlightSuccess,
  [Types.FLY_MY_TRAVEL_INSERT_FLIGHT_FAILURE]: flyMyTravelInsertFlightFailure,
  [Types.FLY_MY_TRAVEL_REMOVE_FLIGHT_REQUEST]: flyMyTravelRemoveFlightRequest,
  [Types.FLY_MY_TRAVEL_REMOVE_FLIGHT_SUCCESS]: flyMyTravelRemoveFlightSuccess,
  [Types.FLY_MY_TRAVEL_REMOVE_FLIGHT_FAILURE]: flyMyTravelRemoveFlightFailure,

})

const getUpdatedSavedFlights = (flightData, state, isFromSave: boolean) => {
  const { item } = flightData
  const myTravlesList = state.myTravelFlightsPayload
    ? Immutable.asMutable(state.myTravelFlightsPayload)
    : {
        errorFlag: false,
        errorPayload: [],
        getMyTravelFlightDetails: [],
        loading: false,
      }

  if (isFromSave) {
    const idx = myTravlesList?.getMyTravelFlightDetails?.findIndex(
      (e) =>
        e?.flightNumber === item?.flightNumber &&
        (e?.direction === item?.direction || e?.flightDirection === item?.direction) &&
        e?.scheduledDate === item?.scheduledDate,
    )
    const needToAdd = idx === -1
    myTravlesList.getMyTravelFlightDetails = [
      ...(myTravlesList?.getMyTravelFlightDetails ? myTravlesList?.getMyTravelFlightDetails : []),
      needToAdd
        ? {
            ...item,
            codeShares: item?.codeShare,
            scheduledTime: item?.timeOfFlight || item?.scheduledTime,
            needShowInSavedFlight: true,
            scheduledDate: item?.scheduledDate || item?.flightDate,
            isPassenger: flightData?.isPassenger
          }
        : {},
    ]
    myTravlesList.getMyTravelFlightDetails = myTravlesList?.getMyTravelFlightDetails.sort((a, b) =>
      moment(`${a?.scheduledDate} ${a?.scheduledTime}`, DateFormats.YearMonthDayTime).diff(
        moment(`${b?.scheduledDate} ${b?.scheduledTime}`, DateFormats.YearMonthDayTime),
      ),
    )
  } else {
    myTravlesList.getMyTravelFlightDetails = [...myTravlesList?.getMyTravelFlightDetails].filter(
      (flight) =>
        !(
          `${flight?.flightNumber}_${flight?.scheduledDate}` ===
            `${item?.flightNumber}_${item?.scheduledDate || item?.flightDate}` &&
          (flight?.direction === item?.direction || flight?.flightDirection === item?.direction)
        ),
    )
  }
  return myTravlesList
}
