import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, View } from "react-native"
import { COMPONENT_NAME, STAFF_PERK_CATEGORY_PILLS, AREA_FILTER_DATA, TERMINAL_FILTER_DATA } from "./staff-perk-filter-bar.constants"
import { INDEX_ITEM_HEIGHT, styles } from "./staff-perk-filter-bar.styles"
import { Text } from "app/elements/text"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import { translate } from "app/i18n"
import React, { MutableRefObject, useEffect, useMemo, useRef, useState } from "react"
import { ArrowDown, Filter, SortWithArrowIcon, LocationOutline } from "ichangi-fe/assets/icons"
import { color } from "app/theme"
import FilterPill from "app/components/filter-pill/filter-pill"
import Animated, { runOnJS, useAnimatedStyle, withTiming } from "react-native-reanimated"
import StaffPerkFilterBottomSheet from "../filter-bottom-sheet/filter-bottom-sheet"
import { FILTER_SECTION, SortBy, CATEGORY_LIST } from "../filter-bottom-sheet/filter-bottom-sheet.constants"
import _get from "lodash/get"
import _isEqual from "lodash/isEqual"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { ALPHABETICAL_INDEX_LIST } from "app/sections/staff-perk-category-listing/staff-perk-category-listing.constants"
import _isNumber from "lodash/isNumber"
import { ifAllTrue } from "app/utils"
import { Gesture, GestureDetector } from "react-native-gesture-handler"

const StaffPerkFilterBar = ({
  disabled,
  fetchData,
  isFirstRequest,
  listData,
  offsetRecalculationCount,
  perkItemOffsetListRef,
  rootItemOffsetRef,
  rootListRef,
  visible,
  vOffset,
}) => {
  const [isBSVisible, setIsBSVisible] = useState(false)
  const [areaFilters, setAreaFilters] = useState([])
  const [terminalFilters, setTerminalFilters] = useState([])
  const [categoryFilters, setCategoryFilters] = useState([])
  const [sortBy, setSortBy] = useState(SortBy.LatestAddedDate)
  const [isFirstLoad, setIsFirstLoad] = useState<boolean>(true)
  const [focusTab, setFocusTab] = useState(FILTER_SECTION.LOCATION)
  const originalFiltersRef: MutableRefObject<any> = useRef({})
  const currentIndexOffset: MutableRefObject<number> = useRef(-1)

  const isFiltering = useMemo(() => {
    return !!(areaFilters?.length || terminalFilters?.length || categoryFilters?.length)
  }, [areaFilters?.length, terminalFilters?.length, categoryFilters?.length])

  const activedContainerStyle = {
    contentColor: color.palette.lightPurple,
    containerStyle: { borderColor: color.palette.purpleD5BBEA, backgroundColor: color.palette.lightestPurple }
  }
  const inactiveContainerStyle = {
    contentColor: color.palette.darkestGrey,
    containerStyle: { backgroundColor: color.palette.almostWhiteGrey, borderColor: color.palette.lighterGrey }
  }

  const filterIconStyle = useMemo(() => {
    return isFiltering
      ? activedContainerStyle
      : inactiveContainerStyle
  }, [isFiltering])

  const sortByIconStyle = useMemo(() => {
    return sortBy === SortBy.LatestAddedDate
      ? inactiveContainerStyle
      : activedContainerStyle
  }, [sortBy])

  const locationIconStyle = useMemo(() => {
    return !!terminalFilters?.length
      ? activedContainerStyle
      : inactiveContainerStyle
  }, [terminalFilters?.length])

  const locationDisplayText = useMemo(() => {
    if (!terminalFilters?.length) {
      return translate("staffPerkListing.filterBar.locationTextDefault")
    }
    // Get terminal display text
    let terminalDisplayText = translate("staffPerkListing.filterBar.locationText.allTerminals")
    if (terminalFilters?.length) {
      terminalDisplayText = terminalFilters
        ?.map?.((terminal) => {
          if (terminal?.tagCode === "tj") {
            return terminal?.tagTitle
          }
          return terminal?.tagCode?.toUpperCase?.()
        })
        ?.join?.(", ")
    }

    return terminalDisplayText
  }, [JSON.stringify(terminalFilters)])

  const indexData = useMemo(() => {
    const result = {}
    for (let i = 0; i < listData?.length; i++) {
      const tenantName = listData[i]?.tenantName
      let firstLetter = tenantName[0]?.toLowerCase?.()
      if (!firstLetter) continue
      if (!/[a-z]/.test(firstLetter)) firstLetter = "#"
      if (result[firstLetter] || result[firstLetter] === 0) continue
      result[firstLetter] = i
    }
    const alphabetList = "#ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")
    let missingIndexes = []
    for (let i = 0; i < alphabetList.length; i++) {
      const letter = alphabetList[i].toLowerCase()
      if (!_isNumber(result[letter])) {
        missingIndexes.push(letter)
      } else {
        for (let j = 0; j < missingIndexes.length; j++) {
          result[missingIndexes[j]] = result[letter]
        }
        missingIndexes = []
      }
    }
    if (missingIndexes.length) {
      for (let i = 0; i < missingIndexes.length; i++) {
        result[missingIndexes[i]] = listData?.length - 1
      }
    }

    return result
  }, [JSON.stringify(listData)])

  const perkItemAccumulateHeights = useMemo(() => {
    return listData.reduce((result, _item, index) => {
      let totalHeights = 0
      for (let j = 0; j < index; j++) {
        totalHeights += perkItemOffsetListRef.current[j]
      }
      return result.concat(totalHeights)
    }, [])
  }, [JSON.stringify(listData), offsetRecalculationCount])

  const animatedStyle = useAnimatedStyle(() => {
    let marginTop = undefined
    if (vOffset.value >= 275) {
      marginTop = withTiming(52, { duration: 200 })
    } else {
      marginTop = withTiming(0, { duration: 200 })
    }
    return {
      marginTop,
    }
  }, [vOffset.value])

  const handlePressFilterIcon = (sectionTab) => {
    if (sectionTab) {
      setFocusTab(sectionTab)
    }
    setIsBSVisible(true)
  }

  const handlePressAreaPill = (item, newValue) => {
    setAreaFilters((list) => {
      let newList
      if (newValue) {
        newList = list?.concat?.(item)
      } else {
        newList = list?.filter?.((area) => area?.tagName !== item?.tagName)
      }
      originalFiltersRef.current = {
        ...originalFiltersRef.current,
        areaFilters: newList,
      }
      return newList
    })
  }

  const handlePressCategoryPill = (item, newValue) => {
    setCategoryFilters((list) => {
      let newList = list?.filter?.((val) => val !== item?.value)
      if (newValue) {
        newList = list?.concat?.(item?.value)
      }
      originalFiltersRef.current = {
        ...originalFiltersRef.current,
        categoryFilters: newList,
      }
      return newList
    })
  }

  const handleToggleSortBy = () => {
    setSortBy((val) => {
      if (val === SortBy.LatestAddedDate) {
        return SortBy.AZ
      }
      return SortBy.LatestAddedDate
    })
  }

  const trackingFilter = () => {
    const dataSortBy = sortBy === SortBy.LatestAddedDate ? "Latest Added Date" : "A-Z"
    const dataTrackingArea = areaFilters?.map((item) => `${item?.tagTitle} Area`).join(", ")
    const dataTrackingTerminal = terminalFilters?.map((item) => `${item?.tagTitle}`).join(", ")
    const dataTrackingCategory = STAFF_PERK_CATEGORY_PILLS.filter((item) =>
      categoryFilters?.includes(item?.value),
    )
      .map((item) => translate(item?.label))
      .join(", ")

    const dataTrackingAreaEmpty = "Public Area, Transit Area"
    const dataTrackingTerminalEmpty = TERMINAL_FILTER_DATA?.map((item) => `${item?.tagTitle}`).join(", ")
    const dataTrackingCategoryEmpty = CATEGORY_LIST?.map((item) => `${translate(item?.label)}`).join(", ")

    const haveArea = dataTrackingArea?.length > 0
    const haveTerminal = dataTrackingTerminal?.length > 0
    const haveCategory = dataTrackingCategory?.length > 0
    if (haveArea || haveTerminal || haveCategory) {
      const stringArea = `${haveArea ? dataTrackingArea : dataTrackingAreaEmpty} `
      const stringTerminal = `: ${haveTerminal ? dataTrackingTerminal : dataTrackingTerminalEmpty} `
      const stringCategory = `| ${haveCategory ? dataTrackingCategory : dataTrackingCategoryEmpty} `
      const stringSortBy = `| ${dataSortBy}`
      const dataTrackingGalaxy = `Apply Filter | ${stringArea}${stringTerminal}${stringCategory}${stringSortBy}`
      trackAction(AdobeTagName.CAppGalaxyFilters, {
        [AdobeTagName.CAppGalaxyFilters]: dataTrackingGalaxy,
      })
    } else if (!haveArea && !haveTerminal && !haveCategory) {
      const stringSortBy = ` | ${dataSortBy}`
      const dataTrackingGalaxy = `Apply Filter | ${dataTrackingAreaEmpty}: ${dataTrackingTerminalEmpty} | ${dataTrackingCategoryEmpty}${stringSortBy}`
      trackAction(AdobeTagName.CAppGalaxyFilters, {
        [AdobeTagName.CAppGalaxyFilters]: dataTrackingGalaxy,
      })
    }
  }

  const handlePressIndexSearch = (letter) => {
    InteractionManager.runAfterInteractions(() => {
      const dataTrackingGalaxy = `ABC Jump | ${letter.toUpperCase()}`
      trackAction(AdobeTagName.CAppGalaxyLanding, {
        [AdobeTagName.CAppGalaxyLanding]: dataTrackingGalaxy,
      })
      const indexToScroll = indexData[letter]
      if (!indexToScroll && indexToScroll !== 0) return
      const totalPerkItemHeights = perkItemAccumulateHeights[indexToScroll]
      let offset =
        Number(rootItemOffsetRef.current?.[0]) + Number(rootItemOffsetRef.current?.[1]) - 140
      offset = offset + totalPerkItemHeights
      rootListRef.current?.scrollToOffset({ animated: false, offset, viewPosition: 0 })
    })
  }

  const handleIndexGesture = (e) => {
    const index = Math.floor(e.y / INDEX_ITEM_HEIGHT)
    if (index === currentIndexOffset.current) return
    if (index >= 0 && index < ALPHABETICAL_INDEX_LIST.length) {
      runOnJS(handlePressIndexSearch)(ALPHABETICAL_INDEX_LIST[index]?.value)
      currentIndexOffset.current = index
    }
  }

  const gesture = Gesture.Pan()
    .onBegin(handleIndexGesture)
    .onUpdate(handleIndexGesture)
    .runOnJS(true)

  useEffect(() => {
    trackingFilter()
    if (isFirstLoad) {
      setIsFirstLoad(false)
      return
    }
    if (
      isBSVisible ||
      _isEqual(originalFiltersRef.current, {
        areaFilters,
        categoryFilters,
        terminalFilters,
      })
    )
      return
    fetchData({
      areaFilters,
      categoryFilters,
      sortBy,
      terminalFilters,
    })
  }, [
    JSON.stringify(areaFilters),
    JSON.stringify(terminalFilters),
    JSON.stringify(categoryFilters),
    isBSVisible,
  ])

  // Reload data when changing Sort By option
  useEffect(() => {
    fetchData({
      areaFilters,
      categoryFilters,
      sortBy,
      terminalFilters,
    })
  }, [sortBy])

  // Reload data when toggling quick filter pills
  useEffect(() => {
    if (isBSVisible) return
    fetchData({
      areaFilters,
      categoryFilters,
      sortBy,
      terminalFilters,
    })
  }, [JSON.stringify(categoryFilters)])

  if (isFirstRequest && !visible) return null
  return (
    <>
      <Animated.View
        accessibilityLabel={`${COMPONENT_NAME}_Container`}
        onLayout={(e) => (rootItemOffsetRef.current[1] = e?.nativeEvent?.layout?.height)}
        style={[styles.containerStyle, animatedStyle]}
        testID={`${COMPONENT_NAME}_Container`}
      >
        <Text
          accessibilityLabel={`${COMPONENT_NAME}_Title`}
          style={styles.titleTextStyle}
          testID={`${COMPONENT_NAME}_Title`}
          tx="staffPerkListing.filterBar.title"
        />
        <ScrollView
          accessibilityLabel={`${COMPONENT_NAME}_Pill_Container`}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filterPillsScrollViewStyle}
          testID={`${COMPONENT_NAME}_Pill_Container`}
          contentContainerStyle={styles.filterPillsContainerStyle}
        >
          <MultimediaTouchableOpacity
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Category`}
            disabled={disabled}
            onPress={() => handlePressFilterIcon(FILTER_SECTION.CATEGORIES)}
            style={[styles.filterIconContainerStyle, styles.filterIconIconOnlyContainerStyle, filterIconStyle.containerStyle]}
            testID={`${COMPONENT_NAME}_Icon_Category`}
          >
            <Filter color={filterIconStyle.contentColor} height={12} width={12} />
            {isFiltering && <View style={styles.activeDotIconStyle} />}
          </MultimediaTouchableOpacity>
          <MultimediaTouchableOpacity
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Sort`}
            disabled={disabled}
            onPress={handleToggleSortBy}
            style={[styles.filterIconContainerStyle, styles.filterIconIconOnlyContainerStyle, sortByIconStyle.containerStyle]}
            testID={`${COMPONENT_NAME}_Icon_Sort`}
          >
            <SortWithArrowIcon color={sortByIconStyle.contentColor} />
            <Text style={[styles.filterIconTextStyle, {color: sortByIconStyle.contentColor}]} text=" A-Z" />
            {sortBy !== SortBy.LatestAddedDate && <View style={styles.activeDotIconStyle} />}
          </MultimediaTouchableOpacity>
          <MultimediaTouchableOpacity
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Location`}
            disabled={disabled}
            onPress={() => handlePressFilterIcon(FILTER_SECTION.LOCATION)}
            style={[styles.filterIconContainerStyle, styles.filterIconFullContainerStyle, locationIconStyle.containerStyle]}
            testID={`${COMPONENT_NAME}_Icon_Location`}
          >
            <LocationOutline width={16} height={16} color={locationIconStyle.contentColor} />
            <Text style={[styles.filterIconTextStyle, styles.filterIconLocationTextStyle, {color: locationIconStyle.contentColor}]} text={locationDisplayText} />
            <ArrowDown color={locationIconStyle.contentColor} height={16} width={16} />
          </MultimediaTouchableOpacity>

          {AREA_FILTER_DATA.map((item, i) => {
            const isActive = areaFilters?.some?.((area) => area?.tagName === item?.tagName)
            const containerStyle = isActive ? activedContainerStyle.containerStyle : inactiveContainerStyle.containerStyle

            return (
              <FilterPill
                accessibilityLabel={`${COMPONENT_NAME}_Pill_Item_${item.tagName}`}
                active={isActive}
                disabled={disabled}
                IconComponent={item?.icon}
                key={`${item.tagName}${isActive ? "_active" : ""}-${i}`}
                label={item?.tagTitle}
                onPress={() => handlePressAreaPill(item, !isActive)}
                variant="outline"
                containerStyle={containerStyle}
              />
            )
          })}

          {STAFF_PERK_CATEGORY_PILLS.map((item) => {
            const isActive = categoryFilters.some((val) => val === item.value)
            const containerStyle = isActive ? activedContainerStyle.containerStyle : inactiveContainerStyle.containerStyle

            return (
              <FilterPill
                accessibilityLabel={`${COMPONENT_NAME}_Pill_Item_${item.value}`}
                active={isActive}
                disabled={disabled}
                IconComponent={item?.icon}
                key={`${item.value}${isActive ? "_active" : ""}`}
                label={translate(item.label)}
                onPress={() => handlePressCategoryPill(item, !isActive)}
                variant="outline"
                outlineColor={color.palette.lighterGrey}
                containerStyle={containerStyle}
              />
            )
          })}
        </ScrollView>
        {/* Alphabetical index search bar */}
        {ifAllTrue([sortBy === SortBy.AZ, !!listData?.length]) && (
          <GestureDetector gesture={Gesture.Simultaneous(gesture, Gesture.Native())}>
            <View style={styles.alphabeticalIndexSearchContainerStyle}>
              {ALPHABETICAL_INDEX_LIST.map((item) => {
                return (
                  <View style={styles.indexContainerStyle}>
                    <Text style={styles.indexTextStyle} text={item.label} />
                  </View>
                )
              })}
            </View>
          </GestureDetector>
        )}
      </Animated.View>
      <StaffPerkFilterBottomSheet
        areaFilterData={AREA_FILTER_DATA}
        areaFilters={areaFilters}
        categoryFilters={categoryFilters}
        focusTab={focusTab}
        originalFiltersRef={originalFiltersRef}
        setAreaFilters={setAreaFilters}
        setCategoryFilters={setCategoryFilters}
        setTerminalFilters={setTerminalFilters}
        setVisible={setIsBSVisible}
        terminalFilterData={TERMINAL_FILTER_DATA}
        terminalFilters={terminalFilters}
        visible={isBSVisible}
      />
    </>
  )
}

export default StaffPerkFilterBar
