import { newPresets } from "app/elements/text"
import { color } from "app/theme"
import { Platform, StyleSheet } from "react-native"

export const INDEX_ITEM_HEIGHT = 14

export const styles = StyleSheet.create({
  containerStyle: {
    paddingTop: 24,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  titleTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    paddingBottom: 4,
    paddingHorizontal: 20,
  },
  filterIconsContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    flexDirection: "row",
    gap: 8,
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    width: "100%",
  },
  filterIconContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.almostWhiteGrey,
    borderRadius: 16,
    flexDirection: "row",
    height: 32,
    justifyContent: "space-between",
    minWidth: 40,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
  },
  filterIconFullContainerStyle: {
    flex: 1,
  },
  filterIconIconOnlyContainerStyle: {
    justifyContent: "center",
  },
  filterIconSubIconStyle: {
    paddingHorizontal: 9,
  },
  filterIconTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
  },
  filterIconLocationTextStyle: {
    marginHorizontal: 3,
  },
  filterPillsScrollViewStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  filterPillsContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    columnGap: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  activeDotIconStyle: {
    backgroundColor: color.palette.lightPurple,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    borderRadius: 5,
    height: 9,
    position: "absolute",
    right: 0,
    top: 0,
    width: 9,
  },
  alphabeticalIndexSearchContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    position: "absolute",
    right: 0,
    top: 224, // 144px (filter bar container) + 80px (gap between)
    width: 15,
  },
  indexContainerStyle: {
    alignItems: "center",
    height: 14,
    justifyContent: "center",
    width: 14,
  },
  indexTextStyle: {
    ...newPresets.bold,
    fontSize: 10,
    lineHeight: 12,
  },
})
