import React, { useEffect, useRef, useContext, forwardRef, useImperativeHandle } from "react"
import { View, TouchableOpacity, Platform, Keyboard, Pressable } from "react-native"
import { ArrowLeftV2 } from "ichangi-fe/assets/icons"
import * as styles from "./search-bar.styles"
import { SearchBarProps } from "./search-bar.props"
import { translate } from "app/i18n"
import isEmpty from "lodash/isEmpty"
import SearchScreenContext from "app/screens/search/search-screen-context"
import InputField from "./input-field"
import InputFieldV2 from "./input-field-v2"
import { useDispatch } from "react-redux"
import SearchActions from "app/redux/searchRedux"
import { useRecentSearch } from "app/screens/search/recent-search-hook"
import { color } from "app/theme"

enum HelperText {
  all = "search.placeHolder.all",
  dine = "search.placeHolder.dine",
  shop = "search.placeHolder.shop",
  flight = "search.placeHolder.flights",
  airport = "search.placeHolder.airport",
  attractions = "search.placeHolder.attractions",
  events = "search.placeHolder.events",
}

export type SearchInputHandle = {
  setInputValue: (value: string) => void;
  focusInput: () => void;
};

const SearchBar = ({
  tab,
  keyword,
  onPressBack,
  onChangeKeyword,
  onSearchClear,
  onSubmitEditing,
  searchAutoCompleteFlag,
  testID = "SearchBar",
  accessibilityLabel = "SearchBar",
  isShowBack = true,
  inputProps,
  arrowLeftColor,
  containerStyle = {},
  inputContainerStyle = {},
  useInputFieldV2 = false,
  shouldFocusAfterClear = false,
  returnKeyType= "search",
  onSubmitLocal,
  autoFocusTextInput
}: SearchBarProps, ref) => {
  const dispatch = useDispatch()
  const inputElement = useRef(null)
  const [value, setValue] = React.useState("")
  const timeout = useRef(null)
  const timeoutSubmitEditing = useRef(null)
  const { setIsPressSearchKey } = useContext(SearchScreenContext)
  const { addNewRecentSearchKeyword } = useRecentSearch()
  const InputFieldComponent = useInputFieldV2 ? InputFieldV2 : InputField


  const onChangeHandler = (newValue) => {
    setValue(newValue)
    onChangeKeyword(newValue)
  }

   useImperativeHandle(ref, () => ({
     setInputValue: setValue,
     focusInput: () => {
       inputElement?.current?.focus?.()
     },
   }))

  const handleClearSearch = () => {
    clearTimeout(timeout.current)
    setValue("")
    onSearchClear()
    shouldFocusAfterClear && inputElement?.current?.focus?.()
  }

  useEffect(() => {
    if (autoFocusTextInput) {
      inputElement.current?.focus()
    }
  }, [autoFocusTextInput])
  
  useEffect(() => {
    return () => {
      clearTimeout(timeoutSubmitEditing.current)
      clearTimeout(timeout.current)
    }
  }, [])

  useEffect(() => {
    !isEmpty(keyword) && setValue(keyword)
  }, [keyword])

  const getPlacehoderValue = () => {
    const placeholder = translate(HelperText[tab.toLowerCase()])
    if (placeholder.length >= 30 && Platform.OS === "android") {
      return placeholder.substring(0, 27) + "..."
    }
    return placeholder
  }

  const handleOnSubmitEditing = () => {
    Keyboard.dismiss()
    const timeout = setTimeout(() => {
      if (setIsPressSearchKey && value.length > 1) {
        dispatch(SearchActions.setSearchKeyword(value))
        addNewRecentSearchKeyword(value)
        setIsPressSearchKey(true)
        if (onSubmitEditing) {
          onSubmitEditing()
        }
      } else if (setIsPressSearchKey) {
        value.length > 1 && addNewRecentSearchKeyword(value)
        setIsPressSearchKey(true)
      }
    }, 800)
    timeoutSubmitEditing.current = timeout
  }

  return (
    <Pressable
      onPress={Keyboard.dismiss}
      hitSlop={{ top: 30, bottom: 10 }}
      style={[styles.containerStyle, containerStyle]}
      testID={`${testID}PressableContainer`}
      accessibilityLabel={`${accessibilityLabel}PressableContainer`}
    >
      {isShowBack && (
        <TouchableOpacity
          style={styles.iconContainer}
          onPress={onPressBack}
          testID={`${testID}TouchableBack`}
          accessibilityLabel={`${accessibilityLabel}TouchableBack`}
        >
          <ArrowLeftV2 color={arrowLeftColor ?? color.palette.lightPurple} />
        </TouchableOpacity>
      )}
      <View style={[styles.inputContainer, inputContainerStyle]}>
        <InputFieldComponent
          isShowClearAll={!isEmpty(value)}
          value={value}
          returnKeyType={returnKeyType}
          highlightOnFocused={true}
          autoCapitalize="none"
          autoCorrect={false}
          maxLength={50}
          onChangeText={onChangeHandler}
          onSearchClear={handleClearSearch}
          forwardedRef={inputElement}
          onSubmitEditing={onSubmitLocal ?? handleOnSubmitEditing}
          placeHolderValue={getPlacehoderValue()}
          testID={`${testID}InputSearch`}
          accessibilityLabel={`${accessibilityLabel}InputSearch`}
          autoFocus={autoFocusTextInput}
          {...inputProps}
        />
      </View>
    </Pressable>
  )
}

export default forwardRef(SearchBar)
