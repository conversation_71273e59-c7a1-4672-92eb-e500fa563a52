import { useEffect, useState } from "react"
import { graphqlOperation } from "aws-amplify"
import { getDealsPromos } from "app/models/queries"
import restApi from "app/services/api/request"
import { env } from "app/config/env-params"
import NetInfo from "@react-native-community/netinfo"
import { ErrorType } from "./deals-promos-category-listing.constants"

const getDealsPromosListingReq = async () => {
  const params = {
    input: {
      sort_by_a_to_z: false,
      page_number: 1,
      page_size: 9999,
    },
  }
  try {
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getDealsPromos, params),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (response.statusCode === 200 && response.success) {
      if (response?.data?.errors) {
        return {
          errors: response?.data?.errors,
        }
      } else {
        return response?.data?.data?.getDealsPromosListing
      }
    }
  } catch (err) {
    console.log("err", err)
  }
}

export const useDealsPromosListingRequests = () => {
  const [loadingDealsPromosList, setLoading] = useState<boolean>(true)
  const [hasError, setError] = useState<ErrorType | null>(null)
  const [listData, setListData] = useState([])
  const [originalListData, setOriginalListData] = useState([])

  const getData = async () => {
    try {
      setLoading(true)
      setError(null)
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setError(ErrorType.NoInternet)
        setLoading(false)
        return
      }
      const response = await getDealsPromosListingReq()
      if (response) {
        if (response?.errors) {
          setError(ErrorType.ErrorDefault)
        } else {
          setListData(response?.promos)
          setOriginalListData(response?.promos)
        }
      } else {
        setError(ErrorType.ErrorDefault)
      }
    } catch (error) {
      setError(ErrorType.ErrorDefault)
    } finally {
      setLoading(false)
    }
  }

  const onRefresh = async () => {
    await getData()
  }

  const handlePressReloadError = () => {
    getData()
  }

  useEffect(() => {
    getData()
  }, [])

  return {
    hasError,
    listData,
    loadingDealsPromosList,
    handlePressReloadError,
    setListData,
    originalListData,
    onRefresh,
  }
}
