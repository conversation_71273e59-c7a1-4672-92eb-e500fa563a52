import React, { useMemo } from "react"
import { StyleSheet, View } from "react-native"
import { presets, Text } from "app/elements/text"
import LinearGradient from "react-native-linear-gradient"
import { Button } from "app/elements/button/button"
import { color } from "app/theme"
import { useSelector } from "react-redux"
import { AemSelectors } from "app/redux/aemRedux"
import { ErrorType } from "../deals-promos-category-listing.constants"
import { CloudOpsIcon } from "assets/icons"
import { BACKGROUND_IMAGE_HEIGHT } from "app/components/collapsible-header"
import { FILTER_BORDER_RADIUS } from "app/components/collapsible-header/styles"

const DealsPromosError = React.memo((props: any) => {
  const dataCommonAEM = useSelector(AemSelectors.getErrorsCommon)
  const ehr42 = dataCommonAEM?.find((e) => e?.code === "EHR42")
  const { handlePressReload, typeError, rootItemOffsetRef } = props

  const dynamicStyles = useMemo(() => {
    return StyleSheet.create({
      container: {
        marginTop: BACKGROUND_IMAGE_HEIGHT,
        paddingTop: 96,
        paddingHorizontal: 24,
        alignItems: "center",
        backgroundColor: color.palette.whiteGrey,
        height: "100%",
        borderTopLeftRadius: FILTER_BORDER_RADIUS,
        borderTopRightRadius: FILTER_BORDER_RADIUS,
      },
    })
  }, [rootItemOffsetRef?.current?.[1]])

  const renderContent = () => {
    const titleTx = ehr42?.header ? ehr42?.header : "errorOverlay.variant1.title"
    const messageTx = ehr42?.subHeader ? ehr42?.subHeader : "errorOverlay.variant1.message"
    const reloadTx = ehr42?.buttonLabel ? ehr42?.buttonLabel : "errorOverlay.variant1.reload"
    return {
      titleTx: titleTx,
      messageTx: messageTx,
      reloadTx: reloadTx,
    }
  }

  if (typeError === ErrorType.NoInternet) {
    return (
      <View style={dynamicStyles.container}>
        <CloudOpsIcon width={120} height={120} />
        <Text style={styles.titleTextStyle} tx={"errorOverlay.variant3.title"} />
        <Text style={styles.messageTextStyle} tx={"errorOverlay.variant3.message"} />
        <LinearGradient
          style={styles.reloadButtonStyle}
          start={{ x: 1, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
        >
          <Button
            onPress={handlePressReload}
            sizePreset="large"
            textPreset="buttonLarge"
            typePreset="primary"
            tx={"errorOverlay.variant3.retry"}
            backgroundPreset="light"
            statePreset="default"
          />
        </LinearGradient>
      </View>
    )
  }

  return (
    <View style={dynamicStyles.container}>
      <CloudOpsIcon width={120} height={120} />
      <Text
        style={styles.titleTextStyle}
        tx={ehr42 ? null : renderContent()?.titleTx}
        text={ehr42 ? renderContent()?.titleTx : null}
      />
      <Text
        style={styles.messageTextStyle}
        tx={ehr42 ? null : renderContent()?.messageTx}
        text={ehr42 ? renderContent()?.messageTx : null}
      />
      <LinearGradient
        style={styles.reloadButtonStyle}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
      >
        <Button
          onPress={handlePressReload}
          sizePreset="large"
          textPreset="buttonLarge"
          typePreset="primary"
          tx={ehr42 ? null : renderContent()?.reloadTx}
          text={ehr42 ? renderContent()?.reloadTx : null}
          backgroundPreset="light"
          statePreset="default"
        />
      </LinearGradient>
    </View>
  )
})

const styles = StyleSheet.create({
  // container styles moved to dynamic styles
  titleTextStyle: {
    ...presets.subTitleBold,
    marginVertical: 16,
    textAlign: "center",
  },
  messageTextStyle: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    textAlign: "center",
  },
  reloadButtonStyle: {
    width: "100%",
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 24,
  },
})

export { DealsPromosError }
