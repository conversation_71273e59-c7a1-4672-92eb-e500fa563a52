import React from "react"
import { StyleSheet, View } from "react-native"
import { color, typography } from "app/theme"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { lightGreyLoadingColors } from "app/components/explore-staff-perk-item/explore-staff-perk-item.styles"

const dataLoading = new Array(3).fill(null)

const DealsPromosLoading = React.memo(({ containerStyle }: { containerStyle?: any }) => {
  return (
    <View style={[styles.viewRootContainer, containerStyle]}>
      {dataLoading.map((_, index) => {
        return (
          <View key={index}>
            <View style={styles.viewRow}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={lightGreyLoadingColors}
                shimmerStyle={styles.imgLoading}
              />
              <View style={styles.loadingTxt}>
                <ShimmerPlaceholder
                  duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                  shimmerColors={lightGreyLoadingColors}
                  shimmerStyle={styles.titleLoading}
                />
                <ShimmerPlaceholder
                  duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                  shimmerColors={lightGreyLoadingColors}
                  shimmerStyle={styles.contentLoading}
                />
              </View>
            </View>
            {index !== dataLoading?.length - 1 && <View style={styles.viewRowBottom} />}
          </View>
        )
      })}
    </View>
  )
})

const styles = StyleSheet.create({
  viewRootContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  txtTitle: {
    fontFamily: typography.black,
    fontSize: 16,
    lineHeight: 20,
    fontWeight: "900",
    color: color.palette.almostBlackGrey,
  },
  filterLoading: {
    width: "100%",
    height: 24,
    borderRadius: 4,
    marginTop: 14,
    marginBottom: 24,
  },
  viewRow: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  loadingTxt: {
    flex: 1,
  },
  imgLoading: {
    width: 120,
    height: 80,
    borderRadius: 4,
    marginRight: 16,
  },
  titleLoading: {
    width: "100%",
    height: 12,
    borderRadius: 4,
    marginBottom: 12,
  },
  contentLoading: {
    width: "40%",
    height: 12,
    borderRadius: 4,
  },
  viewRowBottom: {
    width: "100%",
    height: 1,
    backgroundColor: color.palette.lighterGrey,
    marginBottom: 24,
  },
})

export { DealsPromosLoading }
