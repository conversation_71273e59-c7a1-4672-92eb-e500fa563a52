import { IShopChangiIcon, SaleTagIcon } from "ichangi-fe/assets/icons"

export const COMPONENT_NAME = "DealsPromosFilterBar"

export enum DealsPromosCategory {
  NewlyAdded = "NEW_STAFF_PERKS",
  Dining = "DINING_PERKS",
  Shopping = "SHOPPING_PERKS",
  IShopChangiOffers = "ISHOPCHANGI_OFFERS",
  Services = "SERVICES",
  LimitedOffers = "SEASONAL",
}

export const DEALS_PROMOS_CATEGORY_PILLS = [
  {
    label: "dealsPromosListing.filterBar.categoryPill.newlyAdded",
    value: DealsPromosCategory.NewlyAdded,
  },
  {
    label: "dealsPromosListing.filterBar.categoryPill.dining",
    value: DealsPromosCategory.Dining,
  },
  {
    label: "dealsPromosListing.filterBar.categoryPill.shopping",
    value: DealsPromosCategory.Shopping,
  },
  {
    label: "dealsPromosListing.filterBar.categoryPill.services",
    value: DealsPromosCategory.Services,
  },
  {
    icon: IShopChangiIcon,
    label: "dealsPromosListing.filterBar.categoryPill.iShopChangi",
    value: DealsPromosCategory.IShopChangiOffers,
  },
  {
    icon: SaleTagIcon,
    label: "dealsPromosListing.filterBar.categoryPill.limitedOffer",
    value: DealsPromosCategory.LimitedOffers,
  },
]
