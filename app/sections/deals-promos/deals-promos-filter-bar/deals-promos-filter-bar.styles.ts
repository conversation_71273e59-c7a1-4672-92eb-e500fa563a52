import { newPresets } from "app/elements/text"
import { color } from "app/theme"
import { StyleSheet } from "react-native"

export const INDEX_ITEM_HEIGHT = 14

export const styles = StyleSheet.create({
  containerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    overflow: "hidden",
    justifyContent: 'center',
    height: 70
  },
  filterIconContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.halfLighterGrey,
    borderRadius: 16,
    flexDirection: "row",
    height: 32,
    justifyContent: "space-between",
    minWidth: 40,
    paddingHorizontal: 12,
  },
  filterIconFullContainerStyle: {
    flex: 1,
  },
  filterIconIconOnlyContainerStyle: {
    justifyContent: "center",
  },
  filterIconSubIconStyle: {
    paddingHorizontal: 9,
  },
  filterIconTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.darkestGrey,
  },
  filterPillsScrollViewStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
  },
  filterPillsContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    columnGap: 8,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  activeDotIconStyle: {
    backgroundColor: color.palette.lightPurple,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    borderRadius: 5,
    height: 9,
    position: "absolute",
    right: 0,
    top: 0,
    width: 9,
  },
  alphabeticalIndexSearchContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.whiteGrey,
    position: "absolute",
    right: 0,
    top: 224, // 144px (filter bar container) + 80px (gap between)
    width: 15,
  },
  indexContainerStyle: {
    alignItems: "center",
    height: 14,
    justifyContent: "center",
    width: 14,
  },
  indexTextStyle: {
    ...newPresets.bold,
    fontSize: 10,
    lineHeight: 12,
  },
})
