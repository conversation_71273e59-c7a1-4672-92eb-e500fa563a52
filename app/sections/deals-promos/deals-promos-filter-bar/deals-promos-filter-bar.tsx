import { <PERSON><PERSON><PERSON>ie<PERSON>, View } from "react-native"
import { COMPONENT_NAME, DEALS_PROMOS_CATEGORY_PILLS } from "./deals-promos-filter-bar.constants"
import { styles } from "./deals-promos-filter-bar.styles"
import { translate } from "app/i18n"
import React, { MutableRefObject, useMemo, useRef, useState } from "react"
import { ArrowDown, Filter, LocationOutline, SortWithArrowIcon } from "ichangi-fe/assets/icons"
import FilterPill from "app/components/filter-pill/filter-pill"
import DealsPromosFilterBottomSheet from "../filter-bottom-sheet/filter-bottom-sheet"
import {
  AREA_LIST,
  SortBy,
  TERMINAL_LIST,
} from "../filter-bottom-sheet/filter-bottom-sheet.constants"

import { handleCondition } from "app/utils"
import { color } from "app/theme"

const DealsPromosFilterBar = ({
  isShow,
  areaFilters,
  setAreaFilters,
  terminalFilters,
  setTerminalFilters,
  categoryFilters,
  setCategoryFilters,
  sortBy,
  setSortBy,
}) => {
  const [isBSVisible, setIsBSVisible] = useState(false)
  const originalFiltersRef: MutableRefObject<any> = useRef({})

  const publicArea = useMemo(() => {
    const item = AREA_LIST.find((area) => area.value === "public")
    return {
      tagName: item?.value,
      tagTitle: translate(item?.label),
      tagCode: item?.value,
    }
  }, [])

  const transitArea = useMemo(() => {
    const item = AREA_LIST.find((area) => area.value === "transit")
    return {
      tagName: item?.value,
      tagTitle: translate(item?.label),
      tagCode: item?.value,
    }
  }, [])

  const locationDisplayText = useMemo(() => {
    if (!areaFilters?.length && !terminalFilters?.length) {
      return translate("dealsPromosListing.filterBs.location.title")
    }
    // Get terminal display text
    let terminalDisplayText = translate("dealsPromosListing.filterBs.location.title")
    if (terminalFilters?.length && terminalFilters?.length < TERMINAL_LIST?.length) {
      terminalDisplayText = terminalFilters
        ?.map?.((terminal) => {
          if (terminal?.tagCode === "jewel") {
            return terminal?.tagTitle
          }
          return terminal?.tagCode?.toUpperCase?.()
        })
        ?.join?.(", ")
    }

    return terminalDisplayText
  }, [JSON.stringify(terminalFilters)])

  const handlePressFilterIcon = () => {
    setIsBSVisible(true)
  }

  const handlePressFilterPill = (item, newValue) => {
    setCategoryFilters((list) => {
      let newList = list?.filter?.((val) => val !== item?.value)
      if (newValue) {
        newList = list?.concat?.(item?.value)
      }
      originalFiltersRef.current = {
        ...originalFiltersRef.current,
        categoryFilters: newList,
      }
      return newList
    })
  }

  const handlePressAreaFilterPill = (item, newValue) => {
    setAreaFilters((list) => {
      let newList = list?.filter?.((val) => val?.tagName !== item?.tagName)
      if (newValue) {
        newList = list?.concat?.(item)
      }
      originalFiltersRef.current = {
        ...originalFiltersRef.current,
        areaFilters: newList,
      }
      return newList
    })
  }

  const handleToggleSortBy = () => {
    setSortBy((val) => {
      if (val === SortBy.LatestAddedDate) {
        return SortBy.AZ
      }
      return SortBy.LatestAddedDate
    })
  }

  return (
    <>
      <View
        accessibilityLabel={`${COMPONENT_NAME}_Container`}
        style={[styles.containerStyle, { opacity: isShow ? 1 : 0 }]}
        testID={`${COMPONENT_NAME}_Container`}
      >
        <ScrollView
          accessibilityLabel={`${COMPONENT_NAME}_Pill_Container`}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={[styles.filterPillsScrollViewStyle]}
          testID={`${COMPONENT_NAME}_Pill_Container`}
          contentContainerStyle={[styles.filterPillsContainerStyle]}
          pointerEvents={!isShow ? "none" : "auto"}
        >
          {/* Filter Icon: Category */}
          <FilterPill
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Category`}
            active={!!categoryFilters?.length || sortBy !== SortBy.LatestAddedDate || !!terminalFilters?.length || !!areaFilters?.length}
            IconComponent={Filter}
            label={""}
            onPress={handlePressFilterIcon}
            variant="outline"
            key="filter_category"
            containerStyle={{
              minWidth: 36,
              flexDirection: "row",
              borderColor: (!!categoryFilters?.length || sortBy !== SortBy.LatestAddedDate || !!terminalFilters?.length || !!areaFilters?.length) ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
            }}
            iconSize={16}
            showDot
          />
          {/* Filter Icon: Sort */}
          <FilterPill
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Sort`}
            active={sortBy !== SortBy.LatestAddedDate}
            IconComponent={SortWithArrowIcon}
            label={translate("dealsPromosListing.filterBar.sortBy.az")}
            onPress={handleToggleSortBy}
            variant="outline"
            key="filter_sort"
            containerStyle={{
              minWidth: 40,
              flexDirection: "row",
              gap: 2,
              borderColor: sortBy !== SortBy.LatestAddedDate ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
            }}
            iconSize={16}
            showDot
          />
          {/* Filter Icon: Location */}
          <FilterPill
            accessibilityLabel={`${COMPONENT_NAME}_Icon_Location`}
            active={!!terminalFilters?.length}
            IconComponent={LocationOutline}
            label={locationDisplayText}
            onPress={handlePressFilterIcon}
            variant="outline"
            key="filter_location"
            containerStyle={{
              flexDirection: "row",
              gap: 4,
              borderColor: !!terminalFilters?.length ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
            }}
            rightIconComponent={
              <ArrowDown
                color={handleCondition(
                  !!terminalFilters?.length,
                  color.palette.lighterPurple,
                  color.palette.darkestGrey,
                )}
                height={16}
                width={16}
              />
            }
            iconSize={16}
          />
          {/* Filter Pill: Public */}
          <FilterPill
            accessibilityLabel={`${COMPONENT_NAME}_Pill_Public`}
            active={areaFilters?.some?.((item) => item.tagName === "public")}
            label={translate("dealsPromosListing.filterBar.area.public")}
            onPress={() => {
              const isActive = areaFilters?.some?.((item) => item.tagName === "public")
              handlePressAreaFilterPill(publicArea, !isActive)
            }}
            variant="outline"
            key="filter_public"
            containerStyle={{
              borderColor: areaFilters?.some?.((item) => item.tagName === "public") ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
            }}
          />
          {/* Filter Pill: Transit */}
          <FilterPill
            accessibilityLabel={`${COMPONENT_NAME}_Pill_Transit`}
            active={areaFilters?.some?.((item) => item.tagName === "transit")}
            label={translate("dealsPromosListing.filterBar.area.transit")}
            onPress={() => {
              const isActive = areaFilters?.some?.((item) => item.tagName === "transit")
              handlePressAreaFilterPill(transitArea, !isActive)
            }}
            variant="outline"
            key="filter_transit"
            containerStyle={{
              borderColor: areaFilters?.some?.((item) => item.tagName === "transit") ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
            }}
          />
          {/* Filter Pills */}
          {DEALS_PROMOS_CATEGORY_PILLS.map((item) => {
            const isActive = categoryFilters.some((val) => val === item.value)
            return (
              <FilterPill
                accessibilityLabel={`${COMPONENT_NAME}_Pill_Item_${item.value}`}
                active={isActive}
                IconComponent={item?.icon}
                key={`${item.value}${isActive ? "_active" : ""}`}
                label={translate(item.label)}
                onPress={() => handlePressFilterPill(item, !isActive)}
                variant="outline"
                containerStyle={{
                  borderColor: isActive ? color.palette.purpleD5BBEA : color.palette.lighterGrey,
                }}
                iconSize={16}
              />
            )
          })}
        </ScrollView>
      </View>
      <DealsPromosFilterBottomSheet
        areaFilters={areaFilters}
        categoryFilters={categoryFilters}
        originalFiltersRef={originalFiltersRef}
        setAreaFilters={setAreaFilters}
        setCategoryFilters={setCategoryFilters}
        setTerminalFilters={setTerminalFilters}
        setVisible={setIsBSVisible}
        terminalFilters={terminalFilters}
        visible={isBSVisible}
      />
    </>
  )
}

export default DealsPromosFilterBar
