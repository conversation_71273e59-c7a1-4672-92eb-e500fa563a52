import { DealsPromosCategory } from "../deals-promos-filter-bar/deals-promos-filter-bar.constants"

export const FILTER_SECTION = {
  LOCATION: "location",
  CATEGORIES: "categories",
}

export enum AreaFilter {
  Public = "public",
  Transit = "transit",
}

export enum TerminalFilter {
  All = "all",
  Jewel = "jewel",
  Terminal1 = "t1",
  Terminal2 = "t2",
  Terminal3 = "t3",
  Terminal4 = "t4",
}

export enum SortBy {
  LatestAddedDate = "latestAddedDate",
  AZ = "az",
}

export const CATEGORY_LIST = [
  {
    label: "dealsPromosListing.filterBs.category.newlyAdded",
    value: DealsPromosCategory.NewlyAdded,
  },
  {
    label: "dealsPromosListing.filterBs.category.dining",
    value: DealsPromosCategory.Dining,
  },
  {
    label: "dealsPromosListing.filterBs.category.shopping",
    value: DealsPromosCategory.Shopping,
  },
  {
    label: "dealsPromosListing.filterBs.category.iShopChangiOffers",
    value: DealsPromosCategory.IShopChangiOffers,
  },
  {
    label: "dealsPromosListing.filterBs.category.services",
    value: DealsPromosCategory.Services,
  },
  {
    label: "dealsPromosListing.filterBs.category.limitedOffers",
    value: DealsPromosCategory.LimitedOffers,
  },
]

export const AREA_LIST = [
  {
    label: "dealsPromosListing.filterBs.location.publicArea",
    value: AreaFilter.Public,
  },
  {
    label: "dealsPromosListing.filterBs.location.transitArea",
    value: AreaFilter.Transit,
  },
]

export const TERMINAL_LIST = [
  {
    label: "dealsPromosListing.filterBs.location.all",
    value: TerminalFilter.All,
  },
  {
    label: "dealsPromosListing.filterBs.location.jewel",
    value: TerminalFilter.Jewel,
  },
  {
    label: "dealsPromosListing.filterBs.location.terminal1",
    value: TerminalFilter.Terminal1,
  },
  {
    label: "dealsPromosListing.filterBs.location.terminal2",
    value: TerminalFilter.Terminal2,
  },
  {
    label: "dealsPromosListing.filterBs.location.terminal3",
    value: TerminalFilter.Terminal3,
  },
  {
    label: "dealsPromosListing.filterBs.location.terminal4",
    value: TerminalFilter.Terminal4,
  },
]

export const TERMINAL_SEQUENCE = ["t1", "t2", "t3", "t4", "jewel"]
