import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { Text } from "app/elements/text"
import { View, ScrollView } from "react-native"
import { styles } from "./filter-bottom-sheet.styles"
import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import {
  Cross,
  PublicAreaIcon,
  TransitAreaFillIcon,
  TransitAreaIcon,
} from "ichangi-fe/assets/icons"
import { color } from "app/theme"
import { MutableRefObject, useEffect, useMemo } from "react"
import { translate } from "app/i18n"
import FilterPill from "app/components/filter-pill/filter-pill"
import { AREA_LIST, CATEGORY_LIST, TERMINAL_LIST, TERMINAL_SEQUENCE } from "./filter-bottom-sheet.constants"
import CheckBoxOption from "./components/checkbox-option"
import LinearGradient from "react-native-linear-gradient"

interface BSProps {
  areaFilters?: any[]
  categoryFilters?: string[]
  loadingDealsPromosList?: boolean
  originalFiltersRef: MutableRefObject<any>
  setAreaFilters?: Function
  setCategoryFilters?: Function
  setTerminalFilters?: Function
  setVisible: Function
  terminalFilters?: any[]
  visible: boolean
}

const DealsPromosFilterBottomSheet = (props: BSProps) => {
  const {
    areaFilters,
    categoryFilters,
    originalFiltersRef,
    setAreaFilters,
    setCategoryFilters,
    setTerminalFilters,
    setVisible,
    terminalFilters,
    visible,
  } = props

  const newTerminalFilterData = useMemo(() => {
    return TERMINAL_LIST.map((item) => ({
      tagCode: item.value,
      tagTitle: translate(item.label),
      tagName: item.value,
    }))
  }, [])

  const clearLocationOptions = () => {
    setAreaFilters([])
    setTerminalFilters([])
  }

  const clearCategoryOptions = () => {
    setCategoryFilters([])
  }

  const handleToggleAreaOption = (item, newValue) => {
    setAreaFilters((list) => {
      if (newValue) {
        return list?.concat?.(item)
      }
      return list?.filter?.((area) => area?.tagName !== item?.tagName)
    })
  }

  const otherOptions = newTerminalFilterData?.slice(1)

  const allOptionsSelected = useMemo(() => {
    if (terminalFilters?.length === 0) return false
    return otherOptions?.every((option) =>
      terminalFilters?.some((terminal) => terminal?.tagName === option?.tagName),
    )
  }, [terminalFilters, newTerminalFilterData])

  const handleToggleAllLocationOptions = (newValue) => {
    if (newValue) {
      setTerminalFilters(newTerminalFilterData)
    } else {
      setTerminalFilters([])
    }
  }

  const handleToggleLocationOption = (item, newValue) => {
    if (item.tagName === "all") {
      handleToggleAllLocationOptions(newValue)
      return
    }
    setTerminalFilters((list) => {
      if (newValue) {
        const newList = list?.concat?.(item)
        if (newList.length === otherOptions?.length) {
          const allOption = newTerminalFilterData?.find((option) => option.tagName === "all")
          return allOption ? [allOption, ...newList] : newList
        }
        return TERMINAL_SEQUENCE?.reduce?.((sequence, code) => {
          const foundIndex = newList?.findIndex?.((terminal) => terminal?.tagCode === code)
          const foundData = newList[foundIndex]
          if (foundData) {
            newList.splice(foundIndex, 1)
            return sequence.concat(foundData)
          }
          return sequence
        }, [])
      }
      // Handle unchecking
      const filteredList = list?.filter?.((terminal) => terminal?.tagName !== item?.tagName)
      if (allOptionsSelected) {
        return filteredList?.filter?.((terminal) => terminal?.tagName !== "all")
      }
      return filteredList
    })
  }

  const handleToggleCategoryOption = (item, newValue) => {
    setCategoryFilters((list) => {
      if (newValue) {
        return list?.concat?.(item?.value)
      }
      return list?.filter?.((val) => val !== item?.value)
    })
  }

  const handleClearAllFilters = () => {
    setAreaFilters([])
    setTerminalFilters([])
    setCategoryFilters([])
  }

  const handleApplyFilters = () => {
    setVisible(false)
  }

  const handleCloseBS = () => {
    setAreaFilters(originalFiltersRef.current?.areaFilters || [])
    setTerminalFilters(originalFiltersRef.current?.terminalFilters || [])
    setCategoryFilters(originalFiltersRef.current?.categoryFilters || [])
    setVisible(false)
  }

  useEffect(() => {
    if (visible) {
      originalFiltersRef.current = {
        areaFilters,
        categoryFilters,
        terminalFilters,
      }
    }
  }, [visible])

  return (
    <BottomSheet
      animationInTiming={400}
      animationOutTiming={400}
      containerStyle={styles.containerStyle}
      isModalVisible={visible}
      onClosedSheet={handleCloseBS}
      stopDragCollapse
    >
      {/* Header */}
      <View style={styles.headerContainerStyle}>
        <Text style={styles.titleTextStyle} tx="staffPerkListing.filterBs.title" />
        <MultimediaTouchableOpacity onPress={handleCloseBS} style={styles.closeBtnStyle}>
          <Cross color={color.palette.darkestGrey} height={24} width={24} />
        </MultimediaTouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.filterFormContainerStyle} showsVerticalScrollIndicator={false}>
        {/* Location filter section */}
        <View style={styles.sectionContainerStyle}>
          <View style={styles.sectionHeaderContainerStyle}>
            <Text
              style={styles.sectionTitleTextStyle}
              tx="staffPerkListing.filterBs.location.title"
            />
            {(!!areaFilters?.length || !!terminalFilters?.length) && (
              <MultimediaTouchableOpacity onPress={clearLocationOptions}>
                <Text style={styles.sectionClearBtnStyle} tx="staffPerkListing.filterBs.clearBtn" />
              </MultimediaTouchableOpacity>
            )}
          </View>
          <View style={styles.areaFiltersContainerStyle}>
            {AREA_LIST?.map?.((item) => {
              const areaItem = {
                tagName: item.value,
                tagTitle: translate(item.label),
                tagCode: item.value,
              }
              const isActive = areaFilters?.some?.((area) => area?.tagName === item.value)
              let Icon: any = () => null
              switch (item.value) {
                case "public":
                  Icon = PublicAreaIcon
                  break
                case "transit":
                  Icon = isActive ? TransitAreaFillIcon : TransitAreaIcon
                  break
                default:
                  break
              }
              return (
                <FilterPill
                  key={item.value}
                  active={isActive}
                  size="lg"
                  label={translate(item.label)}
                  labelColor={color.palette.darkestGrey}
                  IconComponent={Icon}
                  onPress={() => handleToggleAreaOption(areaItem, !isActive)}
                  variant="outline"
                  iconSize={16}
                />
              )
            })}
          </View>
          {newTerminalFilterData?.map?.((item) => {
            const checked = terminalFilters?.some?.(
              (terminal) => terminal?.tagName === item?.tagName,
            )
            return (
              <CheckBoxOption
                checked={checked}
                label={item.tagTitle}
                key={`${item.tagName}_${checked}`}
                onCheck={(newValue) => handleToggleLocationOption(item, newValue)}
              />
            )
          })}
        </View>

        {/* Categories filter section */}
        <View style={[styles.sectionContainerStyle, styles.lastSectionContainerStyle]}>
          <View style={styles.categoryHeaderContainerStyle}>
            <View style={styles.categoryTitleContainerStyle}>
              <Text
                style={styles.sectionTitleTextStyle}
                tx="staffPerkListing.filterBs.category.title"
              />
              {!!categoryFilters?.length && (
                <MultimediaTouchableOpacity onPress={clearCategoryOptions}>
                  <Text style={styles.sectionClearBtnStyle} tx="staffPerkListing.filterBs.clearBtn" />
                </MultimediaTouchableOpacity>
              )}
            </View>
          </View>
          {CATEGORY_LIST.map((item, index) => {
            const checked = categoryFilters?.some?.((val) => val === item.value)
            return (
              <CheckBoxOption
                checked={checked}
                containerStyle={index === 0 ? { marginTop: 12 } : undefined}
                label={translate(item?.label)}
                key={`${item.value}_${checked}`}
                onCheck={(newValue) => handleToggleCategoryOption(item, newValue)}
              />
            )
          })}
        </View>
      </ScrollView>

      {/* Footer buttons */}
      <View style={styles.footerContainerStyle}>
        <MultimediaTouchableOpacity onPress={handleClearAllFilters}>
          <Text style={styles.clearAllBtnStyle} tx="staffPerkListing.filterBs.clearAllBtn" />
        </MultimediaTouchableOpacity>
        <LinearGradient
          style={styles.applyFiltersBtnContainerStyle}
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 0 }}
          colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
        >
          <MultimediaTouchableOpacity
            onPress={handleApplyFilters}
            style={styles.applyFiltersBtnStyle}
          >
            <Text
              style={styles.applyFiltersBtnLabelTextStyle}
              tx="staffPerkListing.filterBs.applyFiltersBtn"
            />
          </MultimediaTouchableOpacity>
        </LinearGradient>
      </View>
    </BottomSheet>
  )
}

export default DealsPromosFilterBottomSheet
