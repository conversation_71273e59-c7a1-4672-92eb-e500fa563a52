import { MultimediaTouchableOpacity } from "app/components/multimedia-touchable-opacity/multimedia-touchable-opacity"
import { handleCondition } from "app/utils"
import { newPresets, Text } from "app/elements/text"
import { Dimensions, StyleSheet, View } from "react-native"
import { color } from "app/theme"

interface SectionTabProps {
  activeTab?: string
  disabled?: boolean
  isActiveDot?: boolean
  label?: string
  name: string
  onPress?: () => void
}

const SectionTab = (props: SectionTabProps) => {
  const { activeTab, disabled, isActiveDot, label, name, onPress } = props

  return (
    <MultimediaTouchableOpacity
      disabled={disabled}
      onPress={onPress}
      style={styles.sectionTabContainerStyle}
    >
      <Text
        style={handleCondition(
          name === activeTab,
          [styles.sectionTabTextStyle, styles.sectionTabActiveTextStyle],
          styles.sectionTabTextStyle,
        )}
        text={label}
      />
      {isActiveDot && <View style={styles.tabActiveDotStyle} />}
    </MultimediaTouchableOpacity>
  )
}

const styles = StyleSheet.create({
  sectionTabContainerStyle: {
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
    flexDirection: "row",
    justifyContent: "center",
    height: 40,
    width: Dimensions.get("window").width / 2,
  },
  sectionTabTextStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.darkGrey999,
  },
  sectionTabActiveTextStyle: {
    color: color.palette.almostBlackGrey,
  },
  tabActiveDotStyle: {
    backgroundColor: color.palette.lightPurple,
    borderRadius: 5,
    height: 8,
    marginLeft: 8,
    width: 8,
  },
})

export default SectionTab
