import { Checkbox } from "app/elements/checkbox/checkbox"
import { newPresets } from "app/elements/text"
import { color } from "app/theme"
import { Dimensions, StyleSheet, View, ViewStyle } from "react-native"

interface PropsType {
  accessibilityLabel?: string
  checked?: boolean
  containerStyle?: ViewStyle
  label?: string
  onCheck?: (newValue: boolean) => void
  testID?: string
}

const CheckBoxOption = (props: PropsType) => {
  const { accessibilityLabel, checked, containerStyle, label, onCheck, testID } = props

  return (
    <View
      accessibilityLabel={accessibilityLabel}
      style={[styles.containerStyle, containerStyle]}
      testID={testID}
    >
      <Checkbox
        onToggle={onCheck}
        outlineStyle={styles.uncheckStyle}
        preventHighlightTextOnCheck
        textStyle={styles.checkboxLabelTextStyle}
        text={label}
        value={checked}
      />
      <View style={styles.dividerStyle} />
    </View>
  )
}

const styles = StyleSheet.create({
  containerStyle: {
    height: 38,
    marginTop: 18,
    paddingBottom: 16,
  },
  dividerStyle: {
    backgroundColor: color.palette.lighterGrey,
    bottom: 0,
    height: 1,
    left: 0,
    position: "absolute",
    width: Dimensions.get("window").width - 48,
  },
  uncheckStyle: {
    borderColor: color.palette.midGrey,
    borderWidth: 1,
  },
  checkboxLabelTextStyle: {
    ...newPresets.caption1Regular,
    color: color.palette.almostBlackGrey,
  },
})

export default CheckBoxOption
