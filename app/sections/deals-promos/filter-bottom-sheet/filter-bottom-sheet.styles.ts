import { newPresets } from "app/elements/text"
import { color } from "app/theme"
import { Dimensions, Platform, StyleSheet } from "react-native"

export const styles = StyleSheet.create({
  containerStyle: {
    height: Dimensions.get("window").height - 79,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16,
  },
  headerContainerStyle: {
    alignItems: "center",
    height: 64,
    justifyContent: "center",
  },
  titleTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
  },
  closeBtnStyle: {
    position: "absolute",
    right: 16,
    top: 20,
  },
  filterFormContainerStyle: {
    flex: 1,
  },
  footerContainerStyle: {
    alignItems: "center",
    backgroundColor: color.palette.almostWhiteGrey,
    borderTopColor: color.palette.lighterGrey,
    borderTopWidth: 1,
    flexDirection: "row",
    height: 96,
    justifyContent: "space-between",
    paddingBottom: 40,
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  sectionContainerStyle: {
    paddingHorizontal: 20,
    paddingTop: 24,
    marginBottom: 28,
  },
  lastSectionContainerStyle: {
    paddingBottom: 62,
  },
  sectionHeaderContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 12,
    gap: 8,
  },
  sectionTitleTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.almostBlackGrey,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
  },
  sectionClearBtnStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.lightPurple,
  },
  areaFiltersContainerStyle: {
    alignItems: "center",
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 16,
    rowGap: 8,
  },
  clearAllBtnStyle: {
    ...newPresets.caption1Bold,
    color: color.palette.lightPurple,
  },
  applyFiltersBtnContainerStyle: {
    borderRadius: 60,
    height: 44,
  },
  applyFiltersBtnStyle: {
    height: 44,
    paddingHorizontal: 42,
    width: "100%",
    justifyContent: "center",
  },
  applyFiltersBtnLabelTextStyle: {
    ...newPresets.bodyTextBold,
    color: color.palette.whiteGrey,
  },
  categoryHeaderContainerStyle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  categoryTitleContainerStyle: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
})
