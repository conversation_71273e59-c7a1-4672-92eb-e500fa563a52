export enum TAKE_PHOTO_STEP {
  TAKE_PHOTO = "TAKE_PHOTO",
  CONFIRM_PAGE = "CONFIRM_PAGE",
}

export enum MODAL_SCREENS {
  ONBOARDING_OVERLAY = "ONBOARDING_OVERLAY",
  SUBMITTED_PAGE = "SUBMITTED_PAGE",
}

export interface IRetroClaimConfigs {
  retro_claim_quality: number
  retro_claim_max_height: number
  retro_claim_max_width: number
}

export interface ITakeUploadPhotoContext {
  isCameraGranted: boolean
  setIsCameraGranted: (state: boolean) => void
  retroClaimConfigs?: IRetroClaimConfigs
  onOpenGalleryToSelect: () => void
}
