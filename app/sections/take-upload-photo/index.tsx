import { createContext, useEffect, useMemo, useState } from "react"
import { View, Alert, Platform } from "react-native"
import { useSelector } from "react-redux"
import DeviceInfo from "react-native-device-info"
import ImageCropPicker from "react-native-image-crop-picker"
import {
  RESULTS,
  PERMISSIONS,
  openSettings,
  request as requestPermission,
} from "react-native-permissions"

import { ITakeUploadPhotoContext } from "./types"

import { env } from "app/config/env-params"
import path from "app/services/api/apis.json"
import restApi from "app/services/api/request"

import {
  createResizedImage,
  choosePictureFromGallery,
  selectImageByImageCropPicker,
} from "app/utils/media-helper"
import { translate } from "app/i18n"
import { AemSelectors } from "app/redux/aemRedux"
import TakePhotoScreen from "./take-photo-screen"
import { DT_ANALYTICS_LOG_EVENT_NAME, dtManualActionEvent } from "app/services/firebase"

export const TakeUploadPhotoContext = createContext<ITakeUploadPhotoContext>({
  isCameraGranted: false,
  setIsCameraGranted: (state) => {},
  retroClaimConfigs: undefined,
  onOpenGalleryToSelect: () => {},
})

const TakeUploadPhoto = ({ isCameraGranted: initCameraGranted, shouldOpenGallery, onClosedSheet, handleTakePicture }) => {
  const [isCameraGranted, setIsCameraGranted] = useState(initCameraGranted)
  const [retroClaimConfigs, setRetroClaimConfigs] = useState(undefined)

  const messageCommon = useSelector(AemSelectors.getMessagesCommon)
  const msg901 = messageCommon?.find((e) => e?.code === "MSG90.1")
  const msg902 = messageCommon?.find((e) => e?.code === "MSG90.2")

  const isIOSDevice = Platform.OS === "ios"

  const rationale = {
    title: msg901?.title || translate("requestPermission.gallery.title"),
    message: msg901?.informativeText || translate("requestPermission.gallery.message"),
    buttonPositive: msg901?.secondButton || translate("requestPermission.gallery.buttonPositive"),
    buttonNegative: msg901?.firstButton || translate("requestPermission.gallery.buttonNegative"),
  }

  const handleChooseImageFromGallery = () => {
    const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_BAGGAGE_TRACKER_OPEN_GALLERY)
    setTimeout(async () => {
      try {
        let selectedImageData

        if (isIOSDevice) {
          selectedImageData = await selectImageByImageCropPicker({ multiple: false })
        } else {
          selectedImageData = await choosePictureFromGallery({ multiple: false })
        }

        if (selectedImageData) {
          dtAction.reportStringValue("selected", "success")
          ImageCropPicker.openCropper({
            mediaType: "photo",
            path: isIOSDevice ? selectedImageData?.path : selectedImageData?.uri,
            freeStyleCropEnabled: false,
            cropperCircleOverlay: true,
            width: selectedImageData?.width,
            height: selectedImageData?.width,
            // For Android
            cropperStatusBarColor: "#000000",
            cropperActiveWidgetColor: "#ab76d5",
            cropperToolbarColor: "black",
            cropperToolbarWidgetColor: "#ffffff",
            // For iOS
            cropperCancelColor: "#ab76d5",
            cropperChooseText: "Next",
            cropperChooseColor: "#ab76d5",
          })
            .then(async (croppingResult) => {
              dtAction.reportStringValue("cropping-result", "success")
              const { retro_claim_quality, retro_claim_max_height, retro_claim_max_width } =
                retroClaimConfigs || {}
              const resizeImageConfigs = {
                ...(retro_claim_max_width && { maxWidth: Number(retro_claim_max_width) }),
                ...(retro_claim_max_height && { maxHeight: Number(retro_claim_max_height) }),
                ...(retro_claim_quality && { quality: Number(retro_claim_quality) }),
              }

              dtAction.reportStringValue("configs-quality", `${retro_claim_quality}`)
              dtAction.reportStringValue("configs-max-width", `${retro_claim_max_width}`)
              dtAction.reportStringValue("configs-max-height", `${retro_claim_max_height}`)
              const base64Image = await createResizedImage({
                imageData: croppingResult,
                ...resizeImageConfigs,
              })

              handleTakePicture(base64Image)
            })
            .catch((error) => {
              dtAction.reportStringValue("cropping-result", `Error: ${error?.message || "unknown"}`)
              if (shouldOpenGallery) {
                onClosedSheet?.()
              }
            })
            .finally(() => {
              dtAction.leaveAction()
            })
        } else {
          dtAction.reportStringValue("selected", "failed")
          dtAction.leaveAction()

          if (shouldOpenGallery) {
            onClosedSheet?.()
          }
        }
      } catch (error) {
        dtAction.reportStringValue("selected", `Error: ${error?.message || "unknown"}`)
        dtAction.leaveAction()
        console.log("Error while selecting the picture from gallery", error)
        if (shouldOpenGallery) {
          onClosedSheet?.()
        }
      }
    }, 200)
  }

  const onOpenGalleryToSelect = async () => {
    if (Platform.OS === "android") {
      const systemVersion = DeviceInfo.getSystemVersion()
      const androidVersion = systemVersion?.split(".")?.[0]
      if (Number(androidVersion) < 13) {
        handleChooseImageFromGallery()
        return
      }
    }

    requestPermission(
      isIOSDevice ? PERMISSIONS.IOS.PHOTO_LIBRARY : PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
      rationale,
    ).then((result) => {
      if ([RESULTS.BLOCKED, RESULTS.DENIED].some((val) => val === result)) {
        Alert.alert(
          msg902?.title || translate("requestPermission.gallery.cannotTitle"),
          msg902?.informativeText || translate("requestPermission.gallery.cannotMessage"),
          [
            {
              text: msg902?.firstButton || translate("requestPermission.gallery.firstButton"),
              isPreferred: true,
              onPress: openSettings,
            },
            {
              text: msg902?.secondButton || translate("requestPermission.gallery.secondButton"),
              onPress: () => null,
            },
          ],
        )
      } else if (result === RESULTS.GRANTED || result === RESULTS.LIMITED) {
        handleChooseImageFromGallery()
      }
    })
  }

  const contextValue = useMemo(
    () => ({
      isCameraGranted,
      setIsCameraGranted,
      retroClaimConfigs,
      onOpenGalleryToSelect,
    }),
    [isCameraGranted, setIsCameraGranted, retroClaimConfigs, onOpenGalleryToSelect],
  )

  useEffect(() => {
    if (shouldOpenGallery) {
      onOpenGalleryToSelect()
    }
  },[shouldOpenGallery])

  useEffect(() => {
    let isMounted = true;
    const getRetroClaimConfigs = async () => {
      const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_BAGGAGE_TRACKER_GET_CONFIGS)
      try {
        const paramsArray = path.getConfigurations.split(" ")
        const method = paramsArray[0] || "POST"
        const url = env()?.API_GATEWAY_URL + paramsArray[1]
        const data = {
          keys: ["retro_claim_quality", "retro_claim_max_height", "retro_claim_max_width"],
        }
        const response = await restApi({ url, method, data })

        if (response?.data?.retro_claim_max_width) {
          dtAction.reportStringValue("status", "success")
          dtAction.reportStringValue("configs-quality", `${response?.data?.retro_claim_quality}`)
          dtAction.reportStringValue(
            "configs-max-width",
            `${response?.data?.retro_claim_max_width}`,
          )
          dtAction.reportStringValue(
            "configs-max-height",
            `${response?.data?.retro_claim_max_height}`,
          )
          if (isMounted) setRetroClaimConfigs(response?.data)
        } else {
          dtAction.reportStringValue("status", "failed")
        }
      } catch (error) {
        dtAction.reportStringValue("status", `Error: ${error?.message || "unknown"}`)
        console.log("error", JSON.stringify(error))
      } finally {
        dtAction.leaveAction()
      }
    }

    getRetroClaimConfigs()
    return () => { isMounted = false }
  }, [])

  return (
    <TakeUploadPhotoContext.Provider value={contextValue}>
      <View style={{ flex: 1, overflow: "hidden" }}>
        <TakePhotoScreen onClosedSheet={onClosedSheet} handleTakePicture={handleTakePicture} />
      </View>
    </TakeUploadPhotoContext.Provider>
  )
}

export default TakeUploadPhoto
