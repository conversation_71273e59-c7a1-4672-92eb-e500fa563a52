import { useContext, useEffect, useMemo, useRef, useState } from "react"
import { View, Alert, Linking, TouchableOpacity, Platform } from "react-native"
import Lot<PERSON><PERSON>ie<PERSON> from "lottie-react-native"
import { graphqlOperation } from "aws-amplify"
import NetInfo from "@react-native-community/netinfo"
import { useDispatch, useSelector } from "react-redux"
import { useNavigation } from "@react-navigation/native"
import LinearGradient from "react-native-linear-gradient"
import Geolocation from "react-native-geolocation-service"
import { isLocationEnabledSync } from "react-native-device-info"
import Permissions, { openSettings, PERMISSIONS, request, RESULTS } from "react-native-permissions"

import { translate } from "app/i18n"
import { Text } from "app/elements/text"
import { save, load, StorageKey } from "app/utils/storage"
import useAppStateChange, { APP_STATES } from "app/hooks/useAppStateChange"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"

import { env } from "app/config/env-params"
import restApi from "app/services/api/request"
import { ForYouSelectors } from "app/redux/forYouRedux"
import SystemActions, { SystemSelectors } from "app/redux/systemRedux"

import { vCEACheckAirportPremise } from "app/models/queries"

import { color } from "app/theme"
import { VECA_PAGES } from "./constants"
import { AEM_FILTER_TYPES, NavigationConstants } from "app/utils/constants"
import { CloseCross } from "ichangi-fe/assets/icons"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"
import {
  VceaCall,
  OnlineForm,
  VceaLiveAgent,
  VceaAskMax,
  VceaMoreOptions,
  ServiceUnavailable,
  ContactLiveAgent as ContactLiveAgentIcon,
} from "./icons"
import LoadingAnimation from "app/components/global-loading/loading-animation.json"

import styles from "./styles"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import ForYouActions from "app/redux/forYouRedux"
import { isEmpty } from "validate.js"
import { AemSelectors } from "app/redux/aemRedux"
import { trackAction } from "app/services/adobe"
import { NativeAuthSelectors } from "app/redux/nativeAuthRedux"
import { useModal } from "app/hooks/useModal"
import ErrorPage from "./error-page"
import { LoadingModal } from "app/components/loading-modal/loading-modal"

const ANIMATION_TIMING = 500
const SCREEN_NAME = "VCEA_SCREEN"
const contactUsTx = "vECA.contactUs"

const ContactUsEntryPoints = ({
  setHeaderTitleTx,
  setCurrentPage,
}) => {
  const navigation = useNavigation()
  const { handleNavigation } = useHandleNavigation()
  const {openModal, closeModal} = useModal("bottomSheetVcea")
  const isLoggedIn = useSelector(NativeAuthSelectors.isLoggedIn)

  const bottomSheetVceaData = useSelector(SystemSelectors.bottomSheetVceaData)
  const { adobeTagName } = bottomSheetVceaData || {}

  const moreOptionsData = useSelector(ForYouSelectors.moreOptionsData)
  const moreOptionsError = useSelector(ForYouSelectors.moreOptionsError)

  const navigateToSubmitSuggestionsAndFeedback = () => {
    // @ts-ignore
    navigation.navigate(NavigationConstants.submitSuggestionsAndFeedBack, {
      onGoBack: openModal,
    })
  }

  const mainCtasData = [
    {
      icon: <OnlineForm />,
      labelTx: "vECA.onlineForm",
      onPress: () => {
        trackAction(adobeTagName, {
          [adobeTagName]: `${translate(contactUsTx)} | ${translate("vECA.onlineForm")}`,
        })
        if (isLoggedIn) {
          closeModal()
          navigateToSubmitSuggestionsAndFeedback()
        } else {
          closeModal()
          // @ts-ignore
          navigation.navigate(NavigationConstants.authScreen, {
            callBackAfterLoginSuccess: navigateToSubmitSuggestionsAndFeedback,
            callBackAfterLoginCancel: openModal,
          })
        }
      },
    },
    {
      icon: <VceaCall style={styles.ctaItemIcon as any} />,
      labelTx: "vECA.call",
      onPress: () => {
        trackAction(adobeTagName, {
          [adobeTagName]: `${translate(contactUsTx)} | ${translate("vECA.call")}`,
        })
        Linking.openURL("tel:+6565956868")
      },
    },
    {
      icon: <VceaLiveAgent style={styles.ctaItemIcon as any} />,
      labelTx: "vECA.liveAgent",
      onPress: () => {
        trackAction(adobeTagName, {
          [adobeTagName]: `${translate(contactUsTx)} | ${translate("vECA.liveAgent")}`,
        })
        setHeaderTitleTx("vECA.contactLiveAgent")
        setCurrentPage(VECA_PAGES.CONTACT_LIVE_AGENT)
      },
    },
  ]

  const onGoBackFromExternalLink = () => {
    navigation.goBack()
    openModal()
  }
  const fullLineCatas = [
    {
      icon: <VceaAskMax />,
      labelTx: "vECA.askMax",
      onPress: () => {
        trackAction(adobeTagName, {
          [adobeTagName]: `${translate(contactUsTx)} | ${translate("vECA.askMax")}`,
        })
        closeModal()
        handleNavigation(NavigationTypeEnum.deepLink, NavigationValueDeepLink.chatBot, {
          onCloseWebviewBtnPress: onGoBackFromExternalLink,
        })
      },
    },
    {
      icon: <VceaMoreOptions />,
      labelTx: "vECA.moreOptions",
      onPress: () => {
        trackAction(adobeTagName, {
          [adobeTagName]: `${translate(contactUsTx)} | ${translate("vECA.moreOptions")}`,
        })
        // prettier-ignore
        const contactUsInfo = moreOptionsData?.find?.(
          (ele) => !!ele?.fragmentTags?.find(i => i?.filterType === AEM_FILTER_TYPES.CONTACT_US_VARIATION)
        )
        if (!contactUsInfo || moreOptionsError) {
          setHeaderTitleTx("")
          setCurrentPage(VECA_PAGES.ERROR_PAGE)
          return
        }
        if (contactUsInfo?.navigationValue && contactUsInfo?.navigationType) {
          closeModal()
          handleNavigation(contactUsInfo?.navigationType, contactUsInfo?.navigationValue, {
            onGoBack: onGoBackFromExternalLink,
          })
        }
      },
    },
  ]

  return (
    <View style={styles.bodyWrapper}>
      <View style={styles.ctasWrapper}>
        {mainCtasData.map((item, i) => (
          <TouchableOpacity key={i} style={styles.ctaItem} onPress={item?.onPress}>
            {item?.icon}
            <Text tx={item?.labelTx} style={styles.ctaItemLabel} />
          </TouchableOpacity>
        ))}
      </View>

      {fullLineCatas.map((item, i) => (
        <View key={i} style={styles.fullLineCtaItem}>
          <TouchableOpacity style={styles.fullLineCtaButton} onPress={item?.onPress}>
            {item?.icon}
            <Text tx={item?.labelTx} style={styles.fullLineCtaItemLabel} />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  )
}

const ContactLiveAgent = ({ onGoBackContactUsPage }) => {
  const navigation = useNavigation()
  const { appStateVisible } = useAppStateChange()
  const {openModal, closeModal} = useModal("bottomSheetVcea")

  const [checkingLocation, setCheckingLocation] = useState(true)
  const [isEnabledLocation, setIsEnabledLocation] = useState(true)
  const [isServiceUnavailable, setIsServiceUnavailable] = useState(false)
  const [shouldNotCheckLocationPermission, setShouldNotCheckLocationPermission] = useState(false)
  const isVceaLiveChat = isFlagOnCondition(useContext(AccountContext).vceaLiveChatFlag)

  const isBackFromSettingsRef = useRef(false)
  const [isOpeningSettings, setIsOpeningSettings] = useState(false)

  const pageIcon =
    isServiceUnavailable || !isVceaLiveChat ? (
      <ServiceUnavailable style={styles.contactLiveAgentIcon} />
    ) : (
      <ContactLiveAgentIcon style={styles.contactLiveAgentIcon} />
    )

  const handleGlobalNavigate = (...args) => {
    navigation.navigate(...(args as never))
  }

  const onGoToAppSettings = () => {
    Alert.alert(
      translate("vECA.enableLocationAlertTitle"),
      translate("vECA.enableLocationAlertMessage"),
      [
        {
          text: translate("vECA.enableLocationAlertSettings"),
          isPreferred: true,
          onPress: () => {
            openSettings()
            setCheckingLocation(true)
            setShouldNotCheckLocationPermission(false)
          },
        },
        {
          text: translate("vECA.enableLocationAlertOk"),
          onPress: () => null,
        },
      ],
    )
  }

  const messageTx = useMemo(() => {
    if (isServiceUnavailable || !isVceaLiveChat) {
      return "vECA.serviceUnavailableMessage"
    }
    if (!isEnabledLocation) {
      return "vECA.enableLocationMessage"
    }

    return "vECA.notInTheChangiAirportTerminalsMessage"
  }, [isEnabledLocation, isServiceUnavailable, isVceaLiveChat])

  const renderFirstCtaButton = useMemo(() => {
    let labelTx = "vECA.viewOtherChannels"
    let onPressButton = onGoBackContactUsPage

    if (!isEnabledLocation && isVceaLiveChat) {
      labelTx = "vECA.enableLocation"
      onPressButton = onGoToAppSettings
    }

    return (
      <TouchableOpacity onPress={onPressButton}>
        <LinearGradient
          start={{ x: 1, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.contactLiveAgentFirstButton}
          colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
        >
          <Text tx={labelTx} style={styles.contactLiveAgentFirstButtonText} />
        </LinearGradient>
      </TouchableOpacity>
    )
  }, [isEnabledLocation, isVceaLiveChat])

  const renderSecondCtaButton = useMemo(() => {
    if (!isEnabledLocation && isVceaLiveChat) {
      return (
        <TouchableOpacity
          onPress={onGoBackContactUsPage}
          hitSlop={{ top: 10, bottom: 10 }}
          style={styles.contactLiveAgentSecondButton}
        >
          <Text tx="vECA.notNow" style={styles.contactLiveAgentSecondButtonText} />
        </TouchableOpacity>
      )
    }
  }, [isEnabledLocation, isVceaLiveChat])

  const messageCommon = useSelector(AemSelectors.getMessagesCommon)
  const msg61 = messageCommon?.find((e) => e?.code === "MSG61")
  const msg62 = messageCommon?.find((e) => e?.code === "MSG62")
  const msg95 = messageCommon?.find((e) => e?.code === "MSG95")
  const msg96 = messageCommon?.find((e) => e?.code === "MSG96")

  const cameraRationale = {
    title: msg61?.title || translate("requestPermission.camera.title"),
    message: msg61?.message || translate("requestPermission.camera.message"),
    buttonPositive: msg61?.secondButton || translate("requestPermission.camera.buttonPositive"),
    buttonNegative: msg61?.firstButton || translate("requestPermission.camera.buttonNegative"),
  }

  const microphoneRationale = {
    title: msg95?.title || translate("requestPermission.microphone.title"),
    message: msg95?.message || translate("requestPermission.microphone.message"),
    buttonPositive: msg95?.secondButton || translate("requestPermission.microphone.buttonPositive"),
    buttonNegative: msg95?.firstButton || translate("requestPermission.microphone.buttonNegative"),
  }

  const isIOSDevice = Platform.OS === "ios"
  const cameraPermission = isIOSDevice ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA
  const microPermission = isIOSDevice
    ? PERMISSIONS.IOS.MICROPHONE
    : PERMISSIONS.ANDROID.RECORD_AUDIO

  const onGoBackFromLiveChat = () => {
    navigation.goBack()
    openModal()
  }
  const requestMicrophonePermission = async (vCEALandingPage) => {
    const isRequestedMicroPermission = await load(StorageKey.isRequestedMicroPermission)
    const permissionStatus = await Permissions.check(microPermission)
    if (permissionStatus !== RESULTS.GRANTED && !isRequestedMicroPermission) {
      request(microPermission, microphoneRationale).then((microphoneResult) => {
        if (microphoneResult === RESULTS.GRANTED) {
          if (vCEALandingPage) {
            closeModal()
            handleGlobalNavigate(NavigationConstants.webview, {
              uri: vCEALandingPage,
              originWhitelist: ["*"],
              allowsInlineMediaPlayback: true,
              mediaPlaybackRequiresUserAction: false,
              mediaCapturePermissionGrantType: "grant",
              onGoBack: onGoBackFromLiveChat,
              showHeaderLeftButton: false,
              useForward: false,
            })
            setTimeout(() => {
              onGoBackContactUsPage()
            }, 100)
          }
        } else {
          setCheckingLocation(false)
        }
      })
      save(StorageKey.isRequestedMicroPermission, true)
    } else if (permissionStatus === RESULTS.GRANTED) {
      if (vCEALandingPage) {
        closeModal()
        handleGlobalNavigate(NavigationConstants.webview, {
          uri: vCEALandingPage,
          originWhitelist: ["*"],
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserAction: false,
          mediaCapturePermissionGrantType: "grant",
          onGoBack: onGoBackFromLiveChat,
          showHeaderLeftButton: false,
          useForward: false,
        })
        setTimeout(() => {
          onGoBackContactUsPage()
        }, 100)
      }
    } else {
      setCheckingLocation(false)
      Alert.alert(
        msg96?.title || translate("vECA.microphonePermission.title"),
        msg96?.message || translate("vECA.microphonePermission.description"),
        [
          {
            text: msg96?.firstButton || translate("vECA.microphonePermission.firstButton"),
            isPreferred: true,
            onPress: () => {
              openSettings()
              setCheckingLocation(true)
              setIsOpeningSettings(true)
            },
          },
          {
            text: msg96?.secondButton || translate("vECA.microphonePermission.secondButton"),
            onPress: () => null,
          },
        ],
      )
    }
  }
  const checkCameraAndMicroPermissionAndNavigate = async (vCEALandingPage) => {
    const isRequestedCameraPermission = await load(StorageKey.isRequestedCameraPermission)
    const permissionStatus = await Permissions.check(cameraPermission)
    if (permissionStatus !== RESULTS.GRANTED && !isRequestedCameraPermission) {
      request(cameraPermission, cameraRationale).then((cameraResult) => {
        if (cameraResult === RESULTS.GRANTED) {
          requestMicrophonePermission(vCEALandingPage)
        } else {
          setCheckingLocation(false)
        }
      })
      save(StorageKey.isRequestedCameraPermission, true)
    } else if (permissionStatus === RESULTS.GRANTED) {
      requestMicrophonePermission(vCEALandingPage)
    } else {
      setCheckingLocation(false)
      Alert.alert(
        msg62?.title || translate("retroClaims.needAccessPermission.title"),
        msg62?.message || translate("retroClaims.needAccessPermission.description"),
        [
          {
            text: msg62?.firstButton || translate("retroClaims.needAccessPermission.firstButton"),
            isPreferred: true,
            onPress: () => {
              openSettings()
              setCheckingLocation(true)
              setIsOpeningSettings(true)
            },
          },
          {
            text: msg62?.secondButton || translate("retroClaims.needAccessPermission.secondButton"),
            onPress: () => null,
          },
        ],
      )
    }
  }

  useEffect(() => {
    const checkIsInTheChangiAirportTerminals = async (locationData) => {
      const input = {
        lat: locationData?.coords?.latitude || "",
        long: locationData?.coords?.longitude || "",
      }
      try {
        const response = await restApi({
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(vCEACheckAirportPremise, { input }),
          parameters: {},
          headers: { "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY },
        })

        const { isInAirportPremise, vCEALandingPage } =
          response?.data?.data?.vCEACheckAirportPremise || {}
        if (isInAirportPremise && vCEALandingPage) {
          setShouldNotCheckLocationPermission(true)
          checkCameraAndMicroPermissionAndNavigate(vCEALandingPage)
        } else {
          setCheckingLocation(false)
        }
        if (response?.data?.data?.vCEACheckAirportPremise === null) {
          setIsServiceUnavailable(true)
        }
      } catch (error) {
        setCheckingLocation(false)
        setIsServiceUnavailable(true)
      }
    }

    const checkLocationPermission = async () => {
      const { isConnected } = await NetInfo.fetch()
      if (!isConnected) {
        setCheckingLocation(false)
        setIsServiceUnavailable(true)
        return
      }

      const isLocationEnabled = isLocationEnabledSync()
      if (isLocationEnabled) {
        const iOSLocationPermissions = [
          PERMISSIONS.IOS.LOCATION_ALWAYS,
          PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        ]
        const androidLocationPermissions = [
          PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
        ]
        const permissionsToCheck =
          Platform.OS === "ios" ? iOSLocationPermissions : androidLocationPermissions
        const permissionStatus = await Permissions.checkMultiple(permissionsToCheck)

        const isGranted = !!Object.values(permissionStatus || {}).find((i) => i === RESULTS.GRANTED)
        setIsEnabledLocation(isGranted)

        if (isGranted) {
          if (!isEnabledLocation) {
            setCheckingLocation(true)
          }

          Geolocation.getCurrentPosition(
            (position) => checkIsInTheChangiAirportTerminals(position),
            (error) => console.log(error?.code, error?.message),
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
          )
        }

        if (!isGranted) {
          setCheckingLocation(true)
          setIsEnabledLocation(false)

          setTimeout(() => {
            setCheckingLocation(false)
          }, 1000)
        }
      } else {
        setCheckingLocation(false)
        setIsEnabledLocation(false)
      }
    }

    const shouldCheckLocationPermission = !shouldNotCheckLocationPermission || isBackFromSettingsRef.current
    if (appStateVisible === APP_STATES.ACTIVE && isVceaLiveChat && shouldCheckLocationPermission) {
      checkLocationPermission()
      if (isBackFromSettingsRef.current) {
        isBackFromSettingsRef.current = false
      }
    }

    if (!isVceaLiveChat) {
      setCheckingLocation(false)
    }
  }, [appStateVisible, isVceaLiveChat, isBackFromSettingsRef.current, shouldNotCheckLocationPermission])

  useEffect(() => {
    if (appStateVisible.match(/inactive|background/) && isOpeningSettings) {
      setIsOpeningSettings(false)
      isBackFromSettingsRef.current = true
    }
  }, [appStateVisible, isOpeningSettings])

  if (checkingLocation) {
    return (
      <View style={styles.checkingLocation}>
        <LottieView style={styles.lottieStyle} source={LoadingAnimation} autoPlay loop />
      </View>
    )
  }

  return (
    <View
      style={{
        ...styles.contactLiveAgentWrapper,
        paddingBottom: !isVceaLiveChat || isServiceUnavailable ? 5 : 0,
      }}
    >
      {pageIcon}
      {(isServiceUnavailable || !isVceaLiveChat) && (
        <Text tx="vECA.serviceUnavailable" style={styles.serviceUnavailable} />
      )}
      <Text tx={messageTx} style={styles.contactLiveAgentMessage} />
      {renderFirstCtaButton}
      {renderSecondCtaButton}
    </View>
  )
}

const BottomSheetVcea = () => {
  const dispatch = useDispatch()
  const isPressRetryRef = useRef(false)
  const isInternetErrorRef = useRef(false)
  const isMoreOptionsErrorRef = useRef(false)

  const {isModalVisible, closeModal} = useModal("bottomSheetVcea")

  const [headerTitleTx, setHeaderTitleTx] = useState(contactUsTx)
  const [currentPage, setCurrentPage] = useState(VECA_PAGES.CONTACT_US)
  const moreOptionsData = useSelector(ForYouSelectors.moreOptionsData)
  const moreOptionsError = useSelector(ForYouSelectors.moreOptionsError)
  const moreOptionsFetching = useSelector(ForYouSelectors.moreOptionsFetching)

  const checkConnection = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (!isConnected || moreOptionsError) {
      setHeaderTitleTx("")
      isInternetErrorRef.current = !isConnected
      isMoreOptionsErrorRef.current = moreOptionsError
      setCurrentPage(VECA_PAGES.ERROR_PAGE)
    }
  }


  useEffect(() => {
    if (isModalVisible) {
      checkConnection()
    }
  }, [isModalVisible])

  useEffect(() => {
    if (!isEmpty(moreOptionsData) && isPressRetryRef.current) {
      isPressRetryRef.current = false
      isMoreOptionsErrorRef.current = false
      const contactUsInfo = moreOptionsData?.find?.(
        (ele) =>
          !!ele?.fragmentTags?.find((i) => i?.filterType === AEM_FILTER_TYPES.CONTACT_US_VARIATION),
      )
      if (contactUsInfo?.navigationValue && contactUsInfo?.navigationType) {
        setHeaderTitleTx(contactUsTx)
        setCurrentPage(VECA_PAGES.CONTACT_US)
      }
    }
  }, [moreOptionsData])

  const onGoBackContactUsPage = () => {
    setHeaderTitleTx(contactUsTx)
    setCurrentPage(VECA_PAGES.CONTACT_US)
  }

  const onPressRetryBottomSheetError = async () => {
    const { isConnected } = await NetInfo.fetch()

    if (!isConnected) {
      return
    }

    const contactUsInfo = moreOptionsData?.find?.(
      (ele) =>
        !!ele?.fragmentTags?.find((i) => i?.filterType === AEM_FILTER_TYPES.CONTACT_US_VARIATION),
    )
    const shouldShowContactUsPage = isInternetErrorRef.current || contactUsInfo?.navigationValue && contactUsInfo?.navigationType
    if (shouldShowContactUsPage) {
      setHeaderTitleTx(contactUsTx)
      setCurrentPage(VECA_PAGES.CONTACT_US)
      if (isInternetErrorRef.current) isInternetErrorRef.current = false
      return
    }

    isPressRetryRef.current = true
    if (isMoreOptionsErrorRef.current) {
      dispatch(ForYouActions.moreOptionsRequest())
    }
  }

  const mappingBodyContent = {
    [VECA_PAGES.CONTACT_US]: (
      <ContactUsEntryPoints
        setCurrentPage={setCurrentPage}
        setHeaderTitleTx={setHeaderTitleTx}
      />
    ),
    [VECA_PAGES.CONTACT_LIVE_AGENT]: (
      <ContactLiveAgent
        onGoBackContactUsPage={onGoBackContactUsPage}
      />
    ),
    [VECA_PAGES.ERROR_PAGE]: (
      <ErrorPage onPressRetry={onPressRetryBottomSheetError} />
    ),
  }

  const onCloseSheet = () => {
    if ([VECA_PAGES.CONTACT_US, VECA_PAGES.ERROR_PAGE].includes(currentPage)) {
      closeModal()
      setCurrentPage(VECA_PAGES.CONTACT_US)
      dispatch(SystemActions.resetBottomSheetVceaData())
    }
    if (currentPage === VECA_PAGES.CONTACT_LIVE_AGENT) {
      onGoBackContactUsPage()
    }
  }

  return (
    <BottomSheet
      stopDragCollapse
      onBackPressHandle
      onClosedSheet={onCloseSheet}
      isModalVisible={isModalVisible}
      animationInTiming={ANIMATION_TIMING}
      animationOutTiming={ANIMATION_TIMING}
      containerStyle={styles.wrapperContainer}
    >
      <View style={styles.headerWrapper}>
        <Text tx={headerTitleTx} style={styles.headerTitle} />
        <TouchableOpacity
          onPress={onCloseSheet}
          style={styles.closeButton}
          hitSlop={{ top: 10, left: 10, right: 10, bottom: 10 }}
        >
          <CloseCross style={styles.closeIcon as any} />
        </TouchableOpacity>
      </View>
      {mappingBodyContent[currentPage]}
      <LoadingModal visible={isModalVisible && moreOptionsFetching} />
    </BottomSheet>
  )
}

export default BottomSheetVcea
